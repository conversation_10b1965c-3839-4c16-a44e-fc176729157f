// ========== 認證相關資料模型 ==========

export interface Certification {
  certificationId: number;
  schoolId: number;
  level: number;
  reviewStatus: number;
  reviewDate?: Date;
  approvedDate?: Date;
  createdTime: Date;
  updatedTime?: Date;
  createdUserId?: number;
  reviewerId?: number;
  addType?: string;
  status: number;
}

export interface CertificationWithSchoolInfo extends Certification {
  schoolName?: string;
  schoolEnglishName?: string;
  reviewerAccount?: string;
  applicantName?: string;
}

export interface CertificationListItem {
  id: number;
  status: number;
  statusInfo: {
    label: string;
    icon: string;
    description: string;
    color: string;
    bgColor: string;
  };
  typeInfo: {
    name: string;
    fullName: string;
    level: number;
    isRenewal: boolean;
    icon: string;
  };
  applicantName: string;
  applyDate: Date;
  reviewStatus: number;
  reviewDate?: Date;
  passDate?: Date;
  isEditable: boolean;
  isDeletable: boolean;
  canSubmit: boolean;
  canView: boolean;
}

export interface CertificationStatistics {
  total: number;
  drafts: number;
  pending: number;
  passed: number;
  inReview: number;
  returned: number;
}

export interface CertificationAvailability {
  id: string;
  name: string;
  level: number;
  available: boolean;
  reason?: string;
  frontendId: string;
}

export interface CertificationTypeInfo {
  name: string;
  fullName: string;
  level: number;
  isRenewal: boolean;
  icon: string;
}

export interface CertificationRequest {
  certificationType: string;
  level: number;
}

export interface SchoolAccount {
  schoolId: number;
}

export interface ExistingCertification {
  level: number;
  reviewStatus: number;
  approvedDate?: Date;
}

// 資料庫查詢結果介面
export interface CertificationQueryResult {
  CertificationId: number;
  SchoolId: number;
  Level: number;
  ReviewStatus: number;
  ReviewDate?: Date;
  ApprovedDate?: Date;
  CreatedTime: Date;
  UpdatedTime?: Date;
  CreatedUserId?: number;
  ReviewerId?: number;
  AddType?: string;
  Status: number;
  ApplicantName?: string;
  SchoolName?: string;
  SchoolEnglishName?: string;
  ReviewerAccount?: string;
}

// API 回應介面
export interface CertificationListResponse {
  success: boolean;
  data: {
    all: CertificationListItem[];
    drafts: CertificationListItem[];
    pending: CertificationListItem[];
    passed: CertificationListItem[];
    statistics: CertificationStatistics;
  };
  message?: string;
}

export interface CertificationAvailabilityResponse {
  success: boolean;
  data: {
    availability: CertificationAvailability[];
    hasPassedGreenFlag: boolean;
    greenFlagApprovedYearsAgo: number;
    greenFlagR1ApprovedYearsAgo: number;
    greenFlagR2ApprovedYearsAgo: number;
  };
  message?: string;
}

export interface CertificationDetailRequest {
  certificationId: number;
}

export interface CertificationDetailResponse {
  success: boolean;
  data: Certification;
  message?: string;
}

export interface CertificationCreateResponse {
  success: boolean;
  data: Certification;
  message: string;
}

export interface CertificationUpdateResponse {
  success: boolean;
  data: Certification;
  message: string;
}

export interface CertificationDeleteResponse {
  success: boolean;
  message: string;
}

// 查詢參數類型
export interface CertificationListQueryParams {}

export interface CertificationAvailabilityQueryParams {}

export interface CertificationDetailParams {
  certificationId: number;
}

export interface CertificationCreateParams {}

export interface CertificationUpdateParams {
  certificationId: number;
}

export interface CertificationDeleteParams {
  certificationId: number;
}
