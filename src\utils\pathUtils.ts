/**
 * 路徑處理工具函數
 * 統一處理開發環境與生產環境的路徑差異
 */

/**
 * 獲取前端基礎路徑
 * 在開發環境返回 "/"，在生產環境返回 "/apply/" 等
 */
export const getFrontendBasePath = (): string => {
  return import.meta.env.BASE_URL || '/';
};

/**
 * 獲取完整的前端基礎 URL
 * 包含 origin + base path
 */
export const getFrontendBaseUrl = (): string => {
  return window.location.origin + getFrontendBasePath();
};

/**
 * 構建前端頁面 URL
 * @param path 相對路徑（不需要開頭的 /）
 */
export const buildFrontendUrl = (path: string): string => {
  const basePath = getFrontendBasePath();
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${window.location.origin}${basePath}${cleanPath}`;
};

/**
 * 構建靜態資源 URL
 * @param path 資源路徑（如 "img/logo.png"）
 */
export const buildAssetUrl = (path: string): string => {
  const basePath = getFrontendBasePath();
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${basePath}${cleanPath}`;
};

/**
 * 處理 API 返回的相對路徑，轉換為完整 URL
 * @param relativePath API 返回的相對路徑
 * @param baseUrl API 基礎 URL（可選，預設使用當前 origin）
 */
export const resolveApiAssetUrl = (relativePath: string, baseUrl?: string): string => {
  if (!relativePath) return '';
  
  // 如果已經是完整 URL，直接返回
  if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
    return relativePath;
  }
  
  const base = baseUrl || window.location.origin;
  const cleanPath = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;
  return `${base}${cleanPath}`;
};