/**
 * 共用常數模組入口點
 * 統一匯出所有常數定義，提供一致性的匯入介面
 * 
 * @description 此檔案為 shared/constants 目錄的主要入口，
 * 讓其他模組可以從單一位置匯入所需的常數
 * @version 1.0.0
 */

// === 角色相關常數 ===
export {
  ALLOWED_ROLES,
  ROLE_MAPPING,
  AllowedRole,
  getRoleDisplayName,
  isValidRole,
  normalizeRole
} from './roles';

// === 錯誤碼相關常數 ===
export {
  AuthErrorCode,
  ErrorMessages,
  LEGACY_ERROR_CODES,
  LEGACY_ERROR_CODE_MAPPING,
  HTTP_STATUS_MAPPING,
  getErrorMessage,
  isValidErrorCode,
  createErrorResponse
} from './error-codes';

// === 驗證模式相關常數 ===
export {
  ValidationPatterns,
  ValidationHelper,
  ValidationResult,
  CommonValidationRules
} from './validation-patterns';

/**
 * 快速存取常用功能的便利匯出
 */
export const Constants = {
  // 角色相關
  Roles: {
    ALLOWED_ROLES,
    ROLE_MAPPING,
    getRoleDisplayName,
    isValidRole,
    normalizeRole
  },
  
  // 錯誤處理相關
  Errors: {
    AuthErrorCode,
    ErrorMessages,
    getErrorMessage,
    createErrorResponse
  },
  
  // 驗證相關
  Validation: {
    Patterns: ValidationPatterns,
    Helper: ValidationHelper,
    Rules: CommonValidationRules
  }
} as const;