// ========== 個人資料相關常數 ==========

// 會員角色類型
export const MEMBER_ROLE_TYPE = {
  SCHOOL: "school",
  EPA: "epa",
  TUTOR: "tutor",
} as const;

// 角色欄位映射
export const ROLE_FIELD_MAP = {
  IS_SCHOOL_PARTNER: 1,
  IS_EPA_USER: 1,
  IS_GUIDANCE_TEAM: 1,
} as const;

// 語言代碼
export const LOCALE_CODE = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;

// 資料狀態
export const DATA_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;

// 預設值
export const DEFAULT_VALUES = {
  MEMBER_ROLE: MEMBER_ROLE_TYPE.SCHOOL,
  LOCALE: LOCALE_CODE.ZH_TW,
  STATUS: DATA_STATUS.ACTIVE,
} as const;

// 錯誤訊息
export const PROFILE_ERROR_MESSAGES = {
  USER_NOT_AUTHENTICATED: "使用者未認證",
  TOKEN_MISSING: "缺少 Token",
  MEMBER_NOT_FOUND: "找不到會員資料",
  SCHOOL_NOT_FOUND: "找不到學校資料",
  INVALID_DATA: "資料格式不正確",
  UPDATE_FAILED: "更新失敗",
  ACCESS_DENIED: "沒有權限存取此資料",
  INVALID_COUNTY: "無效的縣市",
  INVALID_DISTRICT: "無效的區域",
  CONTACT_LIMIT_EXCEEDED: "聯絡人數量超過限制",
  STATISTICS_INVALID: "統計資料格式不正確",
} as const;

// 成功訊息
export const PROFILE_SUCCESS_MESSAGES = {
  PROFILE_RETRIEVED: "會員資料獲取成功",
  PROFILE_UPDATED: "會員資料更新成功",
  SCHOOL_BASIC_UPDATED: "學校基本資料更新成功",
  PRINCIPAL_UPDATED: "校長資料更新成功",
  CONTACTS_UPDATED: "聯絡人資料更新成功",
  STATISTICS_UPDATED: "學校統計資料更新成功",
} as const;

// 欄位長度限制
export const FIELD_LIMITS = {
  MEMBER_NAME: 50,
  JOB_TITLE: 100,
  EMAIL: 255,
  PHONE: 20,
  ADDRESS: 500,
  URL: 500,
  INTRODUCTION: 2000,
  SCHOOL_NAME: 200,
  PRINCIPAL_NAME: 50,
  CONTACT_NAME: 50,
} as const;

// 聯絡人限制
export const CONTACT_LIMITS = {
  MAX_CONTACTS: 10,
  MIN_CONTACTS: 0,
} as const;

// 統計資料範圍
export const STATISTICS_RANGES = {
  MIN_VALUE: 0,
  MAX_VALUE: 99999,
} as const;

// 工具函數：判斷會員角色類型
export const determineMemberRole = (isSchoolPartner: number, isEpaUser: number, isGuidanceTeam: number): string => {
  if (isEpaUser === ROLE_FIELD_MAP.IS_EPA_USER) {
    return MEMBER_ROLE_TYPE.EPA;
  }
  if (isGuidanceTeam === ROLE_FIELD_MAP.IS_GUIDANCE_TEAM) {
    return MEMBER_ROLE_TYPE.TUTOR;
  }
  return MEMBER_ROLE_TYPE.SCHOOL;
};

// 工具函數：檢查是否為學校身份
export const isSchoolMember = (roleType: string): boolean => {
  return roleType === MEMBER_ROLE_TYPE.SCHOOL;
};

// 工具函數：檢查是否為EPA身份
export const isEpaMember = (roleType: string): boolean => {
  return roleType === MEMBER_ROLE_TYPE.EPA;
};

// 工具函數：檢查是否為輔導員身份
export const isTutorMember = (roleType: string): boolean => {
  return roleType === MEMBER_ROLE_TYPE.TUTOR;
};

// 工具函數：驗證統計資料
export const validateStatisticsValue = (value: number): boolean => {
  return value >= STATISTICS_RANGES.MIN_VALUE && value <= STATISTICS_RANGES.MAX_VALUE;
};

// 工具函數：驗證聯絡人數量
export const validateContactsCount = (count: number): boolean => {
  return count >= CONTACT_LIMITS.MIN_CONTACTS && count <= CONTACT_LIMITS.MAX_CONTACTS;
};

// 工具函數：驗證Email格式
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 工具函數：驗證電話格式
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\d\-\+\(\)\s]+$/;
  return phoneRegex.test(phone);
};
