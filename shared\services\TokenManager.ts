/**
 * Token 管理服務
 * 統一前後端的 Token 獲取邏輯，提供智能環境檢測
 * 
 * @description 整合原本分散在各處的 Token 獲取邏輯，支援前端 sessionStorage 和後端 HTTP Headers
 * @version 1.0.0
 */

import { ITokenSource, IFrontendTokenSource, IBackendTokenSource } from '../interfaces/ITokenSource';

/**
 * Token 管理器主類別
 * 提供統一的 Token 獲取、設定和管理介面
 */
export class TokenManager {
  /**
   * 前端 Token 管理：從 sessionStorage 獲取 Token
   * 自動檢測瀏覽器環境，避免 SSR 問題
   * 
   * @returns Token 字串或 null（如果不存在或非瀏覽器環境）
   * 
   * @example
   * ```typescript
   * // 前端使用
   * const token = TokenManager.getTokenFromStorage();
   * if (token) {
   *   console.log('找到用戶 Token');
   * }
   * ```
   */
  static getTokenFromStorage(): string | null {
    try {
      // 檢查瀏覽器環境
      if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
        return null;
      }
      
      // 優先從 sessionStorage 取得（推薦方式）
      let token = sessionStorage.getItem('userToken');
      
      // 回退到其他可能的儲存鍵名（相容性）
      if (!token) {
        token = sessionStorage.getItem('authToken') || 
                sessionStorage.getItem('token') ||
                sessionStorage.getItem('accessToken');
      }
      
      return token ? token.trim() : null;
    } catch (error) {
      console.warn('無法存取 sessionStorage:', error);
      return null;
    }
  }

  /**
   * 後端 Token 管理：從 HTTP Headers 獲取 Token
   * 支援多種 Header 格式，按優先級檢查
   * 
   * @param headers HTTP Headers 物件
   * @returns Token 字串或 null（如果不存在）
   * 
   * @example
   * ```typescript
   * // Express.js 中間件使用
   * const token = TokenManager.getTokenFromHeaders(req.headers);
   * if (token) {
   *   console.log('找到認證 Token');
   * }
   * ```
   */
  static getTokenFromHeaders(headers: Record<string, string | string[]>): string | null {
    if (!headers || typeof headers !== 'object') {
      return null;
    }

    // 正規化 headers：轉為小寫並處理陣列值
    const normalizedHeaders: Record<string, string> = {};
    for (const [key, value] of Object.entries(headers)) {
      const lowerKey = key.toLowerCase();
      normalizedHeaders[lowerKey] = Array.isArray(value) ? value[0] : value;
    }

    // 優先級 1: x-user-token（系統標準格式）
    let token = normalizedHeaders['x-user-token'];
    if (token && token.trim()) {
      return token.trim();
    }
    
    // 優先級 2: Authorization Bearer token（標準 HTTP 認證）
    const authHeader = normalizedHeaders['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7).trim();
      if (token) {
        return token;
      }
    }
    
    // 優先級 3: 其他可能的自訂 headers（相容性）
    const customHeaders = [
      'x-auth-token',
      'x-access-token',
      'authentication',
      'token'
    ];
    
    for (const headerName of customHeaders) {
      token = normalizedHeaders[headerName];
      if (token && token.trim()) {
        return token.trim();
      }
    }
    
    return null;
  }

  /**
   * 統一 Token 獲取介面
   * 智能判斷環境（前端/後端）並使用適當的方法獲取 Token
   * 
   * @param source 可選的資料來源（通常是 Request 物件）
   * @returns Token 字串或 null
   * 
   * @example
   * ```typescript
   * // 前端使用（自動偵測）
   * const frontendToken = TokenManager.getToken();
   * 
   * // 後端使用（傳入 request）
   * const backendToken = TokenManager.getToken(req);
   * 
   * // 或明確傳入 headers
   * const headerToken = TokenManager.getToken({ headers: req.headers });
   * ```
   */
  static getToken(source?: any): string | null {
    try {
      // 情況 1: 瀏覽器環境（前端）
      if (typeof window !== 'undefined') {
        return this.getTokenFromStorage();
      }
      
      // 情況 2: 傳入了包含 headers 的物件（後端）
      if (source?.headers) {
        return this.getTokenFromHeaders(source.headers);
      }
      
      // 情況 3: 直接傳入 headers 物件
      if (source && typeof source === 'object' && !source.headers) {
        // 假設直接傳入的就是 headers 物件
        return this.getTokenFromHeaders(source);
      }
      
      return null;
    } catch (error) {
      console.warn('Token 獲取失敗:', error);
      return null;
    }
  }

  /**
   * 設定 Token 到適當的儲存位置
   * 根據環境自動選擇儲存方式
   * 
   * @param token 要儲存的 Token
   * @param options 儲存選項
   * 
   * @example
   * ```typescript
   * // 前端儲存
   * TokenManager.setToken('new-token-value');
   * 
   * // 後端通常不需要儲存 Token，但可以用於快取
   * TokenManager.setToken('token', { environment: 'backend' });
   * ```
   */
  static setToken(token: string, options: { environment?: 'frontend' | 'backend' } = {}): void {
    try {
      if (!token || typeof token !== 'string') {
        throw new Error('Token 必須是非空字串');
      }

      const trimmedToken = token.trim();
      if (!trimmedToken) {
        throw new Error('Token 不能是空白字串');
      }

      // 前端環境：儲存到 sessionStorage
      if (typeof window !== 'undefined' && options.environment !== 'backend') {
        sessionStorage.setItem('userToken', trimmedToken);
        
        // 清除其他可能的舊 Token（維護一致性）
        const oldTokenKeys = ['authToken', 'token', 'accessToken'];
        oldTokenKeys.forEach(key => {
          if (sessionStorage.getItem(key)) {
            sessionStorage.removeItem(key);
          }
        });
      }
      
      // 後端環境：通常不需要儲存，但可以用於記錄
      if (options.environment === 'backend') {
        console.log(`Token 已設定 (後端): ${trimmedToken.substring(0, 8)}...`);
      }
    } catch (error) {
      console.error('設定 Token 失敗:', error);
      throw error;
    }
  }

  /**
   * 移除儲存的 Token
   * 清除所有可能的 Token 儲存位置
   * 
   * @example
   * ```typescript
   * // 登出時清除 Token
   * TokenManager.removeToken();
   * ```
   */
  static removeToken(): void {
    try {
      if (typeof window !== 'undefined' && typeof sessionStorage !== 'undefined') {
        // 清除所有可能的 Token 儲存鍵
        const tokenKeys = ['userToken', 'authToken', 'token', 'accessToken'];
        tokenKeys.forEach(key => {
          sessionStorage.removeItem(key);
        });
        
        // 同時清除 localStorage 中可能的 Token（安全考量）
        if (typeof localStorage !== 'undefined') {
          tokenKeys.forEach(key => {
            localStorage.removeItem(key);
          });
        }
      }
    } catch (error) {
      console.warn('清除 Token 時發生錯誤:', error);
    }
  }

  /**
   * 檢查是否存在有效的 Token
   * 
   * @param source 可選的資料來源
   * @returns 是否存在 Token
   * 
   * @example
   * ```typescript
   * // 檢查前端是否有 Token
   * if (TokenManager.hasToken()) {
   *   console.log('用戶已登入');
   * }
   * 
   * // 檢查後端請求是否有 Token
   * if (TokenManager.hasToken(req)) {
   *   console.log('請求包含認證 Token');
   * }
   * ```
   */
  static hasToken(source?: any): boolean {
    const token = this.getToken(source);
    return token !== null && token.length > 0;
  }

  /**
   * 驗證 Token 格式的基本檢查
   * 不進行實際的認證驗證，只檢查格式
   * 
   * @param token 要檢查的 Token
   * @returns 是否為有效格式
   * 
   * @example
   * ```typescript
   * const token = TokenManager.getToken();
   * if (token && TokenManager.isValidTokenFormat(token)) {
   *   // 進行進一步的認證驗證
   *   await validateToken(token);
   * }
   * ```
   */
  static isValidTokenFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    const trimmedToken = token.trim();
    
    // 基本長度檢查（避免過短的 Token）
    if (trimmedToken.length < 8) {
      return false;
    }
    
    // 檢查是否只包含有效字元（字母、數字、連字號、底線）
    const validTokenPattern = /^[a-zA-Z0-9\-_]+$/;
    if (!validTokenPattern.test(trimmedToken)) {
      return false;
    }
    
    return true;
  }
}

/**
 * 前端專用 Token 來源實作
 * 實作 IFrontendTokenSource 介面
 */
export class FrontendTokenSource implements IFrontendTokenSource {
  getToken(): string | null {
    return TokenManager.getTokenFromStorage();
  }

  setToken(token: string): void {
    TokenManager.setToken(token, { environment: 'frontend' });
  }

  removeToken(): void {
    TokenManager.removeToken();
  }

  hasToken(): boolean {
    return TokenManager.hasToken();
  }

  getTokenInfo() {
    const token = this.getToken();
    if (!token) return null;

    return {
      token,
      createdAt: new Date(),
      source: 'storage' as const,
      secure: true
    };
  }

  isTokenExpired(): boolean {
    // sessionStorage Token 不會自動過期，需要後端驗證
    return false;
  }

  getTokenTimeToLive(): number {
    // sessionStorage Token 沒有 TTL 概念
    return -1;
  }

  getFromSessionStorage(): string | null {
    return TokenManager.getTokenFromStorage();
  }

  getFromLocalStorage(): string | null {
    try {
      if (typeof localStorage === 'undefined') return null;
      return localStorage.getItem('userToken') || 
             localStorage.getItem('authToken') || 
             null;
    } catch {
      return null;
    }
  }

  getFromCookie(cookieName = 'userToken'): string | null {
    try {
      if (typeof document === 'undefined') return null;
      
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.split('=').map(c => c.trim());
        if (name === cookieName) {
          return decodeURIComponent(value);
        }
      }
      return null;
    } catch {
      return null;
    }
  }

  setToSessionStorage(token: string): void {
    this.setToken(token);
  }

  setToLocalStorage(token: string): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('userToken', token);
      }
    } catch (error) {
      console.warn('無法設定 localStorage:', error);
    }
  }

  setToCookie(token: string, cookieName = 'userToken'): void {
    try {
      if (typeof document !== 'undefined') {
        document.cookie = `${cookieName}=${encodeURIComponent(token)}; path=/; secure; samesite=strict`;
      }
    } catch (error) {
      console.warn('無法設定 Cookie:', error);
    }
  }
}

/**
 * 後端專用 Token 來源實作
 * 實作 IBackendTokenSource 介面
 */
export class BackendTokenSource implements IBackendTokenSource {
  private headers: Record<string, string | string[]>;

  constructor(headers: Record<string, string | string[]>) {
    this.headers = headers || {};
  }

  getToken(): string | null {
    return TokenManager.getTokenFromHeaders(this.headers);
  }

  setToken(_token: string): void {
    // 後端通常不設定 Token 到 Headers（只讀）
    console.warn('後端 Token 來源不支援設定操作');
  }

  removeToken(): void {
    // 後端通常不移除 Token 從 Headers（只讀）
    console.warn('後端 Token 來源不支援移除操作');
  }

  hasToken(): boolean {
    return this.getToken() !== null;
  }

  getFromHeaders(headers: Record<string, string | string[]>, headerNames?: string[]): string | null {
    if (headerNames) {
      for (const headerName of headerNames) {
        const value = headers[headerName.toLowerCase()];
        const token = Array.isArray(value) ? value[0] : value;
        if (token && token.trim()) {
          return token.trim();
        }
      }
      return null;
    }
    return TokenManager.getTokenFromHeaders(headers);
  }

  getFromAuthorizationHeader(headers: Record<string, string | string[]>): string | null {
    const authHeader = headers['authorization'] || headers['Authorization'];
    const headerValue = Array.isArray(authHeader) ? authHeader[0] : authHeader;
    
    if (headerValue && headerValue.startsWith('Bearer ')) {
      return headerValue.substring(7).trim();
    }
    return null;
  }

  getFromCustomHeader(headers: Record<string, string | string[]>, headerName: string): string | null {
    const value = headers[headerName.toLowerCase()];
    const token = Array.isArray(value) ? value[0] : value;
    return token && token.trim() ? token.trim() : null;
  }

  getFromQuery(query: Record<string, any>, paramName = 'token'): string | null {
    const token = query[paramName];
    return token && typeof token === 'string' ? token.trim() : null;
  }

  getFromBody(body: Record<string, any>, fieldName = 'token'): string | null {
    const token = body[fieldName];
    return token && typeof token === 'string' ? token.trim() : null;
  }
}

// 預設匯出
export default TokenManager;