import React, { useState, useMemo } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import PasswordForm from "@/components/PasswordForm";
import { useFontSize } from "@/hooks/useFontSize";
import { useAuth } from "@/hooks/useAuth";
import { useRoutePermissions } from "@/components/RoleBasedRoutes";
import {
  formatUserDisplayNameByRole,
  truncateDisplayName,
  safeFormatUserDisplayName,
  validateUserData,
} from "@/utils/userDisplayUtils";

// 根據使用者身份定義導航項目
const getNavItems = (userRole: "school" | "epa" | "tutor" | undefined) => {
  // 按照用戶要求的選單結構
  const menuItems = [
    // 1. 基本資料維護 - 所有身份都可見
    {
      label: "基本資料維護",
      path: "/profile",
      roles: ["school", "epa", "tutor"],
    },

    // 2. 認證申請 - 只有學校身份可見
    { label: "認證申請", path: "/certificate", roles: ["school"] },

    // 3. 校園新聞投稿 - 只有學校身份可見
    { label: "校園新聞投稿", path: "/news", roles: ["school"] },

    // 4. 輔導申請 - 只有學校身份可見
    { label: "輔導申請", path: "/guidance-apply", roles: ["school"] },

    // 5. 自然人綁定 - 只有學校身份可見
    { label: "自然人憑證綁定", path: "/ein", roles: ["school"] },

    // 6. 學校管理 - 只有政府部門和輔導人員可見
    { label: "學校管理", path: "/school-management", roles: ["epa", "tutor"] },
  ];

  // 根據使用者身份過濾可見項目
  if (!userRole) return [];

  return menuItems.filter((item) => item.roles.includes(userRole));
};

// 格式化使用者身份顯示
const formatUserRole = (
  role: "school" | "epa" | "tutor" | undefined
): string => {
  const roleMap = {
    school: "生態學校",
    epa: "縣市政府",
    tutor: "輔導人員",
  };
  return role ? roleMap[role] : "訪客";
};

// 獲取身份對應的徽章變體
const getRoleBadgeVariant = (role: "school" | "epa" | "tutor" | undefined) => {
  switch (role) {
    case "school":
      return "default";
    case "epa":
      return "secondary";
    case "tutor":
      return "outline";
    default:
      return "secondary";
  }
};

const Navbar = () => {
  const { fontSize, setFontSize } = useFontSize();
  const location = useLocation();
  const navigate = useNavigate();
  const { authState, logout } = useAuth();
  const { userRole } = useRoutePermissions();
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);

  const navItems = getNavItems(userRole);

  // 格式化使用者顯示名稱，使用 useMemo 快取結果
  const formattedUserName = useMemo(() => {
    try {
      if (!authState.user) return "未知使用者";

      // 驗證使用者資料
      const validation = validateUserData(authState.user);
      if (!validation.isValid) {
        console.warn("⚠️ [Navbar] 使用者資料不完整:", validation.missingFields);
      }

      const fullName = safeFormatUserDisplayName(authState.user);
      return truncateDisplayName(fullName, 35);
    } catch (error) {
      console.error("❌ [Navbar] 格式化使用者名稱失敗:", error);
      return authState.user?.name || "未知使用者";
    }
  }, [authState.user]);

  // 完整名稱用於 tooltip
  const fullUserName = useMemo(() => {
    try {
      return safeFormatUserDisplayName(authState.user);
    } catch (error) {
      console.error("❌ [Navbar] 格式化完整使用者名稱失敗:", error);
      return authState.user?.name || "未知使用者";
    }
  }, [authState.user]);

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/login", { replace: true });
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // 如果用戶未登入，不顯示導航欄
  if (!authState.isAuthenticated || !authState.user) {
    return null;
  }

  return (
    <nav
      className="w-full bg-[#38513A] text-white flex flex-col gap-0 shadow-md"
      aria-label="主要選單"
      role="navigation"
    >
      {/* 頂端使用者與右側區塊 */}
      <div className="flex items-center justify-between px-8 py-3 w-full flex-wrap">
        {/* 左：使用者名稱 */}
        <div className="flex items-center">
          <Link
            to="/dashboard"
            className="font-bold text-[1.25rem] hover:underline focus:outline-none focus-visible:ring-2 focus-visible:ring-white rounded transition"
            tabIndex={0}
            title={fullUserName} // 完整名稱作為 tooltip
            aria-label={`使用者：${fullUserName} (返回儀表板)`}
          >
            {formattedUserName}
          </Link>
        </div>

        {/* 右：返回官網/修改密碼/登出 */}
        <div className="flex items-center gap-4">
          <a
            href="https://ecocampus.moenv.gov.tw/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:underline focus:outline-none focus-visible:ring-2 focus-visible:ring-white rounded transition"
            aria-label="返回生態學校官方網站（開新視窗）"
          >
            返回官網
          </a>

          {/* 修改密碼對話框 */}
          <Dialog
            open={isPasswordDialogOpen}
            onOpenChange={setIsPasswordDialogOpen}
          >
            <DialogTrigger asChild>
              <button
                className="hover:underline focus:outline-none focus-visible:ring-2 focus-visible:ring-white rounded transition bg-transparent border-none text-white cursor-pointer"
                aria-label="修改密碼"
              >
                修改密碼
              </button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>修改密碼</DialogTitle>
              </DialogHeader>
              <PasswordForm onSuccess={() => setIsPasswordDialogOpen(false)} />
            </DialogContent>
          </Dialog>

          <button
            onClick={handleLogout}
            className="hover:underline focus:outline-none focus-visible:ring-2 focus-visible:ring-white rounded transition bg-transparent border-none text-white cursor-pointer"
            aria-label="登出"
          >
            登出
          </button>
        </div>
      </div>

      {/* 中間：功能選單與字級調整 */}
      <div className="flex justify-between items-center w-full border-t border-white border-opacity-20 px-8 flex-wrap">
        {/* 左：功能選單（置中排版） */}
        <div className="flex-1 flex justify-center">
          <div
            className="flex gap-2 py-2 flex-wrap justify-center"
            aria-label="功能選單"
            role="menubar"
          >
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                role="menuitem"
                tabIndex={0}
                aria-current={
                  location.pathname === item.path ? "page" : undefined
                }
                className={`px-5 py-2 rounded hover:bg-green-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-white transition ${
                  location.pathname === item.path ? "bg-green-800" : ""
                } font-bold whitespace-nowrap`}
              >
                {item.label}
              </Link>
            ))}
          </div>
        </div>

        {/* 右：字級控制 */}
        <div
          className="flex items-center gap-1 text-white px-2 py-1 rounded"
          aria-label="字體大小調整"
          role="group"
        >
          <Button
            aria-label="減小字體"
            variant="secondary"
            onClick={() => setFontSize("sm")}
            className={`px-2 py-1 bg-transparent text-white hover:bg-white hover:text-green-800 focus:bg-white focus:text-green-800 ${
              fontSize === "sm" ? "bg-white text-green-800" : ""
            }`}
            style={{ fontSize: "1.125rem" }}
          >
            小
          </Button>
          <Button
            aria-label="預設字體"
            variant="secondary"
            onClick={() => setFontSize("base")}
            className={`px-2 py-1 bg-transparent text-white hover:bg-white hover:text-green-800 focus:bg-white focus:text-green-800 ${
              fontSize === "base" ? "bg-white text-green-800" : ""
            }`}
            style={{ fontSize: "1.125rem" }}
          >
            中
          </Button>
          <Button
            aria-label="放大字體"
            variant="secondary"
            onClick={() => setFontSize("lg")}
            className={`px-2 py-1 bg-transparent text-white hover:bg-white hover:text-green-800 focus:bg-white focus:text-green-800 ${
              fontSize === "lg" ? "bg-white text-green-800" : ""
            }`}
            style={{ fontSize: "1.125rem" }}
          >
            大
          </Button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
