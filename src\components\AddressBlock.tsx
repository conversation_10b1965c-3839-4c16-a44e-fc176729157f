
import React from "react";
import { Input } from "@/components/ui/input";

interface Option {
  value: string;
  label: string;
}

interface AddressBlockProps {
  city: Option;
  district: Option;
  address: string;
}

const AddressBlock: React.FC<AddressBlockProps> = ({ city, district, address }) => (
  <div>
    <div className="flex gap-3">
      <div className="flex-1 flex flex-col">
        <label className="font-bold">
          <span className="text-destructive mr-1">*</span>縣市
        </label>
        <select
          value={city.value}
          disabled
          className="bg-gray-100 rounded border px-3 py-2 font-size-base"
          aria-readonly
          tabIndex={-1}
        >
          <option value={city.value}>{city.label}</option>
        </select>
      </div>
      <div className="flex-1 flex flex-col">
        <label className="font-bold">
          <span className="text-destructive mr-1">*</span>城鄉區
        </label>
        <select
          value={district.value}
          disabled
          className="bg-gray-100 rounded border px-3 py-2 font-size-base"
          aria-readonly
          tabIndex={-1}
        >
          <option value={district.value}>{district.label}</option>
        </select>
      </div>
    </div>
    <div className="mt-3">
      <label htmlFor="address" className="font-bold">
        <span className="text-destructive mr-1">*</span>地址
      </label>
      <Input
        id="address"
        value={address}
        disabled
        className="bg-gray-100"
        readOnly
      />
    </div>
  </div>
);

export default AddressBlock;
