import crypto from "crypto";

// 驗證結果介面
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings?: Record<string, string>;
}

// 密碼強度配置
export interface PasswordRequirements {
  minLength?: number;
  maxLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
  forbiddenPatterns?: string[];
}

// 預設密碼要求
const DEFAULT_PASSWORD_REQUIREMENTS: Required<PasswordRequirements> = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: false,
  forbiddenPatterns: ["password", "123456", "admin"],
};

/**
 * 驗證電子郵件格式
 */
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== "string") return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 驗證電話號碼格式（台灣格式）
 */
export function validatePhoneNumber(phone: string): boolean {
  if (!phone || typeof phone !== "string") return false;
  // 支援台灣手機和市話格式
  const phoneRegex = /^(\+886|0)?[2-9]\d{7,8}$|^(\+886|0)?9\d{8}$/;
  return phoneRegex.test(phone.replace(/[-\s()]/g, ""));
}

/**
 * 驗證密碼強度
 */
export function validatePassword(password: string, requirements: Partial<PasswordRequirements> = {}): ValidationResult {
  const req = { ...DEFAULT_PASSWORD_REQUIREMENTS, ...requirements };
  const errors: Record<string, string> = {};

  if (!password || typeof password !== "string") {
    errors.password = "密碼不能為空";
    return { isValid: false, errors };
  }

  // 長度檢查
  if (password.length < req.minLength) {
    errors.minLength = `密碼長度至少需要 ${req.minLength} 個字元`;
  }

  if (password.length > req.maxLength) {
    errors.maxLength = `密碼長度不能超過 ${req.maxLength} 個字元`;
  }

  // 大寫字母檢查
  if (req.requireUppercase && !/[A-Z]/.test(password)) {
    errors.uppercase = "密碼必須包含至少一個大寫字母";
  }

  // 小寫字母檢查
  if (req.requireLowercase && !/[a-z]/.test(password)) {
    errors.lowercase = "密碼必須包含至少一個小寫字母";
  }

  // 數字檢查
  if (req.requireNumbers && !/\d/.test(password)) {
    errors.numbers = "密碼必須包含至少一個數字";
  }

  // 特殊字元檢查
  if (req.requireSpecialChars && !/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) {
    errors.specialChars = "密碼必須包含至少一個特殊字元";
  }

  // 禁用模式檢查
  const lowerPassword = password.toLowerCase();
  for (const pattern of req.forbiddenPatterns) {
    if (lowerPassword.includes(pattern.toLowerCase())) {
      errors.forbidden = `密碼不能包含常見的弱密碼模式：${pattern}`;
      break;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

/**
 * 驗證 GUID 格式
 */
export function validateGUID(guid: string): boolean {
  if (!guid || typeof guid !== "string") return false;
  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return guidRegex.test(guid);
}

/**
 * 驗證中文姓名
 */
export function validateChineseName(name: string): boolean {
  if (!name || typeof name !== "string") return false;
  // 允許中文字元和常見標點符號
  const chineseNameRegex = /^[\u4e00-\u9fa5\u3400-\u4dbf\uf900-\ufaff\u2e80-\u2eff·]{1,10}$/;
  return chineseNameRegex.test(name);
}

/**
 * 驗證英文姓名
 */
export function validateEnglishName(name: string): boolean {
  if (!name || typeof name !== "string") return false;
  // 允許英文字母、空格、點號和撇號
  const englishNameRegex = /^[a-zA-Z\s.'-]{1,50}$/;
  return englishNameRegex.test(name);
}

/**
 * 驗證網址格式
 */
export function validateURL(url: string): boolean {
  if (!url || typeof url !== "string") return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 驗證正整數
 */
export function validatePositiveInteger(value: unknown): boolean {
  if (value === null || value === undefined) return false;
  const num = parseInt(value, 10);
  return !isNaN(num) && num > 0 && num.toString() === value.toString();
}

/**
 * 驗證非負整數
 */
export function validateNonNegativeInteger(value: unknown): boolean {
  if (value === null || value === undefined) return false;
  const num = parseInt(value, 10);
  return !isNaN(num) && num >= 0 && num.toString() === value.toString();
}

/**
 * 驗證字串長度範圍
 */
export function validateStringLength(str: string, minLength: number = 0, maxLength: number = 255): boolean {
  if (typeof str !== "string") return false;
  return str.length >= minLength && str.length <= maxLength;
}

/**
 * 清理和驗證輸入字串
 */
export function sanitizeAndValidateString(
  input: unknown,
  maxLength: number = 255,
  allowEmpty: boolean = false
): { isValid: boolean; sanitized?: string; error?: string } {
  if (input === null || input === undefined) {
    return allowEmpty ? { isValid: true, sanitized: "" } : { isValid: false, error: "輸入不能為空" };
  }

  const str = String(input).trim();

  if (!allowEmpty && str.length === 0) {
    return { isValid: false, error: "輸入不能為空" };
  }

  if (str.length > maxLength) {
    return { isValid: false, error: `輸入長度不能超過 ${maxLength} 個字元` };
  }

  // 移除潛在的危險字元
  const sanitized = str
    .replace(/[<>"'&]/g, "") // 移除 HTML 特殊字元
    .replace(/[\p{Cc}]/gu, ""); // 移除控制字元

  return { isValid: true, sanitized };
}

/**
 * 驗證檔案類型
 */
export function validateFileType(filename: string, allowedTypes: string[]): boolean {
  if (!filename || typeof filename !== "string") return false;

  const extension = filename.toLowerCase().split(".").pop();
  if (!extension) return false;

  return allowedTypes.includes(extension);
}

/**
 * 驗證檔案大小
 */
export function validateFileSize(fileSize: number, maxSizeBytes: number): boolean {
  return typeof fileSize === "number" && fileSize > 0 && fileSize <= maxSizeBytes;
}

/**
 * 生成安全的隨機 Token
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString("hex");
}

/**
 * 生成 UUID v4
 */
export function generateUUID(): string {
  return crypto.randomUUID();
}

/**
 * 雜湊密碼（MD5 + Salt）
 */
export function hashPassword(password: string, salt?: string): { hash: string; salt: string } {
  const finalSalt = salt || generateSecureToken(16);
  const hash = crypto
    .createHash("md5")
    .update(finalSalt + password)
    .digest("hex");
  return { hash, salt: finalSalt };
}

/**
 * 驗證密碼雜湊
 */
export function verifyPassword(password: string, hash: string, salt: string): boolean {
  const { hash: newHash } = hashPassword(password, salt);
  return newHash === hash;
}

/**
 * 批量驗證物件屬性
 */
export function validateObjectProperties<T extends Record<string, unknown>>(
  obj: T,
  validators: Record<keyof T, (value: unknown) => boolean | ValidationResult>
): ValidationResult {
  const errors: Record<string, string> = {};
  const warnings: Record<string, string> = {};

  for (const [key, validator] of Object.entries(validators)) {
    const value = obj[key];
    const result = validator(value);

    if (typeof result === "boolean") {
      if (!result) {
        errors[key] = `${key} 驗證失敗`;
      }
    } else {
      if (!result.isValid) {
        Object.assign(errors, result.errors);
      }
      if (result.warnings) {
        Object.assign(warnings, result.warnings);
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings: Object.keys(warnings).length > 0 ? warnings : undefined,
  };
}

/**
 * 創建自定義驗證器
 */
export function createValidator<T>(validationFn: (value: T) => boolean, errorMessage: string) {
  return (value: T): ValidationResult => ({
    isValid: validationFn(value),
    errors: validationFn(value) ? {} : { validation: errorMessage },
  });
}
