// ========== 檔案服務 - 業務邏輯和檔案操作 ==========

import fs from "fs/promises";
import path from "path";
import { executeQuery, executeQuerySingle } from "../config/database-mssql.js";
import { FileConfigManager } from "../config/config-manager.js";
import {
  UploadedFile,
  FileInfo,
  SchoolLogoInfo,
  UploadDiagnostics,
  FileEntry,
  SchoolInfo,
  FileEntryQueryResult,
  SchoolQueryResult,
  SchoolLogoQueryResult,
  SupportedFileTypes,
  DirectoryStatus,
  PermissionStatus,
} from "../models/file.js";
import {
  FILE_SIZE_LIMITS,
  FILE_COUNT_LIMITS,
  ALLOWED_MIME_TYPES,
  FILE_TYPES,
  LOCALE_CODES,
  DATA_STATUS,
  FILE_ERROR_MESSAGES,
  FILE_SUCCESS_MESSAGES,
  isAllowedFileType,
  isValidLogoFormat,
  isValidFileSize,
  isValidFilename,
  generateFilename,
  generateLogoFilename,
  parseOriginalName,
  getSupportedFileTypes,
} from "../constants/file.js";

export class FileService {
  private static configManager = FileConfigManager.getInstance();
  private static uploadConfig = this.configManager.getUploadConfig();

  // 確保上傳目錄存在
  static async ensureUploadDirectory(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  // 確保通用上傳目錄
  static async ensureGeneralUploadDir(): Promise<void> {
    await this.ensureUploadDirectory(this.uploadConfig.generalUploadPath);
  }

  // 處理多檔案上傳
  static async processMultipleFileUpload(
    files: Express.Multer.File[],
    metadata: { file_type?: string; certification_sid?: string; question_sid?: string }
  ): Promise<UploadedFile[]> {
    if (!files || files.length === 0) {
      throw new Error(FILE_ERROR_MESSAGES.NO_FILE_UPLOADED);
    }

    return files.map((file) => ({
      originalName: Buffer.from(file.originalname, "latin1").toString("utf8"),
      filename: file.filename,
      fileUrl: `${this.uploadConfig.basePath}/${file.filename}`,
      size: file.size,
      mimetype: file.mimetype,
      uploadDate: new Date().toISOString(),
    }));
  }

  // 處理單檔案上傳
  static async processSingleFileUpload(
    file: Express.Multer.File | undefined,
    metadata: { file_type?: string; certification_sid?: string; question_sid?: string }
  ): Promise<UploadedFile> {
    if (!file) {
      throw new Error(FILE_ERROR_MESSAGES.NO_FILE_UPLOADED);
    }

    return {
      originalName: Buffer.from(file.originalname, "latin1").toString("utf8"),
      filename: file.filename,
      fileUrl: `${this.uploadConfig.basePath}/${file.filename}`,
      size: file.size,
      mimetype: file.mimetype,
      uploadDate: new Date().toISOString(),
    };
  }

  // 刪除檔案
  static async deleteFile(filename: string): Promise<void> {
    // 驗證檔名安全性
    if (!isValidFilename(filename)) {
      throw new Error(FILE_ERROR_MESSAGES.INVALID_FILENAME);
    }

    const filePath = path.join(this.uploadConfig.generalUploadPath, filename);

    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
    } catch (error) {
      throw new Error(FILE_ERROR_MESSAGES.FILE_NOT_FOUND);
    }
  }

  // 獲取檔案資訊
  static async getFileInfo(filename: string): Promise<FileInfo> {
    // 驗證檔名安全性
    if (!isValidFilename(filename)) {
      throw new Error(FILE_ERROR_MESSAGES.INVALID_FILENAME);
    }

    const filePath = path.join(this.uploadConfig.generalUploadPath, filename);

    try {
      const stats = await fs.stat(filePath);

      return {
        filename,
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        fileUrl: `${this.uploadConfig.basePath}/${filename}`,
      };
    } catch (error) {
      throw new Error(FILE_ERROR_MESSAGES.FILE_NOT_FOUND);
    }
  }

  // 下載檔案
  static async getDownloadPath(filename: string): Promise<{ filePath: string; originalName: string }> {
    // 驗證檔名安全性
    if (!isValidFilename(filename)) {
      throw new Error(FILE_ERROR_MESSAGES.INVALID_FILENAME);
    }

    const filePath = path.join(this.uploadConfig.generalUploadPath, filename);

    try {
      await fs.access(filePath);
      const originalName = parseOriginalName(filename);
      return { filePath, originalName };
    } catch (error) {
      throw new Error(FILE_ERROR_MESSAGES.FILE_NOT_FOUND);
    }
  }

  // 獲取支援的檔案類型
  static getSupportedFileTypes(): SupportedFileTypes {
    return getSupportedFileTypes();
  }

  // 確保校徽上傳目錄
  static async ensureLogoUploadDirectory(): Promise<string> {
    const logoDir = this.uploadConfig.schoolLogoPath;

    try {
      await fs.access(logoDir);
      console.log(`📸 [Logo] 目錄已存在: ${logoDir}`);
    } catch (accessError) {
      console.log(`📸 [Logo] 目錄不存在，嘗試創建: ${logoDir}`);

      try {
        await fs.mkdir(logoDir, { recursive: true });
        console.log(`📸 [Logo] 目錄創建成功: ${logoDir}`);
        await fs.access(logoDir);
        console.log(`📸 [Logo] 目錄創建驗證成功: ${logoDir}`);
      } catch (mkdirError) {
        console.error(`📸 [Logo] 目錄創建失敗: ${logoDir}`, mkdirError);

        // 嘗試使用備用路徑
        const fallbackDir = path.join(process.cwd(), this.uploadConfig.basePath, "school-logos");
        console.log(`📸 [Logo] 嘗試使用備用目錄: ${fallbackDir}`);

        try {
          await fs.mkdir(fallbackDir, { recursive: true });
          console.log(`📸 [Logo] 備用目錄創建成功: ${fallbackDir}`);
          return fallbackDir;
        } catch (fallbackError) {
          console.error(`📸 [Logo] 備用目錄創建也失敗: ${fallbackDir}`, fallbackError);
          throw new Error(FILE_ERROR_MESSAGES.UPLOAD_DIR_CREATE_FAILED);
        }
      }
    }

    // 檢查目錄寫入權限
    try {
      const testFile = path.join(logoDir, ".write-test");
      await fs.writeFile(testFile, "test");
      await fs.unlink(testFile);
      console.log(`📸 [Logo] 目錄寫入權限檢查通過: ${logoDir}`);
    } catch (writeError) {
      console.error(`📸 [Logo] 目錄寫入權限檢查失敗: ${logoDir}`, writeError);
      throw new Error(FILE_ERROR_MESSAGES.NO_WRITE_PERMISSION);
    }

    console.log(`📸 [Logo] 校徽保存目錄準備完成: ${logoDir}`);
    return logoDir;
  }

  // 處理校徽上傳（使用指定的學校 ID）
  static async processSchoolLogoUploadWithSchoolId(
    file: Express.Multer.File | undefined,
    accountId: string | number,
    schoolId: number
  ): Promise<SchoolLogoInfo> {
    if (!file) {
      throw new Error(FILE_ERROR_MESSAGES.PLEASE_SELECT_LOGO);
    }

    // 檢查檔案大小
    if (!isValidFileSize(file.size, FILE_TYPES.SCHOOL_LOGO)) {
      throw new Error(FILE_ERROR_MESSAGES.LOGO_SIZE_ERROR);
    }

    // 檢查檔案格式
    if (!isValidLogoFormat(file.mimetype)) {
      throw new Error(FILE_ERROR_MESSAGES.LOGO_FORMAT_ERROR);
    }

    const logoPath = `${this.uploadConfig.schoolLogoPath || "/uploads/school-logos"}/${file.filename}`;

    console.log(`📸 [Logo] 校徽已保存: ${logoPath}, 帳號ID: ${accountId}, 學校ID: ${schoolId}`);

    // 1. 將檔案資訊插入 FileEntry 表
    const originalExtension = path.extname(file.originalname);
    const fileExtension = path.extname(file.filename);

    const fileResult = await executeQuerySingle<FileEntryQueryResult>(
      `INSERT INTO FileEntry (Id, Type, Path, OriginalFileName, OriginalExtension, FileName, Extension)
       OUTPUT INSERTED.Id
       VALUES (NEWID(), @type, @path, @originalFileName, @originalExtension, @fileName, @extension)`,
      {
        type: FILE_TYPES.SCHOOL_LOGO,
        path: logoPath,
        originalFileName: file.originalname,
        originalExtension: originalExtension,
        fileName: file.filename,
        extension: fileExtension,
      }
    );

    if (!fileResult || !fileResult.Id) {
      throw new Error(FILE_ERROR_MESSAGES.FILE_RECORD_CREATE_FAILED);
    }

    const fileId = fileResult.Id;
    console.log(`📸 [Logo] 檔案記錄已建立，FileId: ${fileId}`);

    // 2. 驗證學校是否存在
    const schoolResult = await executeQuerySingle<SchoolQueryResult>(
      `SELECT s.Id 
       FROM Schools s
       WHERE s.Id = @schoolId AND s.Status = @activeStatus`,
      {
        schoolId,
        activeStatus: DATA_STATUS.ACTIVE,
      }
    );

    if (!schoolResult || !schoolResult.Id) {
      throw new Error(FILE_ERROR_MESSAGES.SCHOOL_NOT_FOUND);
    }

    console.log(`📸 [Logo] 驗證學校ID: ${schoolId}`);

    // 3. 更新 SchoolContents 表的多語言記錄
    await this.updateSchoolLogoReferences(schoolId, fileId, accountId);

    console.log(`📸 [Logo] 校徽上傳完成`);

    return {
      logoUrl: logoPath,
      fileName: file.filename,
      fileId: fileId,
      fileSize: file.size,
      fileType: file.mimetype,
    };
  }

  // 處理校徽上傳（原有方法，向後兼容）
  static async processSchoolLogoUpload(file: Express.Multer.File | undefined, accountId: string | number): Promise<SchoolLogoInfo> {
    if (!file) {
      throw new Error(FILE_ERROR_MESSAGES.PLEASE_SELECT_LOGO);
    }

    // 檢查檔案大小
    if (!isValidFileSize(file.size, FILE_TYPES.SCHOOL_LOGO)) {
      throw new Error(FILE_ERROR_MESSAGES.LOGO_SIZE_ERROR);
    }

    // 檢查檔案格式
    if (!isValidLogoFormat(file.mimetype)) {
      throw new Error(FILE_ERROR_MESSAGES.LOGO_FORMAT_ERROR);
    }

    const logoPath = `${this.uploadConfig.schoolLogoPath || "/uploads/school-logos"}/${file.filename}`;

    console.log(`📸 [Logo] 校徽已保存: ${logoPath}, 帳號ID: ${accountId}`);

    // 1. 將檔案資訊插入 FileEntry 表
    const originalExtension = path.extname(file.originalname);
    const fileExtension = path.extname(file.filename);

    const fileResult = await executeQuerySingle<FileEntryQueryResult>(
      `INSERT INTO FileEntry (Id, Type, Path, OriginalFileName, OriginalExtension, FileName, Extension)
       OUTPUT INSERTED.Id
       VALUES (NEWID(), @type, @path, @originalFileName, @originalExtension, @fileName, @extension)`,
      {
        type: FILE_TYPES.SCHOOL_LOGO,
        path: logoPath,
        originalFileName: file.originalname,
        originalExtension: originalExtension,
        fileName: file.filename,
        extension: fileExtension,
      }
    );

    if (!fileResult || !fileResult.Id) {
      throw new Error(FILE_ERROR_MESSAGES.FILE_RECORD_CREATE_FAILED);
    }

    const fileId = fileResult.Id;
    console.log(`📸 [Logo] 檔案記錄已建立，FileId: ${fileId}`);

    // 2. 獲取學校ID
    const schoolResult = await executeQuerySingle<SchoolQueryResult>(
      `SELECT s.Id 
       FROM Schools s
       INNER JOIN Accounts a ON s.Id = a.SchoolId
       WHERE a.AccountId = @accountId AND s.Status = @activeStatus`,
      {
        accountId,
        activeStatus: DATA_STATUS.ACTIVE,
      }
    );

    if (!schoolResult || !schoolResult.Id) {
      throw new Error(FILE_ERROR_MESSAGES.SCHOOL_NOT_FOUND);
    }

    const schoolId = schoolResult.Id;
    console.log(`📸 [Logo] 找到學校ID: ${schoolId}`);

    // 3. 更新 SchoolContents 表的多語言記錄
    await this.updateSchoolLogoReferences(schoolId, fileId, accountId);

    console.log(`📸 [Logo] 校徽上傳完成`);

    return {
      logoUrl: logoPath,
      fileName: file.filename,
      fileId: fileId,
      fileSize: file.size,
      fileType: file.mimetype,
    };
  }

  // 更新學校標誌引用
  private static async updateSchoolLogoReferences(schoolId: number, fileId: string, accountId: string | number): Promise<void> {
    const updateLogoQueries = [
      {
        locale: LOCALE_CODES.ZH_TW,
        query: `
          UPDATE SchoolContents 
          SET LogoFileId = @fileId, UpdatedTime = GETDATE()
          WHERE SchoolId = @schoolId AND LocaleCode = @localeCode
        `,
      },
      {
        locale: LOCALE_CODES.EN,
        query: `
          UPDATE SchoolContents 
          SET LogoFileId = @fileId, UpdatedTime = GETDATE(), UpdatedUserId = @accountId
          WHERE SchoolId = @schoolId AND LocaleCode = @localeCode
        `,
      },
    ];

    for (const updateInfo of updateLogoQueries) {
      try {
        const result = await executeQuery(updateInfo.query, {
          fileId: fileId,
          schoolId: schoolId,
          localeCode: updateInfo.locale,
          accountId: accountId,
        });
        console.log(`📸 [Logo] ${updateInfo.locale} 語言記錄更新成功`);
      } catch (error) {
        console.error(`📸 [Logo] 更新 ${updateInfo.locale} 語言記錄失敗:`, error);

        // 如果記錄不存在，嘗試插入新記錄
        if (error instanceof Error && error.message && error.message.includes("affected 0 rows")) {
          await this.createSchoolContentRecord(schoolId, updateInfo.locale, fileId, accountId);
        }
      }
    }
  }

  // 創建學校內容記錄
  private static async createSchoolContentRecord(schoolId: number, localeCode: string, fileId: string, accountId: string | number): Promise<void> {
    const insertQuery = `
      INSERT INTO SchoolContents (SchoolId, LocaleCode, Name, LogoFileId, CreatedTime, CreatedUserId, UpdatedTime, UpdatedUserId)
      VALUES (@schoolId, @localeCode, @schoolName, @fileId, GETDATE(), @accountId, GETDATE(), @accountId)
    `;

    try {
      await executeQuery(insertQuery, {
        schoolId: schoolId,
        localeCode: localeCode,
        schoolName: `School_${schoolId}`,
        fileId: fileId,
        accountId: accountId,
      });
      console.log(`📸 [Logo] 新增 ${localeCode} 語言記錄成功`);
    } catch (insertError) {
      console.error(`📸 [Logo] 新增 ${localeCode} 語言記錄失敗:`, insertError);
    }
  }

  // 獲取學校標誌
  static async getSchoolLogo(accountId: string): Promise<SchoolLogoInfo> {
    const query = `
      SELECT fe.Path as logoUrl, fe.FileName
      FROM SchoolContents sc
      INNER JOIN Schools s ON sc.SchoolId = s.Id
      INNER JOIN Accounts a ON s.Id = a.SchoolId
      INNER JOIN FileEntry fe ON sc.LogoFileId = fe.Id
      WHERE a.AccountId = @accountId AND sc.LocaleCode = @localeCode AND s.Status = @activeStatus
    `;

    const result = await executeQuerySingle<SchoolLogoQueryResult>(query, {
      accountId,
      localeCode: LOCALE_CODES.ZH_TW,
      activeStatus: DATA_STATUS.ACTIVE,
    });

    if (result && result.logoUrl) {
      return {
        logoUrl: result.logoUrl,
        fileName: result.FileName,
      };
    } else {
      return {
        logoUrl: null,
        fileName: null,
      };
    }
  }

  // 生成上傳診斷報告
  static async generateUploadDiagnostics(): Promise<UploadDiagnostics> {
    const diagnostics: UploadDiagnostics = {
      timestamp: new Date().toISOString(),
      environment: this.configManager.getEnvironment(),
      config: {
        basePath: this.uploadConfig.basePath,
        schoolLogoPath: this.uploadConfig.schoolLogoPath,
        generalUploadPath: this.uploadConfig.generalUploadPath,
      },
      directories: {},
      permissions: {},
      system: {
        cwd: process.cwd(),
        platform: process.platform,
        nodeVersion: process.version,
      },
    };

    // 檢查各個目錄的狀態
    const pathsToCheck = [
      { name: "basePath", path: this.uploadConfig.basePath },
      { name: "schoolLogoPath", path: this.uploadConfig.schoolLogoPath },
      { name: "generalUploadPath", path: this.uploadConfig.generalUploadPath },
    ];

    for (const { name, path: dirPath } of pathsToCheck) {
      try {
        // 檢查目錄是否存在
        await fs.access(dirPath);
        diagnostics.directories[name] = {
          exists: true,
          path: dirPath,
        };

        // 檢查寫入權限
        try {
          const testFile = path.join(dirPath, ".diagnostic-test");
          await fs.writeFile(testFile, "diagnostic test");
          await fs.unlink(testFile);
          diagnostics.permissions[name] = {
            readable: true,
            writable: true,
          };
        } catch (writeError) {
          diagnostics.permissions[name] = {
            readable: true,
            writable: false,
            writeError: writeError instanceof Error ? writeError.message : String(writeError),
          };
        }
      } catch (accessError) {
        diagnostics.directories[name] = {
          exists: false,
          path: dirPath,
          error: accessError instanceof Error ? accessError.message : String(accessError),
        };

        diagnostics.permissions[name] = {
          readable: false,
          writable: false,
        };
      }
    }

    return diagnostics;
  }
}
