// 儀表板服務 - 業務邏輯處理層
import { CityStatistics, LatestCertification, SchoolArticle, SchoolCertificationStatus, SchoolPassedCertification } from "@/api/dashboardAPI";

/**
 * 儀表板業務邏輯服務
 * 專注於資料處理、驗證、格式化等業務邏輯
 * 不直接進行 API 調用，而是處理從 API 獲得的資料
 */
class DashboardService {
  
  /**
   * 處理縣市統計資料
   * 計算各種衍生指標和格式化顯示
   */
  processCityStatistics(data: CityStatistics | null): ProcessedCityStatistics | null {
    if (!data) return null;

    const totalCertifications = data.bronzeCount + data.silverCount + data.greenFlagCount;
    const certificationRate = data.totalSchools > 0 
      ? (totalCertifications / data.totalSchools * 100).toFixed(1)
      : '0.0';

    return {
      ...data,
      totalCertifications,
      certificationRate: parseFloat(certificationRate),
      certificationRateDisplay: `${certificationRate}%`,
      isEmpty: totalCertifications === 0,
      hasGreenFlag: data.greenFlagCount > 0,
      dominantLevel: this.getDominantCertificationLevel(data),
    };
  }

  /**
   * 處理最新認證列表
   * 排序、分組、格式化日期
   */
  processLatestCertifications(data: LatestCertification[]): ProcessedLatestCertifications {
    if (!data || data.length === 0) {
      return {
        certifications: [],
        isEmpty: true,
        totalCount: 0,
        groupedByLevel: {},
        recentCount: 0,
      };
    }

    // 排序：最新的在前
    const sorted = [...data].sort((a, b) => 
      new Date(b.passDate).getTime() - new Date(a.passDate).getTime()
    );

    // 格式化日期
    const formatted = sorted.map(cert => ({
      ...cert,
      passDateFormatted: this.formatDate(cert.passDate),
      isRecent: this.isRecentCertification(cert.passDate),
      levelColor: this.getCertificationLevelColor(cert.certificationLevel),
    }));

    // 按認證等級分組
    const groupedByLevel = formatted.reduce((groups, cert) => {
      const level = cert.certificationLevel;
      if (!groups[level]) groups[level] = [];
      groups[level].push(cert);
      return groups;
    }, {} as Record<string, typeof formatted>);

    // 計算最近一個月的認證數量
    const recentCount = formatted.filter(cert => cert.isRecent).length;

    return {
      certifications: formatted,
      isEmpty: false,
      totalCount: data.length,
      groupedByLevel,
      recentCount,
    };
  }

  /**
   * 處理學校認證狀態
   * 狀態映射、進度計算、時間處理
   */
  processSchoolCertificationStatus(data: SchoolCertificationStatus | null): ProcessedSchoolCertificationStatus | null {
    if (!data) return null;

    const statusMapping = {
      'pending': { text: '審查中', color: 'yellow', progress: 50 },
      'approved': { text: '已通過', color: 'green', progress: 100 },
      'rejected': { text: '未通過', color: 'red', progress: 100 },
      'draft': { text: '草稿', color: 'gray', progress: 0 },
    };

    const statusInfo = statusMapping[data.status as keyof typeof statusMapping] || 
      { text: '未知狀態', color: 'gray', progress: 0 };

    const daysSinceApply = this.calculateDaysSince(data.applyDate);
    const isOverdue = daysSinceApply > 30; // 假設30天為審查期限

    return {
      ...data,
      statusText: statusInfo.text,
      statusColor: statusInfo.color,
      progress: statusInfo.progress,
      applyDateFormatted: this.formatDate(data.applyDate),
      reviewDateFormatted: data.reviewDate ? this.formatDate(data.reviewDate) : null,
      passDateFormatted: data.passDate ? this.formatDate(data.passDate) : null,
      daysSinceApply,
      isOverdue,
      canResubmit: data.status === 'rejected',
      canEdit: data.status === 'draft',
    };
  }

  /**
   * 處理學校投稿文章列表
   * 狀態處理、排序、摘要優化
   */
  processSchoolArticles(data: SchoolArticle[]): ProcessedSchoolArticles {
    if (!data || data.length === 0) {
      return {
        articles: [],
        isEmpty: true,
        totalCount: 0,
        publishedCount: 0,
        draftCount: 0,
        recentCount: 0,
      };
    }

    // 處理每篇文章
    const processed = data.map(article => ({
      ...article,
      statusText: this.getArticleStatusText(article.status),
      statusColor: this.getArticleStatusColor(article.status),
      publishDateFormatted: article.publishDate ? this.formatDate(article.publishDate) : null,
      createDateFormatted: this.formatDate(article.createDate),
      summaryDisplay: this.truncateSummary(article.summary),
      isPublished: article.status === 'published',
      isDraft: article.status === 'draft',
      isRecent: this.isRecentArticle(article.createDate),
    }));

    // 按創建日期排序（最新在前）
    const sorted = processed.sort((a, b) => 
      new Date(b.createDate).getTime() - new Date(a.createDate).getTime()
    );

    // 統計資訊
    const publishedCount = processed.filter(a => a.isPublished).length;
    const draftCount = processed.filter(a => a.isDraft).length;
    const recentCount = processed.filter(a => a.isRecent).length;

    return {
      articles: sorted,
      isEmpty: false,
      totalCount: data.length,
      publishedCount,
      draftCount,
      recentCount,
    };
  }

  /**
   * 處理學校已通過認證列表
   * 排序、等級顏色、證書狀態
   */
  processSchoolPassedCertifications(data: SchoolPassedCertification[]): ProcessedSchoolPassedCertifications {
    if (!data || data.length === 0) {
      return {
        certifications: [],
        isEmpty: true,
        totalCount: 0,
        latestCertification: null,
        levelCounts: {},
      };
    }

    // 處理每個認證
    const processed = data.map(cert => ({
      ...cert,
      passDateFormatted: this.formatDate(cert.passDate),
      levelColor: this.getCertificationLevelColor(cert.levelName),
      hasCertificate: !!cert.certificateNumber,
      daysSincePass: this.calculateDaysSince(cert.passDate),
    }));

    // 按通過日期排序（最新在前）
    const sorted = processed.sort((a, b) => 
      new Date(b.passDate).getTime() - new Date(a.passDate).getTime()
    );

    // 統計各等級認證數量
    const levelCounts = processed.reduce((counts, cert) => {
      counts[cert.levelName] = (counts[cert.levelName] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return {
      certifications: sorted,
      isEmpty: false,
      totalCount: data.length,
      latestCertification: sorted[0] || null,
      levelCounts,
    };
  }

  // === 私有輔助方法 ===

  private getDominantCertificationLevel(data: CityStatistics): string {
    const levels = [
      { name: 'greenFlag', count: data.greenFlagCount },
      { name: 'silver', count: data.silverCount },
      { name: 'bronze', count: data.bronzeCount },
    ];
    
    const dominant = levels.reduce((max, level) => 
      level.count > max.count ? level : max
    );
    
    return dominant.count > 0 ? dominant.name : 'none';
  }

  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch {
      return dateString;
    }
  }

  private isRecentCertification(dateString: string): boolean {
    try {
      const date = new Date(dateString);
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      return date >= oneMonthAgo;
    } catch {
      return false;
    }
  }

  private isRecentArticle(dateString: string): boolean {
    try {
      const date = new Date(dateString);
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
      return date >= twoWeeksAgo;
    } catch {
      return false;
    }
  }

  private calculateDaysSince(dateString: string): number {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } catch {
      return 0;
    }
  }

  private getCertificationLevelColor(level: string): string {
    const colorMap: Record<string, string> = {
      '綠旗': '#22c55e',
      'silver': '#94a3b8',
      'bronze': '#d97706',
      'green': '#22c55e',
      'Silver': '#94a3b8',
      'Bronze': '#d97706',
    };
    return colorMap[level] || '#6b7280';
  }

  private getArticleStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'published': '已發布',
      'draft': '草稿',
      'pending': '審核中',
      'rejected': '已拒絕',
    };
    return statusMap[status] || status;
  }

  private getArticleStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'published': 'green',
      'draft': 'gray',
      'pending': 'yellow',
      'rejected': 'red',
    };
    return colorMap[status] || 'gray';
  }

  private truncateSummary(summary?: string, maxLength: number = 100): string {
    if (!summary) return '';
    return summary.length > maxLength 
      ? `${summary.substring(0, maxLength)}...` 
      : summary;
  }
}

// === 處理後的資料介面 ===

export interface ProcessedCityStatistics extends CityStatistics {
  totalCertifications: number;
  certificationRate: number;
  certificationRateDisplay: string;
  isEmpty: boolean;
  hasGreenFlag: boolean;
  dominantLevel: string;
}

export interface ProcessedLatestCertifications {
  certifications: (LatestCertification & {
    passDateFormatted: string;
    isRecent: boolean;
    levelColor: string;
  })[];
  isEmpty: boolean;
  totalCount: number;
  groupedByLevel: Record<string, any[]>;
  recentCount: number;
}

export interface ProcessedSchoolCertificationStatus extends SchoolCertificationStatus {
  statusText: string;
  statusColor: string;
  progress: number;
  applyDateFormatted: string;
  reviewDateFormatted: string | null;
  passDateFormatted: string | null;
  daysSinceApply: number;
  isOverdue: boolean;
  canResubmit: boolean;
  canEdit: boolean;
}

export interface ProcessedSchoolArticles {
  articles: (SchoolArticle & {
    statusText: string;
    statusColor: string;
    publishDateFormatted: string | null;
    createDateFormatted: string;
    summaryDisplay: string;
    isPublished: boolean;
    isDraft: boolean;
    isRecent: boolean;
  })[];
  isEmpty: boolean;
  totalCount: number;
  publishedCount: number;
  draftCount: number;
  recentCount: number;
}

export interface ProcessedSchoolPassedCertifications {
  certifications: (SchoolPassedCertification & {
    passDateFormatted: string;
    levelColor: string;
    hasCertificate: boolean;
    daysSincePass: number;
  })[];
  isEmpty: boolean;
  totalCount: number;
  latestCertification: any | null;
  levelCounts: Record<string, number>;
}

export const dashboardService = new DashboardService();
