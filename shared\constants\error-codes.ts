/**
 * 系統錯誤碼統一定義
 * 整合原本分散在各處的錯誤處理邏輯
 * 
 * @description 提供一致性的錯誤碼管理和多語言錯誤訊息支援
 * @version 1.0.0
 */

/**
 * 認證相關錯誤碼列舉
 * 使用標準化的錯誤碼格式：AUTH_xxx
 */
export enum AuthErrorCode {
  /** 認證 Token 已過期 */
  TOKEN_EXPIRED = 'AUTH_001',
  /** 無效的認證 Token */
  TOKEN_INVALID = 'AUTH_002',
  /** 找不到認證 Token */
  TOKEN_NOT_FOUND = 'AUTH_003',
  /** 角色權限不足 */
  ROLE_NOT_ALLOWED = 'AUTH_004',
  /** 帳號已被停用 */
  ACCOUNT_DISABLED = 'AUTH_005',
  /** 未授權的存取 */
  UNAUTHORIZED = 'AUTH_006'
}

/**
 * 多語言錯誤訊息映射表
 * 支援中文和英文錯誤訊息
 */
export const ErrorMessages = {
  [AuthErrorCode.TOKEN_EXPIRED]: {
    zh: '認證 Token 已過期，請重新登入',
    en: 'Authentication token has expired, please login again'
  },
  [AuthErrorCode.TOKEN_INVALID]: {
    zh: '無效的認證 Token，請檢查您的登入狀態',
    en: 'Invalid authentication token, please check your login status'
  },
  [AuthErrorCode.TOKEN_NOT_FOUND]: {
    zh: '找不到認證 Token，請先登入系統',
    en: 'Authentication token not found, please login to the system first'
  },
  [AuthErrorCode.ROLE_NOT_ALLOWED]: {
    zh: '您的帳號角色不被允許存取此系統，請聯繫管理員',
    en: 'Your account role is not authorized to access this system, please contact administrator'
  },
  [AuthErrorCode.ACCOUNT_DISABLED]: {
    zh: '您的帳號已被停用，請聯繫管理員重新啟用',
    en: 'Your account has been deactivated, please contact administrator to reactivate'
  },
  [AuthErrorCode.UNAUTHORIZED]: {
    zh: '未授權的存取，請先登入或檢查您的權限',
    en: 'Unauthorized access, please login or check your permissions'
  }
} as const;

/**
 * 舊版錯誤碼映射（向後相容性支援）
 * 用於支援舊版程式碼中使用的錯誤碼格式
 */
export const LEGACY_ERROR_CODES = [
  'TOKEN_EXPIRED',      // 對應 AUTH_001
  'TOKEN_INVALID',      // 對應 AUTH_002
  'TOKEN_REVOKED',      // 舊版使用，等同於 TOKEN_INVALID
  'UNAUTHORIZED',       // 對應 AUTH_006
  'AUTH_FAILED',        // 通用認證失敗
  'TOKEN_NOT_FOUND',    // 對應 AUTH_003
  'VALIDATION_FAILED',  // 驗證失敗
  'TOKEN_MISSING',      // 等同於 TOKEN_NOT_FOUND
  'AUTHENTICATION_ERROR', // 通用認證錯誤
  'NO_TOKEN'            // 等同於 TOKEN_NOT_FOUND
] as const;

/**
 * 舊版錯誤碼轉換為新版錯誤碼的映射表
 */
export const LEGACY_ERROR_CODE_MAPPING: Record<string, AuthErrorCode> = {
  'TOKEN_EXPIRED': AuthErrorCode.TOKEN_EXPIRED,
  'TOKEN_INVALID': AuthErrorCode.TOKEN_INVALID,
  'TOKEN_REVOKED': AuthErrorCode.TOKEN_INVALID,
  'UNAUTHORIZED': AuthErrorCode.UNAUTHORIZED,
  'AUTH_FAILED': AuthErrorCode.UNAUTHORIZED,
  'TOKEN_NOT_FOUND': AuthErrorCode.TOKEN_NOT_FOUND,
  'VALIDATION_FAILED': AuthErrorCode.TOKEN_INVALID,
  'TOKEN_MISSING': AuthErrorCode.TOKEN_NOT_FOUND,
  'AUTHENTICATION_ERROR': AuthErrorCode.UNAUTHORIZED,
  'NO_TOKEN': AuthErrorCode.TOKEN_NOT_FOUND
};

/**
 * 錯誤訊息取得器
 * @param errorCode 錯誤碼（新版或舊版皆可）
 * @param language 語言代碼，預設為中文
 * @returns 對應的錯誤訊息
 */
export const getErrorMessage = (
  errorCode: AuthErrorCode | string, 
  language: 'zh' | 'en' = 'zh'
): string => {
  let normalizedCode: AuthErrorCode;
  
  // 如果是舊版錯誤碼，先轉換為新版
  if (Object.prototype.hasOwnProperty.call(LEGACY_ERROR_CODE_MAPPING, errorCode)) {
    normalizedCode = LEGACY_ERROR_CODE_MAPPING[errorCode];
  } else if (Object.values(AuthErrorCode).includes(errorCode as AuthErrorCode)) {
    normalizedCode = errorCode as AuthErrorCode;
  } else {
    // 未知錯誤碼，返回通用錯誤訊息
    normalizedCode = AuthErrorCode.UNAUTHORIZED;
  }
  
  const message = ErrorMessages[normalizedCode];
  return message ? message[language] : ErrorMessages[AuthErrorCode.UNAUTHORIZED][language];
};

/**
 * 錯誤碼驗證器
 * @param errorCode 要驗證的錯誤碼
 * @returns 是否為有效的錯誤碼
 */
export const isValidErrorCode = (errorCode: string): boolean => {
  // 檢查新版錯誤碼
  if (Object.values(AuthErrorCode).includes(errorCode as AuthErrorCode)) {
    return true;
  }
  
  // 檢查舊版錯誤碼
  return LEGACY_ERROR_CODES.includes(errorCode as any);
};

/**
 * HTTP 狀態碼對應表
 * 將錯誤碼對應到適當的 HTTP 狀態碼
 */
export const HTTP_STATUS_MAPPING: Record<AuthErrorCode, number> = {
  [AuthErrorCode.TOKEN_EXPIRED]: 401,
  [AuthErrorCode.TOKEN_INVALID]: 401,
  [AuthErrorCode.TOKEN_NOT_FOUND]: 401,
  [AuthErrorCode.ROLE_NOT_ALLOWED]: 403,
  [AuthErrorCode.ACCOUNT_DISABLED]: 403,
  [AuthErrorCode.UNAUTHORIZED]: 401
};

/**
 * 錯誤回應建立器
 * @param errorCode 錯誤碼
 * @param language 語言代碼
 * @returns 標準化的錯誤回應物件
 */
export const createErrorResponse = (
  errorCode: AuthErrorCode | string,
  language: 'zh' | 'en' = 'zh'
) => {
  const normalizedCode = isValidErrorCode(errorCode) 
    ? (LEGACY_ERROR_CODE_MAPPING[errorCode] || errorCode as AuthErrorCode)
    : AuthErrorCode.UNAUTHORIZED;
    
  return {
    success: false,
    error: {
      code: normalizedCode,
      message: getErrorMessage(normalizedCode, language),
      timestamp: new Date().toISOString()
    },
    data: null
  };
};