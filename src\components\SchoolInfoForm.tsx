import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import LogoUpload from "@/components/LogoUpload";
import AddressBlock from "@/components/AddressBlock";
import { buildAssetUrl } from "@/utils/pathUtils";

// 維護基本資料 - 學校資料

const defaultCities = [
  { value: "tp", label: "台北市" },
  { value: "ntc", label: "新北市" },
  { value: "khc", label: "高雄市" },
];
const defaultDistricts = [
  { value: "xinyi", label: "信義區" },
  { value: "banqiao", label: "板橋區" },
  { value: "zuoying", label: "左營區" },
];
const DEFAULT_LOGO = null;

const SchoolInfoForm = () => {
  const {
    register,
    formState: { errors },
  } = useForm();
  const [logoPreview, setLogoPreview] = useState<string | null>(
    buildAssetUrl("img/default-school-logo.png")
  );

  // logo上傳變更
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      setLogoPreview(null);
      return;
    }
    const reader = new FileReader();
    reader.onloadend = () => {
      setLogoPreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // API假資料 (不可變動)
  const city = defaultCities[0];
  const district = defaultDistricts[0];
  const address = "（API 取得的地址顯示於此）";

  return (
    <form aria-label="學校資料表單" tabIndex={0}>
      <div className="flex flex-col gap-3 mb-6">
        <label htmlFor="schoolName" className="font-bold">
          <span className="text-destructive mr-1">*</span>中文校名
        </label>
        <Input
          id="schoolName"
          {...register("schoolName", { required: true })}
        />

        <label htmlFor="schoolNameEn" className="font-bold">
          <span className="text-destructive mr-1">*</span>英文校名
        </label>
        <Input
          id="schoolNameEn"
          {...register("schoolNameEn", { required: true })}
        />

        {/* 地址區塊（不可修改） */}
        <AddressBlock city={city} district={district} address={address} />

        <label htmlFor="phone" className="font-bold">
          <span className="text-destructive mr-1">*</span>學校電話
        </label>
        <Input id="phone" {...register("phone", { required: true })} />

        <label htmlFor="email" className="font-bold">
          <span className="text-destructive mr-1">*</span>電子信箱
        </label>
        <Input id="email" {...register("email", { required: true })} />

        <label htmlFor="url" className="font-bold">
          <span className="text-destructive mr-1">*</span>學校網址
        </label>
        <Input id="url" {...register("url", { required: true })} />

        {/* 校徽上傳 */}
        <label htmlFor="logo" className="font-bold">
          <span className="text-destructive mr-1">*</span>校徽圖檔
        </label>
        <LogoUpload logoPreview={logoPreview} onLogoChange={handleLogoChange} />

        <label htmlFor="desc" className="font-bold">
          <span className="text-destructive mr-1">*</span>校史/簡介
        </label>
        <textarea
          id="desc"
          rows={3}
          className="rounded border p-2 w-full"
          placeholder="請輸入學校簡介"
          maxLength={5000}
          aria-describedby="desc-desc"
          required
        />
        <span id="desc-desc" className="text-muted-foreground font-size-xs">
          最多輸入5000字
        </span>
      </div>

      {/* Gmail 狀態 */}
      <div
        className="bg-muted p-4 rounded font-semibold"
        role="region"
        aria-label="全校使用Gmail狀態"
      >
        <span className="mr-4 text-destructive">*</span>
        本校使用Gmail或公務信箱嗎？
        <div className="flex gap-6 mt-2">
          <label className="inline-flex items-center">
            <input
              type="radio"
              name="gmail_status"
              value="有"
              className="mr-2"
              required
            />
            有
          </label>
          <label className="inline-flex items-center">
            <input
              type="radio"
              name="gmail_status"
              value="無"
              className="mr-2"
              required
            />
            無
          </label>
        </div>
      </div>

      {/* 校長與聯絡人區塊 */}
      <section className="my-8 bg-gray-50 rounded-md p-6">
        <h3 className="font-semibold font-size-lg mb-2">校長資訊</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <label htmlFor="principal" className="font-bold">
              <span className="text-destructive mr-1">*</span>姓名
            </label>
            <Input
              id="principal"
              {...register("principal", { required: true })}
            />
          </div>
          <div>
            <label htmlFor="principal_title" className="font-bold">
              <span className="text-destructive mr-1">*</span>職稱
            </label>
            <Input
              id="principal_title"
              {...register("principal_title", { required: true })}
            />
          </div>
          <div>
            <label htmlFor="principal_tel" className="font-bold">
              <span className="text-destructive mr-1">*</span>聯絡電話
            </label>
            <Input
              id="principal_tel"
              {...register("principal_tel", { required: true })}
            />
          </div>
          <div>
            <label htmlFor="principal_mobile" className="font-bold">
              <span className="text-destructive mr-1">*</span>手機號碼
            </label>
            <Input
              id="principal_mobile"
              {...register("principal_mobile", { required: true })}
            />
          </div>
          <div>
            <label htmlFor="principal_email" className="font-bold">
              <span className="text-destructive mr-1">*</span>電子信箱
            </label>
            <Input
              id="principal_email"
              {...register("principal_email", { required: true })}
            />
          </div>
        </div>
      </section>
      <section className="my-8 bg-gray-50 rounded-md p-6">
        <h3 className="font-semibold font-size-lg mb-2">聯絡人資訊</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <label htmlFor="contact" className="font-bold">
              <span className="text-destructive mr-1">*</span>姓名
            </label>
            <Input id="contact" {...register("contact", { required: true })} />
          </div>
          <div>
            <label htmlFor="contact_title" className="font-bold">
              <span className="text-destructive mr-1">*</span>職稱
            </label>
            <Input
              id="contact_title"
              {...register("contact_title", { required: true })}
            />
          </div>
          <div>
            <label htmlFor="contact_tel" className="font-bold">
              <span className="text-destructive mr-1">*</span>聯絡電話
            </label>
            <Input
              id="contact_tel"
              {...register("contact_tel", { required: true })}
            />
          </div>
          <div>
            <label htmlFor="contact_mobile" className="font-bold">
              <span className="text-destructive mr-1">*</span>手機號碼
            </label>
            <Input
              id="contact_mobile"
              {...register("contact_mobile", { required: true })}
            />
          </div>
          <div>
            <label htmlFor="contact_email" className="font-bold">
              <span className="text-destructive mr-1">*</span>電子信箱
            </label>
            <Input
              id="contact_email"
              {...register("contact_email", { required: true })}
            />
          </div>
        </div>
        <Button type="button" className="mt-3" aria-label="新增聯絡人">
          新增聯絡人
        </Button>
      </section>

      {/* 學校統計資料 */}
      <section className="my-8">
        <h3 className="font-semibold font-size-lg mb-2">學校統計資料</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <div>
            <label className="font-bold" htmlFor="stat_date">
              <span className="text-destructive mr-1">*</span>填報日期
            </label>
            <Input
              id="stat_date"
              type="date"
              {...register("stat_date", { required: true })}
            />
          </div>
          <div>
            <label className="font-bold" htmlFor="teachers">
              <span className="text-destructive mr-1">*</span>教職員工
            </label>
            <Input
              id="teachers"
              type="number"
              min="0"
              {...register("teachers", { required: true })}
            />
          </div>
          {/* 國小一年級~六年級 */}
          {Array.from({ length: 6 }, (_, i) => (
            <div key={`lower_grade_${i + 1}`}>
              <label className="font-bold" htmlFor={`lower_grade_${i + 1}`}>
                <span className="text-destructive mr-1">*</span>國小
                {["一", "二", "三", "四", "五", "六"][i]}年級
              </label>
              <Input
                id={`lower_grade_${i + 1}`}
                type="number"
                min="0"
                {...register(`lower_grade_${i + 1}`, { required: true })}
              />
            </div>
          ))}
          {/* 國中七～九年級 */}
          {Array.from({ length: 3 }, (_, i) => (
            <div key={`middle_grade_${i + 7}`}>
              <label className="font-bold" htmlFor={`middle_grade_${i + 7}`}>
                <span className="text-destructive mr-1">*</span>國中
                {["七", "八", "九"][i]}年級
              </label>
              <Input
                id={`middle_grade_${i + 7}`}
                type="number"
                min="0"
                {...register(`middle_grade_${i + 7}`, { required: true })}
              />
            </div>
          ))}
          {/* 高中職 1～3年級 */}
          {Array.from({ length: 3 }, (_, i) => (
            <div key={`high_grade_${i + 1}`}>
              <label className="font-bold" htmlFor={`high_grade_${i + 1}`}>
                <span className="text-destructive mr-1">*</span>高中職
                {["一", "二", "三"][i]}年級
              </label>
              <Input
                id={`high_grade_${i + 1}`}
                type="number"
                min="0"
                {...register(`high_grade_${i + 1}`, { required: true })}
              />
            </div>
          ))}
        </div>
      </section>
      <Button
        type="submit"
        className="mt-8 mx-auto block w-1/2 bg-[#38513A] font-size-lg"
        aria-label="確認修改"
      >
        確認修改
      </Button>
    </form>
  );
};

export default SchoolInfoForm;
