// ========== 用戶相關常數 ==========

// 角色類型
export const ROLE_TYPES = {
  SCHOOL: "School",
  GOVERNMENT: "Government",
  TUTOR: "Tutor",
  ADMIN: "Admin",
} as const;

// 用戶狀態
export const USER_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  SUSPENDED: 2,
  DELETED: 3,
} as const;

// 認證狀態
export const CERTIFICATION_STATUS = {
  DRAFT: "Draft",
  IN_REVIEW: "InReview",
  PASSED: "Passed",
  REJECTED: "Rejected",
  EXPIRED: "Expired",
} as const;

// 認證類型
export const CERTIFICATION_TYPES = {
  BRONZE: "Bronze",
  SILVER: "Silver",
  GREEN_FLAG: "GreenFlag",
} as const;

// 認證等級
export const CERTIFICATION_LEVELS = {
  BRONZE: 2,
  SILVER: 3,
  GREEN_FLAG: 4,
  GRE<PERSON>_FLAG_R1: 5,
  GREEN_FLAG_R2: 6,
  GREEN_FLAG_R3: 7,
} as const;

// 審核狀態
export const REVIEW_STATUS = {
  IN_REVIEW: 0,
  APPROVED: 1,
  REJECTED: 2,
  NEEDS_SUPPLEMENT: 3,
  NOT_SUBMITTED: 4,
} as const;

// 資料狀態
export const DATA_STATUS = {
  ACTIVE: 0,
  INACTIVE: 1,
  DELETED: 2,
} as const;

// 權限檢查標誌
export const PERMISSION_FLAGS = {
  IS_USE: 1,
  NOT_USE: 0,
} as const;

// 允許更新的帳號欄位 (根據角色類型)
export const ALLOWED_ACCOUNT_FIELDS = {
  [ROLE_TYPES.SCHOOL]: ["NickName", "Email", "Phone", "Avatar"],
  [ROLE_TYPES.GOVERNMENT]: ["NickName", "Email", "Phone", "Avatar"],
  [ROLE_TYPES.TUTOR]: ["NickName", "Email", "Phone", "Avatar"],
  [ROLE_TYPES.ADMIN]: ["NickName", "Email", "Phone", "Avatar"],
} as const;

// 允許更新的學校欄位
export const ALLOWED_SCHOOL_FIELDS = ["Name", "EnglishName", "Address", "Phone", "Email", "Website", "ContactPerson", "ContactPhone", "ContactEmail"] as const;

// 錯誤訊息
export const USER_ERROR_MESSAGES = {
  USER_NOT_AUTHENTICATED: "使用者未認證",
  USER_NOT_FOUND: "找不到使用者資料",
  NO_PERMISSION: "無權限訪問此資料",
  SCHOOL_ONLY: "只有學校身份可以訪問學校資料",
  SCHOOL_NOT_FOUND: "找不到對應的學校資料",
  INVALID_EMAIL: "電子郵件格式不正確",
  INVALID_PHONE: "電話號碼格式不正確",
  UPDATE_FAILED: "更新使用者資料失敗",
  NO_VALID_FIELDS: "沒有提供有效的更新欄位",
  TRANSACTION_FAILED: "資料庫事務處理失敗",
} as const;

// 成功訊息
export const USER_SUCCESS_MESSAGES = {
  PROFILE_RETRIEVED: "使用者資料獲取成功",
  PROFILE_UPDATED: "使用者資料更新成功",
  CERTIFICATIONS_RETRIEVED: "認證資料獲取成功",
  SCHOOL_INFO_RETRIEVED: "學校資料獲取成功",
  PERMISSIONS_RETRIEVED: "權限資料獲取成功",
} as const;

// 預設權限
export const DEFAULT_PERMISSIONS = {
  ADMIN: ["admin"],
  USER: ["user"],
  SCHOOL: ["school"],
  GOVERNMENT: ["government"],
  TUTOR: ["tutor"],
} as const;

// SQL 查詢模板
export const SQL_QUERIES = {
  GET_ACCOUNT: "SELECT * FROM Account WHERE Id = @userId AND IsActive = 1",
  GET_ACCOUNT_WITH_PERMISSIONS: `
    SELECT a.*, 
           STRING_AGG(DISTINCT p.ename, ',') as Permissions,
           STRING_AGG(DISTINCT pg.ename, ',') as PermissionGroups
    FROM Account a
    LEFT JOIN account_permission_group apg ON a.Id = apg.accountSid AND apg.dataStatus = 0
    LEFT JOIN permission_group pg ON apg.groupSid = pg.sid AND pg.isuse = 1
    LEFT JOIN permission_group_map pgm ON pg.sid = pgm.groupSid AND pgm.dataStatus = 0
    LEFT JOIN permission p ON pgm.permissionSid = p.sid AND p.isuse = 1
    WHERE a.Id = @userId AND a.IsActive = 1
    GROUP BY a.Id, a.Account, a.NickName, a.Email, a.Phone, a.Avatar, a.RoleType, a.IsActive, a.CreatedTime, a.UpdatedTime, a.Remark
  `,
  GET_SCHOOL_BY_ACCOUNT: `
    SELECT s.*
    FROM School s
    INNER JOIN Account a ON s.Id = a.CmsUserId
    WHERE a.Id = @accountId AND s.IsActive = 1
  `,
  GET_CERTIFICATIONS: `
    SELECT c.*
    FROM Certifications c
    WHERE c.SchoolId = @userId AND c.Status = 1 
    ORDER BY c.CreatedTime DESC
  `,
  GET_PERMISSIONS: `
    SELECT DISTINCT p.ename as Code, p.cname as Name, p.remark as Description
    FROM account_permission_group apg
    INNER JOIN permission_group pg ON apg.groupSid = pg.sid
    INNER JOIN permission_group_map pgm ON pg.sid = pgm.groupSid
    INNER JOIN permission p ON pgm.permissionSid = p.sid
    WHERE apg.accountSid = @accountId 
      AND apg.dataStatus = 0
      AND pg.isuse = 1
      AND pgm.dataStatus = 0
      AND p.isuse = 1
    ORDER BY p.cname
  `,
  GET_PERMISSION_GROUPS: `
    SELECT DISTINCT pg.ename as Code, pg.cname as Name, pg.remark as Description
    FROM account_permission_group apg
    INNER JOIN permission_group pg ON apg.groupSid = pg.sid
    WHERE apg.accountSid = @accountId 
      AND apg.dataStatus = 0
      AND pg.isuse = 1
    ORDER BY pg.cname
  `,
} as const;

// 工具函數：驗證電子郵件格式
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 工具函數：驗證電話號碼格式
export const isValidPhone = (phone: string): boolean => {
  // 允許多種電話號碼格式
  const phoneRegex = /^[\d\-+() ]{8,20}$/;
  return phoneRegex.test(phone);
};

// 工具函數：檢查角色權限
export const hasPermission = (userRole: string, requiredRole: string): boolean => {
  if (userRole === ROLE_TYPES.ADMIN) return true;
  return userRole === requiredRole;
};

// 工具函數：檢查是否為學校身份
export const isSchoolRole = (roleType: string): boolean => {
  return roleType === ROLE_TYPES.SCHOOL;
};

// 工具函數：檢查是否為管理員
export const isAdminRole = (roleType: string): boolean => {
  return roleType === ROLE_TYPES.ADMIN;
};

// 工具函數：獲取允許更新的帳號欄位
export const getAllowedAccountFields = (roleType: string): string[] => {
  return ALLOWED_ACCOUNT_FIELDS[roleType as keyof typeof ALLOWED_ACCOUNT_FIELDS] || [];
};

// 工具函數：獲取允許更新的學校欄位
export const getAllowedSchoolFields = (): string[] => {
  return [...ALLOWED_SCHOOL_FIELDS];
};

// 工具函數：映射認證等級到類型
export const mapCertificationLevelToType = (level: number): string => {
  switch (level) {
    case CERTIFICATION_LEVELS.BRONZE:
      return CERTIFICATION_TYPES.BRONZE;
    case CERTIFICATION_LEVELS.SILVER:
      return CERTIFICATION_TYPES.SILVER;
    case CERTIFICATION_LEVELS.GREEN_FLAG:
    case CERTIFICATION_LEVELS.GREEN_FLAG_R1:
    case CERTIFICATION_LEVELS.GREEN_FLAG_R2:
    case CERTIFICATION_LEVELS.GREEN_FLAG_R3:
      return CERTIFICATION_TYPES.GREEN_FLAG;
    default:
      return CERTIFICATION_TYPES.BRONZE;
  }
};

// 工具函數：映射審核狀態到認證狀態
export const mapReviewStatusToCertificationStatus = (reviewStatus: number): string => {
  switch (reviewStatus) {
    case REVIEW_STATUS.APPROVED:
      return CERTIFICATION_STATUS.PASSED;
    case REVIEW_STATUS.IN_REVIEW:
      return CERTIFICATION_STATUS.IN_REVIEW;
    case REVIEW_STATUS.REJECTED:
      return CERTIFICATION_STATUS.REJECTED;
    default:
      return CERTIFICATION_STATUS.DRAFT;
  }
};

// 工具函數：檢查使用者是否可以訪問其他用戶資料
export const canAccessUserData = (currentUserId: string, targetUserId: string, permissions: string[]): boolean => {
  // 如果是同一個用戶，允許訪問
  if (currentUserId === targetUserId) return true;

  // 如果有管理員權限，允許訪問
  if (permissions.includes("admin")) return true;

  return false;
};

// 工具函數：驗證更新資料
export const validateUpdateData = (data: any): { isValid: boolean; error?: string } => {
  if (data.email && !isValidEmail(data.email)) {
    return { isValid: false, error: USER_ERROR_MESSAGES.INVALID_EMAIL };
  }

  if (data.phone && !isValidPhone(data.phone)) {
    return { isValid: false, error: USER_ERROR_MESSAGES.INVALID_PHONE };
  }

  return { isValid: true };
};
