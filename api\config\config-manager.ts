import { readFileSync, existsSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 後端 API 配置數據接口
 */
export interface BackendConfigData {
  // 環境設定
  NODE_ENV: string;
  APP_ENV: string;

  // 服務器配置
  API_PORT: number;
  FRONTEND_URL: string;
  ALLOWED_ORIGINS: string[];

  // 資料庫配置
  DB_HOST: string;
  DB_USER: string;
  DB_PASSWORD: string;
  DB_NAME: string;
  DB_PORT: number;
  DB_TYPE: string;

  // JWT 配置
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;

  // 上傳配置
  MAX_UPLOAD_SIZE: number;
  UPLOAD_PATH: string;
  SCHOOL_LOGO_PATH: string;

  // 其他後端專用配置
  [key: string]: string | number | string[] | undefined;
}

/**
 * 配置驗證規則
 */
interface ConfigRule {
  required: boolean;
  type: "string" | "number" | "boolean" | "array";
  defaultValue?: any;
  validator?: (value: any) => boolean;
  description: string;
}

/**
 * 純文件配置管理器 (後端專用)
 * 不使用環境變數，直接從 .env 文件讀取後端配置
 * 不載入前端相關配置 (VITE_ 前綴)
 */
export class FileConfigManager {
  private static instance: FileConfigManager | null = null;
  private configData: BackendConfigData | null = null;
  private projectRoot: string;
  private configPath: string;
  private lastModified: number = 0;

  private constructor() {
    // 項目根目錄 (api/ 的上一層)
    this.projectRoot = join(__dirname, "..");
    this.configPath = join(this.projectRoot, ".env");
    this.loadConfig();
  }

  /**
   * 獲取單例實例
   */
  public static getInstance(): FileConfigManager {
    if (!FileConfigManager.instance) {
      FileConfigManager.instance = new FileConfigManager();
    }
    return FileConfigManager.instance;
  }

  /**
   * 重置實例（用於測試）
   */
  public static resetInstance(): void {
    FileConfigManager.instance = null;
  }

  /**
   * 從 .env 文件載入配置
   */
  public loadConfig(): void {
    if (!existsSync(this.configPath)) {
      throw new Error(`Configuration file not found: ${this.configPath}`);
    }

    try {
      const content = readFileSync(this.configPath, "utf8");
      const parsed = this.parseEnvContent(content);
      this.configData = this.validateAndTransformConfig(parsed);
    } catch (error) {
      throw new Error(`Failed to load configuration: ${(error as Error).message}`);
    }
  }

  /**
   * 解析 .env 文件內容
   */
  private parseEnvContent(content: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = content.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();

      // 跳過空行和註釋
      if (!trimmedLine || trimmedLine.startsWith("#")) {
        continue;
      }

      // 解析 KEY=VALUE
      const equalIndex = trimmedLine.indexOf("=");
      if (equalIndex === -1) {
        continue;
      }

      const key = trimmedLine.substring(0, equalIndex).trim();
      const value = trimmedLine.substring(equalIndex + 1).trim();

      // 移除引號
      const cleanValue = value.replace(/^["']|["']$/g, "");
      result[key] = cleanValue;
    }

    return result;
  }

  /**
   * 驗證和轉換配置 (僅後端配置)
   */
  private validateAndTransformConfig(rawConfig: Record<string, string>): BackendConfigData {
    // 配置驗證規則
    const rules: Record<string, ConfigRule> = {
      NODE_ENV: {
        required: true,
        type: "string",
        defaultValue: "development",
        validator: (value: string) => ["development", "testing", "production"].includes(value),
        description: "應用環境",
      },
      APP_ENV: {
        required: false,
        type: "string",
        defaultValue: "development",
        description: "APP 環境",
      },
      API_PORT: {
        required: false,
        type: "number",
        defaultValue: 3001,
        description: "API 服務器端口",
      },
      FRONTEND_URL: {
        required: true,
        type: "string",
        defaultValue: "http://localhost:8080",
        description: "前端 URL",
      },
      ALLOWED_ORIGINS: {
        required: true,
        type: "array",
        defaultValue: ["http://localhost:8080"],
        description: "允許的 CORS 來源",
      },
      DB_HOST: {
        required: true,
        type: "string",
        defaultValue: "localhost",
        description: "資料庫主機",
      },
      DB_USER: {
        required: true,
        type: "string",
        description: "資料庫用戶",
      },
      DB_PASSWORD: {
        required: true,
        type: "string",
        description: "資料庫密碼",
      },
      DB_NAME: {
        required: true,
        type: "string",
        defaultValue: "Ecocampus",
        description: "資料庫名稱",
      },
      DB_PORT: {
        required: false,
        type: "number",
        defaultValue: 1433,
        description: "資料庫端口",
      },
      DB_TYPE: {
        required: false,
        type: "string",
        defaultValue: "mssql",
        description: "資料庫類型",
      },
      JWT_SECRET: {
        required: true,
        type: "string",
        validator: (value: string) => value.length >= 32,
        description: "JWT 密鑰 (至少32字符)",
      },
      JWT_EXPIRES_IN: {
        required: false,
        type: "string",
        defaultValue: "7d",
        description: "JWT 過期時間",
      },
      MAX_UPLOAD_SIZE: {
        required: false,
        type: "number",
        defaultValue: 10485760, // 10MB
        description: "最大上傳大小",
      },
      UPLOAD_PATH: {
        required: false,
        type: "string",
        defaultValue: "public/uploads",
        description: "上傳路徑",
      },
      SCHOOL_LOGO_PATH: {
        required: false,
        type: "string",
        defaultValue: "",
        description: "校徽上傳路徑",
      },
      CAMPUS_SUBMISSION_PHOTO_PATH: {
        required: false,
        type: "string",
        defaultValue: "./api/uploads/campus-submissions/photos",
        description: "校園投稿照片上傳路徑",
      },
      CAMPUS_SUBMISSION_ATTACHMENT_PATH: {
        required: false,
        type: "string",
        defaultValue: "./api/uploads/campus-submissions/attachments",
        description: "校園投稿附件上傳路徑",
      },
    };

    const result: BackendConfigData = {} as BackendConfigData;
    const errors: string[] = [];
    const warnings: string[] = [];

    // 處理每個配置項目
    for (const [key, rule] of Object.entries(rules)) {
      const rawValue = rawConfig[key];

      if (!rawValue && rule.required) {
        if (rule.defaultValue !== undefined) {
          result[key as keyof BackendConfigData] = this.transformValue(rule.defaultValue, rule.type);
          warnings.push(`使用預設值 ${key}: ${rule.defaultValue}`);
        } else {
          errors.push(`必需的配置項目 ${key} 未設定`);
        }
      } else if (rawValue) {
        const transformedValue = this.transformValue(rawValue, rule.type);

        // 自定義驗證
        if (rule.validator && !rule.validator(transformedValue)) {
          errors.push(`配置項目 ${key} 值無效: ${rawValue}`);
        } else {
          result[key as keyof BackendConfigData] = transformedValue;
        }
      } else if (rule.defaultValue !== undefined) {
        result[key as keyof BackendConfigData] = this.transformValue(rule.defaultValue, rule.type);
      }
    }

    // 跳過前端配置 (VITE_ 前綴) - 後端不需要

    // 錯誤檢查
    if (errors.length > 0) {
      throw new Error(`配置驗證失敗:\n${errors.join("\n")}`);
    }

    // 顯示警告
    if (warnings.length > 0) {
      console.warn(`[FileConfigManager] 配置警告:\n${warnings.join("\n")}`);
    }

    return result;
  }

  /**
   * 轉換值類型
   */
  private transformValue(value: any, type: ConfigRule["type"]): any {
    switch (type) {
      case "number":
        const num = Number(value);
        if (isNaN(num)) {
          throw new Error(`無法將 ${value} 轉換為數字`);
        }
        return num;

      case "boolean":
        if (typeof value === "boolean") return value;
        return value === "true" || value === "1";

      case "array":
        if (Array.isArray(value)) return value;
        return value.split(",").map((item: string) => item.trim());

      case "string":
      default:
        return String(value);
    }
  }

  /**
   * 獲取所有後端配置
   */
  public getConfig(): BackendConfigData {
    if (!this.configData) {
      throw new Error("Configuration not loaded");
    }
    return { ...this.configData };
  }

  /**
   * 獲取特定配置項目
   */
  public get<K extends keyof BackendConfigData>(key: K): BackendConfigData[K] {
    if (!this.configData) {
      throw new Error("Configuration not loaded");
    }
    return this.configData[key];
  }

  /**
   * 檢查配置是否已載入
   */
  public isLoaded(): boolean {
    return this.configData !== null;
  }

  /**
   * 重新載入配置
   */
  public reloadConfig(): void {
    this.configData = null;
    this.loadConfig();
  }

  /**
   * 獲取環境類型
   */
  public getEnvironment(): string {
    return this.get("NODE_ENV");
  }

  /**
   * 是否為開發環境
   */
  public isDevelopment(): boolean {
    return this.getEnvironment() === "development";
  }

  /**
   * 是否為生產環境
   */
  public isProduction(): boolean {
    return this.getEnvironment() === "production";
  }

  /**
   * 是否為測試環境
   */
  public isTesting(): boolean {
    return this.getEnvironment() === "testing";
  }

  /**
   * 獲取資料庫配置
   */
  public getDatabaseConfig() {
    return {
      host: this.get("DB_HOST"),
      user: this.get("DB_USER"),
      password: this.get("DB_PASSWORD"),
      database: this.get("DB_NAME"),
      port: this.get("DB_PORT"),
      type: this.get("DB_TYPE"),
    };
  }

  /**
   * 獲取服務器配置
   */
  public getServerConfig() {
    return {
      port: this.get("API_PORT"),
      frontendUrl: this.get("FRONTEND_URL"),
      allowedOrigins: this.get("ALLOWED_ORIGINS"),
    };
  }

  /**
   * 獲取 JWT 配置
   */
  public getJWTConfig() {
    return {
      secret: this.get("JWT_SECRET"),
      expiresIn: this.get("JWT_EXPIRES_IN"),
    };
  }

  /**
   * 獲取上傳配置
   */
  public getUploadConfig() {
    return {
      maxSize: this.get("MAX_UPLOAD_SIZE"),
      path: this.get("UPLOAD_PATH"),
      basePath: this.get("UPLOAD_PATH"),
      generalUploadPath: this.get("UPLOAD_PATH"),
      schoolLogoPath: this.get("SCHOOL_LOGO_PATH") || join(this.get("UPLOAD_PATH"), "school-logos"),
      campusSubmissionPhotoPath: this.get("CAMPUS_SUBMISSION_PHOTO_PATH"),
      campusSubmissionAttachmentPath: this.get("CAMPUS_SUBMISSION_ATTACHMENT_PATH"),
    };
  }

  /**
   * 獲取配置摘要
   */
  public getConfigSummary() {
    if (!this.configData) {
      return { loaded: false };
    }

    return {
      loaded: true,
      environment: this.getEnvironment(),
      totalConfigs: Object.keys(this.configData).length,
      configPath: this.configPath,
      database: {
        host: this.get("DB_HOST"),
        name: this.get("DB_NAME"),
        port: this.get("DB_PORT"),
      },
      server: {
        port: this.get("API_PORT"),
        frontend: this.get("FRONTEND_URL"),
      },
    };
  }
}

// 導出單例實例
export const fileConfig = FileConfigManager.getInstance();
