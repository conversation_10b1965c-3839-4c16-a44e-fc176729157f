/**
 * TokenContext - React Context 實作，提供全域 Token 狀態管理
 * 
 * 核心功能：
 * - 整合 TokenManager，提供 React Context 介面
 * - 訂閱 Token 變更事件，自動更新元件狀態
 * - 支援臨時 Token 操作的 withTemporaryToken 高階函數
 * - 提供 useToken Hook 給元件使用
 * - 處理 Token 過期和錯誤狀態
 */

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { 
  TokenManager, 
  type TokenSubscriber, 
  type TokenState, 
  type TokenValidationResult 
} from '@/services/tokenManager';

// Context 值介面
export interface TokenContextValue {
  // Token 狀態
  token: string | null;
  tokenState: TokenState;
  isLoading: boolean;
  error: Error | null;
  
  // Token 操作方法
  setToken: (token: string | null) => void;
  clearToken: () => void;
  refreshToken?: () => Promise<void>;
  
  // 臨時 Token 操作
  pushTemporaryToken: (tempToken: string) => void;
  popTemporaryToken: () => string | null;
  clearTemporaryTokens: () => void;
  withTemporaryToken: <T>(tempToken: string, callback: () => Promise<T>) => Promise<T>;
  
  // Token 驗證
  validateToken: (token?: string) => Promise<TokenValidationResult>;
  
  // 狀態查詢
  isTemporaryMode: boolean;
  stackDepth: number;
  
  // 除錯資訊
  getDebugInfo: () => Record<string, any>;
}

// Context 預設值
const defaultContextValue: TokenContextValue = {
  token: null,
  tokenState: {
    currentToken: null,
    isTemporaryMode: false,
    stackDepth: 0,
    lastUpdate: Date.now()
  },
  isLoading: true,
  error: null,
  
  setToken: () => {},
  clearToken: () => {},
  
  pushTemporaryToken: () => {},
  popTemporaryToken: () => null,
  clearTemporaryTokens: () => {},
  withTemporaryToken: async () => { throw new Error('TokenContext not initialized'); },
  
  validateToken: async () => ({ valid: false, reason: 'TokenContext not initialized' }),
  
  isTemporaryMode: false,
  stackDepth: 0,
  
  getDebugInfo: () => ({})
};

// 建立 Context
const TokenContext = createContext<TokenContextValue>(defaultContextValue);

// Provider 屬性介面
export interface TokenProviderProps {
  children: React.ReactNode;
  
  // 可選配置
  enableAutoRefresh?: boolean;        // 是否啟用自動重新整理
  refreshInterval?: number;           // 重新整理間隔（毫秒）
  enableValidationCache?: boolean;    // 是否啟用驗證快取
  onTokenChange?: (token: string | null) => void;    // Token 變更回調
  onTokenError?: (error: Error) => void;             // Token 錯誤回調
  onTokenExpired?: () => void;                       // Token 過期回調
}

/**
 * TokenProvider 元件 - 提供 Token 狀態管理的 React Context Provider
 */
export const TokenProvider: React.FC<TokenProviderProps> = ({
  children,
  enableAutoRefresh = false,
  refreshInterval = 30000,
  enableValidationCache = true,
  onTokenChange,
  onTokenError,
  onTokenExpired
}) => {
  // 獲取 TokenManager 實例
  const tokenManagerRef = useRef<TokenManager>(TokenManager.getInstance());
  const tokenManager = tokenManagerRef.current;
  
  // Context 狀態
  const [token, setTokenState] = useState<string | null>(null);
  const [tokenState, setTokenStateData] = useState<TokenState>(() => tokenManager.getTokenState());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // 自動重新整理相關
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 初始化狀態
  useEffect(() => {
    try {
      // 從 TokenManager 載入初始狀態
      const initialToken = tokenManager.getToken();
      const initialState = tokenManager.getTokenState();
      
      setTokenState(initialToken);
      setTokenStateData(initialState);
      setIsLoading(false);
      
      console.log('🔑 [TokenProvider] 初始化完成:', {
        initialToken: initialToken ? '***已設定***' : '未設定',
        initialState
      });
    } catch (initError) {
      console.error('❌ [TokenProvider] 初始化失敗:', initError);
      setError(initError as Error);
      setIsLoading(false);
    }
  }, [tokenManager]);
  
  // 建立訂閱者實例
  const subscriberRef = useRef<TokenSubscriber | null>(null);
  
  // 設定 TokenManager 訂閱
  useEffect(() => {
    const subscriber: TokenSubscriber = {
      onTokenChange: (newToken: string | null) => {
        console.log('📢 [TokenProvider] 收到 Token 變更通知:', {
          newToken: newToken ? '***已設定***' : '未設定'
        });
        
        setTokenState(newToken);
        setTokenStateData(tokenManager.getTokenState());
        setError(null);
        
        // 呼叫外部回調
        if (onTokenChange) {
          try {
            onTokenChange(newToken);
          } catch (callbackError) {
            console.error('❌ [TokenProvider] Token 變更回調執行失敗:', callbackError);
          }
        }
      },
      
      onTokenExpired: () => {
        console.warn('⏰ [TokenProvider] Token 已過期');
        setError(new Error('Token 已過期'));
        
        if (onTokenExpired) {
          try {
            onTokenExpired();
          } catch (callbackError) {
            console.error('❌ [TokenProvider] Token 過期回調執行失敗:', callbackError);
          }
        }
      },
      
      onTokenError: (tokenError: Error) => {
        console.error('❌ [TokenProvider] Token 錯誤:', tokenError);
        setError(tokenError);
        
        if (onTokenError) {
          try {
            onTokenError(tokenError);
          } catch (callbackError) {
            console.error('❌ [TokenProvider] Token 錯誤回調執行失敗:', callbackError);
          }
        }
      }
    };
    
    // 訂閱 TokenManager
    tokenManager.subscribe(subscriber);
    subscriberRef.current = subscriber;
    
    console.log('👀 [TokenProvider] 已訂閱 TokenManager 事件');
    
    // 清理函數
    return () => {
      if (subscriberRef.current) {
        tokenManager.unsubscribe(subscriberRef.current);
        subscriberRef.current = null;
        console.log('👋 [TokenProvider] 已取消訂閱 TokenManager 事件');
      }
    };
  }, [tokenManager, onTokenChange, onTokenError, onTokenExpired]);
  
  // 自動重新整理邏輯
  useEffect(() => {
    if (!enableAutoRefresh || !token) {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }
      return;
    }
    
    const startAutoRefresh = () => {
      refreshTimerRef.current = setInterval(async () => {
        try {
          const validation = await tokenManager.validateToken();
          if (!validation.valid && validation.shouldRefresh) {
            console.log('🔄 [TokenProvider] Token 需要重新整理');
            // 這裡可以實作 Token 重新整理邏輯
          }
        } catch (refreshError) {
          console.error('❌ [TokenProvider] 自動重新整理檢查失敗:', refreshError);
        }
      }, refreshInterval);
    };
    
    startAutoRefresh();
    
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }
    };
  }, [enableAutoRefresh, refreshInterval, token, tokenManager]);
  
  // Context 方法實作
  const contextSetToken = useCallback((newToken: string | null) => {
    try {
      tokenManager.setToken(newToken);
    } catch (setError) {
      console.error('❌ [TokenProvider] 設定 Token 失敗:', setError);
      setError(setError as Error);
    }
  }, [tokenManager]);
  
  const contextClearToken = useCallback(() => {
    try {
      tokenManager.clearToken();
    } catch (clearError) {
      console.error('❌ [TokenProvider] 清除 Token 失敗:', clearError);
      setError(clearError as Error);
    }
  }, [tokenManager]);
  
  const contextPushTemporaryToken = useCallback((tempToken: string) => {
    try {
      tokenManager.pushTemporaryToken(tempToken);
    } catch (pushError) {
      console.error('❌ [TokenProvider] 推送臨時 Token 失敗:', pushError);
      setError(pushError as Error);
      throw pushError;
    }
  }, [tokenManager]);
  
  const contextPopTemporaryToken = useCallback((): string | null => {
    try {
      return tokenManager.popTemporaryToken();
    } catch (popError) {
      console.error('❌ [TokenProvider] 彈出臨時 Token 失敗:', popError);
      setError(popError as Error);
      return null;
    }
  }, [tokenManager]);
  
  const contextClearTemporaryTokens = useCallback(() => {
    try {
      tokenManager.clearTemporaryTokens();
    } catch (clearError) {
      console.error('❌ [TokenProvider] 清除臨時 Token 失敗:', clearError);
      setError(clearError as Error);
    }
  }, [tokenManager]);
  
  const contextWithTemporaryToken = useCallback(<T extends any>(
    tempToken: string,
    callback: () => Promise<T>
  ): Promise<T> => {
    return tokenManager.withTemporaryToken(tempToken, callback)
      .catch((withTempError) => {
        console.error('❌ [TokenProvider] 臨時 Token 操作失敗:', withTempError);
        setError(withTempError as Error);
        throw withTempError;
      });
  }, [tokenManager]);
  
  const contextValidateToken = useCallback(async (tokenToValidate?: string): Promise<TokenValidationResult> => {
    try {
      return await tokenManager.validateToken(tokenToValidate);
    } catch (validateError) {
      console.error('❌ [TokenProvider] Token 驗證失敗:', validateError);
      return {
        valid: false,
        reason: `驗證過程出錯: ${validateError instanceof Error ? validateError.message : '未知錯誤'}`
      };
    }
  }, [tokenManager]);
  
  const contextGetDebugInfo = useCallback((): Record<string, any> => {
    return {
      context: {
        token: token ? '***已設定***' : '未設定',
        isLoading,
        error: error?.message || null,
        isTemporaryMode: tokenState.isTemporaryMode,
        stackDepth: tokenState.stackDepth
      },
      tokenManager: tokenManager.getDebugInfo()
    };
  }, [token, isLoading, error, tokenState, tokenManager]);
  
  // 建構 Context 值
  const contextValue: TokenContextValue = {
    // 狀態
    token,
    tokenState,
    isLoading,
    error,
    
    // 基本操作
    setToken: contextSetToken,
    clearToken: contextClearToken,
    
    // 臨時 Token 操作
    pushTemporaryToken: contextPushTemporaryToken,
    popTemporaryToken: contextPopTemporaryToken,
    clearTemporaryTokens: contextClearTemporaryTokens,
    withTemporaryToken: contextWithTemporaryToken,
    
    // 驗證
    validateToken: contextValidateToken,
    
    // 計算屬性
    isTemporaryMode: tokenState.isTemporaryMode,
    stackDepth: tokenState.stackDepth,
    
    // 除錯
    getDebugInfo: contextGetDebugInfo
  };
  
  return (
    <TokenContext.Provider value={contextValue}>
      {children}
    </TokenContext.Provider>
  );
};

/**
 * useToken Hook - 便捷存取 Token Context 的 Hook
 * 
 * @returns TokenContextValue Token Context 的完整介面
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const {
 *     token,
 *     setToken,
 *     withTemporaryToken,
 *     isTemporaryMode
 *   } = useToken();
 *
 *   const handleLogin = async (userToken: string) => {
 *     setToken(userToken);
 *   };
 *
 *   const handleSpecialOperation = async () => {
 *     await withTemporaryToken('special-token', async () => {
 *       // 使用特殊 Token 執行操作
 *       return api.specialOperation();
 *     });
 *   };
 *
 *   return (
 *     <div>
 *       <p>當前 Token: {token ? '已設定' : '未設定'}</p>
 *       <p>臨時模式: {isTemporaryMode ? '是' : '否'}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export const useToken = (): TokenContextValue => {
  const context = useContext(TokenContext);
  
  if (context === defaultContextValue) {
    throw new Error(
      'useToken 必須在 TokenProvider 內部使用。' +
      '請確保元件被 TokenProvider 包裹。'
    );
  }
  
  return context;
};

/**
 * withTokenContext 高階元件 - 為元件注入 Token Context
 * 
 * @param WrappedComponent 要包裝的元件
 * @returns 包裝後的元件
 * 
 * @example
 * ```tsx
 * const EnhancedComponent = withTokenContext(MyComponent);
 * ```
 */
export const withTokenContext = <P extends object>(
  WrappedComponent: React.ComponentType<P & { tokenContext?: TokenContextValue }>
) => {
  const WithTokenContextComponent = (props: P) => {
    const tokenContext = useToken();

    return (
      <WrappedComponent 
        {...props} 
        tokenContext={tokenContext} 
      />
    );
  };

  WithTokenContextComponent.displayName = 
    `withTokenContext(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithTokenContextComponent;
};

/**
 * 專門用於臨時 Token 操作的高階元件
 * 
 * @param WrappedComponent 要包裝的元件
 * @returns 包裝後的元件，具有 withTemporaryToken 方法
 * 
 * @example
 * ```tsx
 * const EnhancedComponent = withTemporaryToken(MyComponent);
 * 
 * // 在 MyComponent 中可以使用
 * function MyComponent({ withTemporaryToken }) {
 *   const handleOperation = () => {
 *     return withTemporaryToken('temp-token', async () => {
 *       return api.operation();
 *     });
 *   };
 * }
 * ```
 */
export const withTemporaryToken = <P extends object>(
  WrappedComponent: React.ComponentType<P & { 
    withTemporaryToken: <T extends any>(tempToken: string, callback: () => Promise<T>) => Promise<T> 
  }>
) => {
  const WithTemporaryTokenComponent = (props: P) => {
    const { withTemporaryToken: tempTokenFn } = useToken();

    return (
      <WrappedComponent 
        {...props} 
        withTemporaryToken={tempTokenFn} 
      />
    );
  };

  WithTemporaryTokenComponent.displayName = 
    `withTemporaryToken(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithTemporaryTokenComponent;
};

// 預設導出
export default TokenContext;