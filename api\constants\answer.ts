// ========== 答案相關常數 ==========

// 答案狀態
export const ANSWER_STATUS = {
  NOT_FILLED: 0, // 未填寫/已刪除
  FILLED: 1, // 已填寫
  NEEDS_SUPPLEMENT: 2, // 待補件
  REJECTED: 3, // 退件
  COMPLETED: 4, // 已完成
} as const;

// 認證審核狀態
export const REVIEW_STATUS = {
  IN_REVIEW: 0, // 審核中
  APPROVED: 1, // 已通過
  REJECTED: 2, // 已拒絕
  NEEDS_SUPPLEMENT: 3, // 需要補件
  NOT_SUBMITTED: 4, // 未提交
} as const;

// 問題狀態
export const QUESTION_STATUS = {
  INACTIVE: 0, // 非活躍
  ACTIVE: 1, // 活躍
} as const;

// 認證狀態
export const CERTIFICATION_STATUS = {
  INACTIVE: 0, // 非活躍
  ACTIVE: 1, // 活躍
  DELETED: 2, // 已刪除
} as const;

// 錯誤訊息
export const ANSWER_ERROR_MESSAGES = {
  MISSING_PARAMETERS: "缺少必要參數：certificationId, questionId, answerData",
  MISSING_ANSWER_ID: "缺少答案ID",
  MISSING_CERTIFICATION_ID: "缺少認證ID",
  MISSING_QUESTION_OR_CERTIFICATION_ID: "缺少問題ID或認證ID",
  USER_NOT_ASSIGNED_SCHOOL: "用戶未分配學校，無權限修改認證",
  NO_PERMISSION_MODIFY: "無權限修改此認證",
  NO_PERMISSION_VIEW: "無權限查看此認證",
  NO_PERMISSION_DELETE: "無權限刪除此答案",
  CERTIFICATION_UNDER_REVIEW: "認證正在審核中，無法修改答案",
  QUESTION_NOT_FOUND: "問題不存在或已被刪除",
  ANSWER_NOT_FOUND: "答案不存在",
  ANSWER_ALREADY_DELETED: "答案不存在或已被刪除",
  SAVE_FAILED: "保存答案失敗，請稍後再試",
  QUERY_FAILED: "查詢答案失敗，請稍後再試",
  DELETE_FAILED: "刪除答案失敗，請稍後再試",
  JSON_PARSE_ERROR: "JSON 解析失敗",
} as const;

// 成功訊息
export const ANSWER_SUCCESS_MESSAGES = {
  ANSWER_SAVED: "答案已保存",
  ANSWER_UPDATED: "答案已更新",
  ANSWER_DELETED: "答案已刪除",
  ANSWERS_RETRIEVED: "答案查詢成功",
  NO_ANSWER_FOUND: "尚未找到此問題的答案",
} as const;

// SQL 查詢模板
export const SQL_QUERIES = {
  GET_USER_SCHOOL: "SELECT SchoolId FROM Accounts WHERE AccountId = @userId",
  GET_CERTIFICATION_INFO: "SELECT SchoolId, ReviewStatus FROM Certifications WHERE CertificationId = @certificationId",
  GET_CERTIFICATION_SCHOOL: "SELECT SchoolId FROM Certifications WHERE CertificationId = @certificationId",
  GET_QUESTION_INFO: "SELECT QuestionId, QuestionTemplate FROM Questions WHERE QuestionId = @questionId AND Status = @activeStatus",
  GET_EXISTING_ANSWER: `
    SELECT CertificationAnswerId 
    FROM CertificationAnswers 
    WHERE CertificationId = @certificationId AND QuestionId = @questionId
  `,
  UPDATE_ANSWER: `
    UPDATE CertificationAnswers 
    SET AnswerText = @answerText,
        AnswerStatus = @answerStatus,
        UpdatedTime = GETDATE(),
        UpdatedUserId = @userId
    WHERE CertificationAnswerId = @answerId
  `,
  INSERT_ANSWER: `
    INSERT INTO CertificationAnswers (
      CertificationId,
      QuestionId,
      AnswerText,
      AnswerStatus,
      SubmittedDate,
      CreatedTime,
      CreatedUserId,
      UpdatedTime,
      UpdatedUserId,
      SortOrder
    ) 
    OUTPUT INSERTED.CertificationAnswerId
    VALUES (
      @certificationId,
      @questionId,
      @answerText,
      @answerStatus,
      NULL,
      GETDATE(),
      @userId,
      GETDATE(),
      @userId,
      0
    )
  `,
  GET_CERTIFICATION_ANSWERS: `
    SELECT 
      ca.CertificationAnswerId,
      ca.CertificationId,
      ca.QuestionId,
      ca.AnswerText,
      ca.AnswerStatus,
      ca.SubmittedDate,
      ca.ReviewedDate,
      ca.CreatedTime,
      ca.UpdatedTime,
      q.Title as QuestionTitle,
      q.QuestionTemplate,
      q.StepNumber,
      q.ParentQuestionId
    FROM CertificationAnswers ca
    LEFT JOIN Questions q ON ca.QuestionId = q.QuestionId
    WHERE ca.CertificationId = @certificationId
      AND q.Status = @activeStatus
    ORDER BY q.StepNumber, q.SortOrder, q.QuestionId
  `,
  GET_SINGLE_ANSWER: `
    SELECT 
      ca.CertificationAnswerId,
      ca.CertificationId,
      ca.QuestionId,
      ca.AnswerText,
      ca.AnswerStatus,
      ca.SubmittedDate,
      ca.ReviewedDate,
      ca.CreatedTime,
      ca.UpdatedTime,
      q.Title as QuestionTitle,
      q.QuestionTemplate,
      q.StepNumber,
      q.ParentQuestionId
    FROM CertificationAnswers ca
    LEFT JOIN Questions q ON ca.QuestionId = q.QuestionId
    WHERE ca.CertificationId = @certificationId 
      AND ca.QuestionId = @questionId
      AND q.Status = @activeStatus
  `,
  GET_ANSWER_FOR_DELETE: `
    SELECT 
      ca.CertificationAnswerId,
      ca.CertificationId,
      c.SchoolId
    FROM CertificationAnswers ca
    LEFT JOIN Certifications c ON ca.CertificationId = c.CertificationId
    WHERE ca.CertificationAnswerId = @answerId
  `,
  DELETE_ANSWER: `
    UPDATE CertificationAnswers 
    SET AnswerStatus = @deletedStatus,
        UpdatedTime = GETDATE(),
        UpdatedUserId = @userId
    WHERE CertificationAnswerId = @answerId
  `,
} as const;

// 工具函數：驗證必要參數
export const validateSaveParameters = (request: {
  certificationId?: any;
  questionId?: any;
  answerData?: any;
  userId?: any;
}): { isValid: boolean; error?: string } => {
  const { certificationId, questionId, answerData, userId } = request;

  if (certificationId == null || questionId == null || !answerData || !userId) {
    return {
      isValid: false,
      error: ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS,
    };
  }

  return { isValid: true };
};

// 工具函數：檢查是否可以修改認證
export const canModifyCertification = (reviewStatus: number): boolean => {
  return reviewStatus !== REVIEW_STATUS.IN_REVIEW;
};

// 工具函數：檢查用戶是否有權限訪問認證
export const hasPermissionForCertification = (userSchoolId: number, certificationSchoolId: number): boolean => {
  return userSchoolId === certificationSchoolId;
};

// 工具函數：安全解析 JSON
export const safeJSONParse = (jsonString: string | null): any => {
  if (!jsonString) return null;

  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn(`${ANSWER_ERROR_MESSAGES.JSON_PARSE_ERROR}: ${jsonString}`);
    return null;
  }
};

// 工具函數：將答案記錄轉換為答案資訊
export const formatAnswerRecord = (record: any): any => {
  return {
    answerId: record.CertificationAnswerId,
    certificationId: record.CertificationId,
    questionId: record.QuestionId,
    questionTitle: record.QuestionTitle,
    questionTemplate: record.QuestionTemplate,
    stepNumber: record.StepNumber,
    parentQuestionId: record.ParentQuestionId,
    answerData: safeJSONParse(record.AnswerText),
    answerStatus: record.AnswerStatus,
    submittedDate: record.SubmittedDate,
    reviewedDate: record.ReviewedDate,
    createdTime: record.CreatedTime,
    updatedTime: record.UpdatedTime,
  };
};

// 工具函數：生成調試資訊
export const generateDebugInfo = (request: any): any => {
  return {
    certificationId: request.certificationId,
    questionId: request.questionId,
    hasAnswerData: !!request.answerData,
    userId: request.userId,
  };
};

// 工具函數：檢查ID是否有效
export const isValidId = (id: any): boolean => {
  return id != null && !isNaN(parseInt(id));
};

// 工具函數：獲取操作結果訊息
export const getOperationMessage = (isUpdate: boolean): string => {
  return isUpdate ? ANSWER_SUCCESS_MESSAGES.ANSWER_UPDATED : ANSWER_SUCCESS_MESSAGES.ANSWER_SAVED;
};
