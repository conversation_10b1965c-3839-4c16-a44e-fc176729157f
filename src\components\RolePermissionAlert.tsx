import React, { useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { X, ShieldX, LogOut } from "lucide-react";
import { useRoleCheckMessage, useAuth } from "@/hooks/useAuth";

/**
 * 角色權限提示組件
 * 用於顯示角色檢查失敗的訊息和自動登出通知
 */
const RolePermissionAlert: React.FC = () => {
  const { message, clearMessage } = useRoleCheckMessage();
  const { logout } = useAuth();

  // 🔒 新增：當有權限被拒絕訊息時，自動登出使用者
  useEffect(() => {
    if (message) {
      console.log("檢測到權限被拒絕訊息，執行自動登出...");

      // 自動滾動到頂部讓使用者注意到
      window.scrollTo({ top: 0, behavior: "smooth" });

      // 🚪 立即執行登出操作，確保使用者被完全登出
      logout()
        .then(() => {
          console.log("權限不足自動登出完成");
        })
        .catch((error) => {
          console.error("權限不足自動登出失敗:", error);
          // 即使登出 API 失敗，也要清除本地資料
          try {
            sessionStorage.removeItem("userToken");
            sessionStorage.removeItem("userData");
            console.log("已強制清除本地認證資料");
          } catch (localError) {
            console.error("清除本地資料失敗:", localError);
          }
        });
    }
  }, [message, logout]);

  if (!message) {
    return null;
  }

  // 處理重新登入
  const handleReLogin = () => {
    clearMessage();
    // 重定向到登入頁面
    window.location.href = "/login";
  };

  // 處理關閉訊息
  const handleClose = () => {
    clearMessage();
    // 確保關閉訊息時也重定向到登入頁面
    setTimeout(() => {
      window.location.href = "/login";
    }, 100);
  };

  return (
    <div className="fixed top-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <Alert className="border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive max-w-4xl mx-auto">
        <ShieldX className="h-4 w-4" />
        <AlertTitle className="flex items-center justify-between">
          <span>存取權限被拒絕 - 已自動登出</span>
          <Button variant="outline" size="sm" onClick={handleClose} className="h-6 w-6 p-0 hover:bg-destructive/10">
            <X className="h-4 w-4" />
          </Button>
        </AlertTitle>
        <AlertDescription className="mt-2 space-y-4">
          <div className="whitespace-pre-line">{message}</div>

          <div className="font-size-sm text-muted-foreground bg-muted/20 p-3 rounded-md">
            <p className="font-medium text-destructive">⚠️ 系統已自動執行登出操作</p>
            <p className="mt-1">• 您的認證資料已被清除</p>
            <p>• 請使用符合權限要求的帳號重新登入</p>
            <p>• 如有疑問，請聯繫系統管理員</p>
          </div>

          <div className="flex gap-2 pt-2">
            <Button variant="outline" size="sm" onClick={handleReLogin} className="flex items-center gap-2">
              <LogOut className="h-4 w-4" />
              立即重新登入
            </Button>
            <Button variant="secondary" size="sm" onClick={handleClose}>
              關閉並前往登入頁面
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default RolePermissionAlert;
