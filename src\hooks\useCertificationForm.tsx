import React from "react";
import { useState, useCallback, useEffect } from "react";
import { certificationAPI, type CertificationData, type AnswerRecord, ValidationRules } from "@/services/certificationAPI";

export interface CertificationFormState {
  certification: CertificationData | null;
  formData: Record<string, unknown>;
  currentStep: number;
  currentSubStep: number;
  totalSteps: number;
  loading: boolean;
  saving: boolean;
  validationErrors: Record<string, string>;
  progress: {
    completedSteps: number;
    totalSteps: number;
    completedQuestions: number;
    totalQuestions: number;
  };
}

export interface CertificationFormActions {
  loadCertification: (certificationId: number) => Promise<void>;
  saveFormData: (questionId: number, data: unknown) => Promise<void>;
  updateFormData: (questionId: number, data: unknown) => void;
  validateCurrentForm: (questions: FormQuestion[]) => boolean;
  goToStep: (step: number, subStep?: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  submitForReview: () => Promise<void>;
  autoSave: () => Promise<void>;
}

interface FormQuestion {
  id: string;
  type: string;
  label: string;
  required: boolean;
  validation?: Record<string, { min?: number; min_selected?: number }>;
}

export const useCertificationForm = (initialCertificationId?: number, certificationLevel?: string): [CertificationFormState, CertificationFormActions] => {
  const [state, setState] = useState<CertificationFormState>({
    certification: null,
    formData: {},
    currentStep: 0,
    currentSubStep: 0,
    totalSteps: 9,
    loading: false,
    saving: false,
    validationErrors: {},
    progress: {
      completedSteps: 0,
      totalSteps: 9,
      completedQuestions: 0,
      totalQuestions: 0,
    },
  });

  // 載入認證資料
  const loadCertification = useCallback(async (certificationId: number) => {
    setState((prev) => ({ ...prev, loading: true }));

    try {
      const response = await certificationAPI.getCertification(certificationId);
      if (response.success) {
        setState((prev) => ({
          ...prev,
          certification: response.data,
          loading: false,
        }));

        // 載入進度資訊
        const progressResponse = await certificationAPI.getCertificationProgress(certificationId);
        if (progressResponse.success) {
          setState((prev) => ({
            ...prev,
            progress: progressResponse.data,
          }));
        }
      }
    } catch (error) {
      console.error("Failed to load certification:", error);
      setState((prev) => ({ ...prev, loading: false }));
    }
  }, []);

  // 更新表單資料（本地）
  const updateFormData = useCallback((questionId: number, data: unknown) => {
    setState((prev) => ({
      ...prev,
      formData: {
        ...prev.formData,
        [questionId]: data,
      },
    }));
  }, []);

  // 儲存表單資料（API）
  const saveFormData = useCallback(
    async (questionId: number, data: unknown) => {
      if (!state.certification?.sid) return;

      setState((prev) => ({ ...prev, saving: true }));

      try {
        const response = await certificationAPI.saveAnswer(state.certification!.sid!, questionId, { [questionId]: data });

        if (response.success) {
          setState((prev) => ({
            ...prev,
            formData: {
              ...prev.formData,
              [questionId]: data,
            },
            saving: false,
          }));
        }
      } catch (error) {
        console.error("Failed to save form data:", error);
        setState((prev) => ({ ...prev, saving: false }));
      }
    },
    [state.certification?.sid]
  );

  // 驗證當前表單
  const validateCurrentForm = useCallback(
    (questions: FormQuestion[]): boolean => {
      const errors: Record<string, string> = {};

      questions.forEach((question) => {
        const value = state.formData[question.id];

        // 必填檢查
        if (question.required && !ValidationRules.required(value)) {
          errors[question.id] = `${question.label} 為必填項目`;
          return;
        }

        // 認證等級特定驗證
        if (question.validation && certificationLevel) {
          const levelValidation = question.validation[certificationLevel];
          if (levelValidation) {
            if (levelValidation.min && typeof value === "number" && value < levelValidation.min) {
              errors[question.id] = `${question.label} 最少需要 ${levelValidation.min}`;
            }
            if (levelValidation.min_selected && Array.isArray(value) && value.length < levelValidation.min_selected) {
              errors[question.id] = `${question.label} 至少需要選擇 ${levelValidation.min_selected} 項`;
            }
          }
        }
      });

      setState((prev) => ({ ...prev, validationErrors: errors }));
      return Object.keys(errors).length === 0;
    },
    [state.formData, certificationLevel]
  );

  // 步驟導航
  const goToStep = useCallback((step: number, subStep: number = 0) => {
    setState((prev) => ({
      ...prev,
      currentStep: step,
      currentSubStep: subStep,
      validationErrors: {}, // 清除驗證錯誤
    }));
  }, []);

  const nextStep = useCallback(() => {
    setState((prev) => {
      // 這裡需要根據步驟結構來決定下一步
      // 簡化版本：假設每個步驟有3個子步驟
      const maxSubSteps = 3;

      if (prev.currentSubStep < maxSubSteps - 1) {
        return {
          ...prev,
          currentSubStep: prev.currentSubStep + 1,
        };
      } else if (prev.currentStep < prev.totalSteps - 1) {
        return {
          ...prev,
          currentStep: prev.currentStep + 1,
          currentSubStep: 0,
        };
      }

      return prev;
    });
  }, []);

  const prevStep = useCallback(() => {
    setState((prev) => {
      if (prev.currentSubStep > 0) {
        return {
          ...prev,
          currentSubStep: prev.currentSubStep - 1,
        };
      } else if (prev.currentStep > 0) {
        return {
          ...prev,
          currentStep: prev.currentStep - 1,
          currentSubStep: 2, // 假設前一步的最後一個子步驟
        };
      }

      return prev;
    });
  }, []);

  // 提交審核
  const submitForReview = useCallback(async () => {
    if (!state.certification?.sid) return;

    setState((prev) => ({ ...prev, saving: true }));

    try {
      const response = await certificationAPI.submitForReview(state.certification!.sid!);
      if (response.success) {
        setState((prev) => ({
          ...prev,
          certification: prev.certification
            ? {
                ...prev.certification,
                review: "審核中",
              }
            : null,
          saving: false,
        }));
      }
    } catch (error) {
      console.error("Failed to submit for review:", error);
      setState((prev) => ({ ...prev, saving: false }));
    }
  }, [state.certification?.sid]);

  // 自動儲存
  const autoSave = useCallback(async () => {
    if (!state.certification?.sid || state.saving) return;

    // 只儲存有變更的資料
    const changedData = Object.entries(state.formData).filter(([questionId]) => {
      // 這裡可以加入邏輯來判斷資料是否有變更
      return true; // 簡化版本
    });

    if (changedData.length === 0) return;

    setState((prev) => ({ ...prev, saving: true }));

    try {
      const savePromises = changedData.map(([questionId, data]) =>
        certificationAPI.saveAnswer(state.certification!.sid!, parseInt(questionId), { [questionId]: data })
      );

      await Promise.all(savePromises);
      setState((prev) => ({ ...prev, saving: false }));
    } catch (error) {
      console.error("Auto save failed:", error);
      setState((prev) => ({ ...prev, saving: false }));
    }
  }, [state.certification?.sid, state.formData, state.saving]);

  // 初始載入
  useEffect(() => {
    if (initialCertificationId) {
      loadCertification(initialCertificationId);
    }
  }, [initialCertificationId, loadCertification]);

  // 自動儲存定時器
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (Object.keys(state.formData).length > 0) {
        autoSave();
      }
    }, 30000); // 30秒自動儲存

    return () => clearInterval(autoSaveInterval);
  }, [autoSave, state.formData]);

  const actions: CertificationFormActions = {
    loadCertification,
    saveFormData,
    updateFormData,
    validateCurrentForm,
    goToStep,
    nextStep,
    prevStep,
    submitForReview,
    autoSave,
  };

  return [state, actions];
};

// 自動儲存 Hook
export const useAutoSave = (data: Record<string, unknown>, saveFunction: (data: Record<string, unknown>) => Promise<void>, delay: number = 30000) => {
  const [lastSaved, setLastSaved] = useState<string>("");
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  const debouncedSave = useCallback(async () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    const currentData = JSON.stringify(data);
    if (currentData === lastSaved) return;

    const timeout = setTimeout(async () => {
      try {
        await saveFunction(data);
        setLastSaved(currentData);
      } catch (error) {
        console.error("Auto save failed:", error);
      }
    }, delay);

    setSaveTimeout(timeout);
  }, [data, saveFunction, delay, saveTimeout, lastSaved]);

  useEffect(() => {
    debouncedSave();
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
    };
  }, [debouncedSave]);

  const saveNow = useCallback(async () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }
    try {
      await saveFunction(data);
      setLastSaved(JSON.stringify(data));
    } catch (error) {
      console.error("Manual save failed:", error);
      throw error;
    }
  }, [data, saveFunction, saveTimeout]);

  return { saveNow };
};

// 表單驗證 Hook
export const useFormValidation = (questions: FormQuestion[], formData: Record<string, unknown>, certificationLevel?: string) => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateField = useCallback(
    (questionId: string, value: unknown): string | null => {
      const question = questions.find((q) => q.id === questionId);
      if (!question) return null;

      // 必填檢查
      if (question.required && !ValidationRules.required(value)) {
        return `${question.label} 為必填項目`;
      }

      // 認證等級特定驗證
      if (question.validation && certificationLevel) {
        const levelValidation = question.validation[certificationLevel];
        if (levelValidation) {
          if (levelValidation.min && typeof value === "number" && value < levelValidation.min) {
            return `${question.label} 最少需要 ${levelValidation.min}`;
          }
          if (levelValidation.min_selected && Array.isArray(value) && value.length < levelValidation.min_selected) {
            return `${question.label} 至少需要選擇 ${levelValidation.min_selected} 項`;
          }
        }
      }

      return null;
    },
    [questions, certificationLevel]
  );

  const validateAll = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    questions.forEach((question) => {
      const value = formData[question.id];
      const error = validateField(question.id, value);
      if (error) {
        newErrors[question.id] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [questions, formData, validateField]);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const clearFieldError = useCallback((questionId: string) => {
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[questionId];
      return newErrors;
    });
  }, []);

  return {
    errors,
    validateField,
    validateAll,
    clearErrors,
    clearFieldError,
    hasErrors: Object.keys(errors).length > 0,
  };
};
