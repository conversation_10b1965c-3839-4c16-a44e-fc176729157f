import { Router, Request, Response } from "express";
import { QuestionService } from "../services/question-service.js";
import {
  QuestionQueryParams,
  CertificationStepsQueryParams,
  FormQuestionsQueryParams,
  QuestionDetailParams,
  QuestionListResponse,
  CertificationStepsResponse,
  FormQuestionsResponse,
  QuestionDetailResponse,
  QuestionAnalysisResponse,
} from "../models/question.js";
import { DEFAULT_CERTIFICATION_LEVEL } from "../constants/question.js";

const router = Router();

// 根據步驟和認證等級獲取問題
router.get("/", async (req: Request<{}, QuestionListResponse, {}, QuestionQueryParams>, res: Response<QuestionListResponse>) => {
  try {
    const { step, level } = req.query;

    if (!step) {
      return res.status(400).json({
        success: false,
        message: "步驟參數為必填項目",
        data: [],
        step: 0,
        level: 0,
        count: 0,
      });
    }

    const certificationLevel = parseInt(level as string) || DEFAULT_CERTIFICATION_LEVEL;
    const stepNumber = parseInt(step as string);

    console.log(`[Questions API] 查詢步驟 ${stepNumber}，認證等級 ${certificationLevel} 的問題`);

    const questions = await QuestionService.getQuestionsByStep(stepNumber, certificationLevel);

    console.log(`[Questions API] 找到 ${questions.length} 個問題`);

    res.json({
      success: true,
      data: questions,
      step: stepNumber,
      level: certificationLevel,
      count: questions.length,
    });
  } catch (error: unknown) {
    console.error("獲取問題列表失敗:", error);
    res.status(500).json({
      success: false,
      message: "獲取問題列表失敗",
      data: [],
      step: 0,
      level: 0,
      count: 0,
    });
  }
});

// 獲取認證步驟資訊
router.get(
  "/certification-steps",
  async (req: Request<{}, CertificationStepsResponse, {}, CertificationStepsQueryParams>, res: Response<CertificationStepsResponse>) => {
    try {
      const { level } = req.query;
      const certificationLevel = parseInt(level as string) || DEFAULT_CERTIFICATION_LEVEL;

      console.log(`[Questions API] 查詢認證等級 ${certificationLevel} 的步驟資訊`);

      const steps = await QuestionService.getCertificationSteps(certificationLevel);

      console.log(`[Questions API] 找到 ${steps.length} 個步驟的資料`);

      res.json({
        success: true,
        data: steps,
        level: certificationLevel,
        totalSteps: steps.length,
      });
    } catch (error: unknown) {
      console.error("獲取認證步驟失敗:", error);
      res.status(500).json({
        success: false,
        message: "獲取認證步驟失敗",
        data: [],
        level: 0,
        totalSteps: 0,
      });
    }
  }
);

// 獲取表單問題配置 (支援步驟和父問題雙層分組)
router.get("/form-questions", async (req: Request<{}, FormQuestionsResponse, {}, FormQuestionsQueryParams>, res: Response<FormQuestionsResponse>) => {
  try {
    const { certificationId } = req.query;

    const { questions, stepInfo, totalQuestions } = await QuestionService.getFormQuestions(certificationId ? parseInt(certificationId as string) : undefined);

    const response: FormQuestionsResponse = {
      success: true,
      data: {
        questions,
        stepInfo,
      },
      totalQuestions,
    };

    if (certificationId) {
      response.certificationId = parseInt(certificationId as string);
    }

    res.json(response);
  } catch (error: unknown) {
    console.error("獲取表單問題配置失敗:", error);
    res.status(500).json({
      success: false,
      message: "獲取表單問題配置失敗",
      data: { questions: {}, stepInfo: [] },
      totalQuestions: 0,
    });
  }
});

// 分析問題父子關係結構 (必須在/:questionId之前定義)
router.get("/analyze-structure", async (req: Request, res: Response<QuestionAnalysisResponse>) => {
  try {
    console.log(`[Questions API] 分析問題父子關係結構`);

    const analysisData = await QuestionService.analyzeQuestionStructure();

    res.json({
      success: true,
      data: analysisData,
    });
  } catch (error: unknown) {
    console.error("分析問題結構失敗:", error);
    res.status(500).json({
      success: false,
      message: "分析問題結構失敗",
      data: {
        totalQuestions: 0,
        stepAnalysis: [],
        parentGroups: [],
        greenFlagQuestions: [],
      },
    });
  }
});

// 根據問題ID獲取問題詳情
router.get("/:questionId", async (req: Request<QuestionDetailParams>, res: Response<QuestionDetailResponse>) => {
  try {
    const { questionId } = req.params;

    console.log(`[Questions API] 獲取問題 ${questionId} 的詳情`);

    const question = await QuestionService.getQuestionById(parseInt(questionId));

    if (!question) {
      return res.status(404).json({
        success: false,
        message: "問題不存在",
        data: {},
      });
    }

    console.log(`[Questions API] 找到問題: ${question.title}`);

    res.json({
      success: true,
      data: question,
    });
  } catch (error: unknown) {
    console.error("獲取問題詳情失敗:", error);
    res.status(500).json({
      success: false,
      message: "獲取問題詳情失敗",
      data: {},
    });
  }
});

export default router;
