// ========== 預設資料初始化函數 ==========

export const getDefaultTemplateData = (templateId: number): unknown => {
  switch (templateId) {
    case 1:
      return { is_yes_no: "" };
    case 2:
      return {
        student_list: [{ input_1: "", input_2: "", input_3: "" }],
        teacher_list: [{ input_1: "", input_2: "", input_3: "" }],
        community_member_list: [{ input_1: "", input_2: "", input_3: "" }],
      };
    case 3:
      return {
        meeting_date_and_theme: [{ input_1: "", input_2: "" }],
        file: [],
      };
    case 4:
      return {
        is_yes_no: "",
        share_people: { checkbox: [] },
        how_share_meeting: {
          checkbox: [],
          text: { text_1: "", text_2: "", text_3: "" },
        },
      };
    case 5:
      return {
        is_yes_no: "",
        textarea: "",
      };
    case 6:
      return {
        photo_record: [{ photo_url: "", photo_date: "", photo_des: "" }],
      };
    case 16:
      return { textarea: "" };
    case 19:
      return {
        textarea_1: "",
        textarea_2: "",
        textarea_3: "",
        textarea_4: "",
        textarea_5: "",
      };
    case 21:
      return {
        textarea_1: "",
        textarea_2: "",
        textarea_3: "",
        textarea_4: "",
        textarea_5: "",
        textarea_6: "",
        textarea_7: "",
      };
    case 8:
      return {
        improve_path_list: [
          {
            path: "",
            cname: "",
            who_make: { checkbox: [], text: { text_3: "" } },
            date: { input_1: "", input_2: "" },
            execution_short_aims: "",
            execution_mid_aims: "",
            execution_long_aims: "",
            how_share: {
              checkbox: [],
              text: { text_1: "", text_2: "", text_3: "" },
            },
            photo_record: [{ photo_url: "", photo_date: "", photo_des: "" }],
          },
        ],
      };
    case 9:
      return {
        monitor_list: [
          {
            path: "",
            monitor_topic: [
              {
                topic: { input_1: "", input_2: "" },
                table: [
                  {
                    table_name: "",
                    table_contant: [{ input_1: "", input_2: "", input_3: "", input_4: "" }],
                  },
                ],
              },
            ],
          },
        ],
      };
    case 12:
      return {
        teaching_sample_list: [
          {
            teaching_sample_name: "",
            teaching_sample_designer: "",
            grade: { checkbox: [] },
            path: { checkbox: [] },
            subject: { checkbox: [] },
            teaching_sample_hour: "",
            teaching_sample_aim: "",
            teaching_sample_content: "",
            teaching_sample_method: "",
            teaching_sample_evaluation: "",
            teaching_sample_resource: "",
            teaching_sample_reference: "",
            file: [],
          },
        ],
      };
    case 22:
      return {
        case_1: { grade: "", course: "", activity: "" },
        case_2: { grade: "", course: "", activity: "" },
        case_3: { grade: "", course: "", activity: "" },
      };

    case 11:
      return {
        question_1: "",
        question_2: "",
        question_3: "",
        question_4: "",
      };
    case 13:
      return {
        question_1: "",
        question_2: "",
      };

    // 使用文字區域的模板
    case 7:
    case 10:
    case 14:
    case 15:
    case 17:
    case 18:
    case 20:
      return { textarea: "" };

    default:
      return { textarea: "" }; // 預設使用文字區域
  }
};
