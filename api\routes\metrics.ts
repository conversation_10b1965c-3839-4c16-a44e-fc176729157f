import { Router, Request, Response } from 'express';
import { authMonitor } from '@/shared/monitoring/AuthMetrics';

const router = Router();

/**
 * GET /metrics
 * 獲取系統監控指標（JSON 格式）
 */
router.get('/metrics', (req: Request, res: Response) => {
  try {
    const metrics = authMonitor.getMetricsSnapshot();
    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      metrics
    });
  } catch (error) {
    console.error('獲取指標失敗:', error);
    res.status(500).json({
      success: false,
      error: '無法獲取系統指標'
    });
  }
});

/**
 * GET /metrics/prometheus
 * 獲取 Prometheus 格式的指標
 */
router.get('/metrics/prometheus', (req: Request, res: Response) => {
  try {
    const prometheusMetrics = authMonitor.exportPrometheusMetrics();
    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } catch (error) {
    console.error('導出 Prometheus 指標失敗:', error);
    res.status(500).send('# Error exporting metrics');
  }
});

/**
 * POST /metrics/reset
 * 重置監控指標（僅限開發環境）
 */
router.post('/metrics/reset', (req: Request, res: Response) => {
  // 僅在開發環境允許重置
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({
      success: false,
      error: '生產環境不允許重置指標'
    });
  }

  try {
    authMonitor.resetMetrics();
    res.json({
      success: true,
      message: '指標已重置',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('重置指標失敗:', error);
    res.status(500).json({
      success: false,
      error: '無法重置指標'
    });
  }
});

/**
 * GET /health
 * 健康檢查端點
 */
router.get('/health', (req: Request, res: Response) => {
  const metrics = authMonitor.getMetricsSnapshot();
  const errorRate = parseFloat(metrics.errors.rate);
  const successRate = parseFloat(metrics.tokenValidation.successRate);
  
  // 判斷健康狀態
  const isHealthy = errorRate < 5 && successRate > 90;
  
  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'degraded',
    timestamp: new Date().toISOString(),
    checks: {
      errorRate: {
        value: metrics.errors.rate,
        threshold: '< 5%',
        status: errorRate < 5 ? 'pass' : 'fail'
      },
      tokenValidationSuccessRate: {
        value: metrics.tokenValidation.successRate,
        threshold: '> 90%',
        status: successRate > 90 ? 'pass' : 'fail'
      },
      uptime: metrics.system.uptime
    }
  });
});

export default router;