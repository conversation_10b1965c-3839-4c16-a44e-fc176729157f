/**
 * 用戶服務介面定義
 * 定義統一的用戶資料存取合約
 * 
 * @description 提供標準化的用戶資料介面，支援多種查詢方式和一致的回傳格式
 * @version 1.0.0
 */

import { MemberProfile } from './IAuthService';

/**
 * 用戶查詢選項介面
 */
export interface UserQueryOptions {
  /** 是否包含詳細的 Profile 資料 */
  includeProfile?: boolean;
  /** 是否包含角色映射資訊 */
  includeRoleMapping?: boolean;
  /** 是否包含學校資訊 */
  includeSchoolInfo?: boolean;
  /** 是否包含縣市資訊 */
  includeCountyInfo?: boolean;
  /** 是否檢查帳號狀態 */
  checkAccountStatus?: boolean;
  /** 快取時間（秒），null 表示不使用快取 */
  cacheTimeout?: number | null;
}

/**
 * 用戶查詢結果介面
 */
export interface UserQueryResult {
  /** 查詢是否成功 */
  success: boolean;
  /** 用戶資料 */
  user?: MemberProfile;
  /** 錯誤訊息（查詢失敗時） */
  message?: string;
  /** 資料來源 */
  source?: 'database' | 'cache' | 'test';
  /** 查詢時間 */
  timestamp?: Date;
  /** 是否來自快取 */
  fromCache?: boolean;
}

/**
 * 用戶列表查詢選項
 */
export interface UserListOptions extends UserQueryOptions {
  /** 分頁大小 */
  pageSize?: number;
  /** 頁碼（從 1 開始） */
  page?: number;
  /** 排序欄位 */
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLoginAt';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 角色篩選 */
  roleFilter?: string | string[];
  /** 是否只顯示活躍帳號 */
  activeOnly?: boolean;
}

/**
 * 用戶列表查詢結果
 */
export interface UserListResult {
  /** 查詢是否成功 */
  success: boolean;
  /** 用戶資料列表 */
  users?: MemberProfile[];
  /** 總筆數 */
  totalCount?: number;
  /** 目前頁碼 */
  currentPage?: number;
  /** 總頁數 */
  totalPages?: number;
  /** 錯誤訊息 */
  message?: string;
  /** 查詢時間 */
  timestamp?: Date;
}

/**
 * 用戶更新資料介面
 */
export interface UserUpdateData {
  /** 姓名 */
  name?: string;
  /** 電子郵件 */
  email?: string;
  /** 電話號碼 */
  phone?: string;
  /** 學校名稱 */
  schoolName?: string;
  /** 縣市代碼 */
  countyCode?: string;
  /** 是否啟用 */
  isActive?: boolean;
  /** 額外的設定檔資料 */
  profile?: Record<string, any>;
}

/**
 * 用戶更新結果介面
 */
export interface UserUpdateResult {
  /** 更新是否成功 */
  success: boolean;
  /** 更新後的用戶資料 */
  user?: MemberProfile;
  /** 更新的欄位列表 */
  updatedFields?: string[];
  /** 結果訊息 */
  message?: string;
  /** 更新時間 */
  timestamp?: Date;
}

/**
 * 用戶服務主介面
 * 定義所有用戶資料相關操作的標準合約
 */
export interface IUserService {
  /**
   * 取得目前認證用戶資料
   * 基於 Token 取得用戶資訊，通常用於前端取得當前登入用戶
   * 
   * @param token 用戶認證 Token
   * @param options 查詢選項（可選）
   * @returns Promise<UserQueryResult> 查詢結果
   * 
   * @example
   * ```typescript
   * const result = await userService.getCurrentUser(token);
   * if (result.success && result.user) {
   *   console.log(`目前用戶: ${result.user.name}`);
   * }
   * ```
   */
  getCurrentUser(token: string, options?: UserQueryOptions): Promise<UserQueryResult>;

  /**
   * 根據用戶 ID 取得用戶資料
   * 直接通過用戶 ID 查詢，通常用於管理功能或用戶詳情頁面
   * 
   * @param id 用戶 ID（字串或數字）
   * @param options 查詢選項（可選）
   * @returns Promise<UserQueryResult> 查詢結果
   * 
   * @example
   * ```typescript
   * const result = await userService.getUserById('user-123');
   * if (result.success && result.user) {
   *   console.log(`找到用戶: ${result.user.name}`);
   * }
   * ```
   */
  getUserById(id: string | number, options?: UserQueryOptions): Promise<UserQueryResult>;

  /**
   * 根據 Token 取得用戶資料
   * 與 getCurrentUser 相似但提供更多的查詢控制選項
   * 
   * @param token 用戶認證 Token
   * @param options 查詢選項（可選）
   * @returns Promise<UserQueryResult> 查詢結果
   * 
   * @example
   * ```typescript
   * const result = await userService.getUserByToken(token, { 
   *   includeSchoolInfo: true,
   *   includeRoleMapping: true
   * });
   * ```
   */
  getUserByToken(token: string, options?: UserQueryOptions): Promise<UserQueryResult>;

  /**
   * 根據電子郵件取得用戶資料
   * 通常用於忘記密碼、帳號查詢等功能
   * 
   * @param email 電子郵件地址
   * @param options 查詢選項（可選）
   * @returns Promise<UserQueryResult> 查詢結果
   */
  getUserByEmail?(email: string, options?: UserQueryOptions): Promise<UserQueryResult>;

  /**
   * 搜尋用戶列表
   * 支援分頁、排序和篩選功能
   * 
   * @param searchOptions 搜尋選項
   * @returns Promise<UserListResult> 搜尋結果
   */
  searchUsers?(searchOptions: UserListOptions): Promise<UserListResult>;

  /**
   * 更新用戶資料
   * 更新指定用戶的基本資料
   * 
   * @param id 用戶 ID
   * @param updateData 要更新的資料
   * @returns Promise<UserUpdateResult> 更新結果
   */
  updateUser?(id: string | number, updateData: UserUpdateData): Promise<UserUpdateResult>;

  /**
   * 檢查用戶是否存在
   * 快速檢查用戶是否存在於系統中
   * 
   * @param id 用戶 ID
   * @returns Promise<boolean> 是否存在
   */
  userExists?(id: string | number): Promise<boolean>;

  /**
   * 取得用戶統計資訊
   * 取得系統中的用戶統計資料
   * 
   * @returns Promise<UserStats> 統計資訊
   */
  getUserStats?(): Promise<{
    totalUsers: number;
    activeUsers: number;
    usersByRole: Record<string, number>;
    recentLogins: number;
  }>;
}

/**
 * 用戶資料快取服務介面
 * 專門處理用戶資料的快取管理
 */
export interface IUserCacheService {
  /**
   * 取得快取的用戶資料
   * 
   * @param key 快取鍵值
   * @returns Promise<MemberProfile | null> 用戶資料或 null
   */
  get(key: string): Promise<MemberProfile | null>;

  /**
   * 設定用戶資料快取
   * 
   * @param key 快取鍵值
   * @param user 用戶資料
   * @param ttl 存活時間（秒）
   */
  set(key: string, user: MemberProfile, ttl?: number): Promise<void>;

  /**
   * 刪除用戶資料快取
   * 
   * @param key 快取鍵值
   */
  delete(key: string): Promise<void>;

  /**
   * 清除指定用戶的所有快取
   * 
   * @param userId 用戶 ID
   */
  clearUserCache(userId: string | number): Promise<void>;

  /**
   * 清除所有用戶資料快取
   */
  clearAll(): Promise<void>;
}

/**
 * 用戶資料存取層介面
 * 定義底層資料存取操作
 */
export interface IUserRepository {
  /**
   * 根據 ID 查詢用戶
   * 
   * @param id 用戶 ID
   * @returns Promise<MemberProfile | null> 用戶資料或 null
   */
  findById(id: string | number): Promise<MemberProfile | null>;

  /**
   * 根據 Token 查詢用戶
   * 
   * @param token 認證 Token
   * @returns Promise<MemberProfile | null> 用戶資料或 null
   */
  findByToken(token: string): Promise<MemberProfile | null>;

  /**
   * 根據電子郵件查詢用戶
   * 
   * @param email 電子郵件
   * @returns Promise<MemberProfile | null> 用戶資料或 null
   */
  findByEmail(email: string): Promise<MemberProfile | null>;

  /**
   * 搜尋用戶列表
   * 
   * @param criteria 搜尋條件
   * @returns Promise<MemberProfile[]> 用戶列表
   */
  findAll(criteria?: any): Promise<MemberProfile[]>;

  /**
   * 更新用戶資料
   * 
   * @param id 用戶 ID
   * @param updateData 更新資料
   * @returns Promise<MemberProfile | null> 更新後的用戶資料
   */
  update(id: string | number, updateData: UserUpdateData): Promise<MemberProfile | null>;

  /**
   * 檢查用戶是否存在
   * 
   * @param id 用戶 ID
   * @returns Promise<boolean> 是否存在
   */
  exists(id: string | number): Promise<boolean>;
}