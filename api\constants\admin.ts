// ========== 管理員功能相關常數 ==========

// 答案狀態
export const ANSWER_STATUS = {
  NOT_FILLED: 0, // 未填寫
  FILLED: 1, // 已填寫
  NEEDS_SUPPLEMENT: 2, // 待補件
  REJECTED: 3, // 退件
  COMPLETED: 4, // 已完成
} as const;

// 認證審核狀態
export const REVIEW_STATUS = {
  IN_REVIEW: 0, // 審核中
  APPROVED: 1, // 已通過
  REJECTED: 2, // 已拒絕
  NEEDS_SUPPLEMENT: 3, // 需要補件
  NOT_SUBMITTED: 4, // 未提交
} as const;

// 認證狀態
export const CERTIFICATION_STATUS = {
  INACTIVE: 0, // 非活躍
  ACTIVE: 1, // 活躍
  DELETED: 2, // 已刪除
} as const;

// 管理員操作類型
export const ADMIN_ACTION_TYPES = {
  REQUEST_SUPPLEMENT: "request_supplement", // 要求補件
  APPROVE: "approve", // 批准
  REJECT: "reject", // 拒絕
  COMMENT: "comment", // 評論
} as const;

// 答案狀態描述
export const ANSWER_STATUS_DESCRIPTIONS = {
  [ANSWER_STATUS.NOT_FILLED]: "未填寫",
  [ANSWER_STATUS.FILLED]: "已填寫",
  [ANSWER_STATUS.NEEDS_SUPPLEMENT]: "待補件",
  [ANSWER_STATUS.REJECTED]: "退件",
  [ANSWER_STATUS.COMPLETED]: "已審核",
} as const;

// 管理員角色和權限
export const ADMIN_ROLES = {
  GOVERNMENT: "Government",
  ADMIN: "admin",
} as const;

export const ADMIN_PERMISSIONS = {
  ADMIN: "admin",
  REVIEW: "review",
  MANAGE: "manage",
} as const;

export const ADMIN_PERMISSION_GROUPS = {
  ADMIN: "admin",
  REVIEWER: "reviewer",
  MANAGER: "manager",
} as const;

// 評論長度限制
export const COMMENT_LIMITS = {
  MAX_LENGTH: 20, // 評審意見最大長度
  MIN_LENGTH: 1, // 評審意見最小長度
} as const;

// 錯誤訊息
export const ADMIN_ERROR_MESSAGES = {
  MISSING_PARAMETERS: "Missing required parameters",
  INSUFFICIENT_PERMISSIONS: "Insufficient permissions. Admin role required.",
  CERTIFICATION_NOT_FOUND: "Certification not found",
  ANSWER_NOT_FOUND: "Answer not found for the specified question",
  NOT_IN_REVIEW_STATUS: "Can only mark answers for certifications in review status",
  INVALID_ACTION: "Invalid action",
  COMMENT_TOO_LONG: "評審意見不得超過20個字",
  COMMENT_TOO_SHORT: "評審意見不能為空",
  INTERNAL_SERVER_ERROR: "Internal server error",
  CERTIFICATION_NOT_IN_REVIEW: "Certification is not in review status",
} as const;

// 成功訊息
export const ADMIN_SUCCESS_MESSAGES = {
  ANSWER_STATUS_MARKED: "Answer status marked successfully",
  ACTION_LOGS_RETRIEVED: "Action logs retrieved successfully",
  REVIEW_COMMENT_SAVED: "評審意見保存成功",
  REVIEW_COMMENT_UPDATED: "評審意見更新成功",
  REVIEW_COMMENTS_RETRIEVED: "Review comments retrieved successfully",
} as const;

// SQL 查詢模板
export const SQL_QUERIES = {
  GET_CERTIFICATION: `
    SELECT CertificationId, ReviewStatus, Status
    FROM Certifications 
    WHERE CertificationId = @certificationId AND Status = @activeStatus
  `,
  GET_ANSWER: `
    SELECT AnswerId, QuestionId, AnswerStatus
    FROM CertificationAnswers 
    WHERE CertificationId = @certificationId AND QuestionId = @questionId
  `,
  UPDATE_ANSWER_STATUS: `
    UPDATE CertificationAnswers 
    SET AnswerStatus = @answerStatus, UpdatedTime = GETDATE()
    WHERE AnswerId = @answerId
  `,
  INSERT_ACTION_LOG: `
    INSERT INTO AdminActionLogs (
      CertificationId, QuestionId, AnswerId, AdminUserId, Action, 
      PreviousStatus, NewStatus, ActionTime, Notes
    ) VALUES (
      @certificationId, @questionId, @answerId, @adminUserId, @action,
      @previousStatus, @newStatus, GETDATE(), @notes
    )
  `,
  GET_ACTION_LOGS: `
    SELECT 
      l.ActionLogId,
      l.CertificationId,
      l.QuestionId,
      l.AnswerId,
      l.Action,
      l.PreviousStatus,
      l.NewStatus,
      l.ActionTime,
      l.Notes,
      a.Username as AdminUsername,
      q.Title as QuestionTitle
    FROM AdminActionLogs l
    LEFT JOIN Accounts a ON l.AdminUserId = a.AccountId
    LEFT JOIN Questions q ON l.QuestionId = q.QuestionId
    WHERE l.CertificationId = @certificationId
    ORDER BY l.ActionTime DESC
  `,
  GET_EXISTING_STEP_RECORD: `
    SELECT CertificationStepRecordId 
    FROM CertificationStepRecords 
    WHERE CertificationId = @certificationId AND StepNumber = @stepNumber
  `,
  UPDATE_STEP_RECORD: `
    UPDATE CertificationStepRecords 
    SET StepOpinion = @comment,
        UpdatedUserId = @adminUserId,
        UpdatedTime = GETDATE()
    WHERE CertificationStepRecordId = @recordId
  `,
  INSERT_STEP_RECORD: `
    INSERT INTO CertificationStepRecords (
      CertificationId, StepNumber, StepOpinion, CreatedUserId, UpdatedUserId, UpdatedTime
    ) VALUES (
      @certificationId, @stepNumber, @comment, @adminUserId, @adminUserId, GETDATE()
    )
  `,
  GET_REVIEW_COMMENTS: `
    SELECT 
      csr.CertificationStepRecordId,
      csr.CertificationId,
      csr.StepNumber,
      csr.StepOpinion as Comment,
      csr.CreatedTime,
      csr.UpdatedTime,
      a.Username as AdminUsername
    FROM CertificationStepRecords csr
    LEFT JOIN Accounts a ON csr.UpdatedUserId = a.AccountId
    WHERE csr.CertificationId = @certificationId 
      AND csr.StepOpinion IS NOT NULL 
      AND csr.StepOpinion != ''
    ORDER BY csr.StepNumber, csr.CreatedTime DESC
  `,
} as const;

// 工具函數：檢查是否為管理員
export const isAdmin = (user: { roleType?: string; permissions?: string[]; permissionGroups?: string[] }): boolean => {
  if (!user) return false;

  return (
    user.roleType === ADMIN_ROLES.GOVERNMENT ||
    user.permissions?.includes(ADMIN_PERMISSIONS.ADMIN) ||
    user.permissionGroups?.includes(ADMIN_PERMISSION_GROUPS.ADMIN)
  );
};

// 工具函數：獲取答案狀態和描述
export const getAnswerStatusInfo = (action: string): { status: number; description: string } => {
  switch (action) {
    case ADMIN_ACTION_TYPES.REQUEST_SUPPLEMENT:
      return {
        status: ANSWER_STATUS.NEEDS_SUPPLEMENT,
        description: ANSWER_STATUS_DESCRIPTIONS[ANSWER_STATUS.NEEDS_SUPPLEMENT],
      };
    case ADMIN_ACTION_TYPES.APPROVE:
      return {
        status: ANSWER_STATUS.COMPLETED,
        description: ANSWER_STATUS_DESCRIPTIONS[ANSWER_STATUS.COMPLETED],
      };
    default:
      throw new Error(ADMIN_ERROR_MESSAGES.INVALID_ACTION);
  }
};

// 工具函數：驗證操作類型
export const isValidAction = (action: string): boolean => {
  return Object.values(ADMIN_ACTION_TYPES).includes(action as any);
};

// 工具函數：解析步驟ID
export const parseStepId = (stepId: string): number => {
  return parseInt(stepId.replace("step_", "")) || parseInt(stepId);
};

// 工具函數：驗證評論長度
export const validateComment = (comment: string): { isValid: boolean; error?: string } => {
  const trimmedComment = comment.trim();

  if (trimmedComment.length === 0) {
    return { isValid: false, error: ADMIN_ERROR_MESSAGES.COMMENT_TOO_SHORT };
  }

  if (trimmedComment.length > COMMENT_LIMITS.MAX_LENGTH) {
    return { isValid: false, error: ADMIN_ERROR_MESSAGES.COMMENT_TOO_LONG };
  }

  return { isValid: true };
};

// 工具函數：格式化管理員操作日誌
export const formatActionLog = (log: any): AdminActionLog => {
  return {
    actionLogId: log.ActionLogId,
    certificationId: log.CertificationId,
    questionId: log.QuestionId,
    answerId: log.AnswerId,
    action: log.Action,
    previousStatus: log.PreviousStatus,
    newStatus: log.NewStatus,
    actionTime: log.ActionTime,
    notes: log.Notes,
    adminUsername: log.AdminUsername,
    questionTitle: log.QuestionTitle,
  };
};

// 工具函數：格式化評審意見
export const formatReviewComment = (comment: any): ReviewComment => {
  return {
    certificationStepRecordId: comment.CertificationStepRecordId,
    certificationId: comment.CertificationId,
    stepNumber: comment.StepNumber,
    comment: comment.Comment,
    createdTime: comment.CreatedTime,
    updatedTime: comment.UpdatedTime,
    adminUsername: comment.AdminUsername,
  };
};

// 工具函數：檢查認證是否可以被管理員操作
export const canAdminOperateCertification = (certification: { ReviewStatus: number }): boolean => {
  return certification.ReviewStatus === REVIEW_STATUS.IN_REVIEW;
};

// 工具函數：生成操作日誌備註
export const generateActionNote = (action: string, statusDescription: string): string => {
  return `管理員標示答案為${statusDescription}`;
};
