/**
 * 儲存管理器 - 統一管理 sessionStorage 和 localStorage
 * 
 * 核心功能：
 * - 統一的儲存介面，支援 sessionStorage 和 localStorage
 * - 資料類型自動偵測和轉換
 * - 錯誤處理和回退機制
 * - 儲存配額管理和清理策略
 * - 加密支援（可選）
 */

// 儲存類型列舉
export type StorageType = 'session' | 'local';

// 資料類型列舉
export type DataType = 
  | 'token'           // 認證 Token（敏感資料，僅 session）
  | 'user'            // 用戶資料（敏感資料，僅 session）
  | 'certification'   // 認證相關資料（敏感資料，僅 session）
  | 'config'          // 系統配置（永久保存，local）
  | 'preference'      // 用戶偏好（永久保存，local）
  | 'cache';          // 快取資料（session）

// 儲存配置介面
export interface StorageConfig {
  storageType: StorageType;
  encrypted: boolean;
  maxSize: number;        // 最大儲存大小（bytes）
  ttl?: number;          // 存活時間（毫秒），undefined 表示永不過期
  autoCleanup: boolean;   // 是否自動清理
}

// 儲存項目介面（包含元數據）
export interface StorageItem<T = any> {
  value: T;
  timestamp: number;
  ttl?: number;
  encrypted: boolean;
  type: DataType;
}

// 儲存統計介面
export interface StorageStats {
  totalItems: number;
  usedSpace: number;      // 已使用空間（估算）
  availableSpace: number; // 可用空間（估算）
  expiredItems: number;   // 過期項目數量
}

/**
 * StorageManager 類別 - 單例模式的儲存管理器
 */
export class StorageManager {
  private static instance: StorageManager;
  private readonly configs = new Map<DataType, StorageConfig>();
  private readonly STORAGE_PREFIX = 'ecocampus_';
  private readonly MAX_STORAGE_SIZE = 5 * 1024 * 1024; // 5MB 預設配額
  
  // 加密相關（簡單 Base64 編碼，實際應用中應使用更強的加密）
  private readonly ENCRYPTION_KEY = 'eco_storage_key_2025';
  
  private constructor() {
    this.initializeConfigs();
    this.performStartupCleanup();
  }

  /**
   * 獲取 StorageManager 單例實例
   */
  public static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  /**
   * 初始化儲存配置
   */
  private initializeConfigs(): void {
    // Token 資料：敏感資料，僅在 session 中儲存
    this.configs.set('token', {
      storageType: 'session',
      encrypted: false, // Token 本身已經是加密的
      maxSize: 1024,
      autoCleanup: true
    });

    // 用戶資料：敏感資料，僅在 session 中儲存
    this.configs.set('user', {
      storageType: 'session',
      encrypted: false,
      maxSize: 4096,
      autoCleanup: true
    });

    // 認證相關資料：敏感資料，僅在 session 中儲存
    this.configs.set('certification', {
      storageType: 'session',
      encrypted: false,
      maxSize: 2048,
      autoCleanup: true
    });

    // 系統配置：非敏感，永久保存
    this.configs.set('config', {
      storageType: 'local',
      encrypted: false,
      maxSize: 8192,
      autoCleanup: false
    });

    // 用戶偏好：非敏感，永久保存
    this.configs.set('preference', {
      storageType: 'local',
      encrypted: false,
      maxSize: 4096,
      autoCleanup: false
    });

    // 快取資料：臨時資料，session 儲存，有 TTL
    this.configs.set('cache', {
      storageType: 'session',
      encrypted: false,
      maxSize: 16384,
      ttl: 30 * 60 * 1000, // 30 分鐘
      autoCleanup: true
    });

    console.log('🔧 [StorageManager] 儲存配置已初始化');
  }

  /**
   * 啟動時清理過期資料
   */
  private performStartupCleanup(): void {
    try {
      this.cleanupExpiredItems();
      console.log('🧹 [StorageManager] 啟動清理完成');
    } catch (error) {
      console.error('❌ [StorageManager] 啟動清理失敗:', error);
    }
  }

  /**
   * 生成儲存鍵值
   * @param key 原始鍵值
   * @param type 資料類型
   * @returns 完整的儲存鍵值
   */
  private generateStorageKey(key: string, type: DataType): string {
    return `${this.STORAGE_PREFIX}${type}_${key}`;
  }

  /**
   * 獲取儲存介面
   * @param storageType 儲存類型
   * @returns Storage 介面
   */
  private getStorage(storageType: StorageType): Storage {
    switch (storageType) {
      case 'session':
        return sessionStorage;
      case 'local':
        return localStorage;
      default:
        throw new Error(`不支援的儲存類型: ${storageType}`);
    }
  }

  /**
   * 加密資料
   * @param data 原始資料
   * @returns 加密後的字串
   */
  private encrypt(data: string): string {
    // 簡單的 Base64 編碼（實際應用應使用更強的加密）
    try {
      return btoa(unescape(encodeURIComponent(data + this.ENCRYPTION_KEY)));
    } catch (error) {
      console.error('❌ [StorageManager] 加密失敗:', error);
      return data; // 回退到未加密
    }
  }

  /**
   * 檢查字符串是否為有效的 Base64 格式
   * @param str 要檢查的字符串
   * @returns 是否為有效的 Base64
   */
  private isValidBase64(str: string): boolean {
    try {
      // Base64 字符串只能包含 A-Z, a-z, 0-9, +, /, = 字符
      const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
      
      // 檢查格式是否符合 Base64 模式
      if (!base64Pattern.test(str)) {
        return false;
      }
      
      // Base64 字符串長度必須是 4 的倍數（忽略填充）
      const withoutPadding = str.replace(/=/g, '');
      if (withoutPadding.length % 4 === 1) {
        return false;
      }
      
      // 嘗試解碼測試
      atob(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 解密資料
   * @param encryptedData 加密的資料
   * @returns 解密後的字串
   */
  private decrypt(encryptedData: string): string {
    try {
      // 檢查是否為有效的 Base64 格式
      if (!this.isValidBase64(encryptedData)) {
        console.debug('[StorageManager] 數據非 Base64 格式，跳過解密');
        return encryptedData;
      }
      
      const decrypted = decodeURIComponent(escape(atob(encryptedData)));
      // 移除加密鍵
      return decrypted.replace(this.ENCRYPTION_KEY, '');
    } catch (error) {
      console.error('❌ [StorageManager] 解密失敗:', error);
      console.debug('[StorageManager] 回退到原始數據');
      return encryptedData; // 回退到原始資料
    }
  }

  /**
   * 創建儲存項目
   * @param value 要儲存的值
   * @param type 資料類型
   * @returns StorageItem 物件
   */
  private createStorageItem<T>(value: T, type: DataType): StorageItem<T> {
    const config = this.configs.get(type);
    if (!config) {
      throw new Error(`未找到資料類型 ${type} 的配置`);
    }

    return {
      value,
      timestamp: Date.now(),
      ttl: config.ttl,
      encrypted: config.encrypted,
      type
    };
  }

  /**
   * 序列化儲存項目
   * @param item 儲存項目
   * @returns 序列化後的字串
   */
  private serializeItem(item: StorageItem): string {
    try {
      const serialized = JSON.stringify(item);
      return item.encrypted ? this.encrypt(serialized) : serialized;
    } catch (error) {
      console.error('❌ [StorageManager] 序列化失敗:', error);
      throw new Error('序列化儲存項目失敗');
    }
  }

  /**
   * 反序列化儲存項目
   * @param data 序列化的資料
   * @returns StorageItem 物件
   */
  private deserializeItem<T>(data: string): StorageItem<T> | null {
    try {
      // 嘗試解密（如果需要）
      let jsonString = data;
      
      // 簡單檢測是否為加密資料（Base64 通常不包含 { 字符）
      if (!data.startsWith('{')) {
        try {
          jsonString = this.decrypt(data);
        } catch {
          // 解密失敗，可能是未加密的資料
        }
      }

      const item = JSON.parse(jsonString);
      
      // 驗證是否為有效的 StorageItem
      if (typeof item === 'object' && 
          item !== null && 
          'value' in item && 
          'timestamp' in item) {
        return item as StorageItem<T>;
      }

      return null;
    } catch (error) {
      console.error('❌ [StorageManager] 反序列化失敗:', error);
      return null;
    }
  }

  /**
   * 檢查儲存項目是否過期
   * @param item 儲存項目
   * @returns 是否過期
   */
  private isItemExpired(item: StorageItem): boolean {
    if (!item.ttl) {
      return false; // 沒有 TTL，永不過期
    }

    const now = Date.now();
    return (now - item.timestamp) > item.ttl;
  }

  /**
   * 檢查儲存配額
   * @param storageType 儲存類型
   * @param newDataSize 新資料大小
   * @returns 是否超出配額
   */
  private checkQuota(storageType: StorageType, newDataSize: number): boolean {
    try {
      const storage = this.getStorage(storageType);
      
      // 估算當前使用空間
      let currentSize = 0;
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key && key.startsWith(this.STORAGE_PREFIX)) {
          const value = storage.getItem(key) || '';
          currentSize += key.length + value.length;
        }
      }

      return (currentSize + newDataSize) > this.MAX_STORAGE_SIZE;
    } catch (error) {
      console.error('❌ [StorageManager] 配額檢查失敗:', error);
      return false; // 檢查失敗時允許儲存
    }
  }

  /**
   * 儲存資料
   * @param key 鍵值
   * @param value 要儲存的值
   * @param type 資料類型
   */
  public set<T>(key: string, value: T, type: DataType): void {
    if (!key) {
      throw new Error('儲存鍵值不能為空');
    }

    const config = this.configs.get(type);
    if (!config) {
      throw new Error(`未找到資料類型 ${type} 的配置`);
    }

    try {
      const storage = this.getStorage(config.storageType);
      const storageKey = this.generateStorageKey(key, type);
      const item = this.createStorageItem(value, type);
      const serializedItem = this.serializeItem(item);

      // 檢查資料大小
      if (serializedItem.length > config.maxSize) {
        throw new Error(`資料大小 (${serializedItem.length}) 超過限制 (${config.maxSize})`);
      }

      // 檢查儲存配額
      if (this.checkQuota(config.storageType, serializedItem.length)) {
        console.warn('⚠️ [StorageManager] 儲存配額不足，嘗試清理');
        this.cleanupExpiredItems();
        
        // 再次檢查
        if (this.checkQuota(config.storageType, serializedItem.length)) {
          throw new Error('儲存配額不足');
        }
      }

      storage.setItem(storageKey, serializedItem);
      
      console.log(`💾 [StorageManager] 已儲存 ${type} 資料:`, {
        key,
        size: serializedItem.length,
        storage: config.storageType
      });
    } catch (error) {
      console.error('❌ [StorageManager] 儲存失敗:', error);
      throw error;
    }
  }

  /**
   * 讀取資料
   * @param key 鍵值
   * @param type 資料類型
   * @returns 儲存的值，如果不存在或過期則返回 null
   */
  public get<T>(key: string, type: DataType): T | null {
    if (!key) {
      throw new Error('讀取鍵值不能為空');
    }

    const config = this.configs.get(type);
    if (!config) {
      throw new Error(`未找到資料類型 ${type} 的配置`);
    }

    try {
      const storage = this.getStorage(config.storageType);
      const storageKey = this.generateStorageKey(key, type);
      const serializedItem = storage.getItem(storageKey);

      if (!serializedItem) {
        return null;
      }

      const item = this.deserializeItem<T>(serializedItem);
      if (!item) {
        console.warn('⚠️ [StorageManager] 無法反序列化資料，移除無效項目');
        storage.removeItem(storageKey);
        return null;
      }

      // 檢查是否過期
      if (this.isItemExpired(item)) {
        console.log('⏰ [StorageManager] 資料已過期，自動移除');
        storage.removeItem(storageKey);
        return null;
      }

      return item.value;
    } catch (error) {
      console.error('❌ [StorageManager] 讀取失敗:', error);
      return null;
    }
  }

  /**
   * 移除資料
   * @param key 鍵值
   * @param type 資料類型
   * @returns 是否成功移除
   */
  public remove(key: string, type: DataType): boolean {
    if (!key) {
      throw new Error('移除鍵值不能為空');
    }

    const config = this.configs.get(type);
    if (!config) {
      throw new Error(`未找到資料類型 ${type} 的配置`);
    }

    try {
      const storage = this.getStorage(config.storageType);
      const storageKey = this.generateStorageKey(key, type);
      
      const existed = storage.getItem(storageKey) !== null;
      storage.removeItem(storageKey);

      if (existed) {
        console.log(`🗑️ [StorageManager] 已移除 ${type} 資料:`, key);
      }

      return existed;
    } catch (error) {
      console.error('❌ [StorageManager] 移除失敗:', error);
      return false;
    }
  }

  /**
   * 檢查資料是否存在
   * @param key 鍵值
   * @param type 資料類型
   * @returns 是否存在且未過期
   */
  public exists(key: string, type: DataType): boolean {
    return this.get(key, type) !== null;
  }

  /**
   * 清除指定類型的所有資料
   * @param type 資料類型，如果未提供則清除所有資料
   */
  public clear(type?: DataType): void {
    try {
      if (type) {
        // 清除指定類型
        const config = this.configs.get(type);
        if (!config) {
          throw new Error(`未找到資料類型 ${type} 的配置`);
        }

        const storage = this.getStorage(config.storageType);
        const prefix = `${this.STORAGE_PREFIX}${type}_`;
        
        const keysToRemove: string[] = [];
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i);
          if (key && key.startsWith(prefix)) {
            keysToRemove.push(key);
          }
        }

        keysToRemove.forEach(key => storage.removeItem(key));
        console.log(`🧹 [StorageManager] 已清除 ${type} 類型資料，共 ${keysToRemove.length} 項`);
      } else {
        // 清除所有資料
        [sessionStorage, localStorage].forEach(storage => {
          const keysToRemove: string[] = [];
          for (let i = 0; i < storage.length; i++) {
            const key = storage.key(i);
            if (key && key.startsWith(this.STORAGE_PREFIX)) {
              keysToRemove.push(key);
            }
          }
          keysToRemove.forEach(key => storage.removeItem(key));
        });
        console.log('🧹 [StorageManager] 已清除所有資料');
      }
    } catch (error) {
      console.error('❌ [StorageManager] 清除失敗:', error);
      throw error;
    }
  }

  /**
   * 清理過期項目
   * @returns 清理的項目數量
   */
  public cleanupExpiredItems(): number {
    let cleanedCount = 0;

    try {
      [sessionStorage, localStorage].forEach(storage => {
        const keysToRemove: string[] = [];
        
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i);
          if (key && key.startsWith(this.STORAGE_PREFIX)) {
            const serializedItem = storage.getItem(key);
            if (serializedItem) {
              const item = this.deserializeItem(serializedItem);
              if (!item || this.isItemExpired(item)) {
                keysToRemove.push(key);
              }
            }
          }
        }

        keysToRemove.forEach(key => {
          storage.removeItem(key);
          cleanedCount++;
        });
      });

      if (cleanedCount > 0) {
        console.log(`🧹 [StorageManager] 清理過期項目完成，共清理 ${cleanedCount} 項`);
      }
    } catch (error) {
      console.error('❌ [StorageManager] 清理過期項目失敗:', error);
    }

    return cleanedCount;
  }

  /**
   * 獲取儲存統計資訊
   * @param storageType 儲存類型
   * @returns StorageStats 統計資訊
   */
  public getStats(storageType: StorageType): StorageStats {
    try {
      const storage = this.getStorage(storageType);
      let totalItems = 0;
      let usedSpace = 0;
      let expiredItems = 0;

      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key && key.startsWith(this.STORAGE_PREFIX)) {
          totalItems++;
          const value = storage.getItem(key) || '';
          usedSpace += key.length + value.length;

          // 檢查是否過期
          const item = this.deserializeItem(value);
          if (item && this.isItemExpired(item)) {
            expiredItems++;
          }
        }
      }

      return {
        totalItems,
        usedSpace,
        availableSpace: this.MAX_STORAGE_SIZE - usedSpace,
        expiredItems
      };
    } catch (error) {
      console.error('❌ [StorageManager] 獲取統計失敗:', error);
      return {
        totalItems: 0,
        usedSpace: 0,
        availableSpace: this.MAX_STORAGE_SIZE,
        expiredItems: 0
      };
    }
  }

  /**
   * 獲取所有指定類型的鍵值
   * @param type 資料類型
   * @returns 鍵值陣列
   */
  public getKeys(type: DataType): string[] {
    const config = this.configs.get(type);
    if (!config) {
      return [];
    }

    try {
      const storage = this.getStorage(config.storageType);
      const prefix = `${this.STORAGE_PREFIX}${type}_`;
      const keys: string[] = [];

      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key && key.startsWith(prefix)) {
          // 移除前綴，返回原始鍵值
          keys.push(key.substring(prefix.length));
        }
      }

      return keys;
    } catch (error) {
      console.error('❌ [StorageManager] 獲取鍵值失敗:', error);
      return [];
    }
  }

  /**
   * 獲取除錯資訊
   * @returns 除錯資訊物件
   */
  public getDebugInfo(): Record<string, any> {
    return {
      configsCount: this.configs.size,
      sessionStats: this.getStats('session'),
      localStats: this.getStats('local'),
      supportedTypes: Array.from(this.configs.keys()),
      maxStorageSize: this.MAX_STORAGE_SIZE
    };
  }
}

// 導出單例實例（便於直接使用）
export const storageManager = StorageManager.getInstance();

// 導出預設值
export default storageManager;