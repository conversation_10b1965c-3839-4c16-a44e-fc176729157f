/**
 * Token 驗證服務
 * 統一的 Token 驗證邏輯，整合資料庫驗證、測試環境和舊版相容
 * 
 * @description 抽取並統一原本分散在各處的 Token 驗證邏輯，提供一致性的驗證結果
 * @version 1.0.0
 */

import { TokenValidationResult, MemberProfile } from '../interfaces/IAuthService';
import { AuthErrorCode, ErrorMessages } from '../constants/error-codes';
import { ALLOWED_ROLES, normalizeRole, isValidRole, getRoleDisplayName } from '../constants/roles';

/**
 * Token 驗證器核心類別
 * 提供統一的 Token 驗證邏輯和角色權限檢查
 */
export class TokenValidator {
  /**
   * 核心 Token 驗證方法
   * 按優先級順序進行多重驗證：資料庫 → 測試環境 → 舊版相容
   * 
   * @param token 要驗證的 Token
   * @returns Promise<TokenValidationResult> 驗證結果，包含使用者資料和權限資訊
   * 
   * @example
   * ```typescript
   * const result = await TokenValidator.validate('user-token-123');
   * if (result.valid) {
   *   console.log(`歡迎 ${result.user.name}`);
   *   // 使用 result.user 進行後續操作
   * } else {
   *   console.error(`驗證失敗: ${result.message}`);
   * }
   * ```
   */
  static async validate(token: string): Promise<TokenValidationResult> {
    const timestamp = new Date();
    
    try {
      // 基本輸入驗證
      if (!token || typeof token !== 'string' || !token.trim()) {
        return {
          valid: false,
          message: ErrorMessages[AuthErrorCode.TOKEN_NOT_FOUND].zh,
          errorCode: AuthErrorCode.TOKEN_NOT_FOUND,
          timestamp
        };
      }

      const trimmedToken = token.trim();

      // 基本格式檢查
      if (trimmedToken.length < 8) {
        return {
          valid: false,
          message: ErrorMessages[AuthErrorCode.TOKEN_INVALID].zh,
          errorCode: AuthErrorCode.TOKEN_INVALID,
          timestamp
        };
      }

      console.log(`開始驗證 Token: ${trimmedToken.substring(0, 8)}...`);

      // 優先級 1: 資料庫驗證（主要驗證路徑）
      try {
        const dbResult = await this.validateFromDatabase(trimmedToken);
        if (dbResult.valid) {
          console.log(`資料庫驗證成功: ${dbResult.user?.name} (${dbResult.user?.roleType})`);
          return { ...dbResult, timestamp, tokenSource: 'database' };
        }
      } catch (dbError) {
        console.warn('資料庫驗證失敗，嘗試其他驗證方式', dbError);
      }

      // 優先級 2: 測試 Token 驗證（開發和測試環境）
      if (this.isTestEnvironment()) {
        try {
          const testResult = await this.validateTestToken(trimmedToken);
          if (testResult.valid) {
            console.log(`測試 Token 驗證成功: ${testResult.user?.name}`);
            return { ...testResult, timestamp, tokenSource: 'test' };
          }
        } catch (testError) {
          console.warn('測試 Token 驗證失敗', testError);
        }
      }

      // 優先級 3: 舊版相容 Token 驗證（過渡期）
      try {
        const legacyResult = this.validateLegacyToken(trimmedToken);
        if (legacyResult.valid) {
          console.log(`舊版 Token 驗證成功: ${legacyResult.user?.name}`);
          return { ...legacyResult, timestamp, tokenSource: 'legacy' };
        }
      } catch (legacyError) {
        console.warn('舊版 Token 驗證失敗', legacyError);
      }

      // 所有驗證方式都失敗
      console.log(`Token 驗證失敗: 所有驗證方式都無法通過`);
      return {
        valid: false,
        message: ErrorMessages[AuthErrorCode.TOKEN_NOT_FOUND].zh,
        errorCode: AuthErrorCode.TOKEN_NOT_FOUND,
        timestamp
      };

    } catch (error) {
      console.error('Token 驗證過程發生錯誤:', error);
      return {
        valid: false,
        message: ErrorMessages[AuthErrorCode.TOKEN_INVALID].zh,
        errorCode: AuthErrorCode.TOKEN_INVALID,
        error: error instanceof Error ? error.message : String(error),
        timestamp
      };
    }
  }

  /**
   * 角色權限檢查
   * 驗證使用者角色是否具備系統存取權限
   * 
   * @param roleType 使用者角色類型（字串或數字）
   * @returns boolean 是否有權限
   * 
   * @example
   * ```typescript
   * // 檢查角色權限
   * if (TokenValidator.checkRolePermission('school')) {
   *   console.log('學校角色有權限');
   * }
   * 
   * // 檢查數字角色（TokenType）
   * if (TokenValidator.checkRolePermission(1)) {
   *   console.log('TokenType 1 有權限');
   * }
   * ```
   */
  static checkRolePermission(roleType: string | number): boolean {
    try {
      if (roleType === null || roleType === undefined) {
        return false;
      }

      // 使用統一的角色驗證器
      return isValidRole(roleType);
    } catch (error) {
      console.warn(`角色權限檢查失敗: ${roleType}`, error);
      return false;
    }
  }

  /**
   * 角色正規化處理
   * 將各種格式的角色識別碼轉換為標準格式
   * 
   * @param role 角色識別碼（字串或數字）
   * @returns string 標準化的角色字串
   * 
   * @example
   * ```typescript
   * console.log(TokenValidator.normalizeRole(1)); // 'school'
   * console.log(TokenValidator.normalizeRole('School')); // 'school'
   * console.log(TokenValidator.normalizeRole('生態學校')); // 'school'
   * ```
   */
  static normalizeRole(role: string | number): string | null {
    try {
      return normalizeRole(role);
    } catch (error) {
      console.warn(`角色正規化失敗: ${role}`, error);
      return null;
    }
  }

  /**
   * 取得角色顯示名稱
   * 
   * @param role 角色識別碼
   * @returns string 角色的中文顯示名稱
   */
  static getRoleDisplayName(role: string | number): string {
    try {
      return getRoleDisplayName(role);
    } catch (error) {
      console.warn(`角色名稱取得失敗: ${role}`, error);
      return '未知角色';
    }
  }

  /**
   * 檢查是否為測試環境
   * 
   * @returns boolean 是否為測試環境
   */
  private static isTestEnvironment(): boolean {
    const env = process.env.NODE_ENV || 'production';
    return env === 'development' || env === 'test' || env === 'testing';
  }

  /**
   * 從資料庫驗證 Token
   * 整合現有的 getUserByTokenWithRoleMapping 功能
   * 
   * @param token Token 字串
   * @returns Promise<TokenValidationResult> 驗證結果
   */
  private static async validateFromDatabase(token: string): Promise<TokenValidationResult> {
    try {
      // 動態載入資料庫模組，避免循環依賴
      const { getUserByTokenWithRoleMapping } = require('../../api/config/database-mssql');
      
      const user = await getUserByTokenWithRoleMapping(token);
      if (!user) {
        return {
          valid: false,
          message: '在資料庫中找不到對應的 Token',
        };
      }

      // 正規化角色類型
      const normalizedRole = this.normalizeRole(user.roleType);
      if (!normalizedRole) {
        return {
          valid: false,
          user,
          message: `無效的角色類型: ${user.roleType}`,
          errorCode: AuthErrorCode.ROLE_NOT_ALLOWED
        };
      }

      // 檢查角色權限
      if (!this.checkRolePermission(user.roleType)) {
        return {
          valid: false,
          user,
          message: `角色 ${this.getRoleDisplayName(user.roleType)} 沒有系統存取權限`,
          errorCode: AuthErrorCode.ROLE_NOT_ALLOWED
        };
      }

      // 檢查帳號狀態
      if (user.isActive === false || user.isActive === 0) {
        return {
          valid: false,
          user,
          message: ErrorMessages[AuthErrorCode.ACCOUNT_DISABLED].zh,
          errorCode: AuthErrorCode.ACCOUNT_DISABLED
        };
      }

      // 強化用戶資料
      const enhancedUser: MemberProfile = {
        ...user,
        roleType: normalizedRole,
        originalRoleType: user.roleType,
        isActive: Boolean(user.isActive),
        lastLoginAt: new Date()
      };

      return {
        valid: true,
        user: enhancedUser,
        message: 'Token 資料庫驗證成功'
      };

    } catch (error) {
      console.error('資料庫驗證過程發生錯誤:', error);
      throw error;
    }
  }

  /**
   * 測試 Token 驗證
   * 在開發和測試環境中使用的特殊 Token 驗證
   * 
   * @param token Token 字串
   * @returns Promise<TokenValidationResult> 驗證結果
   */
  private static async validateTestToken(token: string): Promise<TokenValidationResult> {
    try {
      // 動態載入測試 Token 配置
      let TEST_TOKENS: any = {};
      try {
        const testTokensModule = require('../../api/config/testTokens');
        TEST_TOKENS = testTokensModule.TEST_TOKENS || testTokensModule.default || {};
      } catch (error) {
        console.log('未找到測試 Token 配置檔案');
        return { valid: false, message: '測試環境未配置' };
      }

      const tokenData = TEST_TOKENS[token];
      if (!tokenData) {
        return { valid: false, message: '非有效的測試 Token' };
      }

      // 建構測試用戶資料
      const testUser: MemberProfile = {
        accountId: tokenData.user.accountId || 'test-user',
        name: tokenData.user.name || '測試用戶',
        email: tokenData.user.email || '<EMAIL>',
        roleType: this.normalizeRole(tokenData.user.role) || ALLOWED_ROLES.SCHOOL,
        originalRoleType: tokenData.user.role,
        isActive: true,
        schoolId: tokenData.user.schoolId,
        schoolName: tokenData.user.schoolName,
        countyCode: tokenData.user.countyCode,
        countyName: tokenData.user.countyName,
        isSystemAdmin: tokenData.user.isSystemAdmin || false,
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      // 檢查測試用戶的角色權限
      if (!this.checkRolePermission(testUser.roleType)) {
        return {
          valid: false,
          user: testUser,
          message: `測試角色 ${testUser.roleType} 沒有系統存取權限`,
          errorCode: AuthErrorCode.ROLE_NOT_ALLOWED
        };
      }

      return {
        valid: true,
        user: testUser,
        message: '測試 Token 驗證成功'
      };

    } catch (error) {
      console.error('測試 Token 驗證失敗:', error);
      return {
        valid: false,
        message: '測試 Token 驗證過程發生錯誤',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 舊版相容 Token 驗證
   * 支援舊系統的 Token 格式，提供過渡期相容性
   * 
   * @param token Token 字串
   * @returns TokenValidationResult 驗證結果
   */
  private static validateLegacyToken(token: string): TokenValidationResult {
    try {
      // 舊版 Token 通常有特定的格式或前綴
      if (token.startsWith('legacy-') || token.startsWith('old-')) {
        const legacyUser: MemberProfile = {
          accountId: 'legacy-user',
          name: '舊版系統用戶',
          email: '<EMAIL>',
          roleType: ALLOWED_ROLES.SCHOOL, // 預設為學校角色
          originalRoleType: 'legacy',
          isActive: true,
          createdAt: new Date(),
          lastLoginAt: new Date(),
          isSystemAdmin: false
        };

        console.log('使用舊版 Token 相容模式');
        return {
          valid: true,
          user: legacyUser,
          message: '舊版 Token 驗證成功（相容模式）'
        };
      }

      return { valid: false, message: '不是有效的舊版 Token' };

    } catch (error) {
      console.error('舊版 Token 驗證失敗:', error);
      return {
        valid: false,
        message: '舊版 Token 驗證過程發生錯誤',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 檢查 Token 是否即將過期
   * 
   * @param token Token 字串
   * @returns Promise<boolean> 是否即將過期
   */
  static async isTokenExpiringSoon(token: string): Promise<boolean> {
    try {
      // 由於目前系統的 Token 沒有內建過期機制
      // 這個方法主要用於未來擴展
      const result = await this.validate(token);
      
      if (!result.valid) {
        return true; // 無效的 Token 視為已過期
      }

      // TODO: 實作真正的過期檢查邏輯
      // 可以從資料庫查詢 Token 的建立時間並計算是否接近過期
      
      return false; // 暫時假設所有有效 Token 都不會過期
      
    } catch (error) {
      console.warn('檢查 Token 過期狀態失敗:', error);
      return true; // 發生錯誤時保守地假設已過期
    }
  }

  /**
   * 批次驗證多個 Token
   * 
   * @param tokens Token 字串陣列
   * @returns Promise<TokenValidationResult[]> 驗證結果陣列
   */
  static async validateMultiple(tokens: string[]): Promise<TokenValidationResult[]> {
    if (!Array.isArray(tokens)) {
      throw new Error('參數必須是 Token 陣列');
    }

    const validationPromises = tokens.map(token => this.validate(token));
    return Promise.all(validationPromises);
  }

  /**
   * 取得驗證統計資訊
   * 
   * @returns 驗證統計資料
   */
  static getValidationStats() {
    return {
      supportedSources: ['database', 'test', 'legacy'],
      supportedRoles: Object.values(ALLOWED_ROLES),
      isTestEnvironment: this.isTestEnvironment(),
      timestamp: new Date()
    };
  }
}

// 預設匯出
export default TokenValidator;