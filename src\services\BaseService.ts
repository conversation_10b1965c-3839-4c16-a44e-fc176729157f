import { authService } from "./authService";
import { getApiBaseUrl } from "../config/environment";

// 基底 API 響應介面
export interface BaseApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: Record<string, string>;
}

// 基底配置介面
export interface BaseServiceConfig {
  baseUrl?: string;
  timeout?: number;
  debugMode?: boolean;
  headers?: Record<string, string>;
}

// BaseService 抽象基底類別
export abstract class BaseService {
  protected readonly baseUrl: string;
  protected readonly timeout: number;
  protected readonly debugMode: boolean;
  protected readonly defaultHeaders: Record<string, string>;

  constructor(config?: BaseServiceConfig) {
    this.baseUrl = config?.baseUrl || getApiBaseUrl();
    this.timeout = config?.timeout || 30000;
    this.debugMode = config?.debugMode || false;
    this.defaultHeaders = {
      "Content-Type": "application/json",
      ...config?.headers,
    };
  }

  // 通用請求方法
  protected async request<T>(endpoint: string, options: RequestInit = {}): Promise<BaseApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;

    // 調試模式下的日誌
    if (this.debugMode) {
      console.log(`🌐 [${this.constructor.name}] API 請求:`, {
        endpoint,
        url,
        method: options.method || "GET",
      });
    }

    // 設置超時控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 獲取認證 headers
      const authHeaders = authService.getAuthHeaders();

      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...authHeaders,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // 調試模式下的響應日誌
      if (this.debugMode) {
        console.log(`✅ [${this.constructor.name}] 響應成功:`, {
          endpoint,
          status: response.status,
          dataSize: JSON.stringify(data).length,
        });
      }

      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      console.error(`❌ [${this.constructor.name}] 請求失敗 ${endpoint}:`, error);
      throw error;
    }
  }

  // GET 請求便捷方法
  protected async get<T>(endpoint: string, params?: Record<string, string | number>): Promise<BaseApiResponse<T>> {
    let url = endpoint;
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value));
      });
      url += `?${searchParams.toString()}`;
    }

    return this.request<T>(url, { method: "GET" });
  }

  // POST 請求便捷方法
  protected async post<T>(endpoint: string, data?: unknown): Promise<BaseApiResponse<T>> {
    const options: RequestInit = {
      method: "POST",
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, options);
  }

  // PUT 請求便捷方法
  protected async put<T>(endpoint: string, data?: unknown): Promise<BaseApiResponse<T>> {
    const options: RequestInit = {
      method: "PUT",
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, options);
  }

  // DELETE 請求便捷方法
  protected async delete<T>(endpoint: string): Promise<BaseApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }

  // 文件上傳方法
  protected async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, string>): Promise<BaseApiResponse<T>> {
    const formData = new FormData();
    formData.append("file", file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    // 獲取認證 headers (不包含 Content-Type，讓瀏覽器自動設置)
    const authHeaders = authService.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: "POST",
      headers: {
        ...authHeaders,
        // 不設置 Content-Type，讓瀏覽器自動設置 multipart/form-data
      },
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  }

  // 健康檢查方法
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.request<{ status: string }>("/health");
      return response.success && response.data.status === "ok";
    } catch {
      return false;
    }
  }
}
