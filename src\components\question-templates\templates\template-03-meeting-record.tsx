import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, FileText, Trash2, Plus, AlertTriangle } from "lucide-react";
import { DatePicker } from "@/components/ui/date-picker";
import { validateDateIntervals } from "../shared/utils";
import type { MeetingRecordData, TemplateProps } from "../types";

// 模板3 - 會議記錄
export const MeetingRecordTemplate: React.FC<TemplateProps<MeetingRecordData>> = ({ data, onChange, disabled = false }) => {
  const safeData = {
    meeting_date_and_theme: data.meeting_date_and_theme || [{ input_1: "", input_2: "" }],
    file: data.file || [],
  };

  const addMeetingRecord = () => {
    const updatedData = {
      ...safeData,
      meeting_date_and_theme: [...safeData.meeting_date_and_theme, { input_1: "", input_2: "" }],
    };
    onChange(updatedData);
  };

  const removeMeetingRecord = (index: number) => {
    if (safeData.meeting_date_and_theme.length > 1) {
      const updatedList = safeData.meeting_date_and_theme.filter((_, i) => i !== index);
      onChange({ ...safeData, meeting_date_and_theme: updatedList });
    }
  };

  const updateMeetingRecord = (index: number, field: string, value: string) => {
    const updatedList = safeData.meeting_date_and_theme.map((meeting, i) => (i === index ? { ...meeting, [field]: value } : meeting));
    onChange({ ...safeData, meeting_date_and_theme: updatedList });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const newFiles = files.map((file) => ({
      file_url: file.name, // 實際應用中應該上傳到服務器並返回URL
      file_name: file.name,
    }));
    onChange({ ...safeData, file: [...safeData.file, ...newFiles] });
  };

  const removeFile = (index: number) => {
    const updatedFiles = safeData.file.filter((_, i) => i !== index);
    onChange({ ...safeData, file: updatedFiles });
  };

  // 日期間隔驗證
  const dateValidation = validateDateIntervals(safeData.meeting_date_and_theme.map((m) => m.input_1));

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="font-size-base flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            會議日期與主題
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {safeData.meeting_date_and_theme.map((meeting, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-center mb-3">
                <h4 className="font-size-base font-medium text-gray-800">會議記錄 {index + 1}</h4>
                {!disabled && safeData.meeting_date_and_theme.length > 1 && (
                  <Button variant="outline" size="sm" onClick={() => removeMeetingRecord(index)} className="text-red-600 hover:text-red-700">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 會議日期 */}
                <div>
                  <label className="block font-size-sm font-medium text-gray-700 mb-2">
                    會議日期 <span className="text-red-500">*</span>
                  </label>
                  <DatePicker
                    value={meeting.input_1}
                    onChange={(value) => updateMeetingRecord(index, "input_1", value)}
                    placeholder="請選擇會議日期"
                    disabled={disabled}
                  />
                </div>

                {/* 會議主題 */}
                <div>
                  <label className="block font-size-sm font-medium text-gray-700 mb-2">
                    會議主題 <span className="text-red-500">*</span>
                  </label>
                  <Textarea
                    value={meeting.input_2}
                    onChange={(e) => updateMeetingRecord(index, "input_2", e.target.value)}
                    placeholder="請輸入會議主題和內容概要"
                    rows={3}
                    disabled={disabled}
                    className="min-h-[80px]"
                  />
                  <div className="font-size-sm text-gray-500 text-right mt-1">{meeting.input_2?.length || 0} 字</div>
                </div>
              </div>
            </div>
          ))}

          {/* 日期間隔驗證提示 */}
          {!dateValidation.isValid && (
            <Alert className="border-amber-200 bg-amber-50">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">{dateValidation.message}</AlertDescription>
            </Alert>
          )}

          {!disabled && (
            <Button variant="outline" onClick={addMeetingRecord} className="w-full border-dashed">
              <Plus className="w-4 h-4 mr-2" />
              新增會議記錄
            </Button>
          )}
        </CardContent>
      </Card>

      {/* 會議記錄檔案上傳 */}
      <Card>
        <CardHeader>
          <CardTitle className="font-size-base flex items-center gap-2">
            <FileText className="w-4 h-4" />
            會議記錄檔案
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block font-size-sm font-medium text-gray-700 mb-2">上傳會議記錄檔案</label>
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.txt"
              onChange={handleFileUpload}
              disabled={disabled}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <div className="font-size-sm text-gray-500 mt-1">支援格式：PDF、Word文件、文字檔案</div>
          </div>

          {safeData.file.length > 0 && (
            <div className="space-y-2">
              <h5 className="font-size-sm font-medium text-gray-700">已上傳檔案：</h5>
              {safeData.file.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-gray-500" />
                    <span className="font-size-sm text-gray-700">{file.file_name}</span>
                  </div>
                  {!disabled && (
                    <Button variant="outline" size="sm" onClick={() => removeFile(index)} className="text-red-600 hover:text-red-700">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
