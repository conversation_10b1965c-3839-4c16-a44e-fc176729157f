# =====================================
# EcoCampus 前端本地開發環境配置
# =====================================
# 說明：這是前端本地開發環境的配置文件
# 使用方式：cp env.development.example .env

# =====================================
# 環境設定
# =====================================
NODE_ENV=development
APP_ENV=development

# =====================================
# 前端服務器配置
# =====================================
FRONTEND_PORT=8080
FRONTEND_URL=http://localhost:8080

# =====================================
# API 服務器配置（前端連接用）
# =====================================
API_BASE_URL=http://localhost:3001/api

# =====================================
# 外部連結配置（開發環境）
# =====================================
VITE_CERTIFICATION_APPLICATION_GUIDE=https://dev.ecocampus.local/certification/guide
VITE_CERTIFICATION_STANDARDS_INFO=https://dev.ecocampus.local/certification/standards
VITE_CERTIFICATION_PROCESS_FLOW=https://dev.ecocampus.local/certification/process

VITE_GUIDANCE_APPLICATION_FORM=https://dev.ecocampus.local/guidance/apply
VITE_GUIDANCE_CONTACT_INFO=https://dev.ecocampus.local/guidance/contact

VITE_OFFICIAL_MAIN_WEBSITE=https://dev.ecocampus.local
VITE_OFFICIAL_NEWS_PAGE=https://dev.ecocampus.local/news
VITE_OFFICIAL_RESOURCES=https://dev.ecocampus.local/resources

# 社群媒體連結（開發環境使用測試連結）
VITE_SOCIAL_FACEBOOK=https://www.facebook.com/dev.taiwanecoschools
VITE_SOCIAL_LINE=https://line.me/R/ti/p/@dev.ecoschool

# 資源圖片連結（開發環境使用本地資源）
VITE_ASSET_MEDAL_BRONZE=http://localhost:8080/img/medal-bronze.png
VITE_ASSET_MEDAL_SILVER=http://localhost:8080/img/medal-silver.png
VITE_ASSET_MEDAL_GREEN=http://localhost:8080/img/medal-greenflag.png

# =====================================
# 前端開發工具配置
# =====================================
# 啟用詳細日誌
ENABLE_LOGGING=true

# 禁用快取（開發環境）
ENABLE_CACHING=false

# 啟用除錯模式
DEBUG_MODE=true

# =====================================
# 測試用 Token（開發環境）
# =====================================
# 注意：這些 Token 僅用於開發環境測試
DEV_SCHOOL_TOKEN=A0B0D18E-CFA0-4BEF-96EE-F157407E85A7
DEV_EPA_TOKEN=850D03CB-927A-45E6-AFDE-173C45F93E99
DEV_TUTOR_TOKEN=D1433FC9-85C6-4054-A97D-7D12B7AFD5BB 