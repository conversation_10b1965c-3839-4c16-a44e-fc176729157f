// 認證服務 - 業務邏輯層
import { getApiBaseUrl, getOfficialLoginUrl } from "@/config/environment";
import { formatDisplayNameFromProfile } from "@/utils/userDisplayUtils";
import { authAPI } from "@/api";
import { BackendUser, LoginResponse, User } from "@/api/authAPI";

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
}

export interface LoginResult {
  success: boolean;
  message: string;
}

class AuthService {
  private readonly TOKEN_KEY = "userToken";
  private readonly USER_KEY = "userData";

  // 允許的角色類型 (與後端保持一致)
  private readonly ALLOWED_ROLE_TYPES = ["School", "Government", "Tutor"];

  // 新增快取機制
  private userProfileCache: {
    data: User | null;
    timestamp: number;
    ttl: number; // 快取存活時間 (毫秒)
  } = {
    data: null,
    timestamp: 0,
    ttl: 5 * 60 * 1000, // 5分鐘
  };

  // 從 sessionStorage 獲取 Token
  getToken(): string | null {
    try {
      return sessionStorage.getItem(this.TOKEN_KEY);
    } catch (error) {
      console.error("Error getting token from sessionStorage:", error);
      return null;
    }
  }

  // 儲存 Token 到 sessionStorage
  setToken(token: string): void {
    try {
      sessionStorage.setItem(this.TOKEN_KEY, token);
    } catch (error) {
      console.error("Error saving token to sessionStorage:", error);
    }
  }

  // 從 sessionStorage 獲取用戶資料
  getUser(): User | null {
    try {
      const userData = sessionStorage.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Error getting user data from sessionStorage:", error);
      return null;
    }
  }

  // 儲存用戶資料到 sessionStorage
  setUser(user: User): void {
    try {
      sessionStorage.setItem(this.USER_KEY, JSON.stringify(user));
    } catch (error) {
      console.error("Error saving user data to sessionStorage:", error);
    }
  }

  // 🆕 更新用戶名稱（職稱、中英文）
  updateUserName(chineseName: string, englishName?: string, jobTitle?: string): void {
    const currentUser = this.getUser();
    if (currentUser) {
      // 格式化顯示名稱：
      // 有職稱時：職稱-中文姓名 (英文姓名)
      // 沒有職稱時：中文姓名 (英文姓名)
      let displayName = chineseName;

      // 加入職稱前綴
      if (jobTitle && jobTitle.trim()) {
        displayName = `${jobTitle}-${chineseName}`;
      }

      // 加入英文姓名後綴
      if (englishName && englishName.trim()) {
        displayName = `${displayName} (${englishName})`;
      }

      const updatedUser = {
        ...currentUser,
        name: displayName,
      };

      this.setUser(updatedUser);
      console.log("🔄 [AuthService] 用戶名稱已更新:", displayName);
    }
  }

  // 清除認證資料
  clearAuth(): void {
    try {
      sessionStorage.removeItem(this.TOKEN_KEY);
      sessionStorage.removeItem(this.USER_KEY);
    } catch (error) {
      console.error("Error clearing auth data:", error);
    }
  }

  // 檢查是否已認證
  isAuthenticated(): boolean {
    return this.getToken() !== null && this.getUser() !== null;
  }

  // 獲取認證狀態
  getAuthState(): AuthState {
    const token = this.getToken();
    const user = this.getUser();

    return {
      isAuthenticated: token !== null && user !== null,
      user,
      token,
    };
  }

  // 🔒 新增：檢查使用者角色是否被允許
  private checkUserRole(user: User): boolean {
    // 檢查新版 roleType 欄位
    if (user.roleType) {
      return this.ALLOWED_ROLE_TYPES.includes(user.roleType);
    }

    // 回退檢查舊版 role 欄位，進行映射
    const mappedRole = this.mapOldRoleToNew(user.role);
    return this.ALLOWED_ROLE_TYPES.includes(mappedRole);
  }

  // 🔒 新增：將舊的角色映射到新的角色類型
  private mapOldRoleToNew(oldRole: string): string {
    switch (oldRole.toLowerCase()) {
      case "school":
        return "School";
      case "epa":
      case "government":
        return "Government";
      case "tutor":
        return "Tutor";
      default:
        return "Unknown"; // 不在允許列表中
    }
  }

  // 🔒 新增：檢查角色權限並處理不當角色
  async checkRolePermission(): Promise<{ valid: boolean; message?: string }> {
    const user = this.getUser();
    const token = this.getToken();

    if (!user || !token) {
      return { valid: false, message: "No user or token found" };
    }

    // 檢查本地存儲的使用者角色
    if (!this.checkUserRole(user)) {
      console.log(`角色檢查失敗：使用者角色 '${user.roleType || user.role}' 不被允許`);

      // 🚪 立即執行完整的登出操作
      await this.logout();

      return {
        valid: false,
        message: `您的帳號角色 '${user.roleType || user.role}' 不被允許存取此系統`,
      };
    }

    return { valid: true };
  }

  // 🔧 新增：轉換後端使用者資料為前端格式
  private convertBackendUserToFrontend(backendUser: BackendUser): User {
    // 將後端的 roleType 轉換為前端的 role
    const convertRoleType = (roleType: string): string => {
      switch (roleType) {
        case "School":
          return "school";
        case "Government":
          return "epa";
        case "Tutor":
          return "tutor";
        default:
          return roleType.toLowerCase();
      }
    };

    // 修正：當 nickName 是 "Unknown User" 時，標記為需要獲取實際名稱
    const displayName = backendUser.nickName && backendUser.nickName !== "Unknown User" ? backendUser.nickName : backendUser.name || "Unknown User";

    return {
      id: backendUser.id,
      name: displayName, // 使用修正的顯示名稱
      email: backendUser.email || "",
      role: convertRoleType(backendUser.roleType || backendUser.role || ""), // 轉換角色
      permissions: backendUser.permissions || [],
      account: backendUser.account,
      phone: backendUser.phone,
      organization: backendUser.school?.name || backendUser.organization,
      position: backendUser.position,
      address: backendUser.school?.address || backendUser.address,
      status: backendUser.isActive ? "active" : "inactive",
      register_review: backendUser.register_review,
      roleType: backendUser.roleType, // 保留原始 roleType 以供檢查使用
    };
  }

  // 🆕 新增：獲取真實使用者名稱（當 Token 驗證返回 "Unknown User" 時）
  private async fetchRealUserName(user: User): Promise<User> {
    if (user.name !== "Unknown User") {
      return user; // 如果已經有真實名稱，直接返回
    }

    try {
      const token = this.getToken();
      if (!token) {
        return user;
      }

      // 使用 userAPI 獲取用戶資料
      const { userAPI } = await import("@/api");
      const profileData = await userAPI.getCurrentUserProfile();

      if (profileData?.success && profileData.data) {
        const displayName = formatDisplayNameFromProfile(profileData.data);

        // 更新使用者資料，包含更多資訊
        const updatedUser = {
          ...user,
          name: displayName,
          // 🔧 修復：根據角色正確映射學校/機構名稱
          organization: this.getOrganizationName(profileData.data, user.role),
          position: profileData.data.place_cname || user.position,
        };

        // 更新本地存儲
        this.setUser(updatedUser);

        return updatedUser;
      }
    } catch (error) {
      console.error("獲取真實使用者名稱失敗:", error);
    }

    return user; // 如果失敗，返回原始用戶資料
  }

  // 修正：根據角色和API資料獲取正確的機構名稱
  private getOrganizationName(profileData: { place_cname?: string; school_name?: string } | null, userRole: string): string {
    if (!profileData) {
      return "未知機構";
    }

    switch (userRole) {
      case "school":
        // 學校身份：優先使用place_cname（學校名稱），其次school_name
        return profileData.place_cname || profileData.school_name || "未知學校";

      case "epa":
        // 環保署身份：使用place_cname或預設為環保署
        return profileData.place_cname || "環保署";

      case "tutor":
        // 輔導員身份：使用place_cname或預設為輔導機構
        return profileData.place_cname || "輔導機構";

      default:
        return profileData.place_cname || profileData.school_name || "未知機構";
    }
  }

  // 驗證 Token (更新版本，包含角色檢查)
  async validateToken(userToken: string): Promise<LoginResponse> {
    try {
      const response = await authAPI.validateToken(userToken);

      // 🔒 檢查是否為角色權限被拒絕
      if (!response.success && response.data?.details?.action === "account_logout") {
        console.log("角色權限被拒絕，清除本地認證資料");
        this.clearAuth();

        // 觸發登出回調
        this.onRolePermissionDenied?.(response.data.details.userRole || "Unknown", response.data.details.allowedRoles || []);

        return response.data || { valid: false };
      }

      if (response.success && response.data?.valid && response.data?.user) {
        // 🔧 轉換後端使用者資料為前端格式
        const convertedUser = this.convertBackendUserToFrontend(response.data.user);
        console.log("原始後端資料:", response.data.user);
        console.log("轉換後前端資料:", convertedUser);

        // 🔒 本地角色檢查（雙重保護）
        if (!this.checkUserRole(convertedUser)) {
          console.log("本地角色檢查失敗，拒絕登入");
          return {
            valid: false,
            details: {
              userRole: convertedUser.roleType || convertedUser.role,
              allowedRoles: this.ALLOWED_ROLE_TYPES,
              action: "account_logout",
            },
          };
        }

        // 儲存 Token 和轉換後的用戶資料到 session
        this.setToken(userToken);
        this.setUser(convertedUser);

        // 🆕 如果名稱是 "Unknown User"，嘗試獲取真實名稱
        const finalUser = await this.fetchRealUserName(convertedUser);

        // 更新返回資料為轉換後的格式
        return {
          ...response.data,
          user: finalUser,
        };
      }

      return response.data || { valid: false };
    } catch (error) {
      console.error("Token validation failed:", error);
      return {
        valid: false,
      };
    }
  }

  // 處理來自其他網站的登入 POST 請求 (更新版本，使用專用的 token-login 端點)
  async handleExternalLogin(userToken: string): Promise<LoginResult> {
    try {
      // 清除現有的認證資料
      this.clearAuth();

      console.log("嘗試使用 Token 登入:", userToken);

      // 使用專用的 token-login 端點
      const response = await authAPI.tokenLogin(userToken);

      // 檢查是否為角色權限被拒絕
      if (!response.success && response.data?.details?.action === "account_logout") {
        console.log("Token 登入失敗：角色權限被拒絕");
        this.onRolePermissionDenied?.(response.data.details.userRole || "Unknown", response.data.details.allowedRoles || []);
        return { success: false, message: "Token 登入失敗" };
      }

      if (response.success && response.data?.valid && response.data?.user) {
        // 轉換後端使用者資料為前端格式
        const convertedUser = this.convertBackendUserToFrontend(response.data.user);
        console.log("Token 登入成功，原始後端資料:", response.data.user);
        console.log("Token 登入成功，轉換後前端資料:", convertedUser);

        // 本地角色檢查（雙重保護）
        if (!this.checkUserRole(convertedUser)) {
          console.log("本地角色檢查失敗，拒絕登入");
          return {
            success: false,
            message: `您的帳號角色 '${convertedUser.roleType || convertedUser.role}' 不被允許存取此系統`,
          };
        }

        // 儲存 Token 和轉換後的用戶資料到 session
        this.setToken(userToken);
        this.setUser(convertedUser);

        // 🆕 如果名稱是 "Unknown User"，嘗試獲取真實名稱
        const finalUser = await this.fetchRealUserName(convertedUser);

        // 觸發登入成功回調
        this.onLoginSuccess?.(finalUser);

        // 更新返回資料為轉換後的格式
        return {
          success: true,
          message: "Token 登入成功",
        };
      } else {
        console.log("Token 登入失敗:", response.message);
        return {
          success: false,
          message: response.message || "Token 登入失敗",
        };
      }
    } catch (error) {
      console.error("Token 登入請求失敗:", error);
      return {
        success: false,
        message: "Token 登入請求失敗",
      };
    }
  }

  // 🔐 新增：密碼登入方法
  async loginWithPassword(account: string, password: string): Promise<LoginResult> {
    try {
      // 清除現有的認證資料
      this.clearAuth();

      console.log("嘗試使用帳號密碼登入:", account);

      // 使用密碼登入端點
      const response = await authAPI.passwordLogin(account, password);
      console.log("response", response);
      // 檢查是否為角色權限被拒絕
      if (!response.success && response.data?.details?.action === "account_logout") {
        console.log("密碼登入失敗：角色權限被拒絕");
        this.onRolePermissionDenied?.(response.data.details.userRole || "Unknown", response.data.details.allowedRoles || []);
        return { success: false, message: "密碼登入失敗" };
      }

      if (response.success && response.data?.valid && response.data?.user && response.data?.token) {
        // 轉換後端使用者資料為前端格式
        const convertedUser = this.convertBackendUserToFrontend(response.data.user);

        // 本地角色檢查（雙重保護）
        if (!this.checkUserRole(convertedUser)) {
          console.log("本地角色檢查失敗，拒絕登入");
          return {
            success: false,
            message: `您的帳號角色 '${convertedUser.roleType || convertedUser.role}' 不被允許存取此系統`,
          };
        }

        // 儲存 Token 和轉換後的用戶資料到 session
        this.setToken(response.data.token);
        this.setUser(convertedUser);

        // 🆕 如果名稱是 "Unknown User"，嘗試獲取真實名稱
        const finalUser = await this.fetchRealUserName(convertedUser);

        // 觸發登入成功回調
        this.onLoginSuccess?.(finalUser);

        // 更新返回資料為轉換後的格式
        return {
          success: true,
          message: "密碼登入成功",
        };
      } else {
        console.log("密碼登入失敗:", response.message);
        return {
          success: false,
          message: response.message || "密碼登入失敗",
        };
      }
    } catch (error) {
      console.error("密碼登入請求失敗:", error);
      return {
        success: false,
        message: "密碼登入請求失敗",
      };
    }
  }

  // 🔐 新增：修改密碼方法
  async changePassword(
    oldPassword: string,
    newPassword: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      const token = this.getToken();

      if (!token) {
        return {
          success: false,
          message: "請先登入系統",
        };
      }

      console.log("嘗試修改密碼");

      const response = await authAPI.changePassword(oldPassword, newPassword);

      if (response.success) {
        console.log("密碼修改成功");
        return {
          success: true,
          message: "密碼修改成功",
        };
      } else {
        console.log("密碼修改失敗:", response.message);
        return {
          success: false,
          message: "密碼修改失敗",
        };
      }
    } catch (error) {
      console.error("修改密碼請求失敗:", error);
      return {
        success: false,
        message: "修改密碼請求失敗",
      };
    }
  }

  // 登出
  async logout(): Promise<void> {
    try {
      const token = this.getToken();

      // 呼叫後端登出 API（如果有 Token）
      if (token) {
        await authAPI.logout().catch((error) => {
          console.error("Logout API call failed:", error);
        });
      }

      // 清除本地認證資料
      this.clearAuth();

      // 清除使用者資料快取
      this.clearUserProfileCache();

      // 觸發登出回調
      this.onLogout?.();

      // 🌐 重定向到官方網站登入頁
      this.redirectToOfficialLogin();
    } catch (error) {
      console.error("Logout failed:", error);
    }
  }

  // 🌐 新增：重定向到官方網站登入頁
  private redirectToOfficialLogin(): void {
    try {
      const officialLoginUrl = getOfficialLoginUrl();

      console.log(`🌐 [AuthService] 重定向到官方網站登入頁: ${officialLoginUrl}`);

      // 延遲一小段時間，確保登出操作完成
      setTimeout(() => {
        window.location.href = officialLoginUrl;
      }, 500);
    } catch (error) {
      console.error("重定向到官方網站失敗:", error);
      // 如果發生錯誤，回退到首頁
      setTimeout(() => {
        window.location.href = "/";
      }, 500);
    }
  }

  // 檢查 Token 狀態 (更新版本，包含角色檢查)
  async checkTokenStatus(): Promise<boolean> {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    try {
      // 🔒 首先進行本地角色檢查
      const roleCheck = await this.checkRolePermission();
      if (!roleCheck.valid) {
        console.log("本地角色檢查失敗:", roleCheck.message);
        return false;
      }

      // 🔒 然後進行後端 Token 檢查（包含角色驗證）
      const response = await authAPI.checkTokenStatus();

      // 檢查 Token 狀態
      return response.success && response.data?.valid;
    } catch (error) {
      console.error("Token status check failed:", error);
      return false;
    }
  }

  // 🔒 新增：定期檢查角色權限
  async performPeriodicRoleCheck(): Promise<boolean> {
    try {
      const roleCheck = await this.checkRolePermission();
      if (!roleCheck.valid) {
        console.log("定期角色檢查失敗:", roleCheck.message);
        this.onRolePermissionDenied?.(this.getUser()?.roleType || this.getUser()?.role || "Unknown", this.ALLOWED_ROLE_TYPES);
        return false;
      }

      // 進行後端檢查
      const tokenValid = await this.checkTokenStatus();
      if (!tokenValid) {
        console.log("定期 Token 檢查失敗");
        return false;
      }

      return true;
    } catch (error) {
      console.error("定期角色檢查錯誤:", error);
      return false;
    }
  }

  // 生成 API 請求 headers（包含 Token）
  getAuthHeaders(): Record<string, string> {
    const token = this.getToken();
    const headers: Record<string, string> = {};

    if (token) {
      headers["x-user-token"] = token;
    }

    return headers;
  }

  // 🆕 統一的 API 請求函數（自動處理 token 認證）
  async makeAuthenticatedRequest(url: string, options: RequestInit = {}): Promise<Response> {
    const token = this.getToken();

    // 合併 headers，確保包含 token
    const headers = {
      ...options.headers,
      "x-user-token": token || "",
    };

    // 如果沒有設定 Content-Type 且不是 FormData，則設為 JSON
    if (!headers["Content-Type"] && !(options.body instanceof FormData)) {
      headers["Content-Type"] = "application/json";
    }

    return fetch(url, {
      ...options,
      headers,
    });
  }

  // 回調函數（可由外部設置）
  onLoginSuccess?: (user: User) => void;
  onLogout?: () => void;
  onTokenExpired?: () => void;
  onRolePermissionDenied?: (userRole: string, allowedRoles: string[]) => void; // 🔒 新增：角色權限被拒絕回調

  // 設置回調函數 (更新版本)
  setCallbacks(callbacks: {
    onLoginSuccess?: (user: User) => void;
    onLogout?: () => void;
    onTokenExpired?: () => void;
    onRolePermissionDenied?: (userRole: string, allowedRoles: string[]) => void; // 🔒 新增
  }): void {
    this.onLoginSuccess = callbacks.onLoginSuccess;
    this.onLogout = callbacks.onLogout;
    this.onTokenExpired = callbacks.onTokenExpired;
    this.onRolePermissionDenied = callbacks.onRolePermissionDenied; // 🔒 新增
  }

  // 🔒 新增：獲取允許的角色列表
  getAllowedRoles(): string[] {
    return [...this.ALLOWED_ROLE_TYPES];
  }

  // 🔒 新增：檢查當前使用者是否有指定角色
  hasRole(roleType: string): boolean {
    const user = this.getUser();
    if (!user) return false;

    const userRole = user.roleType || this.mapOldRoleToNew(user.role);
    return userRole === roleType;
  }

  // 🔄 新增：刷新使用者資料 (用於基本資料維護後立即更新顯示)
  async refreshUserProfile(forceRefresh: boolean = false): Promise<boolean> {
    try {
      const currentUser = this.getUser();
      if (!currentUser) {
        console.log("無當前使用者，無法刷新資料");
        return false;
      }

      // 檢查快取
      const now = Date.now();
      if (!forceRefresh && this.userProfileCache.data && now - this.userProfileCache.timestamp < this.userProfileCache.ttl) {
        console.log("🔄 使用快取的使用者資料");
        return true;
      }

      console.log("🔄 刷新使用者資料...");
      const updatedUser = await this.fetchRealUserName(currentUser);

      if (updatedUser && updatedUser !== currentUser) {
        // 更新快取
        this.userProfileCache = {
          data: updatedUser,
          timestamp: now,
          ttl: this.userProfileCache.ttl,
        };

        console.log("✅ 使用者資料已更新:", updatedUser.name);
        return true;
      }

      return false;
    } catch (error) {
      console.error("❌ 刷新使用者資料失敗:", error);

      // 如果有快取資料，使用快取
      if (this.userProfileCache.data) {
        console.log("⚠️ 使用快取資料作為備用");
        return true;
      }

      return false;
    }
  }

  // 新增清除快取方法
  clearUserProfileCache(): void {
    this.userProfileCache = {
      data: null,
      timestamp: 0,
      ttl: this.userProfileCache.ttl,
    };
    console.log("🗑️ 使用者資料快取已清除");
  }
}

// 創建全域認證服務實例
export const authService = new AuthService();

// 導出用於 React Hook 的函數
export const useAuthService = () => authService;

export default authService;
