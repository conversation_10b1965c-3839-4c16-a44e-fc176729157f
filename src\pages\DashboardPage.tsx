import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { dashboardAPI, CityStatistics, LatestCertification, SchoolArticle, SchoolCertificationStatus, SchoolPassedCertification } from "@/api/dashboardAPI";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { buildAssetUrl } from "@/utils/pathUtils";

// 移除靜態資料，改用 API

// 圖片判斷邏輯
// 條件是「認證名稱已列出，但若沒有 date，就代表尚未真正通過 → 用預設圖案」。
const getCertImage = (name: string, date?: string) => {
  if (!date) return buildAssetUrl("img/medal-default.svg");
  if (name.includes("綠旗")) return buildAssetUrl("img/medal-luxury-green.png");
  if (name.includes("銀牌")) return buildAssetUrl("img/medal-luxury-silver.png");
  if (name.includes("銅牌")) return buildAssetUrl("img/medal-luxury-bronze.png");
  return buildAssetUrl("img/medal-default.svg");
};

// 學校身份的 Dashboard 組件
const SchoolDashboard = () => {
  const [currentCertification, setCurrentCertification] = useState<SchoolCertificationStatus | null>(null);
  const [articles, setArticles] = useState<SchoolArticle[]>([]);
  const [passedCertifications, setPassedCertifications] = useState<SchoolPassedCertification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSchoolData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 並行獲取學校的三種資料
        const [currentCertResponse, latestArticlesResponse, passedCertsResponse] = await Promise.all([
          dashboardAPI.getSchoolCurrentCertification(),
          dashboardAPI.getSchoolLatestArticles(6),
          dashboardAPI.getSchoolPassedCertifications(),
        ]);

        // 提取 data 屬性
        const currentCert = currentCertResponse.success ? currentCertResponse.data : null;
        const latestArticles = latestArticlesResponse.success ? latestArticlesResponse.data : [];
        const passedCerts = passedCertsResponse.success ? passedCertsResponse.data : [];

        setCurrentCertification(currentCert);
        setArticles(latestArticles);
        setPassedCertifications(passedCerts);
      } catch (err) {
        console.error("獲取學校 Dashboard 資料失敗:", err);
        setError(err instanceof Error ? err.message : "獲取資料失敗");
      } finally {
        setLoading(false);
      }
    };

    fetchSchoolData();
  }, []);

  if (loading) {
    return (
      <div className="flex flex-col gap-8">
        <div className="flex flex-col md:flex-row gap-8 md:items-stretch">
          <div className="bg-white basis-2/5 md:basis-2/5 grow md:grow-0 rounded-xl shadow p-6 min-w-[280px]">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="flex flex-col items-center gap-4 mt-6">
              <Skeleton className="w-24 h-24 rounded-full" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
          <div className="bg-white basis-3/5 md:basis-3/5 grow md:grow-0 rounded-xl shadow p-6 min-w-[280px]">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="space-y-2">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="flex justify-between">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-24" />
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow p-8 min-w-[280px]">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="flex flex-wrap gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="flex flex-col items-center">
                <Skeleton className="w-11 h-11 rounded-full mb-1" />
                <Skeleton className="h-4 w-12 mb-1" />
                <Skeleton className="h-3 w-16" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      {/* 上排兩區塊：申請中的認證 + 最新投稿 */}
      <div className="flex flex-col md:flex-row gap-8 md:items-stretch">
        {/* ✅ 申請中的認證區塊：佔 2/5 寬 */}
        <section aria-label="申請中的認證" className="bg-white basis-2/5 md:basis-2/5 grow md:grow-0 rounded-xl shadow p-6 min-w-[280px]">
          <div className="flex justify-between items-center mb-4">
            <span className="font-semibold font-size-lg text-gray-700">申請中的認證</span>
            <Link to="/certificate" tabIndex={0}>
              <Button variant="link" className="text-green-700 font-size-sm p-0">
                查看紀錄
              </Button>
            </Link>
          </div>
          <div className="flex flex-col items-center gap-4 mt-6">
            {currentCertification ? (
              <>
                <img
                  src={getCertImage(currentCertification.levelName)}
                  alt={`申請中的${currentCertification.levelName}標章`}
                  className="w-24 h-24 object-contain"
                />
                <span className="font-size-base text-gray-700 font-semibold">{currentCertification.levelName}</span>
                <span aria-label="申請進度" className="inline-block mt-3 px-4 py-2 text-gray-600 bg-gray-50 rounded">
                  {currentCertification.status}
                </span>
              </>
            ) : (
              <div className="text-center text-gray-500">
                <img src={buildAssetUrl("img/medal-default.svg")} alt="無申請中認證" className="w-24 h-24 object-contain mx-auto mb-2" />
                <span className="font-size-base">目前無申請中的認證</span>
              </div>
            )}
          </div>
        </section>

        {/* ✅ 最新投稿區塊：佔 3/5 寬 */}
        <section aria-label="最新投稿" className="bg-white basis-3/5 md:basis-3/5 grow md:grow-0 rounded-xl shadow p-6 min-w-[280px]">
          <div className="flex justify-between items-center mb-4">
            <span className="font-semibold font-size-lg text-gray-700">最新投稿</span>
            <Link to="/news" tabIndex={0}>
              <Button variant="link" className="text-green-700 font-size-sm p-0">
                所有投稿
              </Button>
            </Link>
          </div>
          <div className="overflow-auto" tabIndex={0}>
            <table className="w-full border-collapse text-left">
              <thead>
                <tr className="font-size-base tracking-[.25em]">
                  <th className="py-1 pr-4 font-semibold w-1/2">標題</th>
                  <th className="py-1 font-semibold w-1/4">狀態</th>
                  <th className="py-1 pr-4 font-semibold w-1/4">日期</th>
                </tr>
              </thead>
              <tbody>
                {articles.length > 0 ? (
                  articles.map((article, i) => (
                    <tr key={article.articleId} className="border-t border-gray-200 text-gray-700 font-size-base">
                      <td className="py-1 pr-4 whitespace-nowrap w-1/2" title={article.title}>
                        {article.title.length > 20 ? `${article.title.substring(0, 20)}...` : article.title}
                      </td>
                      <td className="py-1 w-1/4">{article.status}</td>
                      <td className="py-1 pr-4 whitespace-nowrap w-1/4">{article.publishDate || article.createDate}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} className="py-4 text-center text-gray-500">
                      暫無投稿文章
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </section>
      </div>

      {/* ✅ 已通過的認證卡片 */}
      <section aria-label="已通過的認證" className="bg-white rounded-xl shadow p-8 min-w-[280px]">
        <div className="flex justify-between items-center mb-4">
          <span className="font-semibold font-size-lg text-gray-700">已通過的認證</span>
        </div>
        <div className="flex flex-wrap items-center gap-6 px-5">
          {passedCertifications.length > 0 ? (
            passedCertifications.map((cert) => (
              <div key={cert.certificationId} className="flex flex-col items-center w-45 text-gray-400" aria-label={cert.levelName} tabIndex={0}>
                <img src={getCertImage(cert.levelName, cert.passDate)} alt={`${cert.levelName}標章`} className="w-11 h-11 object-contain mb-1" />
                <span className="font-size-base">{cert.levelName}</span>
                <span className="font-size-sm pt-2">{cert.passDate}</span>
              </div>
            ))
          ) : (
            <div className="w-full text-center text-gray-500 py-4">暫無已通過的認證</div>
          )}
        </div>
      </section>
    </div>
  );
};

// 縣市政府/輔導人員的 Dashboard 組件
const GovernmentDashboard = () => {
  const [cityStatistics, setCityStatistics] = useState<CityStatistics | null>(null);
  const [certifications, setCertifications] = useState<LatestCertification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 並行獲取統計資料和最新認證
        const [statsResponse, certsResponse] = await Promise.all([
          dashboardAPI.getMyCityStatistics(), 
          dashboardAPI.getMyLatestCertifications(6)
        ]);

        // 提取 data 屬性
        const statsData = statsResponse.success ? statsResponse.data : null;
        const certsData = certsResponse.success ? certsResponse.data : [];

        setCityStatistics(statsData);
        setCertifications(certsData);
      } catch (err) {
        console.error("獲取 Dashboard 資料失敗:", err);
        setError(err instanceof Error ? err.message : "獲取資料失敗");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const getMedalImage = (level: string) => {
    switch (level) {
      case "銅牌":
        return buildAssetUrl("img/medal-bronze.png");
      case "銀牌":
        return buildAssetUrl("img/medal-silver.png");
      case "綠旗":
        return buildAssetUrl("img/medal-greenflag.png");
      default:
        return buildAssetUrl("img/medal-default.svg");
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col gap-8">
        <div className="flex flex-col md:flex-row gap-8">
          <div className="bg-white rounded-xl shadow p-6 flex-1">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="flex justify-center gap-8">
              <div className="text-center">
                <Skeleton className="w-16 h-16 rounded-full mx-auto mb-2" />
                <Skeleton className="h-4 w-12 mx-auto mb-1" />
                <Skeleton className="h-6 w-8 mx-auto" />
              </div>
              <div className="text-center">
                <Skeleton className="w-16 h-16 rounded-full mx-auto mb-2" />
                <Skeleton className="h-4 w-12 mx-auto mb-1" />
                <Skeleton className="h-6 w-8 mx-auto" />
              </div>
              <div className="text-center">
                <Skeleton className="w-16 h-16 rounded-full mx-auto mb-2" />
                <Skeleton className="h-4 w-12 mx-auto mb-1" />
                <Skeleton className="h-6 w-8 mx-auto" />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow p-6">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="space-y-2">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="flex justify-between">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      {/* 基隆市的生態學校數量 */}
      <section aria-label="基隆市的生態學校數量" className="bg-white rounded-xl shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <span className="font-semibold font-size-lg text-gray-700">{cityStatistics?.cityName || "縣市"}的生態學校數量</span>
        </div>

        <div className="flex justify-center gap-12">
          {/* 銅牌 */}
          <div className="text-center">
            <img src={getMedalImage("銅牌")} alt="銅牌" className="w-16 h-16 object-contain mx-auto mb-2" />
            <p className="font-size-sm text-gray-600 mb-1">銅牌</p>
            <p className="font-size-2xl font-bold text-gray-800">{cityStatistics?.bronzeCount || 0}</p>
          </div>

          {/* 銀牌 */}
          <div className="text-center">
            <img src={getMedalImage("銀牌")} alt="銀牌" className="w-16 h-16 object-contain mx-auto mb-2" />
            <p className="font-size-sm text-gray-600 mb-1">銀牌</p>
            <p className="font-size-2xl font-bold text-gray-800">{cityStatistics?.silverCount || 0}</p>
          </div>

          {/* 綠旗 */}
          <div className="text-center">
            <img src={getMedalImage("綠旗")} alt="綠旗" className="w-16 h-16 object-contain mx-auto mb-2" />
            <p className="font-size-sm text-gray-600 mb-1">綠旗</p>
            <p className="font-size-2xl font-bold text-gray-800">{cityStatistics?.greenFlagCount || 0}</p>
          </div>
        </div>
      </section>

      {/* 最新生態學校認證 */}
      <section aria-label="最新生態學校認證" className="bg-white rounded-xl shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <span className="font-semibold font-size-lg text-gray-700">最新生態學校認證</span>
          <Link to="/school-management" tabIndex={0}>
            <Button variant="link" className="text-green-700 font-size-sm p-0">
              查看全部
            </Button>
          </Link>
        </div>

        <div className="overflow-auto" tabIndex={0}>
          <table className="w-full border-collapse text-left">
            <thead>
              <tr className="font-size-base tracking-[.25em]">
                <th className="py-2 pr-4 font-semibold text-left">學校名稱</th>
                <th className="py-2 pr-4 font-semibold text-center">認證等級</th>
                <th className="py-2 font-semibold text-center">通過日期</th>
              </tr>
            </thead>
            <tbody>
              {certifications.length > 0 ? (
                certifications.map((cert, i) => (
                  <tr key={i} className="border-t border-gray-200 text-gray-700 font-size-base">
                    <td className="py-2 pr-4">{cert.schoolName}</td>
                    <td className="py-2 pr-4 text-center">{cert.certificationLevel}</td>
                    <td className="py-2 text-center">{cert.passDate}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="py-4 text-center text-gray-500">
                    暫無認證資料
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </section>
    </div>
  );
};

const DashboardPage = () => {
  const { authState } = useAuth();
  const userRole = authState.user?.role;

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
      <section className="w-full max-w-[90rem] rounded-lg px-8 py-10">
        <h1 className="font-size-4xl font-bold text-primary mb-8 px-2" tabIndex={0}>
          狀態總覽
        </h1>

        {/* 根據用戶角色顯示不同的 Dashboard */}
        {userRole === "school" ? (
          <SchoolDashboard />
        ) : userRole === "epa" || userRole === "tutor" ? (
          <GovernmentDashboard />
        ) : (
          <Alert>
            <AlertDescription>無法識別您的身份角色，請聯繫系統管理員。</AlertDescription>
          </Alert>
        )}
      </section>
    </main>
  );
};

export default DashboardPage;
