import { Request, Response, NextFunction } from "express";
import { UserProfile } from "../models/user.js";
import { UserService } from "../services/user-service.js";
import { validateToken, convertMemberProfileToRequestUser as convertProfile } from "../utils/auth-helpers.js";

// 允許的角色類型
const ALLOWED_ROLE_TYPES = ["School", "Government", "Tutor"];

// 擴展 Request 類型以包含用戶信息 (更新為新的資料結構)
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user?: {
        id: string;
        account: string;
        accountId: string; // 🆕 統一：認證列表 API 需要的帳號ID
        accountSid?: string;
        nickName?: string;
        email?: string;
        roleType: string;
        permissions: string[];
        permissionGroups: string[];
        isActive: boolean;
        school?: {
          id: string;
          name: string;
          englishName?: string;
          code?: string;
          address?: string;
          phone?: string;
          email?: string;
          website?: string;
        };
      };
      userToken?: string;
    }
  }
}

// Token 驗證中間件 (更新為使用新資料庫，並加入角色檢查)
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let token = req.headers["x-user-token"] as string;

    // 如果沒有 x-user-token，檢查 Authorization 標頭
    if (!token) {
      const authHeader = req.headers["authorization"] as string;
      if (authHeader && authHeader.startsWith("Bearer ")) {
        token = authHeader.substring(7); // 移除 "Bearer " 前綴
      }
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "Access denied. No token provided.",
      });
    }

    // 🔧 使用統一的 Token 驗證邏輯
    const validationResult = await validateToken(token);

    if (!validationResult.valid || !validationResult.user) {
      return res.status(401).json({
        success: false,
        message: validationResult.message || "無效的用戶認證",
      });
    }

    // 將用戶信息和 token 附加到請求對象 (使用改進的轉換函數)
    req.user = convertProfile(validationResult.user);
    req.userToken = token;

    next();
  } catch (error) {
    console.error("Token authentication error:", error);
    res.status(500).json({
      success: false,
      message: "Token authentication failed.",
    });
  }
};

// 可選的 Token 驗證中間件（不強制要求 Token，但如果有 Token 則要檢查角色）
export const optionalAuthenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    let token = req.headers["x-user-token"] as string;

    // 如果沒有 x-user-token，檢查 Authorization 標頭
    if (!token) {
      const authHeader = req.headers["authorization"] as string;
      if (authHeader && authHeader.startsWith("Bearer ")) {
        token = authHeader.substring(7); // 移除 "Bearer " 前綴
      }
    }

    if (token) {
      const validationResult = await validateToken(token);
      if (validationResult.valid && validationResult.user) {
        req.user = convertProfile(validationResult.user);
        req.userToken = token;
      }
    }

    next();
  } catch (error) {
    console.error("Optional token authentication error:", error);
    next(); // 繼續執行，不阻止請求
  }
};

// Token 驗證函數 (統一的驗證邏輯，加入角色檢查)
async function validateTokenWithNewDatabase(token: string): Promise<{
  valid: boolean;
  user?: UserProfile;
  message?: string;
}> {
  try {
    console.log(`驗證 Token: ${token}`);

    // 1. 首先嘗試從新的資料庫結構中驗證
    try {
      const user = await UserService.getUserByToken(token);
      if (user) {
        // 🔒 新增：檢查角色是否被允許
        if (!ALLOWED_ROLE_TYPES.includes(user.roleType)) {
          console.log(`資料庫使用者角色不被允許: ${user.roleType}`);
          return {
            valid: false,
            message: `Role ${user.roleType} is not authorized to access this system`,
          };
        }

        // 🔒 新增：檢查帳號是否啟用
        if (!user.isActive) {
          console.log(`資料庫使用者帳號已停用: ${user.account}`);
          return {
            valid: false,
            message: "Account has been deactivated",
          };
        }

        return {
          valid: true,
          user: user,
          message: "Token validated from database",
        };
      }
    } catch (dbError) {
      console.log("資料庫查詢失敗，回退到測試 Token:", dbError);
    }

    // 3. 保留舊的固定 Token 用於向後兼容
    const LEGACY_TOKENS = {
      eco_campus_user_778_token: {
        id: "778",
        name: "使用者 778",
        email: "<EMAIL>",
        role: "school",
        permissions: ["read", "write", "certification_apply", "certification_manage"],
      },
      valid_test_token_123456789: {
        id: "778",
        name: "測試使用者 778",
        email: "<EMAIL>",
        role: "school",
        permissions: ["read", "write", "certification_apply"],
      },
    };

    const legacyUser = LEGACY_TOKENS[token as keyof typeof LEGACY_TOKENS];
    if (legacyUser) {
      console.log("使用舊版 Token:", legacyUser.name, "角色:", legacyUser.role);

      // 🔒 新增：檢查舊版 Token 的角色
      const mappedRole = mapOldRoleToNew(legacyUser.role);
      if (!ALLOWED_ROLE_TYPES.includes(mappedRole)) {
        console.log(`舊版 Token 角色不被允許: ${legacyUser.role} -> ${mappedRole}`);
        return {
          valid: false,
          message: `Legacy token role ${legacyUser.role} is not authorized`,
        };
      }

      const convertedLegacyUser: UserProfile = {
        id: legacyUser.id,
        account: legacyUser.email,
        nickName: legacyUser.name,
        email: legacyUser.email,
        phone: undefined,
        avatar: undefined,
        roleType: mappedRole,
        isActive: true,
        createdTime: new Date(),
        updatedTime: new Date(),
        remark: "Legacy test user",
        permissions: legacyUser.permissions,
        permissionGroups: [legacyUser.role],
        school: undefined,
        certifications: [],
      };

      return {
        valid: true,
        user: convertedLegacyUser,
      };
    }

    // 4. 檢查是否為舊格式的有效 Token（以 "valid_" 開頭且長度大於 10）
    if (token.startsWith("valid_") && token.length > 10) {
      console.log("使用通用舊版 Token 格式");

      // 🔒 新增：通用 Token 預設為學校角色，這是被允許的
      const genericUser: UserProfile = {
        id: "778",
        account: "<EMAIL>",
        nickName: "測試使用者",
        email: "<EMAIL>",
        phone: undefined,
        avatar: undefined,
        roleType: "School", // 預設為學校角色
        isActive: true,
        createdTime: new Date(),
        updatedTime: new Date(),
        remark: "Generic test user",
        permissions: ["read", "write"],
        permissionGroups: ["school"],
        school: undefined,
        certifications: [],
      };

      return {
        valid: true,
        user: genericUser,
      };
    }

    console.log(`無效的 Token: ${token}`);
    return {
      valid: false,
      message: "Token not found in any validation source",
    };
  } catch (error) {
    console.error("Token validation failed:", error);
    return {
      valid: false,
      message: "Token validation error: " + (error as Error).message,
    };
  }
}

// 輔助函數：將舊的角色映射到新的角色類型
function mapOldRoleToNew(oldRole: string): string {
  switch (oldRole.toLowerCase()) {
    case "school":
      return "School";
    case "epa":
    case "government":
      return "Government";
    case "tutor":
      return "Tutor";
    default:
      return "School"; // 預設為學校角色
  }
}

// 權限檢查中間件 (更新為使用新的權限系統)
export const requirePermission = (permission: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required.",
      });
    }

    try {
      // 1. 首先檢查記憶體中的權限資料 (快速檢查)
      const hasMemoryPermission =
        req.user.permissions.includes(permission) || req.user.permissionGroups.some((group) => group.toLowerCase().includes(permission.toLowerCase()));

      if (hasMemoryPermission) {
        return next();
      }

      // 2. 如果記憶體檢查沒通過，進行資料庫權限檢查 (精確檢查)
      const hasPermission = await UserService.checkUserPermission(req.user.id, permission);
      if (hasPermission) {
        return next();
      }

      // 3. 權限檢查失敗
      return res.status(403).json({
        success: false,
        message: `Permission denied. Required permission: ${permission}`,
        userPermissions: req.user.permissions,
        userPermissionGroups: req.user.permissionGroups,
        checkedPermission: permission,
      });
    } catch (error) {
      console.error("Permission check error:", error);
      return res.status(500).json({
        success: false,
        message: "Permission check failed.",
      });
    }
  };
};

// 角色檢查中間件 (增強版)
export const requireRole = (roleType: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required.",
      });
    }

    // 支援多角色檢查
    const allowedRoles = Array.isArray(roleType) ? roleType : [roleType];

    if (!allowedRoles.includes(req.user.roleType)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${allowedRoles.join(" or ")}`,
        userRole: req.user.roleType,
        allowedRoles,
      });
    }

    next();
  };
};

// 複合權限檢查中間件 (權限 OR 角色)
export const requirePermissionOrRole = (permission: string, roleType: string | string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required.",
      });
    }

    try {
      // 檢查角色
      const allowedRoles = Array.isArray(roleType) ? roleType : [roleType];
      const hasRole = allowedRoles.includes(req.user.roleType);

      if (hasRole) {
        return next();
      }

      // 檢查權限
      const hasMemoryPermission =
        req.user.permissions.includes(permission) || req.user.permissionGroups.some((group) => group.toLowerCase().includes(permission.toLowerCase()));

      if (hasMemoryPermission) {
        return next();
      }

      // 資料庫權限檢查
      const hasPermission = await UserService.checkUserPermission(req.user.id, permission);
      if (hasPermission) {
        return next();
      }

      // 權限和角色都不符合
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permission '${permission}' or role '${allowedRoles.join("', '")}'`,
        userRole: req.user.roleType,
        userPermissions: req.user.permissions,
        userPermissionGroups: req.user.permissionGroups,
      });
    } catch (error) {
      console.error("Permission or role check error:", error);
      return res.status(500).json({
        success: false,
        message: "Authorization check failed.",
      });
    }
  };
};

// 管理員權限檢查中間件
export const requireAdmin = async (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: "Authentication required.",
    });
  }

  try {
    // 檢查是否有管理員權限
    const hasAdminPermission = req.user.permissions.includes("admin") || req.user.permissionGroups.includes("admin") || req.user.roleType === "Admin";

    if (hasAdminPermission) {
      return next();
    }

    // 資料庫檢查
    const hasDatabaseAdminPermission = await UserService.checkUserPermission(req.user.id, "admin");

    if (hasDatabaseAdminPermission) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: "Administrator privileges required.",
      userRole: req.user.roleType,
      userPermissions: req.user.permissions,
    });
  } catch (error) {
    console.error("Admin permission check error:", error);
    return res.status(500).json({
      success: false,
      message: "Admin permission check failed.",
    });
  }
};

export default {
  authenticateToken,
  optionalAuthenticateToken,
  requirePermission,
  requireRole,
  requirePermissionOrRole,
  requireAdmin,
};
