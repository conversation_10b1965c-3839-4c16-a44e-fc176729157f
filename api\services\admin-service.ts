// ========== 管理員服務 - 業務邏輯和資料庫操作 ==========

import { executeQuery, executeQuerySingle } from "../config/database-mssql.js";
import {
  AuthenticatedUser,
  AnswerStatusMarkRequest,
  ReviewCommentRequest,
  AnswerStatusInfo,
  AdminActionLog,
  ReviewComment,
  ReviewCommentSaveResult,
  CertificationQueryResult,
  AnswerQueryResult,
  ActionLogQueryResult,
  ReviewCommentQueryResult,
  StepRecordQueryResult,
} from "../models/admin.js";
import {
  ANSWER_STATUS,
  REVIEW_STATUS,
  CERTIFICATION_STATUS,
  ADMIN_ACTION_TYPES,
  ADMIN_ERROR_MESSAGES,
  ADMIN_SUCCESS_MESSAGES,
  SQL_QUERIES,
  isAdmin,
  getAnswerStatusInfo,
  isValidAction,
  parseStepId,
  validateComment,
  formatActionLog,
  formatReviewComment,
  canAdminOperateCertification,
  generateActionNote,
} from "../constants/admin.js";

export class AdminService {
  // 標示答案狀態
  static async markAnswerStatus(request: AnswerStatusMarkRequest, user: AuthenticatedUser): Promise<AnswerStatusInfo> {
    try {
      const { certificationId, questionId, status, action } = request;

      // 驗證必要參數
      if (!certificationId || !questionId || !status || !action) {
        throw new Error(ADMIN_ERROR_MESSAGES.MISSING_PARAMETERS);
      }

      // 驗證管理員權限
      if (!isAdmin(user)) {
        throw new Error(ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS);
      }

      // 驗證操作類型
      if (!isValidAction(action)) {
        throw new Error(ADMIN_ERROR_MESSAGES.INVALID_ACTION);
      }

      // 檢查認證是否存在且狀態正確
      const certification = await this.getCertification(parseInt(certificationId));
      if (!certification) {
        throw new Error(ADMIN_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND);
      }

      if (!canAdminOperateCertification(certification)) {
        throw new Error(ADMIN_ERROR_MESSAGES.NOT_IN_REVIEW_STATUS);
      }

      // 檢查答案是否存在
      const answer = await this.getAnswer(parseInt(certificationId), parseInt(questionId));
      if (!answer) {
        throw new Error(ADMIN_ERROR_MESSAGES.ANSWER_NOT_FOUND);
      }

      // 獲取新狀態和描述
      const { status: newAnswerStatus, description: statusDescription } = getAnswerStatusInfo(action);

      // 更新答案狀態
      await this.updateAnswerStatus(answer.AnswerId, newAnswerStatus);

      // 記錄管理員操作日誌
      await this.logAdminAction({
        certificationId: parseInt(certificationId),
        questionId: parseInt(questionId),
        answerId: answer.AnswerId,
        adminUserId: parseInt(user.id),
        action,
        previousStatus: answer.AnswerStatus,
        newStatus: newAnswerStatus,
        notes: generateActionNote(action, statusDescription),
      });

      return {
        answerId: answer.AnswerId,
        questionId: parseInt(questionId),
        previousStatus: answer.AnswerStatus,
        newStatus: newAnswerStatus,
        statusDescription,
        action,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ [AdminService] 標示答案狀態失敗:", error);
      throw error;
    }
  }

  // 獲取管理員操作歷史
  static async getActionLogs(certificationId: number, user: AuthenticatedUser): Promise<AdminActionLog[]> {
    try {
      // 驗證管理員權限
      if (!isAdmin(user)) {
        throw new Error(ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS);
      }

      const logs = await executeQuery<ActionLogQueryResult>(SQL_QUERIES.GET_ACTION_LOGS, {
        certificationId,
      });

      return logs.map(formatActionLog);
    } catch (error) {
      console.error("❌ [AdminService] 獲取管理員操作歷史失敗:", error);
      throw error;
    }
  }

  // 保存評審意見
  static async saveReviewComment(request: ReviewCommentRequest, user: AuthenticatedUser): Promise<ReviewCommentSaveResult> {
    try {
      const { certificationId, stepId, comment } = request;

      // 驗證必要參數
      if (!certificationId || !stepId || !comment) {
        throw new Error(ADMIN_ERROR_MESSAGES.MISSING_PARAMETERS);
      }

      // 驗證管理員權限
      if (!isAdmin(user)) {
        throw new Error(ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS);
      }

      // 驗證評論長度
      const validation = validateComment(comment);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // 檢查認證是否存在
      const certification = await this.getCertification(parseInt(certificationId));
      if (!certification) {
        throw new Error(ADMIN_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND);
      }

      // 解析步驟編號
      const stepNumber = parseStepId(stepId);

      // 檢查是否已存在該步驟的評審意見
      const existingRecord = await this.getExistingStepRecord(parseInt(certificationId), stepNumber);

      if (existingRecord) {
        // 更新現有評審意見
        await this.updateStepRecord(existingRecord.CertificationStepRecordId, comment.trim(), parseInt(user.id));

        return {
          recordId: existingRecord.CertificationStepRecordId,
          action: "update",
          timestamp: new Date().toISOString(),
        };
      } else {
        // 創建新的評審意見記錄
        await this.createStepRecord(parseInt(certificationId), stepNumber, comment.trim(), parseInt(user.id));

        return {
          action: "create",
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      console.error("❌ [AdminService] 保存評審意見失敗:", error);
      throw error;
    }
  }

  // 獲取評審意見
  static async getReviewComments(certificationId: number): Promise<ReviewComment[]> {
    try {
      const comments = await executeQuery<ReviewCommentQueryResult>(SQL_QUERIES.GET_REVIEW_COMMENTS, {
        certificationId,
      });

      return comments.map(formatReviewComment);
    } catch (error) {
      console.error("❌ [AdminService] 獲取評審意見失敗:", error);
      throw error;
    }
  }

  // ========== 私有輔助方法 ==========

  // 獲取認證資訊
  private static async getCertification(certificationId: number): Promise<CertificationQueryResult | null> {
    return await executeQuerySingle<CertificationQueryResult>(SQL_QUERIES.GET_CERTIFICATION, {
      certificationId,
      activeStatus: CERTIFICATION_STATUS.ACTIVE,
    });
  }

  // 獲取答案資訊
  private static async getAnswer(certificationId: number, questionId: number): Promise<AnswerQueryResult | null> {
    return await executeQuerySingle<AnswerQueryResult>(SQL_QUERIES.GET_ANSWER, {
      certificationId,
      questionId,
    });
  }

  // 更新答案狀態
  private static async updateAnswerStatus(answerId: number, answerStatus: number): Promise<void> {
    await executeQuery(SQL_QUERIES.UPDATE_ANSWER_STATUS, {
      answerStatus,
      answerId,
    });
  }

  // 記錄管理員操作
  private static async logAdminAction(params: {
    certificationId: number;
    questionId: number;
    answerId: number;
    adminUserId: number;
    action: string;
    previousStatus: number;
    newStatus: number;
    notes: string;
  }): Promise<void> {
    await executeQuery(SQL_QUERIES.INSERT_ACTION_LOG, params);
  }

  // 獲取現有步驟記錄
  private static async getExistingStepRecord(certificationId: number, stepNumber: number): Promise<StepRecordQueryResult | null> {
    return await executeQuerySingle<StepRecordQueryResult>(SQL_QUERIES.GET_EXISTING_STEP_RECORD, {
      certificationId,
      stepNumber,
    });
  }

  // 更新步驟記錄
  private static async updateStepRecord(recordId: number, comment: string, adminUserId: number): Promise<void> {
    await executeQuery(SQL_QUERIES.UPDATE_STEP_RECORD, {
      comment,
      adminUserId,
      recordId,
    });
  }

  // 創建步驟記錄
  private static async createStepRecord(certificationId: number, stepNumber: number, comment: string, adminUserId: number): Promise<void> {
    await executeQuery(SQL_QUERIES.INSERT_STEP_RECORD, {
      certificationId,
      stepNumber,
      comment,
      adminUserId,
    });
  }
}
