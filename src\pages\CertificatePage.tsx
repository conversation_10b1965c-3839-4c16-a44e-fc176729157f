import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import CertificateApplyDialog from "@/components/CertificateApplyDialog";
import { getCertificationLinks, openExternalLink } from "@/config/externalLinks";
import { useAuthService } from "@/services/authService";
import { getApiBaseUrl } from "@/config/environment";
import { buildAssetUrl } from "@/utils/pathUtils";
import { certificationAPI } from "@/services/certificationAPI";

// 認證申請

// 日期格式化函數
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-TW", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  } catch {
    return dateString;
  }
};

// 認證列表資料類型
interface CertificationListItem {
  id: string;
  certificationType: string;
  level: number;
  status: string;
  statusInfo: {
    label: string;
    icon: string;
    description: string;
    color: string;
    bgColor: string;
  };
  typeInfo: {
    name: string;
    fullName: string;
    level: number;
    isRenewal: boolean;
    icon: string;
  };
  applicantName: string;
  applyDate: string;
  reviewDate?: string;
  passDate?: string;
  expiredDate?: string;
  certificateNumber?: string;
  isEditable: boolean;
  isDeletable: boolean;
}

interface CertificationListResponse {
  success: boolean;
  message?: string;
  data: {
    all: CertificationListItem[];
    drafts: CertificationListItem[]; // 新增：待送審的認證
    pending: CertificationListItem[];
    passed: CertificationListItem[];
    statistics: {
      total: number;
      drafts: number; // 新增：待送審數量
      pending: number;
      passed: number;
      inReview: number;
      returned: number;
    };
  };
}

const CertificatePage = () => {
  const navigate = useNavigate();
  const authService = useAuthService();
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificationData, setCertificationData] = useState<CertificationListResponse["data"] | null>(null);

  // 載入認證資料
  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        console.log("🔍 [前端] 開始載入認證列表");
        setLoading(true);
        setError(null);

        // 檢查認證狀態
        if (!authService.isAuthenticated()) {
          throw new Error("用戶未登入，請先登入");
        }

        // 使用 certificationAPI 服務
        const result = await certificationAPI.getCertificationList();

        if (!result.success) {
          throw new Error(result.message || "載入認證資料失敗");
        }

        console.log("📊 [前端] 認證資料載入成功:", result.data.statistics);
        setCertificationData(result.data);
      } catch (err) {
        console.error("❌ [前端] 載入認證資料失敗:", err);
        setError(err instanceof Error ? err.message : "未知錯誤");
      } finally {
        setLoading(false);
      }
    };

    fetchCertifications();
  }, [authService]);

  const handleAddCertification = () => {
    // 優先使用導航到選擇類型頁面，如果不存在則使用對話框
    try {
      navigate("/certificate/select-type");
    } catch (error) {
      setOpenDialog(true);
    }
  };

  const handleCloseDialog = () => setOpenDialog(false);

  // 處理申請說明按鈕點擊
  const handleApplicationGuide = () => {
    const certificationLinks = getCertificationLinks();
    // openExternalLink(certificationLinks.applicationGuide, "applicationGuide");
    openExternalLink("https://ecocampus.moenv.gov.tw/certification", "applicationGuide");
  };

  // 處理檢視認證
  const handleViewCertification = (certification: CertificationListItem) => {
    navigate(`/certificate/application/${certification.id}`);
  };

  // 處理刪除認證申請
  const handleDeleteCertification = async (certification: CertificationListItem) => {
    if (!confirm(`確定要刪除「${certification.typeInfo.fullName}」的申請嗎？\n\n刪除後將無法復原，請確認您不再需要此認證申請。`)) {
      return;
    }

    try {
      console.log("🗑️ [前端] 開始刪除認證申請:", certification.id);

      // 檢查認證狀態
      if (!authService.isAuthenticated()) {
        throw new Error("用戶未登入，請先登入");
      }

      // 使用 certificationAPI 服務
      const result = await certificationAPI.deleteCertification(certification.id);

      if (result.success) {
        console.log("✅ [前端] 認證刪除成功:", result.data);
        alert("認證申請已成功刪除！");

        // 重新載入認證列表以反映變化
        window.location.reload();
      } else {
        throw new Error(result.message || "刪除認證失敗");
      }
    } catch (err) {
      console.error("❌ [前端] 刪除認證失敗:", err);
      const errorMessage = err instanceof Error ? err.message : "刪除失敗，請稍後再試";
      alert(errorMessage);
    }
  };

  // 🆕 處理提交送審
  const handleSubmitForReview = async (certification: CertificationListItem) => {
    if (!confirm(`確定要提交「${certification.typeInfo.fullName}」進行審核嗎？提交後將無法修改申請內容。`)) {
      return;
    }

    try {
      console.log("📝 提交認證申請送審:", certification.id);

      // 檢查認證狀態
      if (!authService.isAuthenticated()) {
        throw new Error("用戶未登入，請先登入");
      }

      // 使用 certificationAPI 服務
      const result = await certificationAPI.submitCertificationForReview(certification.id);

      if (result.success) {
        alert("提交成功！您的認證申請已送審，請等待審核結果。");

        // 重新載入認證列表以反映狀態變化
        window.location.reload();
      } else {
        throw new Error(result.message || "提交送審失敗");
      }
    } catch (err) {
      console.error("提交送審失敗:", err);
      const errorMessage = err instanceof Error ? err.message : "提交送審失敗，請稍後再試";
      alert(errorMessage);
    }
  };

  // 渲染認證項目
  const renderCertificationItem = (cert: CertificationListItem) => (
    <div key={cert.id} className="flex flex-wrap items-center p-6 border-b last:border-0 gap-6">
      {/* 認證圖標 */}
      <div className="flex-shrink-0">
        <img
          src={cert.typeInfo.icon.startsWith("http") ? cert.typeInfo.icon : buildAssetUrl(cert.typeInfo.icon)}
          alt={cert.typeInfo.fullName}
          className="w-20 h-20 object-contain"
          onError={(e) => {
            // 備用圖標邏輯
            const target = e.target as HTMLImageElement;
            if (target.src.includes("luxury")) {
              // 如果豪華版失敗，回退到基本版
              const fallbackPath = target.src
                .replace("medal-luxury-bronze.png", "medal-bronze.png")
                .replace("medal-luxury-silver.png", "medal-silver.png")
                .replace("medal-luxury-green.png", "medal-greenflag.png");
              target.src = fallbackPath.startsWith("http") ? fallbackPath : buildAssetUrl(fallbackPath.replace(/.*\//, ""));
            } else {
              // 最終備用圖標
              target.src = buildAssetUrl("img/placeholder.svg");
            }
          }}
        />
      </div>

      {/* 認證資訊 */}
      <div className="flex-1 min-w-[250px]">
        <div className="flex items-center gap-3 mb-2">
          <div className="font-bold text-lg text-gray-800">{cert.typeInfo.fullName}</div>
          <Badge className={`${cert.statusInfo.bgColor} ${cert.statusInfo.color} border-0 font-medium`}>{cert.statusInfo.label}</Badge>
        </div>
        <div className="font-size-sm text-gray-600 mb-2">
          <span className="font-medium">申請人：</span>
          {cert.applicantName}
        </div>
        <div className="font-size-xs text-gray-500">
          <span className="font-medium">申請日期</span> {formatDate(cert.applyDate)}
          {cert.reviewDate && (
            <span className="ml-3">
              <span className="font-medium">審核日期</span> {formatDate(cert.reviewDate)}
            </span>
          )}
          {cert.passDate && (
            <span className="ml-3">
              <span className="font-medium">通過日期</span> {formatDate(cert.passDate)}
            </span>
          )}
        </div>
      </div>

      {/* 操作按鈕 */}
      <div className="flex gap-3 flex-nowrap">
        <Button variant="primary" type="button" className="px-6 py-2" onClick={() => handleViewCertification(cert)}>
          {cert.isEditable ? "編輯申請" : "檢視申請"}
        </Button>
        {cert.isDeletable && (
          <Button
            variant="outline"
            type="button"
            className="px-5 py-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
            onClick={() => handleDeleteCertification(cert)}>
            刪除
          </Button>
        )}
      </div>
    </div>
  );

  // 🆕 渲染待送審認證項目（包含提交送審按鈕）
  const renderDraftCertificationItem = (cert: CertificationListItem) => {
    // 判斷是否為待補件認證（基於狀態標籤）
    const isReturnedForAmendment = cert.statusInfo.label === "待補件";

    return (
      <div key={cert.id} className="flex flex-wrap items-center p-6 border-b last:border-0 gap-6">
        {/* 認證圖標 */}
        <div className="flex-shrink-0">
          <img
            src={cert.typeInfo.icon.startsWith("http") ? cert.typeInfo.icon : buildAssetUrl(cert.typeInfo.icon)}
            alt={cert.typeInfo.fullName}
            className="w-20 h-20 object-contain"
            onError={(e) => {
              // 備用圖標邏輯
              const target = e.target as HTMLImageElement;
              if (target.src.includes("luxury")) {
                // 如果豪華版失敗，回退到基本版
                const fallbackPath = target.src
                  .replace("medal-luxury-bronze.png", "medal-bronze.png")
                  .replace("medal-luxury-silver.png", "medal-silver.png")
                  .replace("medal-luxury-green.png", "medal-greenflag.png");
                target.src = fallbackPath.startsWith("http") ? fallbackPath : buildAssetUrl(fallbackPath.replace(/.*\//, ""));
              } else {
                // 最終備用圖標
                target.src = buildAssetUrl("img/placeholder.svg");
              }
            }}
          />
        </div>

        {/* 認證資訊 */}
        <div className="flex-1 min-w-[250px]">
          <div className="flex items-center gap-3 mb-2">
            <div className="font-bold text-lg text-gray-800">{cert.typeInfo.fullName}</div>
            <Badge className={`${cert.statusInfo.bgColor} ${cert.statusInfo.color} border-0 font-medium`}>{cert.statusInfo.label}</Badge>
          </div>
          <div className="font-size-sm text-gray-600 mb-2">
            <span className="font-medium">申請人：</span>
            {cert.applicantName}
          </div>
          <div className="font-size-xs text-gray-500">
            <span className="font-medium">{isReturnedForAmendment ? "申請日期" : "建立日期"}</span> {formatDate(cert.applyDate)}
            {cert.reviewDate && isReturnedForAmendment && (
              <span className="ml-3">
                <span className="font-medium">審核日期</span> {formatDate(cert.reviewDate)}
              </span>
            )}
            <span className={`ml-3 ${isReturnedForAmendment ? "text-orange-600" : "text-orange-600"}`}>
              <span className="font-medium">狀態：</span>
              {isReturnedForAmendment ? "需要補充資料" : "尚未提交送審"}
            </span>
          </div>
        </div>

        {/* 操作按鈕 */}
        <div className="flex gap-3 flex-nowrap">
          <Button variant="outline" type="button" className="px-6 py-2" onClick={() => handleViewCertification(cert)}>
            {isReturnedForAmendment ? "補充資料" : "編輯申請"}
          </Button>
          {!isReturnedForAmendment && (
            <Button variant="primary" type="button" className="px-6 py-2 bg-green-600 hover:bg-green-700" onClick={() => handleSubmitForReview(cert)}>
              提交送審
            </Button>
          )}
          {cert.isDeletable && (
            <Button
              variant="outline"
              type="button"
              className="px-5 py-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
              onClick={() => handleDeleteCertification(cert)}>
              刪除
            </Button>
          )}
        </div>
      </div>
    );
  };

  // 載入中狀態
  if (loading) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
        <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" style={{ minHeight: "500px" }}>
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">載入認證資料中...</p>
            </div>
          </div>
        </section>
      </main>
    );
  }

  // 錯誤狀態
  if (error) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
        <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" style={{ minHeight: "500px" }}>
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="text-red-600 mb-4">
                <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="font-size-lg font-medium mb-2 text-red-700">載入失敗</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>重新載入</Button>
            </div>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
      <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" aria-label="認證申請" style={{ minHeight: "500px" }}>
        <div className="flex justify-between items-center mb-8 flex-wrap">
          <div>
            <h1 className="font-size-3xl font-bold text-primary mb-2" tabIndex={0}>
              認證申請
            </h1>
            {certificationData && (
              <div className="font-size-sm text-gray-600">
                共 {certificationData.statistics.total} 筆記錄 │ 待處理 {certificationData.statistics.drafts || 0} 筆 │ 申請中{" "}
                {certificationData.statistics.pending} 筆 │ 已通過 {certificationData.statistics.passed} 筆
              </div>
            )}
          </div>
          <div className="flex gap-3 mt-4">
            <Button variant="secondary" type="button" onClick={handleApplicationGuide}>
              申請說明
            </Button>
            <Button variant="primary" type="button" onClick={handleAddCertification}>
              新增認證
            </Button>
          </div>
        </div>

        {/* 🆕 待送審的認證區塊（包含待補件） */}
        <div className="bg-gray-50 rounded shadow mb-6 overflow-hidden">
          <div className="px-4 py-3 font-semibold border-b font-size-lg flex items-center gap-2">
            待處理的認證 ({certificationData?.statistics.drafts || 0})<span className="font-size-xs font-normal text-gray-600 ml-2">包含待送審及待補件</span>
          </div>
          <div>
            {!certificationData?.drafts?.length ? (
              <div className="px-4 py-8 text-gray-400 text-center">目前無待處理的認證</div>
            ) : (
              certificationData.drafts.map(renderDraftCertificationItem)
            )}
          </div>
        </div>

        <div className="bg-gray-50 rounded shadow mb-6 overflow-hidden">
          <div className="px-4 py-3 font-semibold border-b font-size-lg">申請中的認證 ({certificationData?.statistics.pending || 0})</div>
          <div>
            {!certificationData?.pending.length ? (
              <div className="px-4 py-8 text-gray-400 text-center">目前無申請中的認證</div>
            ) : (
              certificationData.pending.map(renderCertificationItem)
            )}
          </div>
        </div>

        <div className="bg-gray-50 rounded shadow overflow-hidden">
          <div className="px-4 py-3 font-semibold border-b font-size-lg">已通過的認證 ({certificationData?.statistics.passed || 0})</div>
          <div>
            {!certificationData?.passed.length ? (
              <div className="px-4 py-8 text-gray-400 text-center">目前無已通過的認證</div>
            ) : (
              certificationData.passed.map(renderCertificationItem)
            )}
          </div>
        </div>
      </section>

      {/* 關閉時清空 Dialog 狀態：強制 key 重設 */}
      {openDialog && <CertificateApplyDialog key={Date.now()} open={openDialog} onClose={handleCloseDialog} />}
    </main>
  );
};

export default CertificatePage;
