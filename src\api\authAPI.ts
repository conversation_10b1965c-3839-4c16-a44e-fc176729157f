import { BaseAPI, ApiResponse } from "./BaseAPI";

// 認證相關的資料介面
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  account?: string;
  phone?: string;
  organization?: string;
  position?: string;
  address?: string;
  status?: string;
  register_review?: string;
  roleType?: string;
}

export interface BackendUser {
  id: string;
  account?: string;
  nickName?: string;
  name?: string;
  email?: string;
  roleType?: string;
  role?: string;
  permissions?: string[];
  phone?: string;
  organization?: string;
  position?: string;
  address?: string;
  isActive?: boolean;
  register_review?: string;
  school?: {
    id: string;
    name: string;
    address?: string;
  };
}

export interface LoginResponse {
  valid: boolean;
  user?: User;
  details?: {
    userRole?: string;
    allowedRoles?: string[];
    action?: string;
  };
}

export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}

export interface ChangePasswordResponse {
  success: boolean;
  message: string;
}

export interface TokenLoginRequest {
  token: string;
}

export interface PasswordLoginRequest {
  account: string;
  password: string;
}

export interface TokenStatusResponse {
  success: boolean;
  valid: boolean;
  message?: string;
}

export interface AdminVerifyResponse {
  isAdmin: boolean;
  userRole: string;
}

// 認證 API 類別
export class AuthAPI extends BaseAPI {
  constructor() {
    super();
  }

  // Token 驗證 (使用 token-status 端點)
  async validateToken(userToken: string): Promise<ApiResponse<LoginResponse>> {
    // 設置 x-user-token header
    const headers = { "x-user-token": userToken };
    return this.get<LoginResponse>("/auth/token-status", headers);
  }

  // Token 登入
  async tokenLogin(token: string): Promise<ApiResponse<LoginResponse>> {
    return this.post<LoginResponse>("/auth/token-login", { token });
  }

  // 密碼登入
  async passwordLogin(account: string, password: string): Promise<ApiResponse<LoginResponse & { token?: string }>> {
    return this.post<LoginResponse & { token?: string }>("/auth/password-login", { account, password });
  }

  // 修改密碼
  async changePassword(oldPassword: string, newPassword: string): Promise<ApiResponse<ChangePasswordResponse>> {
    return this.post<ChangePasswordResponse>("/auth/change-password", { oldPassword, newPassword });
  }

  // 登出
  async logout(): Promise<ApiResponse<void>> {
    return this.post<void>("/auth/logout");
  }

  // 檢查 Token 狀態
  async checkTokenStatus(): Promise<ApiResponse<TokenStatusResponse>> {
    return this.get<TokenStatusResponse>("/auth/token-status");
  }

  // 管理員權限驗證
  async verifyAdminPermission(token: string): Promise<ApiResponse<AdminVerifyResponse>> {
    return this.post<AdminVerifyResponse>("/auth/verify-admin", { token });
  }

  // 創建 Token
  async createToken(accountId: string, days: number = 3000): Promise<ApiResponse<{ token: string }>> {
    return this.post<{ token: string }>("/auth/create-token", { accountId, days });
  }

  // 撤銷 Token
  async revokeToken(token: string): Promise<ApiResponse<void>> {
    return this.post<void>("/auth/revoke-token", { token });
  }
}

// 創建全域認證 API 實例
export const authAPI = new AuthAPI();
