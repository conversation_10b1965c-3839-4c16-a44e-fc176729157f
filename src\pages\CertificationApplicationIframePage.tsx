import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useSearchParams } from "react-router-dom";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { certificationAPI, type CertificationData } from "@/services/certificationAPI";
import { getApiBaseUrl } from "@/config/environment";
import { buildAssetUrl } from "@/utils/pathUtils";
import { ChevronDown, ChevronRight, Save, CheckCircle, AlertCircle, Circle, Clock, Eye } from "lucide-react";
import { useToken } from "@/contexts/TokenContext";

// Import question template system
import { QuestionTemplateFactory, getDefaultTemplateData, SaveButton, formatAnswerForSave } from "@/components/question-templates";

// Import dynamic form components (for legacy support)
import {
  DynamicPathSelection,
  DynamicIndicators,
  DynamicActivities,
  DynamicEvents,
  DynamicActionPlans,
  DynamicSharingEvents,
  type EnvironmentalPath,
  type MonitoringIndicator,
  type TeachingActivity,
  type CommunityEvent,
  type ActionPlan,
  type SharingEvent,
} from "@/components/DynamicFormComponents";

// Application status types
type ApplicationStatus = "no-status" | "modify" | "accepted" | "returned" | "verifying";
type CertificationLevel = "bronze" | "silver" | "green_flag" | "green_flag_r1" | "green_flag_r2" | "green_flag_r3";

interface StatusInfo {
  label: string;
  icon: string;
  description: string;
}

interface FormQuestion {
  id: string;
  database_sid?: number | number[];
  type: string;
  label: string;
  placeholder?: string;
  required: boolean;
  question_tpl: number; // 問題模板ID
  rows?: number;
  min?: number;
  max?: number;
  options?: { value: string; label: string }[];
  accept?: string;
  multiple?: boolean;
  validation?: Record<string, { min?: number; min_selected?: number }>;
  template?: Record<string, unknown>;
  fields?: { id: string; label: string; type: string }[];
  renewal_only?: boolean;
  initial_only?: boolean;
  conditions?: Record<string, unknown>;
  essay_questions?: string[];
  step: number;
  sequence: number;
  parent_id?: number | null;
}

interface QuestionGroup {
  id: string; // 父群組ID (如 'root', '1', '4' 等)
  parentId: number | null; // 原始父問題ID
  title: string; // 群組標題（用第一個問題的標題或父問題標題）
  displayNumber: number; // 顯示編號（問題1, 問題2...）
  questions: FormQuestion[]; // 群組內的問題列表
  templateId: number; // 群組的主要模板ID
  step: number;
  isCompleted: boolean;
}

interface ReviewComment {
  id: string;
  content: string;
  author: string;
  date: string;
  type: "success" | "warning" | "error" | "info";
}

interface CertificationStep {
  step: number;
  title: string;
  description: string;
  questions: FormQuestion[];
  questionGroups?: QuestionGroup[]; // 新增群組信息，但保持向下兼容
  isCompleted: boolean;
  reviewComments?: ReviewComment[];
}

interface FormState {
  currentStep: number;
  currentQuestionId: string | null;
  currentGroupId?: string | null; // 新增群組ID，但保持向下兼容
  expandedSteps: Set<number>;
  saving: boolean;
  savedQuestions: Set<string>;
}

interface ApplicationData {
  id: number;
  certificationType: string;
  status: ApplicationStatus;
  submissionDate: string | null;
  lastModified: Date;
}

const statusMapping: Record<ApplicationStatus, StatusInfo> = {
  "no-status": {
    label: "未送審",
    icon: buildAssetUrl("img/license-icon-no-status.svg"),
    description: "尚未提交審核",
  },
  modify: {
    label: "待補件",
    icon: buildAssetUrl("img/license-icon-modify.svg"),
    description: "需要補充相關文件",
  },
  accepted: {
    label: "審核通過",
    icon: buildAssetUrl("img/license-icon-accepted.svg"),
    description: "申請已通過審核",
  },
  returned: {
    label: "已退件",
    icon: buildAssetUrl("img/license-icon-returned.svg"),
    description: "申請已被退回",
  },
  verifying: {
    label: "審核中",
    icon: buildAssetUrl("img/license-icon-verifying.svg"),
    description: "正在進行審核",
  },
};

const certificationTypeNames: Record<string, string> = {
  bronze: "銅牌",
  silver: "銀牌",
  green_flag: "綠旗",
  "green-flag-r1": "綠旗R1",
  "green-flag-r2": "綠旗R2",
  "green-flag-r3": "綠旗R3",
};

// Helper functions
const getCertificationLevel = (certType: string): number => {
  switch (certType) {
    case "bronze":
      return 1;
    case "silver":
      return 2;
    case "green_flag":
      return 3;
    case "green-flag-r1":
      return 4;
    case "green-flag-r2":
      return 5;
    case "green-flag-r3":
      return 6;
    default:
      return 1;
  }
};

// 根據Level反向獲取認證類型
const getCertificationTypeFromLevel = (level: number): string => {
  switch (level) {
    case 1:
      return "bronze";
    case 2:
      return "silver";
    case 3:
      return "green_flag";
    case 4:
      return "green-flag-r1";
    case 5:
      return "green-flag-r2";
    case 6:
      return "green-flag-r3";
    default:
      return "bronze";
  }
};

const isRenewalCertification = (certType: string): boolean => {
  const level = getCertificationLevel(certType);
  return level >= 4;
};

const mapQuestionType = (questionTpl: number): string => {
  // 根據文件分析，直接返回模板ID作為類型標識
  return `template_${questionTpl}`;
};

// Main Component
const CertificationApplicationIframePage = () => {
  const [searchParams] = useSearchParams();
  const { withTemporaryToken, token: currentToken, isTemporaryMode } = useToken();

  // URL參數
  const applicationId = searchParams.get("id");
  const viewerToken = searchParams.get("token"); // 檢視者身份token

  // 移除readonly和certType參數，改為動態判斷

  // State
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificationSteps, setCertificationSteps] = useState<CertificationStep[]>([]);
  const [localFormData, setLocalFormData] = useState<Record<string, unknown>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isAdminViewer, setIsAdminViewer] = useState(false); // 檢視者是否為管理員
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    id: parseInt(applicationId || "0"),
    certificationType: "bronze", // 預設值，稍後從API更新
    status: "no-status",
    submissionDate: null,
    lastModified: new Date(),
  });

  // 動態計算屬性
  const isReadonly = applicationData.status === "verifying" && !isAdminViewer; // 審核中且非管理員時為唯讀

  const [formState, setFormState] = useState<FormState>({
    currentStep: 0,
    currentQuestionId: null,
    expandedSteps: new Set([0]), // 預設展開第一個步驟
    saving: false,
    savedQuestions: new Set(),
  });

  // 管理員操作狀態
  const [adminActions, setAdminActions] = useState<Record<string, "pending" | "approved" | "request_supplement">>({});

  // 評審意見狀態管理
  const [reviewComments, setReviewComments] = useState<{
    [stepId: string]: string;
  }>({});
  const [savingComments, setSavingComments] = useState<{
    [stepId: string]: boolean;
  }>({});

  // 檢查檢視者身份是否為管理員
  const checkAdminPermission = useCallback(async (token: string) => {
    try {
      console.log("🔐 檢查管理員權限 - Token:", token);

      // 使用 withTemporaryToken 確保使用正確的 Token 進行 API 調用
      const hasAdminPermission = await withTemporaryToken(token, async () => {
        const result = await certificationAPI.verifyAdminPermission(token);
        return result.success && result.data?.isAdmin === true;
      });

      console.log("✅ 管理員權限檢查結果:", {
        hasAdminPermission,
        tokenUsed: token,
        isTemporaryMode: isTemporaryMode,
      });
      setIsAdminViewer(hasAdminPermission);

      return hasAdminPermission;
    } catch (error) {
      console.warn("❌ 管理員權限檢查錯誤:", error);
      setIsAdminViewer(false);
      return false;
    }
  }, [withTemporaryToken, isTemporaryMode]);

  // 管理員操作：標示待補件
  const markAsRequestSupplement = useCallback(
    async (questionId: string) => {
      if (!isAdminViewer || !applicationId) return;

      try {
        console.log("Marking question as request supplement:", questionId);

        // 如果有 viewerToken，使用臨時 Token 進行操作
        const executeOperation = async () => {
          const result = await certificationAPI.markAnswerStatus({
            certificationId: parseInt(applicationId),
            questionId: questionId.replace("question_", ""),
            status: "request_supplement",
            action: "request_supplement",
          });

          if (result.success) {
            setAdminActions((prev) => ({
              ...prev,
              [questionId]: "request_supplement",
            }));
            console.log("✅ Question marked as request supplement successfully");
          }
          return result;
        };

        if (viewerToken) {
          await withTemporaryToken(viewerToken, executeOperation);
        } else {
          await executeOperation();
        }
      } catch (error) {
        console.error("Failed to mark question as request supplement:", error);
      }
    },
    [isAdminViewer, applicationId, viewerToken, withTemporaryToken]
  );

  // 管理員操作：標示已審核
  const markAsApproved = useCallback(
    async (questionId: string) => {
      if (!isAdminViewer || !applicationId) return;

      try {
        console.log("Marking question as approved:", questionId);

        // 如果有 viewerToken，使用臨時 Token 進行操作
        const executeOperation = async () => {
          const result = await certificationAPI.markAnswerStatus({
            certificationId: parseInt(applicationId),
            questionId: questionId.replace("question_", ""),
            status: "approved",
            action: "approve",
          });

          if (result.success) {
            setAdminActions((prev) => ({
              ...prev,
              [questionId]: "approved",
            }));
            console.log("✅ Question marked as approved successfully");
          }
          return result;
        };

        if (viewerToken) {
          await withTemporaryToken(viewerToken, executeOperation);
        } else {
          await executeOperation();
        }
      } catch (error) {
        console.error("Failed to mark question as approved:", error);
      }
    },
    [isAdminViewer, applicationId, viewerToken, withTemporaryToken]
  );

  // 保存評審意見
  const saveReviewComment = useCallback(
    async (stepId: string, comment: string) => {
      if (!isAdminViewer || !applicationId || comment.trim().length === 0) return;

      // 檢查字數限制（20字以内）
      if (comment.trim().length > 20) {
        alert("評審意見不得超過20個字");
        return;
      }

      setSavingComments((prev) => ({ ...prev, [stepId]: true }));

      try {
        console.log("Saving review comment:", { stepId, comment });

        // 如果有 viewerToken，使用臨時 Token 進行操作
        const executeOperation = async () => {
          const result = await certificationAPI.saveReviewComment({
            certificationId: parseInt(applicationId),
            stepId: stepId,
            comment: comment.trim(),
          });

          if (result.success) {
            console.log("✅ Review comment saved successfully");
            // 更新本地狀態
            setReviewComments((prev) => ({
              ...prev,
              [stepId]: comment.trim(),
            }));
          } else {
            console.error("❌ Failed to save review comment:", result.message);
            alert("評審意見保存失敗：" + result.message);
          }
          return result;
        };

        if (viewerToken) {
          await withTemporaryToken(viewerToken, executeOperation);
        } else {
          await executeOperation();
        }
      } catch (error) {
        console.error("Failed to save review comment:", error);
        alert("評審意見保存失敗，請稍後再試");
      } finally {
        setSavingComments((prev) => ({ ...prev, [stepId]: false }));
      }
    },
    [isAdminViewer, applicationId, viewerToken, withTemporaryToken]
  );

  // Data loading
  useEffect(() => {
    const loadData = async () => {
      console.log("Loading certification application data for iframe...");
      console.log("Application ID:", applicationId);
      console.log("Viewer token:", viewerToken);

      setIsLoading(true);
      setError(null);

      // 檢查檢視者身份 - 使用 withTemporaryToken 管理 Token 切換
      if (viewerToken) {
        console.log("🔐 開始檢查檢視者身份 - Token:", viewerToken);

        const isAdmin = await checkAdminPermission(viewerToken);
        console.log("🔐 檢視者身份檢查完成:", { isAdmin, viewerToken });
      } else {
        console.log("⚠️ 沒有提供檢視者Token，將以一般身份訪問");
      }

      try {
        // 使用 withTemporaryToken 包裝所有數據載入操作（如果有 viewerToken）
        const loadAllData = async () => {
          // 1. 載入認證步驟
          console.log("Loading certification steps from API...");
          const stepsResponse = await certificationAPI.getCertificationSteps();
          if (!stepsResponse.success || !stepsResponse.data) {
            throw new Error("Failed to load certification steps");
          }

          // 2. 載入表單問題
          console.log("Loading form questions from API...");
          const questionsResponse = await certificationAPI.getFormQuestions();
          if (!questionsResponse.success || !questionsResponse.data) {
            throw new Error("Failed to load form questions");
          }

          // 3. 如果有申請案號，載入該申請的資料和已保存的答案
          const existingAnswers: Record<string, unknown> = {};
          const savedQuestionIds = new Set<string>();
          const realReviewComments: { [stepNumber: number]: ReviewComment[] } = {};

          if (applicationId) {
            console.log("Loading application data for ID:", applicationId);
            try {
              console.log("🔑 載入認證資料，當前 Token 狀態:", {
                isTemporaryMode,
                currentToken: currentToken ? '***已設定***' : '未設定',
                viewerToken: viewerToken ? '***已提供***' : '未提供',
              });

              // 使用 certificationAPI 服務
              const result = await certificationAPI.getCertification(parseInt(applicationId));

            if (result.success) {
              const certData = result.data;
              console.log("📊 從API載入認證狀態:", certData);

              // 根據 ReviewStatus 數字判斷狀態（與外層頁面一致）
              let status: ApplicationStatus = "no-status";
              if (certData.ReviewStatus === 0) {
                status = "verifying"; // 審核中
              } else if (certData.ReviewStatus === 1) {
                status = "accepted"; // 已通過
              } else if (certData.ReviewStatus === 2) {
                status = "returned"; // 已退件
              } else if (certData.ReviewStatus === 3) {
                status = "modify"; // 待補件
              } else if (certData.ReviewStatus === 4) {
                status = "no-status"; // 尚未審核
              }

              console.log("certData", certData);
              // 根據Level獲取認證類型
              const certificationType = getCertificationTypeFromLevel(certData.level);

              console.log("🔄 狀態映射結果:", {
                ReviewStatus: certData.ReviewStatus,
                mappedStatus: status,
                Level: certData.level,
                certificationType,
                statusLabel:
                  status === "verifying"
                    ? "審核中"
                    : status === "accepted"
                    ? "已通過"
                    : status === "returned"
                    ? "已退件"
                    : status === "modify"
                    ? "待補件"
                    : status === "no-status"
                    ? "未送審"
                    : "未知",
              });

              // 更新 applicationData 狀態
              console.log("🔄 即將更新 applicationData 狀態:", {
                id: parseInt(applicationId),
                status,
                certificationType,
                submissionDate: certData.ApplyDate || null,
                lastModified: new Date(certData.UpdatedTime || certData.CreatedTime),
              });

              setApplicationData((prev) => {
                const newData = {
                  ...prev,
                  id: parseInt(applicationId),
                  status,
                  certificationType,
                  submissionDate: certData.ApplyDate || null,
                  lastModified: new Date(certData.UpdatedTime || certData.CreatedTime),
                };
                console.log("🔄 applicationData 狀態更新:", {
                  before: prev,
                  after: newData,
                });
                return newData;
              });

              console.log("✅ 申請狀態更新完成:", {
                newStatus: status,
                ReviewStatus: certData.ReviewStatus,
                isReadonly: status === "verifying" && !isAdminViewer,
              });
            }

            // 載入已保存的答案
            console.log("📋 嘗試載入已保存答案 - ID:", applicationId);
            const answersResponse = await certificationAPI.getAnswers(parseInt(applicationId));
            console.log("📋 答案API響應:", {
              success: answersResponse.success,
              data: !!answersResponse.data,
            });
            if (answersResponse.success && answersResponse.data) {
              console.log("✅ 成功載入已保存答案 - 認證ID:", applicationId);

              // 處理新的API回應格式
              const apiData = answersResponse.data as { answers?: unknown[]; certificationId?: string } | unknown[];
              const answers = Array.isArray(apiData) ? apiData : (apiData as { answers?: unknown[] }).answers;
              if (Array.isArray(answers)) {
                // 將答案資料轉換為前端格式
                answers.forEach((answer) => {
                  try {
                    // 安全轉換答案項目類型
                    const answerItem = answer as {
                      answerData?: unknown;
                      answer_json?: string;
                      questionId?: number;
                      certification_answer_sid?: number;
                      answerStatus?: number; // 新增 answerStatus 欄位
                    };

                    // 使用新API格式的欄位名稱
                    const answerData = answerItem.answerData || answerItem.answer_json;
                    const questionId = answerItem.questionId || answerItem.certification_answer_sid;
                    const answerStatus = answerItem.answerStatus;
                    const questionKey = `question_${questionId}`;

                    // 如果answerData是字串，嘗試解析JSON；如果已是物件則直接使用
                    const parsedData = typeof answerData === "string" ? JSON.parse(answerData) : answerData;
                    existingAnswers[questionKey] = parsedData;

                    // 根據 AnswerStatus 同步 savedQuestions 狀態
                    // AnswerStatus: 0=未填寫, 1=已填寫, 2=待補件, 3=退件, 4=已完成
                    if (answerStatus === 1 || answerStatus === 4) {
                      savedQuestionIds.add(questionKey);
                      console.log(`Question ${questionId} marked as saved (AnswerStatus: ${answerStatus})`);
                    }

                    console.log(`Loaded answer for question ${questionId}:`, parsedData, `(Status: ${answerStatus})`);
                  } catch (parseError) {
                    console.warn(`Failed to parse answer:`, parseError);
                  }
                });
              }
            } else {
              console.warn("❌ 載入答案失敗或無答案資料:", answersResponse);
            }

            // 載入真實評審意見
            console.log("🔍 載入真實評審意見 - 認證ID:", applicationId);

            try {
              // 使用 certificationAPI 服務
              const commentsResult = await certificationAPI.getReviewComments(applicationId);
              if (commentsResult.success && commentsResult.data) {
                const commentsData: Array<{
                  CertificationStepRecordId: number;
                  StepNumber: number;
                  Comment: string;
                  CreatedTime: string;
                  AdminUsername: string;
                }> = commentsResult.data;

                // 將API回應的評審意見轉換為ReviewComment格式，按步驟分組
                commentsData.forEach((comment) => {
                  const reviewComment: ReviewComment = {
                    id: `review_${comment.CertificationStepRecordId}`,
                    content: comment.Comment,
                    author: comment.AdminUsername || "審查委員",
                    date: new Date(comment.CreatedTime).toLocaleDateString(),
                    type: "info" as const, // 真實評審意見統一使用info類型（藍色樣式）
                  };

                  if (!realReviewComments[comment.StepNumber]) {
                    realReviewComments[comment.StepNumber] = [];
                  }
                  realReviewComments[comment.StepNumber].push(reviewComment);
                });

                // 將評審意見轉換為前端格式（用於管理員輸入功能）
                const loadedComments: { [stepId: string]: string } = {};
                commentsData.forEach((comment) => {
                  const stepId = `step_${comment.StepNumber}`;
                  loadedComments[stepId] = comment.Comment;
                });

                setReviewComments(loadedComments);
                console.log("✅ 真實評審意見載入成功:", {
                  realReviewComments,
                  loadedComments,
                });
              }
            } catch (commentsError) {
              console.warn("⚠️ 載入評審意見失敗:", commentsError);
            }
          } catch (error) {
            console.warn("❌ 載入已保存答案時發生錯誤:", error);
            // 不要因為載入答案失敗而中斷整個頁面載入
          }
          }

          // 4. 組合數據
          const apiSteps = stepsResponse.data as {
            step: number;
            title: string;
            description: string;
          }[];
          const apiQuestions = questionsResponse.data as unknown as {
            questions: Record<
              string,
              Record<
                string,
                Array<{
                  sid: number;
                  title: string;
                  question_tpl: number;
                  is_renew: number;
                  step: number;
                  sequence: number;
                  parent_id: number | null; // 新增 parent_id 欄位
              }>
            >
          >;
        };

        const processedSteps: CertificationStep[] = apiSteps.map((step) => {
          const stepQuestionsObj = apiQuestions.questions[step.step.toString()] || {};

          // 創建問題群組
          const questionGroups: QuestionGroup[] = [];
          let displayNumber = 1;

          // 遍歷所有父分組，創建群組
          Object.entries(stepQuestionsObj).forEach(([parentKey, parentQuestions]) => {
            if (Array.isArray(parentQuestions) && parentQuestions.length > 0) {
              // 按 sequence 和 sid 排序群組內問題

              // 轉換為 FormQuestion 格式
              const formQuestions: FormQuestion[] = parentQuestions.map((q) => ({
                id: `question_${q.sid}`,
                database_sid: q.sid,
                type: mapQuestionType(q.question_tpl),
                label: q.title,
                required: true,
                question_tpl: q.question_tpl,
                renewal_only: q.is_renew === 1,
                initial_only: q.is_renew === -1,
                step: q.step,
                sequence: q.sequence,
                parent_id: q.parent_id,
              }));

              // 創建群組
              const group: QuestionGroup = {
                id: parentKey,
                parentId: parentKey === "root" ? null : parseInt(parentKey),
                title: formQuestions[0]?.label || `問題群組 ${displayNumber}`,
                displayNumber: displayNumber++,
                questions: formQuestions,
                templateId: formQuestions[0]?.question_tpl || 0,
                step: step.step,
                isCompleted: false,
              };

              questionGroups.push(group);
            }
          });

          // 為了向下兼容，仍然創建扁平化的問題列表
          const allQuestions: FormQuestion[] = questionGroups.flatMap((group) => group.questions);

          // 使用真實的評審意見，而非模擬資料
          const stepReviewComments: ReviewComment[] = realReviewComments[step.step] || [];

          return {
            step: step.step,
            title: step.title,
            description: step.description,
            questions: allQuestions, // 向下兼容的扁平化問題列表
            questionGroups: questionGroups, // 新的群組結構
            isCompleted: false,
            reviewComments: stepReviewComments, // 使用真實評審意見
          };
        });

        setCertificationSteps(processedSteps);

        // 5. 設置載入的答案資料到表單狀態中
        if (Object.keys(existingAnswers).length > 0) {
          console.log("Setting existing answers to form data:", existingAnswers);
          setLocalFormData(existingAnswers);
        }

        // 6. 同步 savedQuestions 狀態（根據 AnswerStatus）
        if (savedQuestionIds.size > 0) {
          console.log("Syncing savedQuestions from AnswerStatus:", Array.from(savedQuestionIds));
          setFormState((prev) => ({
            ...prev,
            savedQuestions: savedQuestionIds,
          }));
        }

        // 7. 設置初始選中的問題群組
        if (processedSteps.length > 0 && processedSteps[0].questionGroups && processedSteps[0].questionGroups.length > 0) {
          setFormState((prev) => ({
            ...prev,
            currentGroupId: processedSteps[0].questionGroups![0].id,
            currentQuestionId: processedSteps[0].questionGroups![0].questions[0]?.id || null, // 保持兼容性
          }));
        }

        return { 
          processedSteps, 
          existingAnswers, 
          savedQuestionIds 
        };
      };

      // 使用 withTemporaryToken 執行所有數據載入（如果有 viewerToken）
      if (viewerToken) {
        console.log("🔑 使用臨時 Token 載入數據:", viewerToken);
        await withTemporaryToken(viewerToken, loadAllData);
      } else {
        console.log("🔑 使用當前 Token 載入數據");
        await loadAllData();
      }

      setIsLoading(false);
      console.log("All data loaded successfully for iframe");
    } catch (error) {
      console.error("Failed to load data:", error);
      setError(String(error));
      setIsLoading(false);
    }
    };

    loadData();
  }, [applicationId, viewerToken, withTemporaryToken, checkAdminPermission]);

  // 刷新認證狀態函數
  const refreshCertificationStatus = useCallback(async () => {
    if (!applicationId) return;

    try {
      console.log("🔄 刷新認證狀態 - ID:", applicationId);
      console.log("🔑 刷新時 Token 狀態:", {
        isTemporaryMode,
        viewerToken: viewerToken ? '***已提供***' : '未提供',
      });

      // 定義操作函數
      const executeRefresh = async () => {
        return await certificationAPI.getCertification(parseInt(applicationId));
      };

      // 如果有 viewerToken，使用臨時 Token 進行操作
      const result = viewerToken 
        ? await withTemporaryToken(viewerToken, executeRefresh)
        : await executeRefresh();

      if (result.success) {
        const certData = result.data;
        console.log("📊 刷新獲得的認證狀態:", certData);

        // 根據 ReviewStatus 數字判斷狀態（與loadData中的邏輯一致）
        let status: ApplicationStatus = "no-status";
        if (certData.ReviewStatus === 0) {
          status = "verifying"; // 審核中
        } else if (certData.ReviewStatus === 1) {
          status = "accepted"; // 已通過
        } else if (certData.ReviewStatus === 2) {
          status = "returned"; // 已退件
        } else if (certData.ReviewStatus === 3) {
          status = "modify"; // 待補件
        } else if (certData.ReviewStatus === 4) {
          status = "no-status"; // 尚未審核
        }

        // 根據Level獲取認證類型
        const certificationType = getCertificationTypeFromLevel(certData.level);

        console.log("🔄 刷新時狀態映射:", {
          ReviewStatus: certData.ReviewStatus,
          mappedStatus: status,
          certificationType,
          willShowAdminActions: isAdminViewer && status === "verifying",
        });

        // 更新 applicationData 狀態
        setApplicationData((prev) => ({
          ...prev,
          status,
          certificationType,
          submissionDate: certData.ApplyDate || null,
          lastModified: new Date(certData.UpdatedTime || certData.CreatedTime),
        }));

        console.log("✅ 認證狀態刷新完成:", {
          newStatus: status,
          ReviewStatus: certData.ReviewStatus,
          isAdminViewer,
          shouldShowActions: isAdminViewer && status === "verifying",
        });
      }
    } catch (error) {
      console.warn("❌ 刷新認證狀態失敗:", error);
    }
  }, [applicationId, isAdminViewer, viewerToken, withTemporaryToken]);

  // 定期刷新認證狀態（每60秒）
  useEffect(() => {
    if (!applicationId) return;

    const interval = setInterval(() => {
      refreshCertificationStatus();
    }, 60000); // 60秒刷新一次

    return () => clearInterval(interval);
  }, [applicationId, isAdminViewer, refreshCertificationStatus]);

  // 當頁面重新獲得焦點時刷新狀態
  useEffect(() => {
    const handleFocus = () => {
      refreshCertificationStatus();
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [refreshCertificationStatus]);

  // Event handlers
  const toggleStepExpansion = (stepIndex: number) => {
    setFormState((prev) => {
      const newExpanded = new Set<number>();
      // 手風琴效果：如果當前步驟已展開，則收合；否則展開當前步驟並收合其他步驟
      if (!prev.expandedSteps.has(stepIndex)) {
        newExpanded.add(stepIndex);
      }
      return { ...prev, expandedSteps: newExpanded };
    });
  };

  const selectQuestion = (questionId: string, stepIndex: number) => {
    setFormState((prev) => ({
      ...prev,
      currentStep: stepIndex,
      currentQuestionId: questionId,
    }));
  };

  // 新增選擇群組的函數
  const selectGroup = (groupId: string, stepIndex: number) => {
    const step = certificationSteps[stepIndex];
    const group = step.questionGroups?.find((g) => g.id === groupId);

    setFormState((prev) => ({
      ...prev,
      currentStep: stepIndex,
      currentGroupId: groupId,
      currentQuestionId: group?.questions[0]?.id || null, // 選擇群組時自動選中第一個問題以保持兼容性
    }));
  };

  const updateFormData = (questionId: string, value: unknown) => {
    if (isReadonly) return; // 唯讀模式下不允許修改

    setLocalFormData((prev) => ({
      ...prev,
      [questionId]: value,
    }));
    // 清除驗證錯誤
    if (validationErrors[questionId]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[questionId];
        return newErrors;
      });
    }
  };

  // 初始化問題模板資料
  const initializeQuestionData = (question: FormQuestion) => {
    if (!localFormData[question.id]) {
      const defaultData = getDefaultTemplateData(question.question_tpl);
      if (!isReadonly) {
        setLocalFormData((prev) => ({
          ...prev,
          [question.id]: defaultData,
        }));
      }
      return defaultData;
    }
    return localFormData[question.id];
  };

  const saveCurrentQuestion = async () => {
    if (isReadonly) return; // 唯讀模式下不允許儲存
    if (!formState.currentQuestionId) return;

    const question = getCurrentQuestion();
    if (!question) return;

    const value = localFormData[formState.currentQuestionId];

    // 基本驗證：檢查是否有資料
    if (question.required && (!value || value === "")) {
      setValidationErrors((prev) => ({
        ...prev,
        [formState.currentQuestionId!]: `${question.label} 為必填項目`,
      }));
      return;
    }

    setFormState((prev) => ({ ...prev, saving: true }));

    try {
      // 使用新的答案保存 API
      const { formatAnswerForSave } = await import("../components/question-templates");

      // 格式化答案數據
      const formattedData = formatAnswerForSave(question.question_tpl, value);

      const questionId = Array.isArray(question.database_sid) ? question.database_sid[0] : question.database_sid;
      const response = await certificationAPI.saveAnswer(applicationData.id, questionId as number, formattedData as Record<string, unknown>);

      if (response.success) {
        // 保存成功後更新狀態
        setFormState((prev) => ({
          ...prev,
          saving: false,
          savedQuestions: new Set([...prev.savedQuestions, formState.currentQuestionId!]),
        }));

        // 保存成功後刷新認證狀態
        refreshCertificationStatus();

        console.log(`Question ${formState.currentQuestionId} saved successfully`);
      } else {
        throw new Error(response.message || "保存失敗");
      }
    } catch (error) {
      console.error("Failed to save question:", error);
      setFormState((prev) => ({ ...prev, saving: false }));

      // 顯示錯誤提示
      setValidationErrors((prev) => ({
        ...prev,
        [formState.currentQuestionId!]: error instanceof Error ? error.message : "保存失敗，請稍後再試",
      }));
    }
  };

  const getCurrentQuestion = (): FormQuestion | null => {
    if (!formState.currentQuestionId) return null;

    for (const step of certificationSteps) {
      const question = step.questions.find((q) => q.id === formState.currentQuestionId);
      if (question) return question;
    }
    return null;
  };

  // 獲取當前選中的群組
  const getCurrentGroup = (): QuestionGroup | null => {
    if (!formState.currentGroupId || certificationSteps.length === 0) return null;

    for (const step of certificationSteps) {
      if (step.questionGroups) {
        const group = step.questionGroups.find((g) => g.id === formState.currentGroupId);
        if (group) return group;
      }
    }
    return null;
  };

  // 檢查問題是否已完成填寫
  const isQuestionCompleted = (question: FormQuestion): boolean => {
    const data = localFormData[question.id];
    if (!data) return false;

    // 根據問題模板類型檢查完成狀態
    switch (question.question_tpl) {
      case 1: {
        // 是非選擇題
        return !!(data as { is_yes_no: string }).is_yes_no;
      }

      case 2: {
        // 團隊成員表單 (生態行動團隊成員)
        const teamData = data as {
          student_list?: Array<{
            input_1: string;
            input_2: string;
            input_3: string;
          }>;
          teacher_list?: Array<{
            input_1: string;
            input_2: string;
            input_3: string;
          }>;
          community_member_list?: Array<{
            input_1: string;
            input_2: string;
            input_3: string;
          }>;
        };
        return (
          teamData.student_list?.some((member) => member.input_2) ||
          false ||
          teamData.teacher_list?.some((member) => member.input_2) ||
          false ||
          teamData.community_member_list?.some((member) => member.input_2) ||
          false
        );
      }

      case 3: {
        // 會議記錄（七個問題）
        const meetingData = data as {
          question_1?: string;
          question_2?: string;
          question_3?: string;
          question_4?: string;
          question_5?: string;
          question_6?: string;
          question_7?: string;
        };
        return !!(
          meetingData.question_1?.trim() &&
          meetingData.question_2?.trim() &&
          meetingData.question_3?.trim() &&
          meetingData.question_4?.trim() &&
          meetingData.question_5?.trim() &&
          meetingData.question_6?.trim() &&
          meetingData.question_7?.trim()
        );
      }

      case 4: {
        // 分享會議資訊
        const shareData = data as { is_yes_no: string };
        return !!shareData.is_yes_no;
      }

      case 5: {
        // 招募新成員
        const recruitData = data as { is_yes_no: string; textarea?: string };
        return !!recruitData.is_yes_no;
      }

      case 6: {
        // 照片上傳
        const photoData = data as {
          photo_record?: Array<{ photo_url: string; photo_des: string }>;
        };
        return photoData.photo_record?.some((photo) => photo.photo_url || photo.photo_des) || false;
      }

      case 8: {
        // 環境路徑選擇
        const pathData = data as {
          improve_path_list?: Array<{ path: string; cname: string }>;
        };
        return pathData.improve_path_list?.some((path) => path.path && path.cname) || false;
      }

      case 16: {
        // 文字區域輸入
        const textData = data as { textarea: string };
        return !!(textData.textarea && textData.textarea.trim().length > 0);
      }

      case 19: {
        // 步驟8總結申論
        const essayData = data as {
          textarea_1: string;
          textarea_2: string;
          textarea_3: string;
          textarea_4: string;
          textarea_5: string;
        };
        return !!(
          essayData.textarea_1?.trim() &&
          essayData.textarea_2?.trim() &&
          essayData.textarea_3?.trim() &&
          essayData.textarea_4?.trim() &&
          essayData.textarea_5?.trim()
        );
      }

      case 21: {
        // 會議記錄（再認證）
        const renewalData = data as {
          textarea_1: string;
          textarea_2: string;
          textarea_3: string;
          textarea_4: string;
          textarea_5: string;
          textarea_6: string;
          textarea_7: string;
        };
        return !!(
          renewalData.textarea_1?.trim() ||
          renewalData.textarea_2?.trim() ||
          renewalData.textarea_3?.trim() ||
          renewalData.textarea_4?.trim() ||
          renewalData.textarea_5?.trim() ||
          renewalData.textarea_6?.trim() ||
          renewalData.textarea_7?.trim()
        );
      }

      case 11: {
        // 第四步申論（四個問題）
        const template11Data = data as {
          question_1?: string;
          question_2?: string;
          question_3?: string;
          question_4?: string;
        };
        return !!(
          template11Data.question_1?.trim() &&
          template11Data.question_2?.trim() &&
          template11Data.question_3?.trim() &&
          template11Data.question_4?.trim()
        );
      }

      case 13: {
        // 第五步申論（兩個問題）
        const template13Data = data as {
          question_1?: string;
          question_2?: string;
        };
        return !!(template13Data.question_1?.trim() && template13Data.question_2?.trim());
      }

      case 22: {
        // 教學案例整合（三個案例）
        const template22Data = data as {
          case_1?: { grade: string; course: string; activity: string };
          case_2?: { grade: string; course: string; activity: string };
          case_3?: { grade: string; course: string; activity: string };
        };
        // 至少要有一個案例的年級、課程和活動都有填寫
        return !!(
          (template22Data.case_1?.grade?.trim() && template22Data.case_1?.course?.trim() && template22Data.case_1?.activity?.trim()) ||
          (template22Data.case_2?.grade?.trim() && template22Data.case_2?.course?.trim() && template22Data.case_2?.activity?.trim()) ||
          (template22Data.case_3?.grade?.trim() && template22Data.case_3?.course?.trim() && template22Data.case_3?.activity?.trim())
        );
      }

      // 其他模板使用文字區域的邏輯
      case 7:
      case 9:
      case 10:
      case 12:
      case 14:
      case 15:
      case 17:
      case 18:
      case 20: {
        const defaultTextData = data as { textarea: string };
        return !!(defaultTextData.textarea && defaultTextData.textarea.trim().length > 0);
      }

      default:
        return false;
    }
  };

  // 檢查問題的完成狀態類型
  const getQuestionStatus = (question: FormQuestion): "completed" | "saved" | "incomplete" => {
    const isCompleted = isQuestionCompleted(question);
    const isSaved = formState.savedQuestions.has(question.id);

    if (isCompleted && isSaved) return "completed";
    if (isSaved) return "saved";
    return "incomplete";
  };

  // 計算步驟完成度（基於問題群組）
  const getStepProgress = (step: CertificationStep): { completed: number; total: number; percentage: number } => {
    if (step.questionGroups && step.questionGroups.length > 0) {
      // 使用新的群組結構計算
      const completedGroups = step.questionGroups.filter((group) => {
        const groupCompleted = group.questions.filter((q) => getQuestionStatus(q) === "completed").length;
        return groupCompleted === group.questions.length; // 群組內所有問題都完成才算群組完成
      }).length;
      const totalGroups = step.questionGroups.length;
      const percentage = totalGroups > 0 ? Math.round((completedGroups / totalGroups) * 100) : 0;
      return { completed: completedGroups, total: totalGroups, percentage };
    } else {
      // 向下兼容：使用原有的問題結構計算
      const completed = step.questions.filter((q) => getQuestionStatus(q) === "completed").length;
      const total = step.questions.length;
      const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
      return { completed, total, percentage };
    }
  };

  // 計算群組完成度
  const getGroupProgress = (group: QuestionGroup): { completed: number; total: number; percentage: number } => {
    const completed = group.questions.filter((q) => getQuestionStatus(q) === "completed").length;
    const total = group.questions.length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    return { completed, total, percentage };
  };

  const renderFormField = (question: FormQuestion) => {
    // 初始化問題資料（如果沒有的話）
    const currentData = initializeQuestionData(question);
    const error = validationErrors[question.id];

    const fieldWrapper = (children: React.ReactNode) => (
      <div className="space-y-2">
        {children}
        {error && <p className="font-size-sm text-red-600">{error}</p>}
      </div>
    );

    // 使用新的模板系統，僅傳入基本屬性
    return fieldWrapper(
      <QuestionTemplateFactory
        templateId={question.question_tpl}
        data={currentData}
        onChange={(data) => updateFormData(question.id, data)}
        disabled={isReadonly}
      />
    );
  };

  // 移除 renderAdminActions 函數，管理員操作按鈕已直接整合到保存按鈕位置

  // 渲染群組表單（包含該群組的所有子問題）
  const renderGroupForm = (group: QuestionGroup) => {
    return (
      <div className="space-y-6">
        {/* 群組內的所有問題 */}
        {group.questions.map((question, index) => (
          <div key={question.id} className="border border-gray-200 rounded-lg p-4 bg-white">
            {/* 問題狀態指示器 */}
            <div className="flex items-center gap-2 mb-4">
              {(() => {
                const status = getQuestionStatus(question);
                if (status === "completed") {
                  return (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="font-size-sm text-green-600">已完成</span>
                    </>
                  );
                } else if (status === "saved") {
                  return (
                    <>
                      <Clock className="w-4 h-4 text-yellow-500" />
                      <span className="font-size-sm text-yellow-600">已保存</span>
                    </>
                  );
                } else {
                  return (
                    <>
                      <AlertCircle className="w-4 h-4 text-gray-400" />
                      <span className="font-size-sm text-gray-500">待填寫</span>
                    </>
                  );
                }
              })()}
              <span className="font-size-sm font-medium text-gray-700">{question.label}</span>
            </div>

            {/* 問題表單字段 */}
            {renderFormField(question)}

            {/* 保存按鈕 或 管理員操作按鈕 */}
            {!isReadonly && (
              <div className="mt-4 pt-3 border-t">
                {/* 審核中狀態且為管理員時顯示管理員操作按鈕，否則顯示保存按鈕 */}
                {isAdminViewer && applicationData.status === "verifying"
                  ? // 管理員操作按鈕
                    (() => {
                      const questionId = question.id;
                      const currentAction = adminActions[questionId];

                      return (
                        <div>
                          <div className="grid grid-cols-2 gap-2 mb-2">
                            <Button
                              size="sm"
                              variant={currentAction === "request_supplement" ? "default" : "outline"}
                              onClick={() => markAsRequestSupplement(questionId)}
                              disabled={currentAction === "request_supplement"}
                              className={`font-size-xs w-full ${
                                currentAction === "request_supplement"
                                  ? "bg-orange-500 text-white hover:bg-orange-600"
                                  : "border-orange-500 text-orange-600 hover:bg-orange-50"
                              }`}>
                              {currentAction === "request_supplement" ? "✓ 已標示待補件" : "標示待補件"}
                            </Button>

                            <Button
                              size="sm"
                              variant={currentAction === "approved" ? "default" : "outline"}
                              onClick={() => markAsApproved(questionId)}
                              disabled={currentAction === "approved"}
                              className={`font-size-xs w-full ${
                                currentAction === "approved"
                                  ? "bg-green-500 text-white hover:bg-green-600"
                                  : "border-green-500 text-green-600 hover:bg-green-50"
                              }`}>
                              {currentAction === "approved" ? "✓ 已標示審核" : "標示已審核"}
                            </Button>
                          </div>

                          {currentAction && (
                            <div className="flex items-center justify-center gap-1 mt-2">
                              <div className={`w-2 h-2 rounded-full ${currentAction === "approved" ? "bg-green-500" : "bg-orange-500"}`}></div>
                              <span className={`font-size-xs ${currentAction === "approved" ? "text-green-600" : "text-orange-600"}`}>
                                {currentAction === "approved" ? "已審核" : "待補件"}
                              </span>
                            </div>
                          )}
                        </div>
                      );
                    })()
                  : // 一般保存答案按鈕（非審核中狀態時顯示）
                    applicationData.status !== "verifying" && (
                      <SaveButton
                        data={localFormData[question.id]}
                        templateId={question.question_tpl}
                        certificationId={applicationData.id}
                        questionId={Array.isArray(question.database_sid) ? question.database_sid[0] : (question.database_sid as number)}
                        questionTitle={question.label}
                        disabled={formState.saving}
                        onSave={async (data) => {
                          const formattedData = formatAnswerForSave(question.question_tpl, data);

                          const response = await certificationAPI.saveCertificationAnswer({
                            certificationId: applicationData.id,
                            questionId: Array.isArray(question.database_sid) ? question.database_sid[0] : (question.database_sid as number),
                            answerData: formattedData as Record<string, unknown>,
                            templateId: question.question_tpl,
                          });

                          if (response.success) {
                            setFormState((prev) => ({
                              ...prev,
                              savedQuestions: new Set([...prev.savedQuestions, question.id]),
                            }));

                            // 保存成功後刷新認證狀態
                            refreshCertificationStatus();
                          } else {
                            throw new Error(response.message || "保存失敗");
                          }
                        }}
                      />
                    )}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Loading screen
  if (isLoading) {
    return (
      <main className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="font-size-lg font-medium mb-2">載入中...</h3>
            <p className="text-gray-600">正在載入認證申請資料</p>
          </CardContent>
        </Card>
      </main>
    );
  }

  // Error screen
  if (error) {
    return (
      <main className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <div className="text-red-600 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="font-size-lg font-medium mb-2 text-red-700">載入失敗</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} className="w-full">
              重新載入
            </Button>
          </CardContent>
        </Card>
      </main>
    );
  }

  // Main interface - Iframe optimized layout
  const currentStatus = statusMapping[applicationData.status];
  const certificationName = certificationTypeNames[applicationData.certificationType] || applicationData.certificationType;
  const currentQuestion = getCurrentQuestion();
  const currentGroup = getCurrentGroup();

  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex gap-4">
          {/* Left Sidebar - Navigation */}
          <div className="w-72 flex-shrink-0">
            {/* Certification Info */}
            <Card className="mb-4">
              <CardHeader className="pb-3">
                <CardTitle className="font-size-base flex items-center gap-3">
                  <img
                    src={currentStatus.icon}
                    alt={currentStatus.label}
                    className="w-16 h-16"
                    // onError={(e) => {
                    //   (e.target as HTMLImageElement).src = "/placeholder.svg";
                    // }}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = buildAssetUrl("img/placeholder.svg");
                    }}
                  />
                  <div>
                    <div className="font-size-sm">{certificationName}</div>
                    <div className="font-size-xs font-normal text-gray-500">{currentStatus.label}</div>
                    {applicationId && <div className="font-size-xs text-blue-600">申請案號: {applicationId}</div>}
                  </div>
                </CardTitle>
              </CardHeader>
            </Card>

            {/* Navigation Steps with Questions */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="font-size-sm">申請流程</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {certificationSteps.map((step, stepIndex) => (
                    <div key={step.step}>
                      {/* Step Header */}
                      <button
                        onClick={() => toggleStepExpansion(stepIndex)}
                        className={`w-full text-left px-3 py-2 flex items-center gap-2 hover:bg-gray-50 transition-colors font-size-xs ${
                          formState.currentStep === stepIndex ? "bg-blue-50 border-r-2 border-blue-500" : ""
                        }`}>
                        <div className="flex-1 min-w-0">
                          <div className={`font-medium truncate ${formState.currentStep === stepIndex ? "text-blue-700" : "text-gray-700"}`}>{step.title}</div>
                          <div className="font-size-xs text-gray-500 flex items-center gap-2">
                            {(() => {
                              const progress = getStepProgress(step);
                              return (
                                <div className="flex items-center gap-1">
                                  <div
                                    className={`w-1.5 h-1.5 rounded-full ${
                                      progress.percentage === 100 ? "bg-green-500" : progress.percentage > 0 ? "bg-yellow-500" : "bg-gray-300"
                                    }`}></div>
                                  <span
                                    className={`font-size-xs ${
                                      progress.percentage === 100 ? "text-green-600" : progress.percentage > 0 ? "text-yellow-600" : "text-gray-500"
                                    }`}>
                                    {progress.completed}/{progress.total}
                                  </span>
                                </div>
                              );
                            })()}
                          </div>
                        </div>
                        <div className="flex items-center gap-1 flex-shrink-0">
                          <span
                            className={`font-size-xs px-1.5 py-0.5 rounded-full ${
                              formState.currentStep === stepIndex ? "bg-blue-100 text-blue-700" : "bg-gray-100 text-gray-500"
                            }`}>
                            {Number.isInteger(stepIndex) ? stepIndex + 1 : 1}
                          </span>
                          {(() => {
                            const progress = getStepProgress(step);
                            return progress.percentage === 100 ? (
                              <CheckCircle className="w-3 h-3 text-green-500" />
                            ) : progress.percentage > 0 ? (
                              <Clock className="w-3 h-3 text-yellow-500" />
                            ) : (
                              <Circle className="w-3 h-3 text-gray-400" />
                            );
                          })()}
                          {formState.expandedSteps.has(stepIndex) ? <ChevronDown className="w-3 h-3" /> : <ChevronRight className="w-3 h-3" />}
                        </div>
                      </button>

                      {/* Question Groups List */}
                      {formState.expandedSteps.has(stepIndex) && (
                        <div className="bg-gray-50">
                          {step.questionGroups
                            ? // 顯示問題群組
                              step.questionGroups.map((group, groupIndex) => (
                                <button
                                  key={group.id}
                                  onClick={() => selectGroup(group.id, stepIndex)}
                                  className={`w-full text-left px-6 py-1.5 font-size-xs hover:bg-gray-100 transition-colors ${
                                    formState.currentGroupId === group.id ? "bg-white border-r-2 border-blue-400 text-blue-700" : "text-gray-600"
                                  }`}>
                                  <div className="flex items-center gap-2">
                                    {(() => {
                                      const progress = getGroupProgress(group);
                                      const isActive = formState.currentGroupId === group.id;

                                      if (progress.percentage === 100) {
                                        return <CheckCircle className="w-2.5 h-2.5 text-green-500 flex-shrink-0" />;
                                      } else if (progress.percentage > 0) {
                                        return <Clock className="w-2.5 h-2.5 text-yellow-500 flex-shrink-0" />;
                                      } else if (isActive) {
                                        return <div className="w-2.5 h-2.5 rounded-full bg-blue-400 flex-shrink-0"></div>;
                                      } else {
                                        return <AlertCircle className="w-2.5 h-2.5 text-gray-400 flex-shrink-0" />;
                                      }
                                    })()}
                                    <span className="flex-1 truncate">
                                      問題{group.displayNumber}: {group.title}
                                    </span>
                                    {(() => {
                                      const progress = getGroupProgress(group);
                                      if (progress.percentage === 100) {
                                        return (
                                          <div className="flex items-center gap-1">
                                            <div className="w-1 h-1 rounded-full bg-green-500"></div>
                                            <span className="font-size-xs text-green-600 font-medium">完成</span>
                                          </div>
                                        );
                                      } else if (progress.percentage > 0) {
                                        return (
                                          <div className="flex items-center gap-1">
                                            <div className="w-1 h-1 rounded-full bg-yellow-500"></div>
                                            <span className="font-size-xs text-yellow-600 font-medium">
                                              {progress.completed}/{progress.total}
                                            </span>
                                          </div>
                                        );
                                      }
                                      return null;
                                    })()}
                                  </div>
                                </button>
                              ))
                            : // 向下兼容：顯示個別問題
                              step.questions.map((question, questionIndex) => (
                                <button
                                  key={question.id}
                                  onClick={() => selectQuestion(question.id, stepIndex)}
                                  className={`w-full text-left px-6 py-1.5 font-size-xs hover:bg-gray-100 transition-colors ${
                                    formState.currentQuestionId === question.id ? "bg-white border-r-2 border-blue-400 text-blue-700" : "text-gray-600"
                                  }`}>
                                  <div className="flex items-center gap-2">
                                    {(() => {
                                      const status = getQuestionStatus(question);
                                      const isActive = formState.currentQuestionId === question.id;

                                      if (status === "completed") {
                                        return <CheckCircle className="w-2.5 h-2.5 text-green-500 flex-shrink-0" />;
                                      } else if (status === "saved") {
                                        return <Clock className="w-2.5 h-2.5 text-yellow-500 flex-shrink-0" />;
                                      } else if (isActive) {
                                        return <div className="w-2.5 h-2.5 rounded-full bg-blue-400 flex-shrink-0"></div>;
                                      } else {
                                        return <AlertCircle className="w-2.5 h-2.5 text-gray-400 flex-shrink-0" />;
                                      }
                                    })()}
                                    <span className="flex-1 truncate">{question.label}</span>
                                    {(() => {
                                      const status = getQuestionStatus(question);
                                      if (status === "completed") {
                                        return (
                                          <div className="flex items-center gap-1">
                                            <div className="w-1 h-1 rounded-full bg-green-500"></div>
                                            <span className="font-size-xs text-green-600 font-medium">完成</span>
                                          </div>
                                        );
                                      } else if (status === "saved") {
                                        return (
                                          <div className="flex items-center gap-1">
                                            <div className="w-1 h-1 rounded-full bg-yellow-500"></div>
                                            <span className="font-size-xs text-yellow-600 font-medium">已存</span>
                                          </div>
                                        );
                                      }
                                      return null;
                                    })()}
                                  </div>
                                </button>
                              ))}

                          {/* 評審意見區塊 */}
                          <div className="mx-3 my-2 p-3 bg-white rounded-lg border border-gray-200">
                            <div className="flex items-center gap-2 mb-2">
                              <div className="w-3 h-3 rounded-full bg-blue-100 flex items-center justify-center">
                                <span className="text-blue-600 font-size-xs">💬</span>
                              </div>
                              <span className="font-size-xs font-medium text-gray-700">評審意見</span>
                            </div>

                            {/* 顯示已存在的評審意見 */}
                            {step.reviewComments && step.reviewComments.length > 0 && (
                              <div className="space-y-2 mb-3">
                                {step.reviewComments.map((comment) => (
                                  <div
                                    key={comment.id}
                                    className={`p-2 rounded font-size-xs border-l-2 ${
                                      comment.type === "success"
                                        ? "bg-green-50 border-green-400 text-green-700"
                                        : comment.type === "warning"
                                        ? "bg-yellow-50 border-yellow-400 text-yellow-700"
                                        : comment.type === "error"
                                        ? "bg-red-50 border-red-400 text-red-700"
                                        : "bg-blue-50 border-blue-400 text-blue-700"
                                    }`}>
                                    <div className="font-medium mb-1">{comment.content}</div>
                                    <div className="flex items-center justify-between font-size-xs opacity-75">
                                      <span>{comment.author}</span>
                                      <span>{comment.date}</span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}

                            {/* 管理員評審意見輸入 */}
                            {isAdminViewer && applicationData.status === "verifying" && (
                              <div className="space-y-2">
                                <div className="border-t border-gray-200 pt-2">
                                  <div className="flex items-center gap-2 mb-2">
                                    <span className="font-size-xs text-gray-600">管理員評審意見:</span>
                                    <span className="font-size-xs text-gray-400">(最多20字)</span>
                                  </div>
                                  <div className="flex gap-2">
                                    <input
                                      type="text"
                                      value={reviewComments[`step_${step.step}`] || ""}
                                      onChange={(e) => {
                                        if (e.target.value.length <= 20) {
                                          setReviewComments((prev) => ({
                                            ...prev,
                                            [`step_${step.step}`]: e.target.value,
                                          }));
                                        }
                                      }}
                                      placeholder="請輸入評審意見..."
                                      maxLength={20}
                                      className="flex-1 px-2 py-1 border border-gray-300 rounded font-size-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                    <button
                                      onClick={() => saveReviewComment(`step_${step.step}`, reviewComments[`step_${step.step}`] || "")}
                                      disabled={
                                        savingComments[`step_${step.step}`] ||
                                        !reviewComments[`step_${step.step}`] ||
                                        reviewComments[`step_${step.step}`].trim().length === 0
                                      }
                                      className={`px-3 py-1 font-size-xs rounded text-white font-medium ${
                                        savingComments[`step_${step.step}`] ||
                                        !reviewComments[`step_${step.step}`] ||
                                        reviewComments[`step_${step.step}`].trim().length === 0
                                          ? "bg-gray-400 cursor-not-allowed"
                                          : "bg-blue-500 hover:bg-blue-600"
                                      }`}>
                                      {savingComments[`step_${step.step}`] ? "保存中..." : "保存"}
                                    </button>
                                  </div>
                                  <div className="text-right mt-1">
                                    <span className="font-size-xs text-gray-400">
                                      {(reviewComments[`step_${step.step}`] || "").length}
                                      /20
                                    </span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Content - Single Question Form */}
          <div className="flex-1">
            <Card className="h-fit">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="font-size-lg">
                    {currentGroup ? `問題${currentGroup.displayNumber}: ${currentGroup.title}` : currentQuestion ? currentQuestion.label : "請選擇問題群組"}
                  </CardTitle>
                  {(currentGroup || currentQuestion) &&
                    (() => {
                      if (currentGroup) {
                        const progress = getGroupProgress(currentGroup);
                        if (progress.percentage === 100) {
                          return (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              群組已完成
                            </Badge>
                          );
                        } else if (progress.percentage > 0) {
                          return (
                            <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                              <Clock className="w-3 h-3 mr-1" />
                              進行中 ({progress.completed}/{progress.total})
                            </Badge>
                          );
                        } else {
                          return (
                            <Badge variant="outline" className="border-gray-300 text-gray-600">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              {isReadonly ? "檢視中" : "待填寫"}
                            </Badge>
                          );
                        }
                      } else if (currentQuestion) {
                        const status = getQuestionStatus(currentQuestion);
                        if (status === "completed") {
                          return (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              已完成
                            </Badge>
                          );
                        } else if (status === "saved") {
                          return (
                            <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                              <Clock className="w-3 h-3 mr-1" />
                              已儲存
                            </Badge>
                          );
                        } else {
                          return (
                            <Badge variant="outline" className="border-gray-300 text-gray-600">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              {isReadonly ? "檢視中" : "待填寫"}
                            </Badge>
                          );
                        }
                      }
                    })()}
                </div>
                {(currentGroup || currentQuestion) && (
                  <p className="font-size-sm text-gray-600">
                    {certificationSteps[formState.currentStep]?.title}
                    {currentGroup && <span className="ml-2">• 包含 {currentGroup.questions.length} 個子問題</span>}
                  </p>
                )}
              </CardHeader>
              <CardContent>
                {currentGroup ? (
                  // 顯示群組表單
                  <div className="space-y-4">
                    {renderGroupForm(currentGroup)}

                    {/* 群組保存按鈕 - Hidden in readonly mode */}
                    {!isReadonly && (
                      <div className="flex justify-between items-center pt-3 border-t">
                        <div className="font-size-sm text-gray-500">
                          {(() => {
                            const progress = getGroupProgress(currentGroup);
                            if (progress.percentage === 100) {
                              return (
                                <div className="flex items-center gap-2 text-green-600">
                                  <CheckCircle className="w-4 h-4" />
                                  <span>此群組所有問題已完成填寫</span>
                                </div>
                              );
                            } else if (progress.percentage > 0) {
                              return (
                                <div className="flex items-center gap-2 text-yellow-600">
                                  <Clock className="w-4 h-4" />
                                  <span>
                                    已完成 {progress.completed}/{progress.total} 個問題
                                  </span>
                                </div>
                              );
                            }
                          })()}
                        </div>
                      </div>
                    )}
                  </div>
                ) : currentQuestion ? (
                  // 向下兼容：顯示個別問題表單
                  <div className="space-y-4">
                    {/* Question Description */}
                    {currentQuestion.placeholder && (
                      <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                        <p className="text-blue-700 font-size-sm">{currentQuestion.placeholder}</p>
                      </div>
                    )}

                    {/* Form Field */}
                    {renderFormField(currentQuestion)}

                    {/* Status and Save Button 或 管理員操作按鈕 */}
                    {!isReadonly && (
                      <div className="space-y-3 pt-3 border-t">
                        {/* Status Display */}
                        <div className="font-size-sm text-gray-500">
                          {(() => {
                            const status = getQuestionStatus(currentQuestion);

                            if (status === "completed") {
                              return (
                                <div className="flex items-center gap-2 text-green-600">
                                  <CheckCircle className="w-4 h-4" />
                                  <span>此問題已完成填寫</span>
                                </div>
                              );
                            } else if (status === "saved") {
                              return (
                                <div className="flex items-center gap-2 text-yellow-600">
                                  <Clock className="w-4 h-4" />
                                  <span>已儲存，請繼續完善內容</span>
                                </div>
                              );
                            } else {
                              return (
                                <div className="flex items-center gap-2 text-gray-600">
                                  <AlertCircle className="w-4 h-4" />
                                  <span>請填寫必要內容後儲存</span>
                                </div>
                              );
                            }
                          })()}
                        </div>

                        {/* 審核中狀態且為管理員時顯示管理員操作按鈕，否則顯示保存按鈕 */}
                        {isAdminViewer && applicationData.status === "verifying"
                          ? // 管理員操作按鈕
                            (() => {
                              const questionId = currentQuestion.id;
                              const currentAction = adminActions[questionId];

                              return (
                                <div>
                                  <div className="grid grid-cols-2 gap-2 mb-2">
                                    <Button
                                      size="sm"
                                      variant={currentAction === "request_supplement" ? "default" : "outline"}
                                      onClick={() => markAsRequestSupplement(questionId)}
                                      disabled={currentAction === "request_supplement"}
                                      className={`font-size-xs w-full ${
                                        currentAction === "request_supplement"
                                          ? "bg-orange-500 text-white hover:bg-orange-600"
                                          : "border-orange-500 text-orange-600 hover:bg-orange-50"
                                      }`}>
                                      {currentAction === "request_supplement" ? "✓ 已標示待補件" : "標示待補件"}
                                    </Button>

                                    <Button
                                      size="sm"
                                      variant={currentAction === "approved" ? "default" : "outline"}
                                      onClick={() => markAsApproved(questionId)}
                                      disabled={currentAction === "approved"}
                                      className={`font-size-xs w-full ${
                                        currentAction === "approved"
                                          ? "bg-green-500 text-white hover:bg-green-600"
                                          : "border-green-500 text-green-600 hover:bg-green-50"
                                      }`}>
                                      {currentAction === "approved" ? "✓ 已標示審核" : "標示已審核"}
                                    </Button>
                                  </div>

                                  {currentAction && (
                                    <div className="flex items-center justify-center gap-1 mt-2">
                                      <div className={`w-2 h-2 rounded-full ${currentAction === "approved" ? "bg-green-500" : "bg-orange-500"}`}></div>
                                      <span className={`font-size-xs ${currentAction === "approved" ? "text-green-600" : "text-orange-600"}`}>
                                        {currentAction === "approved" ? "已審核" : "待補件"}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              );
                            })()
                          : // 一般保存答案按鈕（非審核中狀態時顯示）
                            applicationData.status !== "verifying" && (
                              <SaveButton
                                data={localFormData[currentQuestion.id]}
                                templateId={currentQuestion.question_tpl}
                                certificationId={applicationData.id}
                                questionId={
                                  Array.isArray(currentQuestion.database_sid) ? currentQuestion.database_sid[0] : (currentQuestion.database_sid as number)
                                }
                                questionTitle={currentQuestion.label}
                                disabled={formState.saving}
                                onSave={async (data) => {
                                  const formattedData = formatAnswerForSave(currentQuestion.question_tpl, data);

                                  const response = await certificationAPI.saveCertificationAnswer({
                                    certificationId: applicationData.id,
                                    questionId: Array.isArray(currentQuestion.database_sid)
                                      ? currentQuestion.database_sid[0]
                                      : (currentQuestion.database_sid as number),
                                    answerData: formattedData as Record<string, unknown>,
                                    templateId: currentQuestion.question_tpl,
                                  });

                                  if (response.success) {
                                    setFormState((prev) => ({
                                      ...prev,
                                      savedQuestions: new Set([...prev.savedQuestions, currentQuestion.id]),
                                    }));

                                    // 保存成功後刷新認證狀態
                                    refreshCertificationStatus();
                                  } else {
                                    throw new Error(response.message || "保存失敗");
                                  }
                                }}
                              />
                            )}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <h3 className="font-size-lg font-medium mb-2">請選擇問題群組</h3>
                    <p className="font-size-sm">
                      從左側導航中選擇一個問題群組
                      {isReadonly ? "檢視" : "開始填寫"}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  );
};

export default CertificationApplicationIframePage;
