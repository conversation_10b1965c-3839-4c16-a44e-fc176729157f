
import React, { useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload } from "lucide-react";

interface LogoUploadProps {
  logoPreview: string | null;
  onLogoChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const LogoUpload: React.FC<LogoUploadProps> = ({ logoPreview, onLogoChange }) => {
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleButtonClick = () => {
    inputRef.current?.click();
  };

  return (
    <div className="flex flex-col items-start gap-2">
      <input
        ref={inputRef}
        type="file"
        id="logo"
        accept="image/png, image/jpeg"
        className="hidden"
        onChange={onLogoChange}
      />
      <Button
        type="button"
        variant="default"
        className="!bg-primary text-white font-bold px-8 py-2 rounded-full shadow-lg font-size-base"
        onClick={handleButtonClick}
        aria-label="上傳校徽"
      >
        <Upload className="mr-2" /> 上傳校徽
      </Button>
      <span className="text-muted-foreground font-size-xs">
        僅限上傳jpg、jpeg、png格式，尺寸150x150px
      </span>
      <div className="w-[90px] h-[90px] border rounded mt-2 bg-gray-50 flex items-center justify-center overflow-hidden">
        {logoPreview ? (
          <img src={logoPreview} alt="logo 預覽" className="object-contain w-full h-full" />
        ) : (
          <Upload size={40} className="text-gray-300" />
        )}
      </div>
    </div>
  );
};

export default LogoUpload;
