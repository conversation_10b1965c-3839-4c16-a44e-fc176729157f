import React from "react";
import { ExternalLink } from "lucide-react";
import { getGuidanceLinks, openExternalLink } from "@/config/externalLinks";

const GuidanceApplyPage = () => {
  const handleExternalLink = () => {
    const guidanceLinks = getGuidanceLinks();
    openExternalLink(guidanceLinks.applicationForm, "guidanceApplication");
  };

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-start py-12">
      <section
        className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10"
        aria-label="輔導申請說明"
      >
        <h1
          className="font-size-3xl font-bold text-primary mb-8 text-center"
          tabIndex={0}
        >
          輔導申請說明
        </h1>

        <div className="text-left text-gray-700 space-y-6 leading-relaxed">
          {/* 主要說明 */}
          <p className="font-size-base">
            如果您與學生團隊在執行計畫上有任何困難，我們有專業的輔導團隊將可以協助您克服難關唷！另針對生態學校到校輔導方式可分為電話諮詢與實際到校輔導兩種方式。
          </p>

          {/* 輔導方式說明 */}
          <div className="space-y-4">
            <div>
              <p className="font-size-base">
                <span className="font-semibold">1. 電話諮詢</span>
                ：輔導人員直接或間接獲得學校諮詢電話需求，依據受輔導之學校所提之問題，以電話聯繫之方式進行問題之瞭解。一般來說，電話諮詢可協助處理之問題包含：生態學校認證系統之操作、認證申請應準備之資料、認證流程等。
              </p>
            </div>

            <div>
              <p className="font-size-base">
                <span className="font-semibold">2. 到校輔導</span>
                ：當輔導人員經電話諮詢後，研判需進行到校輔導時，應先回報環保署委辦單位，並可視學校需求及欲精進之領域，邀請1位專家學者或顧問、環保局人員或相關委辦單位等，共同到校進行輔導。
              </p>
            </div>
          </div>

          {/* 聯絡資訊 */}
          <div className="border-t pt-6">
            <p className="font-size-base mb-4 font-medium">
              除了輔導團隊之外，您也可以直接與本計畫團隊聯繫唷！
            </p>

            <ul className="space-y-2 font-size-base">
              <li className="flex items-center">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                陳小姐 / 02-2775-3919 #232 / <EMAIL>
              </li>
              <li className="flex items-center">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                蔡小姐 / 02-2775-3919 #350 / <EMAIL>
              </li>
              <li className="flex items-center">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                游小姐 / 02-2775-3919 #293 / <EMAIL>
              </li>
              <li className="flex items-center">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                林先生 / 02-2775-3919 #376 / <EMAIL>
              </li>
            </ul>
          </div>

          {/* 線上填單連結 */}
          <div className="border-t pt-6">
            <div className=" rounded-lg p-6 text-center">
              {/* bg-blue-50 */}
              <p className="font-size-base mb-4 font-medium text-gray-800">
                輔導申請，線上填單
              </p>
              {/* <button
                onClick={handleExternalLink}
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="開啟線上填單頁面（將在新分頁開啟）"
              >
                <ExternalLink className="w-4 h-4" />
                立即申請輔導
              </button> */}
              <a
                href="https://reurl.cc/ZGmkXA"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="開啟線上填單頁面（將在新分頁開啟）"
              >
                <ExternalLink className="w-4 h-4" />
                立即申請輔導
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default GuidanceApplyPage;
