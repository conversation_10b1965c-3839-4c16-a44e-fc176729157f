// ========== 位置相關資料模型 ==========

export interface LocationArea {
  id: number;
  name: string;
  cityId: number;
}

export interface LocationCity {
  id: number;
  name: string;
}

export interface LocationCityWithAreas {
  id: number;
  name: string;
  areas: LocationArea[];
}

// 資料庫查詢結果介面
export interface CityQueryResult {
  CountyId: number;
  Name: string;
}

export interface AreaQueryResult {
  DistrictId: number;
  Name: string;
  CountyId: number;
}

export interface HierarchyQueryResult {
  CountyId: number;
  CountyName: string;
  DistrictId?: number;
  DistrictName?: string;
}

// API 回應介面
export interface LocationCitiesResponse {
  success: boolean;
  message?: string;
  data?: LocationCity[];
  error?: string;
}

export interface LocationAreasResponse {
  success: boolean;
  message?: string;
  data?: LocationArea[];
  error?: string;
}

export interface LocationHierarchyResponse {
  success: boolean;
  message?: string;
  data?: LocationCityWithAreas[];
  error?: string;
}

// 查詢參數類型
export interface LocationCitiesParams {}

export interface LocationAreasParams {
  cityId: string;
}

export interface LocationHierarchyParams {}

export interface LocationCitiesQueryParams {}

export interface LocationAreasQueryParams {}

export interface LocationHierarchyQueryParams {}
