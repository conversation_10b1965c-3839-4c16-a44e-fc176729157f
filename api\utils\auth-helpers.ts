import { UserService } from "../services/user-service.js";
import { APILogger } from "./logger.js";

// 擴展 Request 介面以包含自定義屬性
interface ExtendedRequest extends Request {
  userToken?: string;
  user?: {
    id: string;
    account: string;
    accountId: string; // 🆕 新增：認證列表 API 需要的帳號ID
    nickName: string;
    email: string;
    roleType: string;
    permissions: string[];
    permissionGroups: string[];
    isActive: boolean;
    school?: {
      id: string;
      name: string;
      [key: string]: unknown;
    };
  };
}

// 允許的角色類型
export const ALLOWED_ROLE_TYPES = ["School", "Government", "Tutor"];

// Token 驗證結果介面
export interface TokenValidationResult {
  valid: boolean;
  user?: MemberProfile;
  message?: string;
}

// Token 提取配置
export interface TokenExtractionOptions {
  headerName?: string;
  authorizationBearer?: boolean;
  required?: boolean;
}

/**
 * 從請求中提取 Token
 * 支援多種提取方式：x-user-token header, Authorization Bearer, 自定義 header
 */
export function extractTokenFromRequest(req: ExtendedRequest, options: TokenExtractionOptions = {}): string | null {
  const { headerName = "x-user-token", authorizationBearer = true, required = false } = options;

  // 1. 檢查指定的 header
  let token = req.headers[headerName] as string;

  // 2. 如果沒有找到且啟用了 Authorization Bearer
  if (!token && authorizationBearer) {
    const authHeader = req.headers["authorization"] as string;
    if (authHeader && authHeader.startsWith("Bearer ")) {
      token = authHeader.substring(7); // 移除 "Bearer " 前綴
    }
  }

  // 3. 檢查 userToken 屬性（中間件設置）
  if (!token && req.userToken) {
    token = req.userToken;
  }

  return token || null;
}

/**
 * 統一的 Token 驗證函數
 * 整合資料庫驗證、測試 Token 和角色檢查
 */
export async function validateToken(token: string): Promise<TokenValidationResult> {
  try {
    // 1. 嘗試從資料庫驗證（支援角色映射）
    try {
      const user = await UserService.getUserByTokenWithRoleMapping(token);
      if (user) {
        // 檢查角色是否被允許
        if (!ALLOWED_ROLE_TYPES.includes(user.roleType)) {
          return {
            valid: false,
            user,
            message: `Role ${user.roleType} is not authorized to access this system`,
          };
        }

        // 檢查帳號是否啟用
        if (!user.isActive) {
          return {
            valid: false,
            user,
            message: "Account has been deactivated",
          };
        }

        return {
          valid: true,
          user,
          message: "Token validated from database with role mapping",
        };
      }
    } catch (dbError) {
      APILogger.logAuth("資料庫驗證失敗，回退到測試模式", { error: dbError }, false);
    }

    // 2. 檢查舊版相容 Token
    const legacyTokenResult = validateLegacyToken(token);
    if (legacyTokenResult.valid) {
      return legacyTokenResult;
    }

    return {
      valid: false,
      message: "Token not found in any validation source",
    };
  } catch (error) {
    APILogger.logAuth("Token 驗證發生錯誤", { error }, false);
    return {
      valid: false,
      message: "Token validation error: " + (error as Error).message,
    };
  }
}

/**
 * 舊版 Token 相容性驗證
 */
function validateLegacyToken(token: string): TokenValidationResult {
  // 固定的舊版 Token
  const LEGACY_TOKENS = {
    eco_campus_user_778_token: {
      id: "778",
      name: "使用者 778",
      email: "<EMAIL>",
      role: "school",
      permissions: ["read", "write", "certification_apply", "certification_manage"],
    },
    valid_test_token_123456789: {
      id: "778",
      name: "測試使用者 778",
      email: "<EMAIL>",
      role: "school",
      permissions: ["read", "write", "certification_apply"],
    },
  };

  const legacyUser = LEGACY_TOKENS[token as keyof typeof LEGACY_TOKENS];
  if (legacyUser) {
    const mappedRole = mapOldRoleToNew(legacyUser.role);
    if (!ALLOWED_ROLE_TYPES.includes(mappedRole)) {
      return {
        valid: false,
        message: `Legacy token role ${legacyUser.role} is not authorized`,
      };
    }

    const convertedUser: MemberProfile = {
      id: legacyUser.id,
      account: legacyUser.email,
      nickName: legacyUser.name,
      email: legacyUser.email,
      phone: undefined,
      avatar: undefined,
      roleType: mappedRole,
      isActive: true,
      createdTime: new Date(),
      updatedTime: new Date(),
      remark: "Legacy test user",
      permissions: legacyUser.permissions,
      permissionGroups: [legacyUser.role],
      school: undefined,
      certifications: [],
    };

    return { valid: true, user: convertedUser };
  }

  // 通用舊版格式檢查
  if (token.startsWith("valid_") && token.length > 10) {
    const genericUser: MemberProfile = {
      id: "778",
      account: "<EMAIL>",
      nickName: "測試使用者",
      email: "<EMAIL>",
      phone: undefined,
      avatar: undefined,
      roleType: "School",
      isActive: true,
      createdTime: new Date(),
      updatedTime: new Date(),
      remark: "Generic test user",
      permissions: ["read", "write"],
      permissionGroups: ["school"],
      school: undefined,
      certifications: [],
    };

    return { valid: true, user: genericUser };
  }

  return { valid: false, message: "Not a legacy token" };
}

/**
 * 撤銷無效 Token 並記錄
 */
export async function revokeInvalidToken(token: string, reason: string): Promise<void> {
  try {
    await revokeUserToken(token);
  } catch (error) {
    APILogger.logAuth(`撤銷 Token 失敗: ${reason}`, { error }, false);
  }
}

/**
 * 將 MemberProfile 轉換為 Request.user 格式
 */
export function convertMemberProfileToRequestUser(profile: MemberProfile): NonNullable<ExtendedRequest["user"]> {
  return {
    id: profile.id,
    account: profile.account,
    accountId: profile.id, // 🆕 新增：確保 accountId 被設置，用於認證列表 API
    nickName: profile.nickName || "",
    email: profile.email || "",
    roleType: profile.roleType,
    permissions: profile.permissions,
    permissionGroups: profile.permissionGroups,
    isActive: profile.isActive,
    school: profile.school,
  };
}

/**
 * 角色映射：舊格式 → 新格式
 */
export function mapOldRoleToNew(oldRole: string): string {
  switch (oldRole.toLowerCase()) {
    case "school":
      return "School";
    case "epa":
    case "government":
      return "Government";
    case "tutor":
      return "Tutor";
    default:
      return "School";
  }
}

/**
 * 角色映射：新格式 → 前端格式
 */
export function mapRoleTypeToFrontendRole(roleType: string): string {
  switch (roleType) {
    case "School":
      return "school";
    case "Government":
      return "epa";
    case "Tutor":
      return "tutor";
    default:
      return "school";
  }
}

/**
 * 檢查用戶是否有特定權限
 */
export async function checkUserPermission(user: MemberProfile, permission: string): Promise<boolean> {
  // 1. 檢查內存權限
  const hasMemoryPermission =
    user.permissions.includes(permission) || user.permissionGroups.some((group) => group.toLowerCase().includes(permission.toLowerCase()));

  if (hasMemoryPermission) {
    return true;
  }

  // 2. 資料庫權限檢查
  try {
    const { checkUserPermission: dbCheckPermission } = await import("../config/database-mssql.js");
    return await dbCheckPermission(user.id, permission);
  } catch (error) {
    APILogger.logAuth("權限檢查失敗", { userId: user.id, permission, error }, false);
    return false;
  }
}

/**
 * 檢查用戶是否有指定角色
 */
export function checkUserRole(user: MemberProfile, allowedRoles: string | string[]): boolean {
  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  return roles.includes(user.roleType);
}
