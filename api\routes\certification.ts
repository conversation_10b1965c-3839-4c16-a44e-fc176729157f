import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import { CertificationService } from "../services/certification-service.js";
import {
  CertificationListQueryParams,
  CertificationAvailabilityQueryParams,
  CertificationDetailParams,
  CertificationCreateParams,
  CertificationUpdateParams,
  CertificationDeleteParams,
  CertificationListResponse,
  CertificationAvailabilityResponse,
  CertificationDetailResponse,
  CertificationCreateResponse,
  CertificationUpdateResponse,
  CertificationDeleteResponse,
  CertificationRequest,
  CertificationQueryResult,
  Certification,
  CertificationDetailRequest,
} from "../models/certification.js";
import { CERTIFICATION_ERROR_MESSAGES, CERTIFICATION_SUCCESS_MESSAGES } from "../constants/certification.js";

const router = express.Router();

// 獲取認證清單
router.get(
  "/list",
  authenticateToken,
  async (req: express.Request<{}, CertificationListResponse, {}, CertificationListQueryParams>, res: express.Response<CertificationListResponse>) => {
    try {
      const accountId = parseInt(req.user?.accountId as string);

      if (!accountId) {
        return res.status(401).json({
          success: false,
          data: { all: [], drafts: [], pending: [], passed: [], statistics: { total: 0, drafts: 0, pending: 0, passed: 0, inReview: 0, returned: 0 } },
          message: CERTIFICATION_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
        });
      }

      const data = await CertificationService.getCertificationList(accountId);

      res.json({
        success: true,
        data,
        message: CERTIFICATION_SUCCESS_MESSAGES.LIST_RETRIEVED,
      });
    } catch (error: unknown) {
      console.error("❌ [API] 獲取認證清單失敗:", error);
      res.status(500).json({
        success: false,
        data: { all: [], drafts: [], pending: [], passed: [], statistics: { total: 0, drafts: 0, pending: 0, passed: 0, inReview: 0, returned: 0 } },
        message: "獲取認證失敗",
      });
    }
  }
);

// 檢查認證可用性
router.get(
  "/availability",
  authenticateToken,
  async (
    req: express.Request<{}, CertificationAvailabilityResponse, {}, CertificationAvailabilityQueryParams>,
    res: express.Response<CertificationAvailabilityResponse>
  ) => {
    try {
      const accountId = parseInt(req.user?.accountId as string);

      if (!accountId) {
        return res.status(401).json({
          success: false,
          data: { availability: [], hasPassedGreenFlag: false, greenFlagApprovedYearsAgo: 0, greenFlagR1ApprovedYearsAgo: 0, greenFlagR2ApprovedYearsAgo: 0 },
          message: CERTIFICATION_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
        });
      }

      const data = await CertificationService.checkCertificationAvailability(accountId);

      res.json({
        success: true,
        data,
        message: CERTIFICATION_SUCCESS_MESSAGES.AVAILABILITY_CHECKED,
      });
    } catch (error: unknown) {
      console.error("❌ [API] 檢查認證可用性失敗:", error);

      if (error instanceof Error && error.message === CERTIFICATION_ERROR_MESSAGES.SCHOOL_NOT_ASSIGNED) {
        return res.status(400).json({
          success: false,
          data: { availability: [], hasPassedGreenFlag: false, greenFlagApprovedYearsAgo: 0, greenFlagR1ApprovedYearsAgo: 0, greenFlagR2ApprovedYearsAgo: 0 },
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        data: { availability: [], hasPassedGreenFlag: false, greenFlagApprovedYearsAgo: 0, greenFlagR1ApprovedYearsAgo: 0, greenFlagR2ApprovedYearsAgo: 0 },
        message: "檢查認證可用性失敗",
      });
    }
  }
);

// 獲取單個認證資訊
router.get(
  "/:certificationId",
  authenticateToken,
  async (req: express.Request<{}, CertificationDetailResponse, CertificationDetailRequest>, res: express.Response<CertificationDetailResponse>) => {
    try {
      const { certificationId } = req.params as CertificationDetailRequest;

      const certification = await CertificationService.getCertificationById(certificationId);

      if (!certification) {
        return res.status(404).json({
          success: false,
          data: {} as Certification,
          message: CERTIFICATION_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND,
        });
      }

      res.json({
        success: true,
        data: certification,
      });
    } catch (error) {
      console.error("❌ [API] 獲取認證失敗:", error);
      res.status(500).json({
        success: false,
        data: {} as Certification,
        message: "獲取認證失敗",
      });
    }
  }
);

// 建立新認證
router.post(
  "/create",
  authenticateToken,
  async (
    req: express.Request<CertificationCreateParams, CertificationCreateResponse, CertificationRequest>,
    res: express.Response<CertificationCreateResponse>
  ) => {
    try {
      const { certificationType, level } = req.body;
      const accountId = parseInt(req.user?.accountId as string);

      if (!accountId) {
        return res.status(401).json({
          success: false,
          data: {} as Certification,
          message: CERTIFICATION_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
        });
      }

      if (!certificationType || !level) {
        return res.status(400).json({
          success: false,
          data: {} as Certification,
          message: CERTIFICATION_ERROR_MESSAGES.INVALID_CERTIFICATION_TYPE,
        });
      }

      const certification = await CertificationService.createCertification(accountId, { certificationType, level });

      res.json({
        success: true,
        data: certification,
        message: CERTIFICATION_SUCCESS_MESSAGES.CERTIFICATION_CREATED,
      });
    } catch (error: unknown) {
      console.error("❌ [API] 建立認證失敗:", error);

      if (error instanceof Error) {
        if (error.message === CERTIFICATION_ERROR_MESSAGES.SCHOOL_NOT_ASSIGNED) {
          return res.status(400).json({
            success: false,
            data: {} as Certification,
            message: error.message,
          });
        }

        if (error.message === CERTIFICATION_ERROR_MESSAGES.DUPLICATE_CERTIFICATION) {
          return res.status(400).json({
            success: false,
            data: {} as Certification,
            message: error.message,
          });
        }

        if (error.message === CERTIFICATION_ERROR_MESSAGES.GREEN_FLAG_REQUIRED) {
          return res.status(400).json({
            success: false,
            data: {} as Certification,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        data: {} as Certification,
        message: "建立認證失敗",
      });
    }
  }
);

// 更新認證
router.put(
  "/:certificationId",
  authenticateToken,
  async (req: express.Request<{ certificationId: string }, CertificationUpdateResponse>, res: express.Response<CertificationUpdateResponse>) => {
    try {
      const { certificationId } = req.params;
      const accountId = parseInt(req.user?.accountId as string);
      const updateData = req.body;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          data: {} as Certification,
          message: CERTIFICATION_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
        });
      }

      const certification = await CertificationService.updateCertification(parseInt(certificationId), accountId, updateData);

      res.json({
        success: true,
        data: certification,
        message: CERTIFICATION_SUCCESS_MESSAGES.CERTIFICATION_UPDATED,
      });
    } catch (error: unknown) {
      console.error("❌ [API] 更新認證失敗:", error);

      if (error instanceof Error) {
        if (error.message === CERTIFICATION_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND) {
          return res.status(404).json({
            success: false,
            data: {} as Certification,
            message: error.message,
          });
        }

        if (error.message === CERTIFICATION_ERROR_MESSAGES.INVALID_PERMISSIONS) {
          return res.status(403).json({
            success: false,
            data: {} as Certification,
            message: error.message,
          });
        }

        res.status(500).json({
          success: false,
          data: {} as Certification,
          message: "更新認證失敗",
        });
      }
    }
  }
);

// 刪除認證
router.delete(
  "/:certificationId",
  authenticateToken,
  async (req: express.Request<{ certificationId: string }, CertificationDeleteResponse>, res: express.Response<CertificationDeleteResponse>) => {
    try {
      const { certificationId } = req.params;
      const accountId = parseInt(req.user?.accountId as string);

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: CERTIFICATION_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
        });
      }

      await CertificationService.deleteCertification(parseInt(certificationId), accountId);

      res.json({
        success: true,
        message: CERTIFICATION_SUCCESS_MESSAGES.CERTIFICATION_DELETED,
      });
    } catch (error: Error | unknown) {
      console.error("❌ [API] 刪除認證失敗:", error);

      if (error instanceof Error) {
        if (error.message === CERTIFICATION_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND) {
          return res.status(404).json({
            success: false,
            message: error.message,
          });
        }

        if (error.message === CERTIFICATION_ERROR_MESSAGES.INVALID_PERMISSIONS) {
          return res.status(403).json({
            success: false,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: "刪除認證失敗",
      });
    }
  }
);

export default router;
