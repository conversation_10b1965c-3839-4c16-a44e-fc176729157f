/**
 * 統一用戶資料服務
 * 提供一致的用戶資料存取介面，整合現有的資料庫查詢功能
 * 
 * @description 統一各種用戶資料查詢方式，提供快取支援和錯誤處理
 * @version 1.0.0
 */

import { 
  IUserService, 
  UserQueryOptions, 
  UserQueryResult, 
  UserListOptions, 
  UserListResult,
  UserUpdateData, 
  UserUpdateResult 
} from '../interfaces/IUserService';
import { MemberProfile } from '../interfaces/IAuthService';
import { normalizeRole, isValidRole, getRoleDisplayName } from '../constants/roles';

/**
 * 用戶資料服務實作類別
 * 實作 IUserService 介面，提供完整的用戶資料管理功能
 */
export class UserDataService implements IUserService {
  private static instance: UserDataService;
  private cache: Map<string, { data: MemberProfile; timestamp: number; ttl: number }> = new Map();

  /**
   * 單例模式：取得服務實例
   * 
   * @returns UserDataService 服務實例
   */
  static getInstance(): UserDataService {
    if (!UserDataService.instance) {
      UserDataService.instance = new UserDataService();
    }
    return UserDataService.instance;
  }

  /**
   * 取得目前認證用戶資料
   * 基於 Token 取得用戶資訊，是最常用的用戶查詢方法
   * 
   * @param token 用戶認證 Token
   * @param options 查詢選項
   * @returns Promise<UserQueryResult> 查詢結果
   * 
   * @example
   * ```typescript
   * const userService = UserDataService.getInstance();
   * const result = await userService.getCurrentUser(token, {
   *   includeSchoolInfo: true,
   *   cacheTimeout: 300 // 快取 5 分鐘
   * });
   * 
   * if (result.success && result.user) {
   *   console.log(`歡迎 ${result.user.name}！`);
   *   console.log(`角色: ${result.user.roleType}`);
   * }
   * ```
   */
  async getCurrentUser(token: string, options: UserQueryOptions = {}): Promise<UserQueryResult> {
    const timestamp = new Date();
    
    try {
      // 參數驗證
      if (!token || typeof token !== 'string' || !token.trim()) {
        return {
          success: false,
          message: '無效的 Token 參數',
          timestamp
        };
      }

      // 檢查快取
      if (options.cacheTimeout && options.cacheTimeout > 0) {
        const cachedResult = this.getCachedUser(`token:${token}`, options.cacheTimeout);
        if (cachedResult) {
          return {
            success: true,
            user: cachedResult,
            source: 'cache',
            timestamp,
            fromCache: true
          };
        }
      }

      // 調用統一的 Token 查詢方法
      const result = await this.getUserByToken(token, options);
      
      // 快取結果（如果成功且有設定快取時間）
      if (result.success && result.user && options.cacheTimeout && options.cacheTimeout > 0) {
        this.setCachedUser(`token:${token}`, result.user, options.cacheTimeout);
      }

      return {
        ...result,
        timestamp
      };

    } catch (error) {
      console.error('取得目前用戶資料失敗:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知錯誤',
        timestamp
      };
    }
  }

  /**
   * 根據用戶 ID 取得用戶資料
   * 直接通過用戶 ID 查詢，適合管理功能使用
   * 
   * @param id 用戶 ID（字串或數字）
   * @param options 查詢選項
   * @returns Promise<UserQueryResult> 查詢結果
   * 
   * @example
   * ```typescript
   * const result = await userService.getUserById('123', {
   *   includeProfile: true,
   *   checkAccountStatus: true
   * });
   * 
   * if (result.success && result.user) {
   *   console.log(`用戶資料: ${result.user.name} (${result.user.email})`);
   * } else {
   *   console.error(`查詢失敗: ${result.message}`);
   * }
   * ```
   */
  async getUserById(id: string | number, options: UserQueryOptions = {}): Promise<UserQueryResult> {
    const timestamp = new Date();
    
    try {
      // 參數驗證
      if (id === null || id === undefined || (typeof id === 'string' && !id.trim())) {
        return {
          success: false,
          message: '無效的用戶 ID 參數',
          timestamp
        };
      }

      // 轉換 ID 格式
      const userId = typeof id === 'number' ? id : String(id).trim();

      // 檢查快取
      if (options.cacheTimeout && options.cacheTimeout > 0) {
        const cachedResult = this.getCachedUser(`id:${userId}`, options.cacheTimeout);
        if (cachedResult) {
          return {
            success: true,
            user: cachedResult,
            source: 'cache',
            timestamp,
            fromCache: true
          };
        }
      }

      // 調用資料庫查詢
      const user = await this.queryUserById(userId, options);
      
      if (!user) {
        return {
          success: false,
          message: `找不到 ID 為 ${userId} 的用戶`,
          timestamp
        };
      }

      // 處理和驗證用戶資料
      const processedUser = this.processUserData(user, options);

      // 快取結果
      if (options.cacheTimeout && options.cacheTimeout > 0) {
        this.setCachedUser(`id:${userId}`, processedUser, options.cacheTimeout);
      }

      return {
        success: true,
        user: processedUser,
        source: 'database',
        timestamp
      };

    } catch (error) {
      console.error(`根據 ID 取得用戶資料失敗 (ID: ${id}):`, error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '查詢用戶資料時發生錯誤',
        timestamp
      };
    }
  }

  /**
   * 根據 Token 取得用戶資料
   * 整合現有的 getUserByTokenWithRoleMapping 功能
   * 
   * @param token 用戶認證 Token
   * @param options 查詢選項
   * @returns Promise<UserQueryResult> 查詢結果
   * 
   * @example
   * ```typescript
   * const result = await userService.getUserByToken(userToken, {
   *   includeRoleMapping: true,
   *   includeSchoolInfo: true,
   *   checkAccountStatus: true
   * });
   * ```
   */
  async getUserByToken(token: string, options: UserQueryOptions = {}): Promise<UserQueryResult> {
    const timestamp = new Date();
    
    try {
      // 參數驗證
      if (!token || typeof token !== 'string' || !token.trim()) {
        return {
          success: false,
          message: 'Token 參數不能為空',
          timestamp
        };
      }

      const trimmedToken = token.trim();

      // 調用現有的資料庫查詢方法
      const user = await this.queryUserByToken(trimmedToken, options);
      
      if (!user) {
        return {
          success: false,
          message: '找不到對應的用戶資料',
          timestamp
        };
      }

      // 處理和驗證用戶資料
      const processedUser = this.processUserData(user, options);

      // 額外的帳號狀態檢查
      if (options.checkAccountStatus !== false) {
        if (!processedUser.isActive) {
          return {
            success: false,
            user: processedUser,
            message: '此帳號已被停用',
            timestamp
          };
        }
      }

      return {
        success: true,
        user: processedUser,
        source: 'database',
        timestamp
      };

    } catch (error) {
      console.error('根據 Token 取得用戶資料失敗:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '查詢用戶資料時發生錯誤',
        timestamp
      };
    }
  }

  /**
   * 根據電子郵件取得用戶資料
   * 
   * @param email 電子郵件地址
   * @param options 查詢選項
   * @returns Promise<UserQueryResult> 查詢結果
   */
  async getUserByEmail(email: string, options: UserQueryOptions = {}): Promise<UserQueryResult> {
    const timestamp = new Date();
    
    try {
      if (!email || typeof email !== 'string' || !email.trim()) {
        return {
          success: false,
          message: '無效的電子郵件參數',
          timestamp
        };
      }

      const trimmedEmail = email.trim().toLowerCase();

      // 基本郵件格式驗證
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(trimmedEmail)) {
        return {
          success: false,
          message: '電子郵件格式不正確',
          timestamp
        };
      }

      // TODO: 實作資料庫查詢邏輯
      // 這裡需要新的資料庫查詢方法
      console.log(`查詢電子郵件 ${trimmedEmail} 的用戶資料（功能尚未實作）`);
      
      return {
        success: false,
        message: '根據電子郵件查詢功能尚未實作',
        timestamp
      };

    } catch (error) {
      console.error('根據電子郵件取得用戶資料失敗:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '查詢失敗',
        timestamp
      };
    }
  }

  /**
   * 檢查用戶是否存在
   * 
   * @param id 用戶 ID
   * @returns Promise<boolean> 是否存在
   */
  async userExists(id: string | number): Promise<boolean> {
    try {
      const result = await this.getUserById(id, { cacheTimeout: 60 }); // 快取 1 分鐘
      return result.success;
    } catch (error) {
      console.error('檢查用戶是否存在時發生錯誤:', error);
      return false;
    }
  }

  /**
   * 取得用戶統計資訊
   * 
   * @returns Promise<UserStats> 統計資訊
   */
  async getUserStats() {
    try {
      // TODO: 實作統計查詢
      return {
        totalUsers: 0,
        activeUsers: 0,
        usersByRole: {},
        recentLogins: 0
      };
    } catch (error) {
      console.error('取得用戶統計資訊失敗:', error);
      throw error;
    }
  }

  /**
   * 搜尋用戶列表
   * 
   * @param searchOptions 搜尋選項
   * @returns Promise<UserListResult> 搜尋結果
   */
  async searchUsers(searchOptions: UserListOptions): Promise<UserListResult> {
    const timestamp = new Date();
    
    try {
      // TODO: 實作搜尋邏輯
      return {
        success: false,
        message: '搜尋用戶功能尚未實作',
        timestamp
      };
    } catch (error) {
      console.error('搜尋用戶失敗:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '搜尋失敗',
        timestamp
      };
    }
  }

  /**
   * 更新用戶資料
   * 
   * @param id 用戶 ID
   * @param updateData 要更新的資料
   * @returns Promise<UserUpdateResult> 更新結果
   */
  async updateUser(id: string | number, updateData: UserUpdateData): Promise<UserUpdateResult> {
    const timestamp = new Date();
    
    try {
      // TODO: 實作更新邏輯
      return {
        success: false,
        message: '更新用戶功能尚未實作',
        timestamp
      };
    } catch (error) {
      console.error('更新用戶資料失敗:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '更新失敗',
        timestamp
      };
    }
  }

  /**
   * 私有方法：調用資料庫查詢用戶（根據 Token）
   */
  private async queryUserByToken(token: string, options: UserQueryOptions): Promise<any> {
    try {
      // 動態載入資料庫模組，避免循環依賴
      const { getUserByTokenWithRoleMapping } = require('../../api/config/database-mssql');
      
      const user = await getUserByTokenWithRoleMapping(token);
      
      console.log(`根據 Token 查詢用戶: ${token.substring(0, 8)}... -> ${user ? user.name || user.account : 'null'}`);
      
      return user;
    } catch (error) {
      console.error('資料庫查詢用戶（Token）失敗:', error);
      throw error;
    }
  }

  /**
   * 私有方法：調用資料庫查詢用戶（根據 ID）
   */
  private async queryUserById(id: string | number, options: UserQueryOptions): Promise<any> {
    try {
      // 動態載入資料庫模組
      const { getUserByAccountId } = require('../../api/config/database-mssql');
      
      const user = await getUserByAccountId(id);
      
      console.log(`根據 ID 查詢用戶: ${id} -> ${user ? user.name || user.account : 'null'}`);
      
      return user;
    } catch (error) {
      console.error('資料庫查詢用戶（ID）失敗:', error);
      throw error;
    }
  }

  /**
   * 私有方法：處理和標準化用戶資料
   */
  private processUserData(rawUser: any, options: UserQueryOptions): MemberProfile {
    try {
      // 正規化角色
      const normalizedRole = normalizeRole(rawUser.roleType);
      
      // 建立標準化的用戶資料
      const processedUser: MemberProfile = {
        accountId: rawUser.accountId || rawUser.id,
        name: rawUser.name || rawUser.account || '未知用戶',
        email: rawUser.email || '',
        phone: rawUser.phone,
        roleType: normalizedRole || 'unknown',
        originalRoleType: rawUser.roleType,
        isActive: Boolean(rawUser.isActive),
        schoolId: rawUser.schoolId,
        schoolName: rawUser.schoolName,
        countyCode: rawUser.countyCode,
        countyName: rawUser.countyName,
        createdAt: rawUser.createdAt ? new Date(rawUser.createdAt) : undefined,
        updatedAt: rawUser.updatedAt ? new Date(rawUser.updatedAt) : undefined,
        lastLoginAt: rawUser.lastLoginAt ? new Date(rawUser.lastLoginAt) : new Date(),
        isSystemAdmin: Boolean(rawUser.isSystemAdmin || rawUser.isAdmin)
      };

      // 根據選項包含額外資訊
      if (options.includeProfile && rawUser.profile) {
        processedUser.profile = rawUser.profile;
      }

      console.log(`處理用戶資料: ${processedUser.name} (${processedUser.roleType})`);
      
      return processedUser;
    } catch (error) {
      console.error('處理用戶資料時發生錯誤:', error);
      throw new Error('用戶資料處理失敗');
    }
  }

  /**
   * 私有方法：取得快取的用戶資料
   */
  private getCachedUser(cacheKey: string, maxAge: number): MemberProfile | null {
    try {
      const cached = this.cache.get(cacheKey);
      if (!cached) return null;

      const now = Date.now();
      const isExpired = (now - cached.timestamp) > (cached.ttl * 1000);
      
      if (isExpired) {
        this.cache.delete(cacheKey);
        return null;
      }

      console.log(`使用快取的用戶資料: ${cacheKey}`);
      return cached.data;
    } catch (error) {
      console.warn('取得快取用戶資料失敗:', error);
      return null;
    }
  }

  /**
   * 私有方法：設定用戶資料快取
   */
  private setCachedUser(cacheKey: string, user: MemberProfile, ttl: number): void {
    try {
      this.cache.set(cacheKey, {
        data: user,
        timestamp: Date.now(),
        ttl
      });

      console.log(`設定用戶資料快取: ${cacheKey} (TTL: ${ttl}s)`);
    } catch (error) {
      console.warn('設定用戶資料快取失敗:', error);
    }
  }

  /**
   * 清除所有快取
   */
  public clearCache(): void {
    this.cache.clear();
    console.log('已清除所有用戶資料快取');
  }

  /**
   * 清除特定用戶的快取
   */
  public clearUserCache(userId: string | number): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if (key.includes(`id:${userId}`) || key.includes(`user:${userId}`)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key));
    
    console.log(`已清除用戶 ${userId} 的快取資料 (${keysToDelete.length} 項)`);
  }

  /**
   * 取得快取統計資訊
   */
  public getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      timestamp: new Date()
    };
  }
}

// 建立全域實例
export const userDataService = UserDataService.getInstance();

// 預設匯出
export default UserDataService;