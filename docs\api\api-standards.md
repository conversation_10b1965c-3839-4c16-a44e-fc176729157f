# API 端點標準化規範

## 📋 目標
統一 API 端點命名和回應格式，確保前後端整合一致性

## 🎯 標準化原則

### 1. RESTful 路由規範
```
GET    /api/{resource}           - 取得資源列表
GET    /api/{resource}/{id}      - 取得特定資源
POST   /api/{resource}           - 建立新資源
PUT    /api/{resource}/{id}      - 更新特定資源
DELETE /api/{resource}/{id}      - 刪除特定資源
```

### 2. 統一回應格式
所有 API 端點必須使用 `ApiResponse<T>` 格式：

```typescript
interface ApiResponse<T = unknown> {
  success: boolean;
  data: T | null;
  message?: string;
  errors?: Record<string, string>;
  code?: string;
  timestamp?: string;
}
```

### 3. 認證 Header 標準
- 統一使用 `x-user-token` header 傳遞認證 token
- 所有需要認證的端點都必須支援此 header

## 🔧 當前端點分析

### ✅ 已標準化端點
- `/api/auth/token-status` - 支援 x-user-token header
- `/api/auth/password-login` - 標準 POST 格式
- `/api/auth/logout` - 標準 POST 格式

### ⚠️ 需要修復的端點
1. **混合回應格式**: 某些端點回應格式不一致
2. **認證流程**: 部分端點可能不支援標準 header
3. **錯誤處理**: 錯誤回應格式需要統一

## 📊 修復優先級

### 高優先級 (立即修復)
- Auth 相關端點 - 影響所有用戶登入
- Dashboard 端點 - 影響主要功能頁面

### 中優先級 (本週完成)
- User Profile 端點
- Certification 端點

### 低優先級 (後續優化)
- File Upload 端點
- Admin 管理端點

## 🧪 測試策略

### 1. 整合測試
- 驗證 header 傳遞正確性
- 檢查回應格式一致性
- 測試錯誤處理機制

### 2. 單元測試
- API 方法正確調用
- 參數傳遞驗證
- 異常處理覆蓋

## 📝 實作檢查清單

- [x] BaseAPI.get() 支援 headers 參數
- [x] ApiResponse 介面統一定義
- [ ] 所有 auth 端點標準化驗證
- [ ] Dashboard 端點回應格式統一
- [ ] 錯誤處理標準化
- [ ] 整合測試覆蓋關鍵流程