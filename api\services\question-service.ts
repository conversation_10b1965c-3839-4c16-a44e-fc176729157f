// ========== 問題服務 - 業務邏輯和資料庫查詢 ==========

import { executeQuery, executeQuerySingle, QuestionRow } from "../config/database-mssql.js";
import {
  QuestionModel,
  StepInfo,
  FormQuestion,
  StepQuestionGroup,
  StepStatistics,
  QuestionStructureAnalysis,
  ParentGroupAnalysis,
} from "../models/question.js";
import { MAX_CERTIFICATION_STEPS, EXCLUDED_QUESTION_TEMPLATES, getStepTitle, isGreenFlagStep, QUESTION_STATUS } from "../constants/question.js";

export class QuestionService {
  // 根據步驟和認證等級獲取問題列表
  static async getQuestionsByStep(stepNumber: number, certificationLevel: number): Promise<QuestionModel[]> {
    const query = `
      SELECT 
        QuestionId,
        Title,
        ParentQuestionId,
        StepNumber,
        IsRenewed,
        SortOrder,
        QuestionTemplate,
        Status
      FROM Questions 
      WHERE StepNumber = @stepNumber
        AND Status = @status 
        AND (QuestionTemplate IS NULL OR QuestionTemplate NOT IN (${EXCLUDED_QUESTION_TEMPLATES.join(", ")}))
      ORDER BY SortOrder ASC, QuestionId ASC
    `;

    const questions = await executeQuery<QuestionRow>(query, {
      stepNumber,
      status: QUESTION_STATUS.ACTIVE,
    });

    return this.mapQuestionRowsToModels(questions);
  }

  // 獲取認證步驟資訊
  static async getCertificationSteps(certificationLevel: number): Promise<StepInfo[]> {
    const query = `
      SELECT 
        StepNumber,
        COUNT(*) as question_count
      FROM Questions 
      WHERE StepNumber BETWEEN 1 AND @max_step
        AND Status = @status 
        AND (QuestionTemplate IS NULL OR QuestionTemplate NOT IN (${EXCLUDED_QUESTION_TEMPLATES.join(", ")}))
      GROUP BY StepNumber
      ORDER BY StepNumber ASC
    `;

    const stepCounts = await executeQuery<{ StepNumber: number; question_count: number }>(query, {
      max_step: MAX_CERTIFICATION_STEPS,
      status: QUESTION_STATUS.ACTIVE,
    });

    return this.buildStepInfoList(stepCounts);
  }

  // 獲取表單問題配置（支援步驟和父問題雙層分組）
  static async getFormQuestions(certificationId?: number): Promise<{
    questions: StepQuestionGroup;
    stepInfo: StepStatistics[];
    totalQuestions: number;
  }> {
    const query = `
      SELECT 
        QuestionId,
        Title,
        ParentQuestionId,
        StepNumber,
        IsRenewed,
        SortOrder,
        QuestionTemplate,
        Status
      FROM Questions 
      WHERE Status = @status 
        AND (QuestionTemplate IS NULL OR QuestionTemplate NOT IN (${EXCLUDED_QUESTION_TEMPLATES.join(", ")}))
      ORDER BY StepNumber ASC, SortOrder ASC, ParentQuestionId ASC, QuestionId ASC
    `;

    const questions = await executeQuery<QuestionRow>(query, {
      status: QUESTION_STATUS.ACTIVE,
    });

    const groupedQuestions = this.groupQuestionsByStepAndParent(questions);
    const stepInfo = this.buildStepStatistics(questions);

    return {
      questions: groupedQuestions,
      stepInfo,
      totalQuestions: questions.length,
    };
  }

  // 獲取問題詳情
  static async getQuestionById(questionId: number): Promise<QuestionModel | null> {
    const query = `
      SELECT 
        QuestionId,
        Title,
        ParentQuestionId,
        StepNumber,
        IsRenewed,
        SortOrder,
        QuestionTemplate,
        Status,
        CreatedTime,
        UpdatedTime
      FROM Questions 
      WHERE QuestionId = @questionId 
        AND Status = @status 
        AND (QuestionTemplate IS NULL OR QuestionTemplate NOT IN (${EXCLUDED_QUESTION_TEMPLATES.join(", ")}))
    `;

    const question = await executeQuerySingle<QuestionRow>(query, {
      questionId,
      status: QUESTION_STATUS.ACTIVE,
    });

    return question ? this.mapQuestionRowToModel(question) : null;
  }

  // 分析問題結構
  static async analyzeQuestionStructure(): Promise<QuestionStructureAnalysis> {
    const query = `
      SELECT 
        QuestionId,
        Title,
        ParentQuestionId,
        StepNumber,
        IsRenewed,
        SortOrder,
        QuestionTemplate,
        Status
      FROM Questions 
      WHERE Status = @status 
        AND (QuestionTemplate IS NULL OR QuestionTemplate NOT IN (${EXCLUDED_QUESTION_TEMPLATES.join(", ")}))
      ORDER BY StepNumber ASC, SortOrder ASC, ParentQuestionId ASC
    `;

    const questions = await executeQuery<QuestionRow>(query, {
      status: QUESTION_STATUS.ACTIVE,
    });

    return this.buildStructureAnalysis(questions);
  }

  // ========== 私有輔助方法 ==========

  // 將資料庫行映射為模型
  private static mapQuestionRowsToModels(questions: QuestionRow[]): QuestionModel[] {
    return questions.map(this.mapQuestionRowToModel);
  }

  private static mapQuestionRowToModel(question: QuestionRow): QuestionModel {
    return {
      id: question.QuestionId,
      title: question.Title,
      parentId: question.ParentQuestionId,
      step: question.StepNumber || 0,
      sequence: question.SortOrder || 0,
      isRequired: true,
      templateId: question.QuestionTemplate,
      isRenewed: question.IsRenewed || false,
      status: question.Status,
      createdTime: question.CreatedTime,
      updatedTime: question.UpdatedTime,
    };
  }

  // 建立步驟資訊列表
  private static buildStepInfoList(stepCounts: Array<{ StepNumber: number; question_count: number }>): StepInfo[] {
    const steps: StepInfo[] = [];

    for (let step = 1; step <= MAX_CERTIFICATION_STEPS; step++) {
      const stepData = stepCounts.find((s) => s.StepNumber === step);
      const questionCount = stepData ? stepData.question_count : 0;

      steps.push({
        step,
        title: getStepTitle(step),
        description: "",
        questionCount,
        isEnabled: questionCount > 0,
      });
    }

    return steps;
  }

  // 按步驟和父問題分組
  private static groupQuestionsByStepAndParent(questions: QuestionRow[]): StepQuestionGroup {
    // 第一層：按步驟分組
    const stepGroups: Record<string, QuestionRow[]> = {};
    questions.forEach((question) => {
      const stepKey = (question.StepNumber || 0).toString();
      if (!stepGroups[stepKey]) {
        stepGroups[stepKey] = [];
      }
      stepGroups[stepKey].push(question);
    });

    // 第二層：在每個步驟內以根問題為基礎創建群組
    const questionsByStepAndParent: StepQuestionGroup = {};

    Object.entries(stepGroups).forEach(([stepKey, stepQuestions]) => {
      const stepNum = parseInt(stepKey);

      // 分離根問題和子問題
      const rootQuestions = stepQuestions.filter((q) => q.ParentQuestionId === null);

      // 為每個根問題創建群組
      questionsByStepAndParent[stepKey] = {};

      rootQuestions.forEach((rootQuestion) => {
        // 只處理有模板的問題
        if (!rootQuestion.QuestionTemplate || rootQuestion.QuestionTemplate === 0) {
          return;
        }

        const groupKey = rootQuestion.QuestionId.toString();

        questionsByStepAndParent[stepKey][groupKey] = [
          {
            sid: rootQuestion.QuestionId,
            title: rootQuestion.Title,
            question_tpl: rootQuestion.QuestionTemplate,
            is_renew: rootQuestion.IsRenewed ? 1 : 0,
            step: rootQuestion.StepNumber || 0,
            sequence: rootQuestion.SortOrder || 0,
            parent_id: rootQuestion.ParentQuestionId,
          },
        ];
      });

      console.log(
        `[Question Service] 步驟 ${stepKey}${isGreenFlagStep(stepNum) ? " (綠旗再認證)" : ""}: ${stepQuestions.length} 問題, ${
          rootQuestions.filter((q) => q.QuestionTemplate && q.QuestionTemplate > 0).length
        } 個根問題群組`
      );
    });

    return questionsByStepAndParent;
  }

  // 建立步驟統計資訊
  private static buildStepStatistics(questions: QuestionRow[]): StepStatistics[] {
    const stepGroups: Record<string, QuestionRow[]> = {};
    questions.forEach((question) => {
      const stepKey = (question.StepNumber || 0).toString();
      if (!stepGroups[stepKey]) {
        stepGroups[stepKey] = [];
      }
      stepGroups[stepKey].push(question);
    });

    return Object.entries(stepGroups).map(([stepKey, stepQuestions]) => {
      const stepNum = parseInt(stepKey);
      const parentGroupCount = new Set(stepQuestions.map((q) => q.ParentQuestionId || "root")).size;

      return {
        step: stepNum,
        isGreenFlag: isGreenFlagStep(stepNum),
        totalQuestions: stepQuestions.length,
        parentGroups: parentGroupCount,
      };
    });
  }

  // 建立結構分析
  private static buildStructureAnalysis(questions: QuestionRow[]): QuestionStructureAnalysis {
    // 統計分析
    const stepStats: Record<number, { total: number; withParent: number; uniqueParents: Set<number> }> = {};
    const parentGroups: Record<number, QuestionRow[]> = {};

    questions.forEach((question) => {
      const step = question.StepNumber || 0;

      // 統計步驟
      if (!stepStats[step]) {
        stepStats[step] = { total: 0, withParent: 0, uniqueParents: new Set() };
      }
      stepStats[step].total++;

      if (question.ParentQuestionId) {
        stepStats[step].withParent++;
        stepStats[step].uniqueParents.add(question.ParentQuestionId);

        // 父問題分組
        if (!parentGroups[question.ParentQuestionId]) {
          parentGroups[question.ParentQuestionId] = [];
        }
        parentGroups[question.ParentQuestionId].push(question);
      }
    });

    // 格式化統計結果
    const stepAnalysis = Object.entries(stepStats).map(([step, stats]) => {
      const stepNum = parseInt(step);
      return {
        step: stepNum,
        isGreenFlag: isGreenFlagStep(stepNum),
        total: stats.total,
        withParent: stats.withParent,
        uniqueParents: stats.uniqueParents.size,
      };
    });

    // 格式化父問題分組
    const parentGroupsFormatted: ParentGroupAnalysis[] = Object.entries(parentGroups).map(([parentId, children]) => ({
      parentId: parseInt(parentId),
      childrenCount: children.length,
      children: children.map((child) => ({
        id: child.QuestionId,
        title: child.Title,
        step: child.StepNumber || 0,
        template: child.QuestionTemplate,
      })),
    }));

    // 綠旗再認證問題
    const greenFlagQuestions = questions
      .filter((q) => isGreenFlagStep(q.StepNumber || 0))
      .map((q) => ({
        id: q.QuestionId,
        title: q.Title,
        step: q.StepNumber || 0,
        parentId: q.ParentQuestionId,
        template: q.QuestionTemplate,
        isRenewed: q.IsRenewed || false,
      }));

    return {
      totalQuestions: questions.length,
      stepAnalysis,
      parentGroups: parentGroupsFormatted,
      greenFlagQuestions,
    };
  }
}
