/* #root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
} */

/* .logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
} */
/* .logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
} */

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* 校園投稿 */
/* ReactQuill 大型編輯器樣式優化 */
.quill-editor-large .ql-container {
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  font-size: 14px;
  /* min-height: 1150px; */
  /* 保留工具欄空間 */
}

.quill-editor-large .ql-toolbar {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  border-bottom: none;
  background-color: #f8fafc;
  padding: 12px;
}

.quill-editor-large .ql-editor {
  min-height: 500px;
  padding: 20px;
  line-height: 1.8;
  font-size: 14px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", sans-serif;
  overflow-y: auto;
  resize: vertical;
}

.quill-editor-large .ql-editor:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.quill-editor-large .ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: italic;
  font-size: 14px;
  line-height: 1.8;
}

/* 滾動條美化 */
.quill-editor-large .ql-editor::-webkit-scrollbar {
  width: 8px;
}

.quill-editor-large .ql-editor::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.quill-editor-large .ql-editor::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.quill-editor-large .ql-editor::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 工具欄按鈕樣式優化 */
.quill-editor-large .ql-toolbar button {
  padding: 6px;
  margin: 2px;
  border-radius: 4px;
}

.quill-editor-large .ql-toolbar button:hover {
  background-color: #e2e8f0;
}

.quill-editor-large .ql-toolbar button.ql-active {
  background-color: #dbeafe;
  color: #2563eb;
}
