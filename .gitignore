# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# User uploads and generated files
public/uploads/school-logos/
public/uploads/user-files/
public/uploads/temp/

# Test files
test-*.html
test-*.js
test-*.cjs
test-*.ts
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Experimental and refactored files
*-refactored.ts
*-refactored.js
*-backup.*
*-old.*
*-temp.*

# Environment and configuration files
.env
.env.*
.env.backup
!.env.example
config.local.*

# Database and API related
*.db
*.sqlite
*.sql.backup
api-test-results.json
test-results.json

# Temporary files
*.tmp
*.temp
.temp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Desktop.ini
Thumbs.db

# Build artifacts
build/
out/
.next/
.nuxt/

# IDE specific
.vscode/settings.json
.vscode/launch.json
*.swp
*.swo
*~

# Claude AI agent configurations
.claude/
