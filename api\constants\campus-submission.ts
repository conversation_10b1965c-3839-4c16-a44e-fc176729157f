// ========== 校園投稿相關常數 ==========

// 投稿狀態
export const SUBMISSION_STATUS = {
  NOT_SUBMITTED: -1,
  UNDER_REVIEW: 0,
  PUBLISHED: 1,
  DELETED: 2,
  REJECTED: 3,
} as const;

// 審核狀態
export const REVIEW_STATUS = {
  WITHDRAWN: 0,
  PUBLISHED: 1,
  REJECTED: 3,
} as const;

// 狀態映射文字
export const STATUS_TEXT_MAP: Record<number, string> = {
  [-1]: "未送審",
  [0]: "審核中",
  [1]: "已發布",
  [2]: "已刪除",
  [3]: "已退件",
};

// 徽章類型
export const BADGE_TYPE = {
  NONE: 0,
  BRONZE: 1,
  SILVER: 2,
  GOLD: 3,
  GREEN_FLAG: 4,
} as const;

// 精選狀態
export const FEATURED_STATUS = {
  NORMAL: 0,
  FEATURED: 1,
} as const;

// 內容類型代碼
export const CONTENT_TYPE_CODE = {
  IMAGE: "image",
  VIDEO: "video",
  DOCUMENT: "document",
  LINK: "link",
} as const;

// 地區代碼
export const LOCALE_CODE = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;

// 預設分頁設定
export const DEFAULT_PAGINATION = {
  LIMIT: 50,
  PAGE: 1,
} as const;

// 描述摘要長度
export const DESCRIPTION_SUMMARY_LENGTH = 200;

// 狀態映射函數
export const getStatusText = (status: number): string => {
  return STATUS_TEXT_MAP[status] || "未知狀態";
};

// 根據審核記錄和投稿狀態決定最終狀態
export const getFinalStatus = (
  submissionStatus: number,
  reviewStatus?: number,
  hasReview?: boolean,
  reviewComment?: string
): { status: number; statusText: string } => {
  // 如果有審核記錄，優先使用審核狀態
  if (hasReview && reviewStatus !== undefined && reviewStatus !== null) {
    // 特殊處理：如果是撤回記錄（ReviewStatus=0且包含撤回備註），顯示為「未送審」
    if (
      reviewStatus === REVIEW_STATUS.WITHDRAWN &&
      reviewComment &&
      (reviewComment.includes("投稿已由學校撤回") || reviewComment.includes("投稿已由學校建立") || reviewComment.includes("投稿已由學校編輯修改"))
    ) {
      return {
        status: SUBMISSION_STATUS.NOT_SUBMITTED,
        statusText: getStatusText(SUBMISSION_STATUS.NOT_SUBMITTED),
      };
    }

    return {
      status: reviewStatus,
      statusText: getStatusText(reviewStatus),
    };
  }

  // 如果沒有審核記錄，使用投稿狀態
  return {
    status: submissionStatus,
    statusText: getStatusText(submissionStatus),
  };
};

// 檢查是否有有效的審核記錄
export const hasValidReview = (reviewComment?: string): boolean => {
  return reviewComment !== undefined && reviewComment !== null && reviewComment !== "";
};

// 格式化日期為 ISO 日期字串
export const formatDateToISOString = (date: Date | string): string => {
  return new Date(date).toISOString().split("T")[0];
};

// 生成描述摘要
export const generateDescriptionSummary = (description: string): string => {
  if (!description) return "";
  if (description.length <= DESCRIPTION_SUMMARY_LENGTH) return description;
  return description.substring(0, DESCRIPTION_SUMMARY_LENGTH) + "...";
};
