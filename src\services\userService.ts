// 用戶服務 - 業務邏輯層
import { user<PERSON><PERSON>, MemberProfile } from "@/api";
import { MemberLeve<PERSON>, MemberPath, PathStatistics, RoleStatistics, CityData, AreaData } from "@/api/userAPI";
import authService from "./authService";
import { resolveApiAssetUrl } from "@/utils/pathUtils";

// 聯絡人資訊
export interface ContactPerson {
  contact_sid: string;
  contact_cname: string;
  contact_job_title: string;
  contact_tel: string;
  contact_phone: string;
  contact_email: string;
}

// EPA 會員記錄
export interface EPAMemberRecord {
  member_role: "epa";
  member_sid: string;
  sid: string;
  place_sid: string;
  member_cname: string;
  member_cname_en: string;
  member_tel: string;
  member_phone: string;
  member_email: string;
}

// School 會員記錄
export interface SchoolMemberRecord {
  member_role: "school";
  member_sid: string;
  sid: string;
  member_cname: string;
  member_cname_en: string;
  member_address: string;
  member_tel: string;
  member_email: string;
  member_url: string;
  file_token_member_pohto?: string;
  member_Introduction: string;
  member_exchange: string;

  // 校長資訊
  school_principal_sid: string;
  principal_cname: string;
  principal_tel: string;
  principal_phone: string;
  principal_email: string;

  // 聯絡人資訊
  contact_num: string;
  contact: ContactPerson[];

  // 學校統計資料
  school_statistics_sid: string;
  write_date: string;
  staff_total: string;
  elementary1: string;
  elementary2: string;
  elementary3: string;
  elementary4: string;
  elementary5: string;
  elementary6: string;
  middle7: string;
  middle8: string;
  middle9: string;
  hight10: string;
  hight11: string;
  hight12: string;
}

// Tutor 會員記錄
export interface TutorMemberRecord {
  member_role: "tutor";
  member_sid: string;
  sid: string;
  place_cname: string;
  job_cname: string;
  member_cname: string;
  member_cname_en: string;
  member_tel: string;
  member_phone: string;
  member_email: string;
  allowedFields?: string[];
}

// 統一使用 BaseAPI 的 ApiResponse 介面
import { ApiResponse } from "../api/BaseAPI";
import env from "@/config/env";

// 校徽相關介面
export interface SchoolLogoResponse {
  success: boolean;
  data: {
    logoUrl?: string;
  };
  message?: string;
}

class UserService {
  // 獲取當前使用者完整資料
  async getCurrentUserProfile(): Promise<ApiResponse<MemberProfile>> {
    try {
      const response = await userAPI.getCurrentUserProfile();
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("獲取用戶資料失敗:", error);
      throw error;
    }
  }

  // 獲取縣市列表
  async getCities(): Promise<ApiResponse<CityData[]>> {
    try {
      const response = await userAPI.getCities();
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("❌ [CityData] 載入縣市資料失敗:", error);
      throw error;
    }
  }

  // 獲取指定縣市的區域列表
  async getAreas(cityId: number): Promise<ApiResponse<AreaData[]>> {
    try {
      const response = await userAPI.getAreas(cityId);
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("獲取區域列表失敗:", error);
      throw error;
    }
  }

  // 依 ID 獲取使用者完整資料
  async getUserProfile(userId: string): Promise<ApiResponse<MemberProfile>> {
    try {
      const response = await userAPI.getUserProfile(userId);
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("獲取用戶資料失敗:", error);
      throw error;
    }
  }

  // 更新使用者基本資料
  async updateUserProfile(
    updateData: Partial<MemberProfile> & {
      member_record_data?: EPAMemberRecord | SchoolMemberRecord | TutorMemberRecord;
    }
  ): Promise<ApiResponse<MemberProfile>> {
    try {
      const response = await userAPI.updateUserProfile(updateData);
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("更新用戶資料失敗:", error);
      throw error;
    }
  }

  // 獲取會員認證等級
  async getCertificationLevels(): Promise<ApiResponse<MemberLevel[]>> {
    try {
      const response = await userAPI.getCertificationLevels();
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("獲取認證等級失敗:", error);
      throw error;
    }
  }

  // 獲取會員環境路徑
  async getEnvironmentPaths(): Promise<
    ApiResponse<{
      paths: MemberPath[];
      statistics: PathStatistics;
    }>
  > {
    try {
      const response = await userAPI.getEnvironmentPaths();
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("獲取環境路徑失敗:", error);
      throw error;
    }
  }

  // 更新環境路徑
  async updateEnvironmentPaths(
    level_sid: number,
    paths: Record<string, boolean>
  ): Promise<
    ApiResponse<{
      path_id: number;
    }>
  > {
    try {
      const response = await userAPI.updateEnvironmentPaths(level_sid, paths);
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("更新環境路徑失敗:", error);
      throw error;
    }
  }

  // 獲取身份統計資訊（管理員功能）
  async getRoleStatistics(): Promise<ApiResponse<RoleStatistics>> {
    try {
      const response = await userAPI.getRoleStatistics();
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      console.error("獲取用戶統計失敗:", error);
      throw error;
    }
  }

  // 獲取學校校徽
  async getSchoolLogo(schoolId: string | number): Promise<ApiResponse<{ logoUrl: string }>> {
    try {
      console.log("🔄 [Logo] 載入學校校徽，學校ID:", schoolId);

      const apiUrl = `${env.API_BASE_URL}/file/school-logo/${schoolId}`;
      const response = await authService.makeAuthenticatedRequest(apiUrl, {
        method: "GET",
      });

      const result: SchoolLogoResponse = await response.json();

      if (result.success && result.data.logoUrl) {
        // 使用工具函數處理 API 返回的相對路徑
        const fullLogoUrl = resolveApiAssetUrl(result.data.logoUrl);
        console.log("✅ [Logo] 校徽載入成功:", fullLogoUrl);

        return {
          success: true,
          data: { logoUrl: fullLogoUrl },
          message: "校徽載入成功",
        };
      } else {
        console.log("📸 [Logo] 目前沒有上傳校徽");
        return {
          success: false,
          data: { logoUrl: "" },
          message: "目前沒有上傳校徽",
        };
      }
    } catch (error) {
      console.log("📸 [Logo] 載入校徽失敗或尚未上傳:", error);
      return {
        success: false,
        data: { logoUrl: "" },
        message: "載入校徽失敗",
      };
    }
  }

  // 上傳學校校徽
  async uploadSchoolLogo(schoolId: string | number, file: File): Promise<ApiResponse<{ logoUrl: string }>> {
    try {
      console.log("🔄 [Logo] 上傳學校校徽，學校ID:", schoolId);

      const formData = new FormData();
      formData.append("logo", file);

      const apiUrl = `${env.API_BASE_URL}/file/upload-school-logo`;
      const response = await authService.makeAuthenticatedRequest(apiUrl, {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success && result.data.logoUrl) {
        const fullLogoUrl = resolveApiAssetUrl(result.data.logoUrl);
        console.log("✅ [Logo] 校徽上傳成功:", fullLogoUrl);

        return {
          success: true,
          data: { logoUrl: fullLogoUrl },
          message: "校徽上傳成功",
        };
      } else {
        throw new Error(result.message || "校徽上傳失敗");
      }
    } catch (error) {
      console.error("❌ [Logo] 校徽上傳失敗:", error);
      throw error;
    }
  }

  // 根據身份類型獲取允許的更新欄位
  static getAllowedUpdateFields(memberRole: "school" | "epa" | "tutor"): string[] {
    switch (memberRole) {
      case "school":
        return ["member_cname", "member_cname_en", "member_email", "member_tel", "member_address", "member_url", "member_Introduction", "member_exchange"];
      case "epa":
      case "tutor":
        return ["member_cname", "member_cname_en", "member_email", "member_tel", "member_phone", "place_cname", "job_cname", "city_sid"];
      default:
        return [];
    }
  }

  // 驗證電子郵件格式
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // 驗證電話號碼格式
  static isValidPhone(phone: string): boolean {
    // 台灣手機和電話號碼格式
    const phoneRegex = /^(\+886|0)?([2-9]\d{7,8}|[2-9]\d{2}-\d{3}-\d{3})$/;
    return phoneRegex.test(phone.replace(/[-\s]/g, ""));
  }

  // 格式化電話號碼顯示
  static formatPhoneNumber(phone: string): string {
    const cleaned = phone.replace(/\D/g, "");

    if (cleaned.startsWith("886")) {
      return `+886 ${cleaned.slice(3, 6)}-${cleaned.slice(6, 9)}-${cleaned.slice(9)}`;
    }

    if (cleaned.startsWith("0")) {
      if (cleaned.length === 10) {
        return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
      } else if (cleaned.length === 9) {
        return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
      }
    }

    return phone;
  }

  // 格式化身份類型顯示
  static formatMemberRole(role: "school" | "epa" | "tutor"): string {
    const roleMap = {
      school: "生態學校",
      epa: "縣市政府",
      tutor: "輔導人員",
    };
    return roleMap[role] || role;
  }

  // 格式化審核狀態顯示
  static formatRegisterReview(status: "尚未審核" | "未通過" | "已通過"): string {
    const statusMap = {
      尚未審核: { label: "待審核", color: "orange" },
      未通過: { label: "審核未通過", color: "red" },
      已通過: { label: "審核通過", color: "green" },
    };
    return statusMap[status]?.label || status;
  }

  // 格式化環境路徑中文名稱
  static formatEnvironmentPath(path: string): string {
    const pathMap: Record<string, string> = {
      water: "水",
      food: "永續食物",
      biological: "生物多樣性",
      traffic: "交通",
      weather: "氣候變遷",
      consume: "消耗與廢棄物",
      energy: "能源",
      life: "健康生活",
      school: "健康校園",
      habitat: "學校棲地",
      forest: "森林",
      protection: "水體保護",
    };
    return pathMap[path] || path;
  }

  // 獲取環境路徑列表
  static getEnvironmentPathList(): Array<{ key: string; label: string }> {
    return [
      { key: "water", label: "水" },
      { key: "food", label: "永續食物" },
      { key: "biological", label: "生物多樣性" },
      { key: "traffic", label: "交通" },
      { key: "weather", label: "氣候變遷" },
      { key: "consume", label: "消耗與廢棄物" },
      { key: "energy", label: "能源" },
      { key: "life", label: "健康生活" },
      { key: "school", label: "健康校園" },
      { key: "habitat", label: "學校棲地" },
      { key: "forest", label: "森林" },
      { key: "protection", label: "水體保護" },
    ];
  }

  // 處理 API 錯誤
  static handleApiError(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }

    if (typeof error === "string") {
      return error;
    }

    return "發生未知錯誤";
  }

  // 驗證會員記錄資料
  static validateMemberRecord(
    role: "school" | "epa" | "tutor",
    data: EPAMemberRecord | SchoolMemberRecord | TutorMemberRecord
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 基本欄位驗證
    if (!data.member_cname?.trim()) {
      errors.push("姓名/校名為必填項目");
    }

    if (!data.member_email?.trim()) {
      errors.push("電子郵件為必填項目");
    } else if (!this.isValidEmail(data.member_email)) {
      errors.push("電子郵件格式不正確");
    }

    // 身份特定驗證
    switch (role) {
      case "school": {
        const schoolData = data as SchoolMemberRecord;
        if (!schoolData.member_address?.trim()) {
          errors.push("學校地址為必填項目");
        }
        if (!schoolData.member_url?.trim()) {
          errors.push("學校網址為必填項目");
        }
        break;
      }
      case "epa":
      case "tutor": {
        const govData = data as EPAMemberRecord | TutorMemberRecord;
        if (!govData.member_phone?.trim()) {
          errors.push("手機號碼為必填項目");
        } else if (!this.isValidPhone(govData.member_phone)) {
          errors.push("手機號碼格式不正確");
        }
        break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 創建全域使用者服務實例
export const userService = new UserService();

export default userService;
