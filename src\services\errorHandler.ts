/**
 * 統一錯誤處理和日誌記錄系統
 * 
 * 核心功能：
 * - 標準化錯誤格式和日誌輸出
 * - 錯誤分類和嚴重性等級
 * - 自動錯誤恢復機制
 * - 錯誤統計和上報
 */

// 錯誤等級列舉
export enum ErrorLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL'
}

// 錯誤分類列舉
export enum ErrorCategory {
  AUTHENTICATION = 'AUTH',
  STORAGE = 'STORAGE',
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  BUSINESS_LOGIC = 'BUSINESS',
  SYSTEM = 'SYSTEM',
  USER_INPUT = 'USER_INPUT'
}

// 錯誤內容介面
export interface ErrorDetails {
  level: ErrorLevel;
  category: ErrorCategory;
  code: string;
  message: string;
  context?: Record<string, any>;
  timestamp: number;
  stackTrace?: string;
  userId?: string;
  sessionId?: string;
}

// 日誌項目介面
export interface LogEntry extends ErrorDetails {
  id: string;
  resolved: boolean;
  retryCount?: number;
  relatedErrors?: string[];
}

// 錯誤恢復策略介面
export interface RecoveryStrategy {
  canRecover(error: ErrorDetails): boolean;
  recover(error: ErrorDetails): Promise<boolean>;
  getRetryDelay(retryCount: number): number;
  getMaxRetries(): number;
}

/**
 * 認證錯誤恢復策略
 */
class AuthRecoveryStrategy implements RecoveryStrategy {
  canRecover(error: ErrorDetails): boolean {
    return error.category === ErrorCategory.AUTHENTICATION && 
           ['TOKEN_EXPIRED', 'TOKEN_INVALID'].includes(error.code);
  }

  async recover(error: ErrorDetails): Promise<boolean> {
    try {
      // 嘗試重新整理 Token
      const { tokenManager } = await import('./tokenManager');
      const currentToken = tokenManager.getToken();
      
      if (currentToken) {
        const validation = await tokenManager.validateToken(currentToken);
        if (!validation.valid && validation.shouldRefresh) {
          // 這裡應該調用 Token 重新整理 API
          console.log('🔄 [ErrorHandler] 嘗試恢復認證錯誤');
          return true;
        }
      }
    } catch (recoveryError) {
      console.error('❌ [ErrorHandler] 認證錯誤恢復失敗:', recoveryError);
    }
    return false;
  }

  getRetryDelay(retryCount: number): number {
    return Math.min(1000 * Math.pow(2, retryCount), 10000); // 指數退避，最大10秒
  }

  getMaxRetries(): number {
    return 3;
  }
}

/**
 * 網路錯誤恢復策略
 */
class NetworkRecoveryStrategy implements RecoveryStrategy {
  canRecover(error: ErrorDetails): boolean {
    return error.category === ErrorCategory.NETWORK && 
           ['NETWORK_ERROR', 'TIMEOUT', 'CONNECTION_FAILED'].includes(error.code);
  }

  async recover(error: ErrorDetails): Promise<boolean> {
    // 檢查網路連線狀態
    if (navigator.onLine) {
      console.log('🔄 [ErrorHandler] 網路已恢復，可嘗試重試');
      return true;
    }
    return false;
  }

  getRetryDelay(retryCount: number): number {
    return Math.min(2000 * retryCount, 30000); // 線性增長，最大30秒
  }

  getMaxRetries(): number {
    return 5;
  }
}

/**
 * 統一錯誤處理器
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private logs: LogEntry[] = [];
  private recoveryStrategies: RecoveryStrategy[] = [];
  private sessionId: string;
  private userId?: string;
  
  // 配置選項
  private readonly MAX_LOGS = 1000;
  private readonly LOG_CLEANUP_INTERVAL = 60000; // 1 分鐘
  
  private constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeRecoveryStrategies();
    this.startPeriodicCleanup();
  }

  /**
   * 獲取 ErrorHandler 單例實例
   */
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * 初始化恢復策略
   */
  private initializeRecoveryStrategies(): void {
    this.recoveryStrategies = [
      new AuthRecoveryStrategy(),
      new NetworkRecoveryStrategy()
    ];
  }

  /**
   * 生成 Session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 設定當前使用者 ID
   */
  public setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * 記錄錯誤
   */
  public logError(
    level: ErrorLevel,
    category: ErrorCategory,
    code: string,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): string {
    const errorDetails: ErrorDetails = {
      level,
      category,
      code,
      message,
      context,
      timestamp: Date.now(),
      stackTrace: error?.stack,
      userId: this.userId,
      sessionId: this.sessionId
    };

    const logId = this.generateLogId();
    const logEntry: LogEntry = {
      ...errorDetails,
      id: logId,
      resolved: false
    };

    this.logs.push(logEntry);
    this.outputToConsole(logEntry);
    
    // 嘗試自動恢復
    this.attemptRecovery(logEntry);
    
    return logId;
  }

  /**
   * 生成日誌 ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 輸出到控制台
   */
  private outputToConsole(log: LogEntry): void {
    const prefix = this.getConsolePrefix(log);
    const message = `${prefix} [${log.category}:${log.code}] ${log.message}`;
    
    switch (log.level) {
      case ErrorLevel.DEBUG:
        console.debug(message, log.context);
        break;
      case ErrorLevel.INFO:
        console.info(message, log.context);
        break;
      case ErrorLevel.WARN:
        console.warn(message, log.context);
        break;
      case ErrorLevel.ERROR:
        console.error(message, log.context);
        if (log.stackTrace) console.error('Stack trace:', log.stackTrace);
        break;
      case ErrorLevel.FATAL:
        console.error('💀', message, log.context);
        if (log.stackTrace) console.error('Stack trace:', log.stackTrace);
        break;
    }
  }

  /**
   * 獲取控制台前綴
   */
  private getConsolePrefix(log: LogEntry): string {
    const timestamp = new Date(log.timestamp).toISOString();
    
    switch (log.level) {
      case ErrorLevel.DEBUG: return `🔍 [${timestamp}]`;
      case ErrorLevel.INFO: return `ℹ️ [${timestamp}]`;
      case ErrorLevel.WARN: return `⚠️ [${timestamp}]`;
      case ErrorLevel.ERROR: return `❌ [${timestamp}]`;
      case ErrorLevel.FATAL: return `💀 [${timestamp}]`;
      default: return `📝 [${timestamp}]`;
    }
  }

  /**
   * 嘗試自動恢復
   */
  private async attemptRecovery(log: LogEntry): Promise<void> {
    const strategy = this.recoveryStrategies.find(s => s.canRecover(log));
    if (!strategy) return;

    const retryCount = log.retryCount || 0;
    if (retryCount >= strategy.getMaxRetries()) {
      this.logError(
        ErrorLevel.ERROR,
        log.category,
        'RECOVERY_FAILED',
        `錯誤恢復失敗，已達最大重試次數: ${log.code}`,
        { originalLog: log.id, retryCount }
      );
      return;
    }

    // 等待恢復延遲
    const delay = strategy.getRetryDelay(retryCount);
    setTimeout(async () => {
      try {
        const recovered = await strategy.recover(log);
        if (recovered) {
          log.resolved = true;
          this.logError(
            ErrorLevel.INFO,
            log.category,
            'RECOVERY_SUCCESS',
            `錯誤已自動恢復: ${log.code}`,
            { originalLog: log.id, retryCount: retryCount + 1 }
          );
        } else {
          log.retryCount = retryCount + 1;
          await this.attemptRecovery(log);
        }
      } catch (recoveryError) {
        this.logError(
          ErrorLevel.ERROR,
          ErrorCategory.SYSTEM,
          'RECOVERY_ERROR',
          `錯誤恢復過程中發生異常: ${recoveryError instanceof Error ? recoveryError.message : String(recoveryError)}`,
          { originalLog: log.id, retryCount: retryCount + 1 },
          recoveryError instanceof Error ? recoveryError : undefined
        );
      }
    }, delay);
  }

  /**
   * 便利方法 - 記錄除錯訊息
   */
  public debug(category: ErrorCategory, code: string, message: string, context?: Record<string, any>): string {
    return this.logError(ErrorLevel.DEBUG, category, code, message, context);
  }

  /**
   * 便利方法 - 記錄資訊
   */
  public info(category: ErrorCategory, code: string, message: string, context?: Record<string, any>): string {
    return this.logError(ErrorLevel.INFO, category, code, message, context);
  }

  /**
   * 便利方法 - 記錄警告
   */
  public warn(category: ErrorCategory, code: string, message: string, context?: Record<string, any>): string {
    return this.logError(ErrorLevel.WARN, category, code, message, context);
  }

  /**
   * 便利方法 - 記錄錯誤
   */
  public error(category: ErrorCategory, code: string, message: string, context?: Record<string, any>, error?: Error): string {
    return this.logError(ErrorLevel.ERROR, category, code, message, context, error);
  }

  /**
   * 便利方法 - 記錄致命錯誤
   */
  public fatal(category: ErrorCategory, code: string, message: string, context?: Record<string, any>, error?: Error): string {
    return this.logError(ErrorLevel.FATAL, category, code, message, context, error);
  }

  /**
   * 獲取錯誤日誌
   */
  public getLogs(filter?: Partial<LogEntry>): LogEntry[] {
    if (!filter) return [...this.logs];
    
    return this.logs.filter(log => {
      return Object.entries(filter).every(([key, value]) => {
        return log[key as keyof LogEntry] === value;
      });
    });
  }

  /**
   * 清除已解決的日誌
   */
  public clearResolvedLogs(): number {
    const initialCount = this.logs.length;
    this.logs = this.logs.filter(log => !log.resolved);
    return initialCount - this.logs.length;
  }

  /**
   * 獲取錯誤統計
   */
  public getStats(): Record<string, any> {
    const totalLogs = this.logs.length;
    const resolvedLogs = this.logs.filter(log => log.resolved).length;
    
    const levelStats = this.logs.reduce((stats, log) => {
      stats[log.level] = (stats[log.level] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);
    
    const categoryStats = this.logs.reduce((stats, log) => {
      stats[log.category] = (stats[log.category] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);

    return {
      totalLogs,
      resolvedLogs,
      unresolvedLogs: totalLogs - resolvedLogs,
      levelStats,
      categoryStats,
      sessionId: this.sessionId,
      userId: this.userId
    };
  }

  /**
   * 定期清理舊日誌
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      if (this.logs.length > this.MAX_LOGS) {
        const excessCount = this.logs.length - this.MAX_LOGS;
        this.logs.splice(0, excessCount);
        console.log(`🧹 [ErrorHandler] 清理舊日誌 ${excessCount} 條`);
      }
    }, this.LOG_CLEANUP_INTERVAL);
  }

  /**
   * 銷毀錯誤處理器
   */
  public destroy(): void {
    this.logs = [];
    this.recoveryStrategies = [];
    console.log('💥 [ErrorHandler] 錯誤處理器已銷毀');
  }
}

// 導出單例實例（便於直接使用）
export const errorHandler = ErrorHandler.getInstance();

// 導出預設值
export default errorHandler;

// 便利函數導出
export const logDebug = (category: ErrorCategory, code: string, message: string, context?: Record<string, any>) => 
  errorHandler.debug(category, code, message, context);

export const logInfo = (category: ErrorCategory, code: string, message: string, context?: Record<string, any>) => 
  errorHandler.info(category, code, message, context);

export const logWarn = (category: ErrorCategory, code: string, message: string, context?: Record<string, any>) => 
  errorHandler.warn(category, code, message, context);

export const logError = (category: ErrorCategory, code: string, message: string, context?: Record<string, any>, error?: Error) => 
  errorHandler.error(category, code, message, context, error);

export const logFatal = (category: ErrorCategory, code: string, message: string, context?: Record<string, any>, error?: Error) => 
  errorHandler.fatal(category, code, message, context, error);