{"generatedAt": "2025-08-21T08:49:16.299Z", "version": "1.0.0", "routes": [{"name": "admin", "file": "routes/admin.ts", "endpoints": [{"method": "POST", "path": "/mark-answer-status", "description": ""}, {"method": "GET", "path": "/action-logs/:certificationId", "description": ""}, {"method": "POST", "path": "/save-review-comment", "description": ""}, {"method": "GET", "path": "/review-comments/:certificationId", "description": ""}], "imports": ["express", "../middleware/auth.js", "../services/admin-service.js", "../utils/logger.js", "../constants/admin.js"], "exports": []}, {"name": "answer", "file": "routes/answer.ts", "endpoints": [{"method": "POST", "path": "/save", "description": ""}, {"method": "GET", "path": "/certification/:certificationId", "description": ""}, {"method": "GET", "path": "/question/:questionId/certification/:certificationId", "description": ""}, {"method": "DELETE", "path": "/:answerId", "description": ""}], "imports": ["express", "../middleware/auth.js", "../services/answer-service.js", "../utils/response-helpers.js", "../constants/answer.js"], "exports": []}, {"name": "auth", "file": "routes/auth.ts", "endpoints": [{"method": "GET", "path": "/token-status", "description": ""}, {"method": "POST", "path": "/token-login", "description": "Token 直接登入 API"}, {"method": "POST", "path": "/password-login", "description": "密碼登入 API"}, {"method": "POST", "path": "/change-password", "description": ""}, {"method": "POST", "path": "/logout", "description": "登出 API"}, {"method": "POST", "path": "/admin-reset-password", "description": ""}], "imports": ["express", "../services/auth-service.js", "../constants/auth.js", "../utils/logger.js"], "exports": []}, {"name": "campus-submissions", "file": "routes/campus-submissions.ts", "endpoints": [{"method": "GET", "path": "/", "description": "獲取單一校園投稿詳細資料"}, {"method": "GET", "path": "/:id", "description": "獲取單一校園投稿詳細資料"}, {"method": "DELETE", "path": "/:id", "description": "獲取單一校園投稿詳細資料"}, {"method": "POST", "path": "/:submissionId/photos", "description": ""}, {"method": "POST", "path": "/:submissionId/attachments", "description": ""}, {"method": "DELETE", "path": "/:submissionId/photos/:filename", "description": ""}, {"method": "DELETE", "path": "/:submissionId/attachments/:filename", "description": ""}], "imports": ["express", "multer", "path", "../middleware/auth.js", "../services/campus-submission-service.js", "../services/campus-submission-file-service.js", "../constants/campus-submission.js", "../utils/logger.js", "../constants/file.js"], "exports": []}, {"name": "certificate", "file": "routes/certificate.ts", "endpoints": [{"method": "POST", "path": "/validate", "description": ""}, {"method": "POST", "path": "/bind", "description": ""}, {"method": "DELETE", "path": "/unbind/:accountId", "description": ""}, {"method": "GET", "path": "/bindings", "description": ""}, {"method": "POST", "path": "/login", "description": ""}, {"method": "GET", "path": "/test", "description": ""}], "imports": ["express", "../middleware/auth.js", "../services/certificate-service.js", "../constants/certificate.js"], "exports": []}, {"name": "certification", "file": "routes/certification.ts", "endpoints": [{"method": "GET", "path": "/list", "description": ""}, {"method": "GET", "path": "/availability", "description": ""}, {"method": "GET", "path": "/:certificationId", "description": ""}, {"method": "POST", "path": "/create", "description": ""}, {"method": "PUT", "path": "/:certificationId", "description": ""}, {"method": "DELETE", "path": "/:certificationId", "description": ""}], "imports": ["express", "../middleware/auth.js", "../services/certification-service.js", "../constants/certification.js"], "exports": []}, {"name": "dashboard", "file": "routes/dashboard.ts", "endpoints": [{"method": "GET", "path": "/test", "description": "測試端點（無需認證）"}, {"method": "GET", "path": "/city-statistics/:cityId", "description": "獲取指定縣市的生態學校統計"}, {"method": "GET", "path": "/my-city-statistics", "description": "獲取當前用戶所屬縣市的統計資料"}, {"method": "GET", "path": "/school/passed-certifications", "description": ""}, {"method": "GET", "path": "/school/latest-articles", "description": "獲取學校最新投稿文章"}, {"method": "GET", "path": "/latest-certifications/:cityId", "description": "獲取指定縣市的最新認證（最多6則）"}, {"method": "GET", "path": "/my-latest-certifications", "description": "獲取當前用戶所屬縣市的最新認證"}, {"method": "GET", "path": "/school/current-certification", "description": "獲取學校當前申請中的認證"}], "imports": ["express", "../middleware/auth.js", "../services/dashboard-service.js", "../constants/dashboard.js", "../utils/logger.js", "../models/certification.js"], "exports": []}, {"name": "file", "file": "routes/file.ts", "endpoints": [{"method": "POST", "path": "/upload", "description": ""}, {"method": "POST", "path": "/upload-single", "description": ""}, {"method": "DELETE", "path": "/:filename", "description": "刪除檔案"}, {"method": "GET", "path": "/info/:filename", "description": "獲取檔案資訊"}, {"method": "GET", "path": "/download/:filename", "description": "下載檔案"}, {"method": "GET", "path": "/supported-types", "description": ""}, {"method": "POST", "path": "/upload-school-logo", "description": ""}, {"method": "GET", "path": "/school-logo/:accountId", "description": "取得校徽 API"}, {"method": "GET", "path": "/upload-diagnostics", "description": ""}], "imports": ["express", "multer", "path", "url", "../middleware/auth.js", "../services/file-service.js", "../config/config-manager.js"], "exports": []}, {"name": "location", "file": "routes/location.ts", "endpoints": [{"method": "GET", "path": "/cities", "description": ""}, {"method": "GET", "path": "/areas/:cityId", "description": ""}, {"method": "GET", "path": "/hierarchy", "description": ""}], "imports": ["express", "../middleware/auth.js", "../services/location-service.js", "../utils/logger.js", "../constants/location.js"], "exports": []}, {"name": "metrics", "file": "routes/metrics.ts", "endpoints": [{"method": "GET", "path": "/metrics", "description": "*  * GET /metrics  * 獲取系統監控指標（JSON 格式）"}, {"method": "GET", "path": "/metrics/prometheus", "description": "*  * GET /metrics/prometheus  * 獲取 Prometheus 格式的指標"}, {"method": "POST", "path": "/metrics/reset", "description": "*  * POST /metrics/reset  * 重置監控指標（僅限開發環境）"}, {"method": "GET", "path": "/health", "description": "*  * GET /health  * 健康檢查端點"}], "imports": ["express", "@/shared/monitoring/AuthMetrics"], "exports": []}, {"name": "question", "file": "routes/question.ts", "endpoints": [{"method": "GET", "path": "/", "description": "根據步驟和認證等級獲取問題"}, {"method": "GET", "path": "/certification-steps", "description": ""}, {"method": "GET", "path": "/form-questions", "description": "獲取表單問題配置 (支援步驟和父問題雙層分組)"}, {"method": "GET", "path": "/analyze-structure", "description": "分析問題父子關係結構 (必須在/:questionId之前定義)"}, {"method": "GET", "path": "/:questionId", "description": "根據問題ID獲取問題詳情"}], "imports": ["express", "../services/question-service.js", "../constants/question.js"], "exports": []}, {"name": "simple-profile", "file": "routes/simple-profile.ts", "endpoints": [{"method": "GET", "path": "/", "description": "簡化版基本資料維護 API - 獲取會員資料"}, {"method": "PUT", "path": "/", "description": "簡化版基本資料維護 API - 獲取會員資料"}], "imports": ["express", "../middleware/auth.js", "../config/database-mssql.js", "../utils/logger"], "exports": []}, {"name": "template-answer", "file": "routes/template-answer.ts", "endpoints": [{"method": "POST", "path": "/save", "description": ""}, {"method": "POST", "path": "/validate", "description": ""}], "imports": ["express", "../services/template-answer-service.js", "../constants/template-answer.js"], "exports": []}, {"name": "user", "file": "routes/user.ts", "endpoints": [{"method": "GET", "path": "/profile", "description": "獲取使用者基本資料 (依 ID)"}, {"method": "GET", "path": "/profile/:userId", "description": "獲取使用者基本資料 (依 ID)"}, {"method": "PUT", "path": "/profile", "description": "獲取使用者基本資料 (依 ID)"}, {"method": "GET", "path": "/certifications", "description": ""}, {"method": "GET", "path": "/school", "description": ""}, {"method": "GET", "path": "/permissions", "description": ""}], "imports": ["express", "../middleware/auth.js", "../services/user-service.js", "../constants/user.js"], "exports": []}], "models": [{"name": "admin", "file": "models/admin.ts", "interfaces": [{"name": "AuthenticatedUser", "properties": [{"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "account", "type": "string", "optional": false, "description": ""}, {"name": "roleType", "type": "string", "optional": false, "description": ""}, {"name": "permissions", "type": "string[]", "optional": false, "description": ""}, {"name": "permissionGroups", "type": "string[]", "optional": false, "description": ""}, {"name": "isActive", "type": "boolean", "optional": false, "description": ""}]}, {"name": "AnswerStatusMarkRequest", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}, {"name": "questionId", "type": "string", "optional": false, "description": ""}, {"name": "status", "type": "string", "optional": false, "description": ""}, {"name": "action", "type": "string", "optional": false, "description": ""}]}, {"name": "ReviewCommentRequest", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}, {"name": "stepId", "type": "string", "optional": false, "description": ""}, {"name": "comment", "type": "string", "optional": false, "description": ""}]}, {"name": "AnswerStatusInfo", "properties": [{"name": "answerId", "type": "number", "optional": false, "description": ""}, {"name": "questionId", "type": "number", "optional": false, "description": ""}, {"name": "previousStatus", "type": "number", "optional": false, "description": ""}, {"name": "newStatus", "type": "number", "optional": false, "description": ""}, {"name": "statusDescription", "type": "string", "optional": false, "description": ""}, {"name": "action", "type": "string", "optional": false, "description": ""}, {"name": "timestamp", "type": "string", "optional": false, "description": ""}]}, {"name": "AdminActionLog", "properties": [{"name": "actionLogId", "type": "number", "optional": false, "description": ""}, {"name": "certificationId", "type": "number", "optional": false, "description": ""}, {"name": "questionId", "type": "number", "optional": false, "description": ""}, {"name": "answerId", "type": "number", "optional": false, "description": ""}, {"name": "action", "type": "string", "optional": false, "description": ""}, {"name": "previousStatus", "type": "number", "optional": false, "description": ""}, {"name": "newStatus", "type": "number", "optional": false, "description": ""}, {"name": "actionTime", "type": "Date", "optional": false, "description": ""}, {"name": "notes", "type": "string", "optional": false, "description": ""}, {"name": "adminUsername", "type": "string", "optional": false, "description": ""}, {"name": "questionTitle", "type": "string", "optional": false, "description": ""}]}, {"name": "ReviewComment", "properties": [{"name": "certificationStepRecordId", "type": "number", "optional": false, "description": ""}, {"name": "certificationId", "type": "number", "optional": false, "description": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "optional": false, "description": ""}, {"name": "comment", "type": "string", "optional": false, "description": ""}, {"name": "createdTime", "type": "Date", "optional": false, "description": ""}, {"name": "updatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "adminUsername", "type": "string", "optional": false, "description": ""}]}, {"name": "ReviewCommentSaveResult", "properties": [{"name": "recordId", "type": "number", "optional": true, "description": ""}, {"name": "action", "type": "string", "optional": false, "description": ""}, {"name": "timestamp", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationQueryResult", "properties": [{"name": "CertificationId", "type": "number", "optional": false, "description": ""}, {"name": "ReviewStatus", "type": "number", "optional": false, "description": ""}, {"name": "Status", "type": "number", "optional": false, "description": ""}]}, {"name": "AnswerQueryResult", "properties": [{"name": "AnswerId", "type": "number", "optional": false, "description": ""}, {"name": "QuestionId", "type": "number", "optional": false, "description": ""}, {"name": "AnswerStatus", "type": "number", "optional": false, "description": ""}]}, {"name": "ActionLogQueryResult", "properties": [{"name": "ActionLogId", "type": "number", "optional": false, "description": ""}, {"name": "CertificationId", "type": "number", "optional": false, "description": ""}, {"name": "QuestionId", "type": "number", "optional": false, "description": ""}, {"name": "AnswerId", "type": "number", "optional": false, "description": ""}, {"name": "Action", "type": "string", "optional": false, "description": ""}, {"name": "PreviousStatus", "type": "number", "optional": false, "description": ""}, {"name": "NewStatus", "type": "number", "optional": false, "description": ""}, {"name": "ActionTime", "type": "Date", "optional": false, "description": ""}, {"name": "Notes", "type": "string", "optional": false, "description": ""}, {"name": "AdminUsername", "type": "string", "optional": false, "description": ""}, {"name": "QuestionTitle", "type": "string", "optional": false, "description": ""}]}, {"name": "ReviewCommentQueryResult", "properties": [{"name": "CertificationStepRecordId", "type": "number", "optional": false, "description": ""}, {"name": "CertificationId", "type": "number", "optional": false, "description": ""}, {"name": "StepNumber", "type": "number", "optional": false, "description": ""}, {"name": "Comment", "type": "string", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "UpdatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "AdminUsername", "type": "string", "optional": false, "description": ""}]}, {"name": "StepRecordQueryResult", "properties": [{"name": "CertificationStepRecordId", "type": "number", "optional": false, "description": ""}]}, {"name": "AnswerStatusMarkResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "AnswerStatusInfo", "optional": false, "description": ""}]}, {"name": "ActionLogsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "AdminActionLog[]", "optional": false, "description": ""}]}, {"name": "ReviewCommentSaveResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "ReviewCommentSaveResult", "optional": false, "description": ""}]}, {"name": "ReviewCommentsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "ReviewComment[]", "optional": false, "description": ""}]}, {"name": "ActionLogsParams", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}]}, {"name": "ReviewCommentsParams", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "answer", "file": "models/answer.ts", "interfaces": [{"name": "AnswerSaveRequest", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}, {"name": "questionId", "type": "number", "optional": false, "description": ""}, {"name": "answerData", "type": "any", "optional": false, "description": ""}, {"name": "templateId", "type": "number", "optional": true, "description": ""}]}, {"name": "AnswerInfo", "properties": [{"name": "answerId", "type": "number", "optional": false, "description": ""}, {"name": "certificationId", "type": "string", "optional": false, "description": ""}, {"name": "questionId", "type": "number", "optional": false, "description": ""}, {"name": "questionTitle", "type": "string", "optional": false, "description": ""}, {"name": "questionTemplate", "type": "number", "optional": false, "description": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "optional": false, "description": ""}, {"name": "parentQuestionId", "type": "number | null", "optional": false, "description": ""}, {"name": "answerData", "type": "any", "optional": false, "description": ""}, {"name": "answerStatus", "type": "number", "optional": false, "description": ""}, {"name": "submittedDate", "type": "Date | null", "optional": false, "description": ""}, {"name": "reviewedDate", "type": "Date | null", "optional": false, "description": ""}, {"name": "createdTime", "type": "Date", "optional": false, "description": ""}, {"name": "updatedTime", "type": "Date | null", "optional": false, "description": ""}]}, {"name": "AnswerSaveResult", "properties": [{"name": "answerId", "type": "number", "optional": false, "description": ""}, {"name": "questionId", "type": "number", "optional": false, "description": ""}, {"name": "templateId", "type": "number", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationAnswersData", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}, {"name": "answers", "type": "AnswerInfo[]", "optional": false, "description": ""}, {"name": "total", "type": "number", "optional": false, "description": ""}]}, {"name": "AnswerRecord", "properties": [{"name": "CertificationAnswerId", "type": "number", "optional": false, "description": ""}, {"name": "CertificationId", "type": "string", "optional": false, "description": ""}, {"name": "QuestionId", "type": "number", "optional": false, "description": ""}, {"name": "AnswerText", "type": "string", "optional": false, "description": ""}, {"name": "AnswerStatus", "type": "number", "optional": false, "description": ""}, {"name": "SubmittedDate", "type": "Date | null", "optional": false, "description": ""}, {"name": "ReviewedDate", "type": "Date | null", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "UpdatedTime", "type": "Date | null", "optional": false, "description": ""}, {"name": "QuestionTitle", "type": "string", "optional": false, "description": ""}, {"name": "QuestionTemplate", "type": "number", "optional": false, "description": ""}, {"name": "StepNumber", "type": "number", "optional": false, "description": ""}, {"name": "ParentQuestionId", "type": "number | null", "optional": false, "description": ""}]}, {"name": "UserSchoolQueryResult", "properties": [{"name": "SchoolId", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationCheckQueryResult", "properties": [{"name": "SchoolId", "type": "number", "optional": false, "description": ""}, {"name": "ReviewStatus", "type": "number", "optional": true, "description": ""}]}, {"name": "Question<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": [{"name": "QuestionId", "type": "number", "optional": false, "description": ""}, {"name": "QuestionTemplate", "type": "number", "optional": false, "description": ""}]}, {"name": "ExistingAnswerQueryResult", "properties": [{"name": "CertificationAnswerId", "type": "number", "optional": false, "description": ""}]}, {"name": "AnswerCheckQuery<PERSON>ult", "properties": [{"name": "CertificationAnswerId", "type": "number", "optional": false, "description": ""}, {"name": "CertificationId", "type": "string", "optional": false, "description": ""}, {"name": "SchoolId", "type": "number", "optional": false, "description": ""}]}, {"name": "AnswerSaveResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "AnswerSaveResult", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "CertificationAnswersResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "CertificationAnswersData", "optional": false, "description": ""}]}, {"name": "SingleAnswerResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "AnswerInfo | null", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "AnswerDeleteResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationAnswersParams", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}]}, {"name": "SingleAnswerParams", "properties": [{"name": "questionId", "type": "string", "optional": false, "description": ""}, {"name": "certificationId", "type": "string", "optional": false, "description": ""}]}, {"name": "AnswerDeleteParams", "properties": [{"name": "answerId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "auth", "file": "models/auth.ts", "interfaces": [{"name": "LoginResult", "properties": [{"name": "valid", "type": "boolean", "optional": false, "description": ""}, {"name": "user", "type": "UserProfile", "optional": true, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "TokenValidationResult", "properties": [{"name": "valid", "type": "boolean", "optional": false, "description": ""}, {"name": "user", "type": "UserProfile", "optional": true, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "AccountRow", "properties": [{"name": "AccountId", "type": "number", "optional": false, "description": ""}, {"name": "Username", "type": "string", "optional": false, "description": ""}, {"name": "password", "type": "string", "optional": false, "description": ""}, {"name": "password_salt", "type": "string", "optional": false, "description": ""}, {"name": "Email", "type": "string", "optional": true, "description": ""}, {"name": "Phone", "type": "string", "optional": true, "description": ""}, {"name": "Status", "type": "number", "optional": false, "description": ""}, {"name": "IsSchoolPartner", "type": "number", "optional": true, "description": ""}, {"name": "IsEpaUser", "type": "number", "optional": true, "description": ""}, {"name": "IsGuidanceTeam", "type": "number", "optional": true, "description": ""}, {"name": "SchoolId", "type": "number", "optional": true, "description": ""}]}, {"name": "MemberProfileRow", "properties": [{"name": "MemberName", "type": "string", "optional": true, "description": ""}, {"name": "EnglishName", "type": "string", "optional": true, "description": ""}, {"name": "JobTitle", "type": "string", "optional": true, "description": ""}, {"name": "ContactNumber", "type": "string", "optional": true, "description": ""}, {"name": "Email", "type": "string", "optional": true, "description": ""}]}, {"name": "SchoolRow", "properties": [{"name": "Id", "type": "number", "optional": false, "description": ""}, {"name": "Name", "type": "string", "optional": true, "description": ""}, {"name": "EnglishName", "type": "string", "optional": true, "description": ""}, {"name": "Code", "type": "string", "optional": true, "description": ""}, {"name": "Address", "type": "string", "optional": true, "description": ""}, {"name": "Phone", "type": "string", "optional": true, "description": ""}, {"name": "Email", "type": "string", "optional": true, "description": ""}, {"name": "Website", "type": "string", "optional": true, "description": ""}]}, {"name": "TokenRow", "properties": [{"name": "UserTokenSid", "type": "number", "optional": false, "description": ""}, {"name": "Token", "type": "string", "optional": false, "description": ""}, {"name": "TokenType", "type": "string", "optional": false, "description": ""}, {"name": "ExpirationDate", "type": "Date", "optional": false, "description": ""}, {"name": "IsActive", "type": "number", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "LastAccessDate", "type": "Date", "optional": true, "description": ""}]}, {"name": "TokenValidationRequest", "properties": [{"name": "userToken", "type": "string", "optional": false, "description": ""}]}, {"name": "TokenLoginRequest", "properties": [{"name": "token", "type": "string", "optional": false, "description": ""}]}, {"name": "PasswordLoginRequest", "properties": [{"name": "account", "type": "string", "optional": false, "description": ""}, {"name": "password", "type": "string", "optional": false, "description": ""}]}, {"name": "ChangePasswordRequest", "properties": [{"name": "oldPassword", "type": "string", "optional": false, "description": ""}, {"name": "newPassword", "type": "string", "optional": false, "description": ""}]}, {"name": "AdminResetPasswordRequest", "properties": [{"name": "accountIds", "type": "number[]", "optional": false, "description": ""}, {"name": "newPassword", "type": "string", "optional": false, "description": ""}]}, {"name": "CreateTokenRequest", "properties": [{"name": "accountId", "type": "number", "optional": true, "description": ""}, {"name": "tokenType", "type": "string", "optional": true, "description": ""}, {"name": "validityDays", "type": "number", "optional": true, "description": ""}]}, {"name": "TokenValidationResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "valid", "type": "boolean", "optional": false, "description": ""}, {"name": "user", "type": "UserProfile", "optional": true, "description": ""}, {"name": "details", "type": "{", "optional": true, "description": ""}, {"name": "userRole", "type": "string", "optional": true, "description": ""}, {"name": "allowedRoles", "type": "string[]", "optional": true, "description": ""}, {"name": "action", "type": "string", "optional": true, "description": ""}]}, {"name": "PasswordLoginResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "user", "type": "UserProfile", "optional": true, "description": ""}, {"name": "valid", "type": "boolean", "optional": true, "description": ""}, {"name": "token", "type": "string", "optional": true, "description": ""}, {"name": "details", "type": "{", "optional": true, "description": ""}, {"name": "userRole", "type": "string", "optional": true, "description": ""}, {"name": "allowedRoles", "type": "string[]", "optional": true, "description": ""}, {"name": "action", "type": "string", "optional": true, "description": ""}]}, {"name": "ChangePasswordResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "user", "type": "{", "optional": true, "description": ""}, {"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "account", "type": "string", "optional": false, "description": ""}]}, {"name": "AdminResetPasswordResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "results", "type": "Array<{", "optional": false, "description": ""}, {"name": "accountId", "type": "number", "optional": false, "description": ""}, {"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CreateTokenResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": true, "description": ""}, {"name": "token", "type": "string", "optional": false, "description": ""}, {"name": "tokenType", "type": "string", "optional": false, "description": ""}, {"name": "expirationDate", "type": "string", "optional": false, "description": ""}, {"name": "accountId", "type": "number", "optional": false, "description": ""}]}, {"name": "LogoutResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "RoleCheckResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "valid", "type": "boolean", "optional": false, "description": ""}, {"name": "user", "type": "UserProfile", "optional": true, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}, {"name": "details", "type": "{", "optional": true, "description": ""}, {"name": "userRole", "type": "string", "optional": true, "description": ""}, {"name": "allowedRoles", "type": "string[]", "optional": true, "description": ""}, {"name": "action", "type": "string", "optional": true, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "campus-submission", "file": "models/campus-submission.ts", "interfaces": [{"name": "CampusSubmission", "properties": [{"name": "submissionId", "type": "string", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": false, "description": ""}, {"name": "description", "type": "string", "optional": false, "description": ""}, {"name": "status", "type": "number", "optional": false, "description": ""}, {"name": "statusText", "type": "string", "optional": false, "description": ""}, {"name": "submissionDate", "type": "string", "optional": false, "description": ""}, {"name": "createdTime", "type": "string", "optional": false, "description": ""}, {"name": "updatedTime", "type": "string", "optional": true, "description": ""}, {"name": "badgeType", "type": "number", "optional": false, "description": ""}, {"name": "featuredStatus", "type": "number", "optional": false, "description": ""}]}, {"name": "CampusSubmissionAttachment", "properties": [{"name": "attachmentId", "type": "string", "optional": false, "description": ""}, {"name": "fileEntryId", "type": "string", "optional": true, "description": ""}, {"name": "contentTypeCode", "type": "string", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": true, "description": ""}, {"name": "altUrl", "type": "string", "optional": true, "description": ""}]}, {"name": "CreateCampusSubmissionRequest", "properties": [{"name": "zhTitle", "type": "string", "optional": false, "description": ""}, {"name": "<PERSON>h<PERSON><PERSON>nt", "type": "string", "optional": false, "description": ""}, {"name": "enTitle", "type": "string", "optional": true, "description": ""}, {"name": "enContent", "type": "string", "optional": true, "description": ""}, {"name": "attachments", "type": "CampusSubmissionAttachment[]", "optional": true, "description": ""}]}, {"name": "SchoolInfo", "properties": [{"name": "schoolId", "type": "number", "optional": false, "description": ""}, {"name": "schoolName", "type": "string", "optional": false, "description": ""}, {"name": "AccountId", "type": "number", "optional": false, "description": ""}]}, {"name": "SubmissionQueryResult", "properties": [{"name": "CampusSubmissionId", "type": "string", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": false, "description": ""}, {"name": "description", "type": "string", "optional": true, "description": ""}, {"name": "SubmissionStatus", "type": "number", "optional": false, "description": ""}, {"name": "ReviewStatus", "type": "number", "optional": true, "description": ""}, {"name": "CampusSubmissionReviewId", "type": "string", "optional": true, "description": ""}, {"name": "ReviewComment", "type": "string", "optional": true, "description": ""}, {"name": "SubmissionDate", "type": "string", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "string", "optional": false, "description": ""}, {"name": "UpdatedTime", "type": "string", "optional": true, "description": ""}, {"name": "BadgeType", "type": "number", "optional": false, "description": ""}, {"name": "FeaturedStatus", "type": "number", "optional": false, "description": ""}]}, {"name": "SubmissionDetailQueryResult", "properties": [{"name": "CampusSubmissionId", "type": "string", "optional": false, "description": ""}, {"name": "ZhTitle", "type": "string", "optional": false, "description": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "optional": false, "description": ""}, {"name": "EnTitle", "type": "string", "optional": true, "description": ""}, {"name": "En<PERSON><PERSON>nt", "type": "string", "optional": true, "description": ""}, {"name": "SubmissionStatus", "type": "number", "optional": false, "description": ""}, {"name": "ReviewStatus", "type": "number", "optional": true, "description": ""}, {"name": "ReviewComment", "type": "string", "optional": true, "description": ""}, {"name": "SubmissionDate", "type": "string", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "string", "optional": false, "description": ""}, {"name": "UpdatedTime", "type": "string", "optional": true, "description": ""}, {"name": "BadgeType", "type": "number", "optional": false, "description": ""}, {"name": "FeaturedStatus", "type": "number", "optional": false, "description": ""}]}, {"name": "AttachmentQueryResult", "properties": [{"name": "AttachmentId", "type": "string", "optional": false, "description": ""}, {"name": "FileEntryId", "type": "string", "optional": true, "description": ""}, {"name": "ContentTypeCode", "type": "string", "optional": false, "description": ""}, {"name": "Title", "type": "string", "optional": true, "description": ""}, {"name": "AltUrl", "type": "string", "optional": true, "description": ""}]}, {"name": "CampusSubmissionListResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "CampusSubmission[]", "optional": false, "description": ""}, {"name": "schoolInfo", "type": "{", "optional": false, "description": ""}, {"name": "schoolId", "type": "number", "optional": false, "description": ""}, {"name": "schoolName", "type": "string", "optional": false, "description": ""}]}, {"name": "CampusSubmissionDetailResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "CampusSubmissionDetail", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "CampusSubmissionCreateResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "submissionId", "type": "string", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CampusSubmissionUpdateResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "submissionId", "type": "string", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CampusSubmissionDeleteResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CampusSubmissionSubmitResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "submissionId", "type": "string", "optional": false, "description": ""}, {"name": "reviewId", "type": "string", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CampusSubmissionWithdrawResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "submissionId", "type": "string", "optional": false, "description": ""}, {"name": "reviewId", "type": "string", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CampusSubmissionListQueryParams", "properties": [{"name": "limit", "type": "string", "optional": true, "description": ""}, {"name": "page", "type": "string", "optional": true, "description": ""}]}, {"name": "CampusSubmissionDetailParams", "properties": [{"name": "submissionId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "certificate", "file": "models/certificate.ts", "interfaces": [{"name": "CertificateInfo", "properties": [{"name": "subject", "type": "string", "optional": false, "description": ""}, {"name": "issuer", "type": "string", "optional": false, "description": ""}, {"name": "serialNumber", "type": "string", "optional": false, "description": ""}, {"name": "validFrom", "type": "string", "optional": false, "description": ""}, {"name": "validTo", "type": "string", "optional": false, "description": ""}, {"name": "fingerprint", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificateValidateRequest", "properties": [{"name": "certificateData", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificateBindRequest", "properties": [{"name": "certificateInfo", "type": "CertificateInfo", "optional": false, "description": ""}, {"name": "accountId", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificateLoginRequest", "properties": [{"name": "certificateData", "type": "string", "optional": false, "description": ""}, {"name": "password", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificateValidationResult", "properties": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "boolean", "optional": false, "description": ""}, {"name": "certificateInfo", "type": "CertificateInfo", "optional": false, "description": ""}]}, {"name": "CertificateBindResult", "properties": [{"name": "certificateId", "type": "string", "optional": false, "description": ""}, {"name": "bindTime", "type": "string", "optional": false, "description": ""}, {"name": "status", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificateBindingInfo", "properties": [{"name": "accountId", "type": "string", "optional": false, "description": ""}, {"name": "hasCertificate", "type": "boolean", "optional": false, "description": ""}, {"name": "certificateId", "type": "string | null", "optional": false, "description": ""}, {"name": "lastUpdateTime", "type": "Date", "optional": false, "description": ""}]}, {"name": "CertificateLoginResult", "properties": [{"name": "token", "type": "string", "optional": false, "description": ""}, {"name": "user", "type": "{", "optional": false, "description": ""}, {"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "username", "type": "string", "optional": false, "description": ""}, {"name": "displayName", "type": "string", "optional": false, "description": ""}, {"name": "role", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificateTestResult", "properties": [{"name": "userId", "type": "string", "optional": false, "description": ""}, {"name": "userAccount", "type": "string", "optional": false, "description": ""}, {"name": "userRole", "type": "string", "optional": false, "description": ""}, {"name": "timestamp", "type": "string", "optional": false, "description": ""}]}, {"name": "Account<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": [{"name": "citizen_digital_number", "type": "string", "optional": false, "description": ""}]}, {"name": "Account<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": [{"name": "sid", "type": "string", "optional": false, "description": ""}, {"name": "account", "type": "string", "optional": false, "description": ""}, {"name": "cname", "type": "string", "optional": false, "description": ""}, {"name": "isuse", "type": "number", "optional": false, "description": ""}, {"name": "member_role", "type": "string", "optional": false, "description": ""}]}, {"name": "BindingQueryResult", "properties": [{"name": "accountId", "type": "string", "optional": false, "description": ""}, {"name": "certificateId", "type": "string", "optional": false, "description": ""}, {"name": "lastUpdateTime", "type": "Date", "optional": false, "description": ""}]}, {"name": "CertificateValidateResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "CertificateValidationResult", "optional": true, "description": ""}]}, {"name": "CertificateBindResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "CertificateBindResult", "optional": true, "description": ""}]}, {"name": "CertificateUnbindResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificateBindingsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}, {"name": "data", "type": "CertificateBindingInfo", "optional": true, "description": ""}]}, {"name": "CertificateLoginResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "CertificateLoginResult", "optional": true, "description": ""}]}, {"name": "CertificateTestResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "CertificateTestResult", "optional": true, "description": ""}]}, {"name": "CertificateUnbindParams", "properties": [{"name": "accountId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "certification", "file": "models/certification.ts", "interfaces": [{"name": "Certification", "properties": [{"name": "certificationId", "type": "number", "optional": false, "description": ""}, {"name": "schoolId", "type": "number", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "reviewStatus", "type": "number", "optional": false, "description": ""}, {"name": "reviewDate", "type": "Date", "optional": true, "description": ""}, {"name": "approvedDate", "type": "Date", "optional": true, "description": ""}, {"name": "createdTime", "type": "Date", "optional": false, "description": ""}, {"name": "updatedTime", "type": "Date", "optional": true, "description": ""}, {"name": "createdUserId", "type": "number", "optional": true, "description": ""}, {"name": "reviewerId", "type": "number", "optional": true, "description": ""}, {"name": "addType", "type": "string", "optional": true, "description": ""}, {"name": "status", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationListItem", "properties": [{"name": "id", "type": "number", "optional": false, "description": ""}, {"name": "status", "type": "number", "optional": false, "description": ""}, {"name": "statusInfo", "type": "{", "optional": false, "description": ""}, {"name": "label", "type": "string", "optional": false, "description": ""}, {"name": "icon", "type": "string", "optional": false, "description": ""}, {"name": "description", "type": "string", "optional": false, "description": ""}, {"name": "color", "type": "string", "optional": false, "description": ""}, {"name": "bgColor", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationStatistics", "properties": [{"name": "total", "type": "number", "optional": false, "description": ""}, {"name": "drafts", "type": "number", "optional": false, "description": ""}, {"name": "pending", "type": "number", "optional": false, "description": ""}, {"name": "passed", "type": "number", "optional": false, "description": ""}, {"name": "inReview", "type": "number", "optional": false, "description": ""}, {"name": "returned", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationAvailability", "properties": [{"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "available", "type": "boolean", "optional": false, "description": ""}, {"name": "reason", "type": "string", "optional": true, "description": ""}, {"name": "frontendId", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationTypeInfo", "properties": [{"name": "name", "type": "string", "optional": false, "description": ""}, {"name": "fullName", "type": "string", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "isRenewal", "type": "boolean", "optional": false, "description": ""}, {"name": "icon", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationRequest", "properties": [{"name": "certificationType", "type": "string", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}]}, {"name": "SchoolAccount", "properties": [{"name": "schoolId", "type": "number", "optional": false, "description": ""}]}, {"name": "ExistingCertification", "properties": [{"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "reviewStatus", "type": "number", "optional": false, "description": ""}, {"name": "approvedDate", "type": "Date", "optional": true, "description": ""}]}, {"name": "CertificationQueryResult", "properties": [{"name": "CertificationId", "type": "number", "optional": false, "description": ""}, {"name": "SchoolId", "type": "number", "optional": false, "description": ""}, {"name": "Level", "type": "number", "optional": false, "description": ""}, {"name": "ReviewStatus", "type": "number", "optional": false, "description": ""}, {"name": "ReviewDate", "type": "Date", "optional": true, "description": ""}, {"name": "ApprovedDate", "type": "Date", "optional": true, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "UpdatedTime", "type": "Date", "optional": true, "description": ""}, {"name": "CreatedUserId", "type": "number", "optional": true, "description": ""}, {"name": "ReviewerId", "type": "number", "optional": true, "description": ""}, {"name": "AddType", "type": "string", "optional": true, "description": ""}, {"name": "Status", "type": "number", "optional": false, "description": ""}, {"name": "ApplicantName", "type": "string", "optional": true, "description": ""}, {"name": "SchoolName", "type": "string", "optional": true, "description": ""}, {"name": "SchoolEnglishName", "type": "string", "optional": true, "description": ""}, {"name": "ReviewerAccount", "type": "string", "optional": true, "description": ""}]}, {"name": "CertificationListResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "all", "type": "CertificationListItem[]", "optional": false, "description": ""}, {"name": "drafts", "type": "CertificationListItem[]", "optional": false, "description": ""}, {"name": "pending", "type": "CertificationListItem[]", "optional": false, "description": ""}, {"name": "passed", "type": "CertificationListItem[]", "optional": false, "description": ""}, {"name": "statistics", "type": "CertificationStatistics", "optional": false, "description": ""}]}, {"name": "CertificationAvailabilityResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "availability", "type": "CertificationAvailability[]", "optional": false, "description": ""}, {"name": "hasPassedGreenFlag", "type": "boolean", "optional": false, "description": ""}, {"name": "greenFlagApprovedYearsAgo", "type": "number", "optional": false, "description": ""}, {"name": "greenFlagR1ApprovedYearsAgo", "type": "number", "optional": false, "description": ""}, {"name": "greenFlagR2ApprovedYearsAgo", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationDetailRequest", "properties": [{"name": "certificationId", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationDetailResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "Certification", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "CertificationCreateResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "Certification", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationUpdateResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "Certification", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationDeleteResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CertificationDetailParams", "properties": [{"name": "certificationId", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationUpdateParams", "properties": [{"name": "certificationId", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationDeleteParams", "properties": [{"name": "certificationId", "type": "number", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "dashboard", "file": "models/dashboard.ts", "interfaces": [{"name": "CityStatistics", "properties": [{"name": "cityId", "type": "number", "optional": false, "description": ""}, {"name": "cityName", "type": "string", "optional": false, "description": ""}, {"name": "bronzeCount", "type": "number", "optional": false, "description": ""}, {"name": "silverCount", "type": "number", "optional": false, "description": ""}, {"name": "greenFlagCount", "type": "number", "optional": false, "description": ""}, {"name": "totalSchools", "type": "number", "optional": false, "description": ""}]}, {"name": "LatestCertification", "properties": [{"name": "schoolName", "type": "string", "optional": false, "description": ""}, {"name": "certificationLevel", "type": "string", "optional": false, "description": ""}, {"name": "passDate", "type": "string", "optional": false, "description": ""}, {"name": "cityName", "type": "string", "optional": false, "description": ""}]}, {"name": "SchoolCertificationStatus", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "levelName", "type": "string", "optional": false, "description": ""}, {"name": "status", "type": "string", "optional": false, "description": ""}, {"name": "reviewStatus", "type": "number", "optional": false, "description": ""}, {"name": "applyDate", "type": "string", "optional": false, "description": ""}, {"name": "reviewDate", "type": "string", "optional": true, "description": ""}, {"name": "passDate", "type": "string", "optional": true, "description": ""}]}, {"name": "SchoolArticle", "properties": [{"name": "articleId", "type": "string", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": false, "description": ""}, {"name": "summary", "type": "string", "optional": true, "description": ""}, {"name": "status", "type": "string", "optional": false, "description": ""}, {"name": "publishDate", "type": "string", "optional": true, "description": ""}, {"name": "createDate", "type": "string", "optional": false, "description": ""}]}, {"name": "SchoolPassedCertification", "properties": [{"name": "certificationId", "type": "string", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "levelName", "type": "string", "optional": false, "description": ""}, {"name": "passDate", "type": "string", "optional": false, "description": ""}]}, {"name": "SchoolInfo", "properties": [{"name": "schoolId", "type": "string", "optional": false, "description": ""}, {"name": "schoolName", "type": "string", "optional": false, "description": ""}, {"name": "accountId", "type": "number", "optional": false, "description": ""}]}, {"name": "UserCityInfo", "properties": [{"name": "countyId", "type": "number", "optional": false, "description": ""}, {"name": "cityName", "type": "string", "optional": false, "description": ""}]}, {"name": "CityQueryResult", "properties": [{"name": "cityId", "type": "number", "optional": false, "description": ""}, {"name": "cityName", "type": "string", "optional": false, "description": ""}]}, {"name": "StatisticsQueryResult", "properties": [{"name": "totalSchools", "type": "number", "optional": false, "description": ""}, {"name": "bronzeCount", "type": "number", "optional": false, "description": ""}, {"name": "silverCount", "type": "number", "optional": false, "description": ""}, {"name": "greenFlagCount", "type": "number", "optional": false, "description": ""}]}, {"name": "CertificationQueryResult", "properties": [{"name": "CertificationId", "type": "string", "optional": false, "description": ""}, {"name": "Level", "type": "number", "optional": false, "description": ""}, {"name": "ReviewStatus", "type": "number", "optional": false, "description": ""}, {"name": "applyDate", "type": "string", "optional": false, "description": ""}, {"name": "ReviewDate", "type": "string", "optional": true, "description": ""}, {"name": "passDate", "type": "string", "optional": true, "description": ""}, {"name": "levelName", "type": "string", "optional": false, "description": ""}]}, {"name": "LatestCertificationQueryResult", "properties": [{"name": "schoolName", "type": "string", "optional": false, "description": ""}, {"name": "certificationLevel", "type": "string", "optional": false, "description": ""}, {"name": "passDate", "type": "string", "optional": false, "description": ""}, {"name": "cityName", "type": "string", "optional": false, "description": ""}]}, {"name": "TestResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "CityStatistics", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CityStatisticsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "CityStatistics", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "LatestCertificationsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "LatestCertification[]", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "SchoolCurrentCertificationResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "SchoolCertificationStatus | null", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "SchoolArticlesResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "SchoolArticle[]", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "SchoolPassedCertificationsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "SchoolPassedCertification[]", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "CityStatisticsParams", "properties": [{"name": "cityId", "type": "string", "optional": false, "description": ""}]}, {"name": "LatestCertificationsParams", "properties": [{"name": "cityId", "type": "string", "optional": false, "description": ""}]}, {"name": "LatestCertificationsQueryParams", "properties": [{"name": "limit", "type": "string", "optional": true, "description": ""}]}, {"name": "SchoolArticlesQueryParams", "properties": [{"name": "limit", "type": "string", "optional": true, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "file", "file": "models/file.ts", "interfaces": [{"name": "UploadedFile", "properties": [{"name": "originalName", "type": "string", "optional": false, "description": ""}, {"name": "filename", "type": "string", "optional": false, "description": ""}, {"name": "fileUrl", "type": "string", "optional": false, "description": ""}, {"name": "size", "type": "number", "optional": false, "description": ""}, {"name": "mimetype", "type": "string", "optional": false, "description": ""}, {"name": "uploadDate", "type": "string", "optional": false, "description": ""}]}, {"name": "FileInfo", "properties": [{"name": "filename", "type": "string", "optional": false, "description": ""}, {"name": "size", "type": "number", "optional": false, "description": ""}, {"name": "createdAt", "type": "Date", "optional": false, "description": ""}, {"name": "modifiedAt", "type": "Date", "optional": false, "description": ""}, {"name": "fileUrl", "type": "string", "optional": false, "description": ""}]}, {"name": "SupportedFileTypes", "properties": [{"name": "images", "type": "string[]", "optional": false, "description": ""}, {"name": "documents", "type": "string[]", "optional": false, "description": ""}, {"name": "videos", "type": "string[]", "optional": false, "description": ""}]}, {"name": "SchoolLogoInfo", "properties": [{"name": "logoUrl", "type": "string | null", "optional": false, "description": ""}, {"name": "fileName", "type": "string | null", "optional": false, "description": ""}, {"name": "fileId", "type": "string", "optional": true, "description": ""}, {"name": "fileSize", "type": "number", "optional": true, "description": ""}, {"name": "fileType", "type": "string", "optional": true, "description": ""}]}, {"name": "UploadDiagnostics", "properties": [{"name": "timestamp", "type": "string", "optional": false, "description": ""}, {"name": "environment", "type": "string", "optional": false, "description": ""}, {"name": "config", "type": "{", "optional": false, "description": ""}, {"name": "basePath", "type": "string", "optional": false, "description": ""}, {"name": "schoolLogoPath", "type": "string", "optional": false, "description": ""}, {"name": "generalUploadPath", "type": "string", "optional": false, "description": ""}]}, {"name": "DirectoryStatus", "properties": [{"name": "exists", "type": "boolean", "optional": false, "description": ""}, {"name": "path", "type": "string", "optional": false, "description": ""}, {"name": "error", "type": "string", "optional": true, "description": ""}]}, {"name": "PermissionStatus", "properties": [{"name": "readable", "type": "boolean", "optional": false, "description": ""}, {"name": "writable", "type": "boolean", "optional": false, "description": ""}, {"name": "writeError", "type": "string", "optional": true, "description": ""}]}, {"name": "FileEntry", "properties": [{"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "type", "type": "string", "optional": false, "description": ""}, {"name": "path", "type": "string", "optional": false, "description": ""}, {"name": "originalFileName", "type": "string", "optional": false, "description": ""}, {"name": "originalExtension", "type": "string", "optional": false, "description": ""}, {"name": "fileName", "type": "string", "optional": false, "description": ""}, {"name": "extension", "type": "string", "optional": false, "description": ""}, {"name": "createdTime", "type": "Date", "optional": true, "description": ""}, {"name": "updatedTime", "type": "Date", "optional": true, "description": ""}]}, {"name": "SchoolInfo", "properties": [{"name": "id", "type": "number", "optional": false, "description": ""}]}, {"name": "SchoolContent", "properties": [{"name": "schoolId", "type": "number", "optional": false, "description": ""}, {"name": "localeCode", "type": "string", "optional": false, "description": ""}, {"name": "logoFileId", "type": "string", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": true, "description": ""}, {"name": "createdUserId", "type": "number", "optional": true, "description": ""}, {"name": "updatedUserId", "type": "number", "optional": true, "description": ""}]}, {"name": "FileUploadRequest", "properties": [{"name": "file_type", "type": "string", "optional": true, "description": ""}, {"name": "certification_sid", "type": "string", "optional": true, "description": ""}, {"name": "question_sid", "type": "string", "optional": true, "description": ""}]}, {"name": "FileEntryQueryResult", "properties": [{"name": "Id", "type": "string", "optional": false, "description": ""}, {"name": "Type", "type": "string", "optional": false, "description": ""}, {"name": "Path", "type": "string", "optional": false, "description": ""}, {"name": "OriginalFileName", "type": "string", "optional": false, "description": ""}, {"name": "OriginalExtension", "type": "string", "optional": false, "description": ""}, {"name": "FileName", "type": "string", "optional": false, "description": ""}, {"name": "Extension", "type": "string", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": true, "description": ""}, {"name": "UpdatedTime", "type": "Date", "optional": true, "description": ""}]}, {"name": "SchoolQueryResult", "properties": [{"name": "Id", "type": "number", "optional": false, "description": ""}]}, {"name": "SchoolLogoQueryResult", "properties": [{"name": "logoUrl", "type": "string", "optional": false, "description": ""}, {"name": "FileName", "type": "string", "optional": false, "description": ""}]}, {"name": "FileUploadResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "UploadedFile | UploadedFile[]", "optional": false, "description": ""}]}, {"name": "SingleFileUploadResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "url", "type": "string", "optional": false, "description": ""}, {"name": "fileName", "type": "string", "optional": false, "description": ""}, {"name": "fileType", "type": "string", "optional": false, "description": ""}, {"name": "fileSize", "type": "number", "optional": false, "description": ""}]}, {"name": "FileDeleteResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "FileInfoResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "FileInfo", "optional": true, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "SupportedTypesResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "SupportedFileTypes", "optional": false, "description": ""}]}, {"name": "SchoolLogoUploadResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "SchoolLogoInfo", "optional": false, "description": ""}]}, {"name": "SchoolLogoGetResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "SchoolLogoInfo", "optional": false, "description": ""}]}, {"name": "UploadDiagnosticsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "UploadDiagnostics", "optional": false, "description": ""}]}, {"name": "FileDeleteParams", "properties": [{"name": "filename", "type": "string", "optional": false, "description": ""}]}, {"name": "FileInfoParams", "properties": [{"name": "filename", "type": "string", "optional": false, "description": ""}]}, {"name": "FileDownloadParams", "properties": [{"name": "filename", "type": "string", "optional": false, "description": ""}]}, {"name": "SchoolLogoGetParams", "properties": [{"name": "accountId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "location", "file": "models/location.ts", "interfaces": [{"name": "LocationArea", "properties": [{"name": "id", "type": "number", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": false, "description": ""}, {"name": "cityId", "type": "number", "optional": false, "description": ""}]}, {"name": "LocationCity", "properties": [{"name": "id", "type": "number", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": false, "description": ""}]}, {"name": "LocationCityWithAreas", "properties": [{"name": "id", "type": "number", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": false, "description": ""}, {"name": "areas", "type": "LocationArea[]", "optional": false, "description": ""}]}, {"name": "CityQueryResult", "properties": [{"name": "CountyId", "type": "number", "optional": false, "description": ""}, {"name": "Name", "type": "string", "optional": false, "description": ""}]}, {"name": "AreaQueryResult", "properties": [{"name": "DistrictId", "type": "number", "optional": false, "description": ""}, {"name": "Name", "type": "string", "optional": false, "description": ""}, {"name": "CountyId", "type": "number", "optional": false, "description": ""}]}, {"name": "HierarchyQueryResult", "properties": [{"name": "CountyId", "type": "number", "optional": false, "description": ""}, {"name": "CountyName", "type": "string", "optional": false, "description": ""}, {"name": "DistrictId", "type": "number", "optional": true, "description": ""}, {"name": "DistrictName", "type": "string", "optional": true, "description": ""}]}, {"name": "LocationCitiesResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}, {"name": "data", "type": "LocationCity[]", "optional": true, "description": ""}, {"name": "error", "type": "string", "optional": true, "description": ""}]}, {"name": "LocationAreasResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}, {"name": "data", "type": "LocationArea[]", "optional": true, "description": ""}, {"name": "error", "type": "string", "optional": true, "description": ""}]}, {"name": "LocationHierarchyResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}, {"name": "data", "type": "LocationCityWithAreas[]", "optional": true, "description": ""}, {"name": "error", "type": "string", "optional": true, "description": ""}]}, {"name": "LocationAreasParams", "properties": [{"name": "cityId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "profile", "file": "models/profile.ts", "interfaces": [{"name": "MemberProfile", "properties": [{"name": "accountId", "type": "number", "optional": false, "description": ""}, {"name": "account", "type": "string", "optional": false, "description": ""}, {"name": "email", "type": "string", "optional": false, "description": ""}, {"name": "telephone", "type": "string", "optional": true, "description": ""}, {"name": "phone", "type": "string", "optional": true, "description": ""}, {"name": "address", "type": "string", "optional": true, "description": ""}, {"name": "countyId", "type": "number", "optional": true, "description": ""}, {"name": "isSchoolPartner", "type": "number", "optional": false, "description": ""}, {"name": "isEpaUser", "type": "number", "optional": false, "description": ""}, {"name": "isGuidanceTeam", "type": "number", "optional": false, "description": ""}, {"name": "memberName", "type": "string", "optional": true, "description": ""}, {"name": "memberEmail", "type": "string", "optional": true, "description": ""}, {"name": "memberTelephone", "type": "string", "optional": true, "description": ""}, {"name": "memberPhone", "type": "string", "optional": true, "description": ""}, {"name": "member<PERSON><PERSON><PERSON>", "type": "string", "optional": true, "description": ""}, {"name": "memberUrl", "type": "string", "optional": true, "description": ""}, {"name": "jobTitle", "type": "string", "optional": true, "description": ""}, {"name": "placeName", "type": "string", "optional": true, "description": ""}, {"name": "memberRole", "type": "string", "optional": true, "description": ""}, {"name": "memberIntroduction", "type": "string", "optional": true, "description": ""}, {"name": "memberExchange", "type": "string", "optional": true, "description": ""}, {"name": "memberNameEn", "type": "string", "optional": true, "description": ""}, {"name": "countyNameZh", "type": "string", "optional": true, "description": ""}, {"name": "countyNameEn", "type": "string", "optional": true, "description": ""}]}, {"name": "SchoolInfo", "properties": [{"name": "schoolId", "type": "number", "optional": false, "description": ""}, {"name": "schoolCode", "type": "string", "optional": true, "description": ""}, {"name": "schoolPhone", "type": "string", "optional": true, "description": ""}, {"name": "schoolMobile", "type": "string", "optional": true, "description": ""}, {"name": "schoolEmail", "type": "string", "optional": true, "description": ""}, {"name": "schoolCountyId", "type": "number", "optional": true, "description": ""}, {"name": "schoolDistrictId", "type": "number", "optional": true, "description": ""}, {"name": "schoolName", "type": "string", "optional": true, "description": ""}, {"name": "schoolAddress", "type": "string", "optional": true, "description": ""}, {"name": "schoolDepartment", "type": "string", "optional": true, "description": ""}, {"name": "schoolJobTitle", "type": "string", "optional": true, "description": ""}, {"name": "schoolIntroduction", "type": "string", "optional": true, "description": ""}, {"name": "schoolWebsite", "type": "string", "optional": true, "description": ""}, {"name": "schoolNameEn", "type": "string", "optional": true, "description": ""}, {"name": "schoolAddressEn", "type": "string", "optional": true, "description": ""}, {"name": "schoolCountyNameZh", "type": "string", "optional": true, "description": ""}, {"name": "schoolCountyNameEn", "type": "string", "optional": true, "description": ""}, {"name": "logoPath", "type": "string", "optional": true, "description": ""}]}, {"name": "PrincipalInfo", "properties": [{"name": "principalName", "type": "string", "optional": true, "description": ""}, {"name": "principalTelephone", "type": "string", "optional": true, "description": ""}, {"name": "principalPhone", "type": "string", "optional": true, "description": ""}, {"name": "principalEmail", "type": "string", "optional": true, "description": ""}]}, {"name": "ContactInfo", "properties": [{"name": "contactId", "type": "number", "optional": false, "description": ""}, {"name": "contactName", "type": "string", "optional": false, "description": ""}, {"name": "jobTitle", "type": "string", "optional": false, "description": ""}, {"name": "contactTelephone", "type": "string", "optional": true, "description": ""}, {"name": "contactPhone", "type": "string", "optional": true, "description": ""}, {"name": "contactEmail", "type": "string", "optional": true, "description": ""}]}, {"name": "SchoolStatistics", "properties": [{"name": "schoolStatisticsId", "type": "number", "optional": false, "description": ""}, {"name": "staffTotal", "type": "number", "optional": false, "description": ""}, {"name": "elementary1", "type": "number", "optional": false, "description": ""}, {"name": "elementary2", "type": "number", "optional": false, "description": ""}, {"name": "elementary3", "type": "number", "optional": false, "description": ""}, {"name": "elementary4", "type": "number", "optional": false, "description": ""}, {"name": "elementary5", "type": "number", "optional": false, "description": ""}, {"name": "elementary6", "type": "number", "optional": false, "description": ""}, {"name": "middle7", "type": "number", "optional": false, "description": ""}, {"name": "middle8", "type": "number", "optional": false, "description": ""}, {"name": "middle9", "type": "number", "optional": false, "description": ""}, {"name": "high10", "type": "number", "optional": false, "description": ""}, {"name": "high11", "type": "number", "optional": false, "description": ""}, {"name": "high12", "type": "number", "optional": false, "description": ""}, {"name": "writeDate", "type": "Date", "optional": false, "description": ""}]}, {"name": "MemberQueryResult", "properties": [{"name": "AccountId", "type": "number", "optional": false, "description": ""}, {"name": "account", "type": "string", "optional": false, "description": ""}, {"name": "email", "type": "string", "optional": false, "description": ""}, {"name": "tel", "type": "string", "optional": false, "description": ""}, {"name": "phone", "type": "string", "optional": false, "description": ""}, {"name": "address", "type": "string", "optional": false, "description": ""}, {"name": "account_county_id", "type": "number", "optional": false, "description": ""}, {"name": "is_school_partner", "type": "number", "optional": false, "description": ""}, {"name": "is_epa_user", "type": "number", "optional": false, "description": ""}, {"name": "is_guidance_team", "type": "number", "optional": false, "description": ""}, {"name": "member_cname_zh", "type": "string", "optional": false, "description": ""}, {"name": "member_email", "type": "string", "optional": false, "description": ""}, {"name": "member_tel", "type": "string", "optional": false, "description": ""}, {"name": "member_phone", "type": "string", "optional": false, "description": ""}, {"name": "member_address", "type": "string", "optional": false, "description": ""}, {"name": "member_url", "type": "string", "optional": false, "description": ""}, {"name": "job_cname", "type": "string", "optional": false, "description": ""}, {"name": "place_cname", "type": "string", "optional": false, "description": ""}, {"name": "member_role", "type": "string", "optional": false, "description": ""}, {"name": "member_Introduction", "type": "string", "optional": false, "description": ""}, {"name": "member_exchange", "type": "string", "optional": false, "description": ""}, {"name": "member_cname_en", "type": "string", "optional": false, "description": ""}, {"name": "county_name_zh", "type": "string", "optional": false, "description": ""}, {"name": "county_name_en", "type": "string", "optional": false, "description": ""}, {"name": "school_county_name_zh", "type": "string", "optional": false, "description": ""}, {"name": "school_county_name_en", "type": "string", "optional": false, "description": ""}, {"name": "school_id", "type": "number", "optional": false, "description": ""}, {"name": "school_code", "type": "string", "optional": false, "description": ""}, {"name": "school_phone", "type": "string", "optional": false, "description": ""}, {"name": "school_mobile", "type": "string", "optional": false, "description": ""}, {"name": "school_email", "type": "string", "optional": false, "description": ""}, {"name": "school_county_id", "type": "number", "optional": false, "description": ""}, {"name": "school_district_id", "type": "number", "optional": false, "description": ""}, {"name": "school_name", "type": "string", "optional": false, "description": ""}, {"name": "school_address", "type": "string", "optional": false, "description": ""}, {"name": "school_department", "type": "string", "optional": false, "description": ""}, {"name": "school_job_title", "type": "string", "optional": false, "description": ""}, {"name": "school_introduction", "type": "string", "optional": false, "description": ""}, {"name": "school_website", "type": "string", "optional": false, "description": ""}, {"name": "school_name_en", "type": "string", "optional": false, "description": ""}, {"name": "school_address_en", "type": "string", "optional": false, "description": ""}, {"name": "principal_cname", "type": "string", "optional": false, "description": ""}, {"name": "principal_tel", "type": "string", "optional": false, "description": ""}, {"name": "principal_phone", "type": "string", "optional": false, "description": ""}, {"name": "principal_email", "type": "string", "optional": false, "description": ""}, {"name": "school_logo_path", "type": "string", "optional": true, "description": ""}]}, {"name": "ContactQueryResult", "properties": [{"name": "contact_cname", "type": "string", "optional": false, "description": ""}, {"name": "contact_job_title", "type": "string", "optional": false, "description": ""}, {"name": "contact_tel", "type": "string", "optional": false, "description": ""}, {"name": "contact_phone", "type": "string", "optional": false, "description": ""}, {"name": "contact_email", "type": "string", "optional": false, "description": ""}, {"name": "contact_sid", "type": "number", "optional": false, "description": ""}]}, {"name": "StatisticsQueryResult", "properties": [{"name": "staff_total", "type": "number", "optional": false, "description": ""}, {"name": "elementary1", "type": "number", "optional": false, "description": ""}, {"name": "elementary2", "type": "number", "optional": false, "description": ""}, {"name": "elementary3", "type": "number", "optional": false, "description": ""}, {"name": "elementary4", "type": "number", "optional": false, "description": ""}, {"name": "elementary5", "type": "number", "optional": false, "description": ""}, {"name": "elementary6", "type": "number", "optional": false, "description": ""}, {"name": "middle7", "type": "number", "optional": false, "description": ""}, {"name": "middle8", "type": "number", "optional": false, "description": ""}, {"name": "middle9", "type": "number", "optional": false, "description": ""}, {"name": "hight10", "type": "number", "optional": false, "description": ""}, {"name": "hight11", "type": "number", "optional": false, "description": ""}, {"name": "hight12", "type": "number", "optional": false, "description": ""}, {"name": "write_date", "type": "Date", "optional": false, "description": ""}, {"name": "school_statistics_sid", "type": "number", "optional": false, "description": ""}]}, {"name": "UpdateMemberProfileRequest", "properties": [{"name": "jobTitle", "type": "string", "optional": true, "description": ""}, {"name": "memberName", "type": "string", "optional": true, "description": ""}, {"name": "memberNameEn", "type": "string", "optional": true, "description": ""}, {"name": "officePhone", "type": "string", "optional": true, "description": ""}, {"name": "mobilePhone", "type": "string", "optional": true, "description": ""}, {"name": "email", "type": "string", "optional": true, "description": ""}]}, {"name": "UpdateSchoolBasicRequest", "properties": [{"name": "schoolName", "type": "string", "optional": true, "description": ""}, {"name": "schoolNameEn", "type": "string", "optional": true, "description": ""}, {"name": "countyId", "type": "number", "optional": true, "description": ""}, {"name": "districtId", "type": "number", "optional": true, "description": ""}, {"name": "address", "type": "string", "optional": true, "description": ""}, {"name": "addressEn", "type": "string", "optional": true, "description": ""}, {"name": "phone", "type": "string", "optional": true, "description": ""}, {"name": "mobile", "type": "string", "optional": true, "description": ""}, {"name": "email", "type": "string", "optional": true, "description": ""}, {"name": "website", "type": "string", "optional": true, "description": ""}]}, {"name": "UpdatePrincipalRequest", "properties": [{"name": "principalName", "type": "string", "optional": true, "description": ""}, {"name": "principalPhone", "type": "string", "optional": true, "description": ""}, {"name": "principalMobile", "type": "string", "optional": true, "description": ""}, {"name": "principalEmail", "type": "string", "optional": true, "description": ""}]}, {"name": "UpdateContactsRequest", "properties": [{"name": "contacts", "type": "Array<{", "optional": false, "description": ""}, {"name": "contactId", "type": "number", "optional": true, "description": ""}, {"name": "contactName", "type": "string", "optional": false, "description": ""}, {"name": "jobTitle", "type": "string", "optional": false, "description": ""}, {"name": "contactPhone", "type": "string", "optional": true, "description": ""}, {"name": "contactMobile", "type": "string", "optional": true, "description": ""}, {"name": "contactEmail", "type": "string", "optional": true, "description": ""}]}, {"name": "UpdateStatisticsRequest", "properties": [{"name": "staffTotal", "type": "number", "optional": true, "description": ""}, {"name": "elementary1", "type": "number", "optional": true, "description": ""}, {"name": "elementary2", "type": "number", "optional": true, "description": ""}, {"name": "elementary3", "type": "number", "optional": true, "description": ""}, {"name": "elementary4", "type": "number", "optional": true, "description": ""}, {"name": "elementary5", "type": "number", "optional": true, "description": ""}, {"name": "elementary6", "type": "number", "optional": true, "description": ""}, {"name": "middle7", "type": "number", "optional": true, "description": ""}, {"name": "middle8", "type": "number", "optional": true, "description": ""}, {"name": "middle9", "type": "number", "optional": true, "description": ""}, {"name": "high10", "type": "number", "optional": true, "description": ""}, {"name": "high11", "type": "number", "optional": true, "description": ""}, {"name": "high12", "type": "number", "optional": true, "description": ""}]}, {"name": "ProfileResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "FullProfile", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "UpdateProfileResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": true, "description": ""}, {"name": "updated", "type": "boolean", "optional": false, "description": ""}, {"name": "profile", "type": "FullProfile", "optional": true, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "question", "file": "models/question.ts", "interfaces": [{"name": "QuestionModel", "properties": [{"name": "id", "type": "number", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": false, "description": ""}, {"name": "parentId", "type": "number | null", "optional": false, "description": ""}, {"name": "step", "type": "number", "optional": false, "description": ""}, {"name": "sequence", "type": "number", "optional": false, "description": ""}, {"name": "isRequired", "type": "boolean", "optional": false, "description": ""}, {"name": "templateId", "type": "number | null", "optional": false, "description": ""}, {"name": "isRenewed", "type": "boolean", "optional": false, "description": ""}, {"name": "status", "type": "number", "optional": false, "description": ""}, {"name": "createdTime", "type": "Date", "optional": true, "description": ""}, {"name": "updatedTime", "type": "Date", "optional": true, "description": ""}]}, {"name": "StepInfo", "properties": [{"name": "step", "type": "number", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": false, "description": ""}, {"name": "description", "type": "string", "optional": false, "description": ""}, {"name": "questionCount", "type": "number", "optional": false, "description": ""}, {"name": "isEnabled", "type": "boolean", "optional": false, "description": ""}]}, {"name": "FormQuestion", "properties": [{"name": "sid", "type": "number", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": false, "description": ""}, {"name": "question_tpl", "type": "number", "optional": false, "description": ""}, {"name": "is_renew", "type": "number", "optional": false, "description": ""}, {"name": "step", "type": "number", "optional": false, "description": ""}, {"name": "sequence", "type": "number", "optional": false, "description": ""}, {"name": "parent_id", "type": "number | null", "optional": false, "description": ""}]}, {"name": "QuestionGroup", "properties": []}, {"name": "StepQuestionGroup", "properties": []}, {"name": "StepStatistics", "properties": [{"name": "step", "type": "number", "optional": false, "description": ""}, {"name": "isGreenFlag", "type": "boolean", "optional": false, "description": ""}, {"name": "totalQuestions", "type": "number", "optional": false, "description": ""}, {"name": "parentGroups", "type": "number", "optional": false, "description": ""}]}, {"name": "ParentGroupAnalysis", "properties": [{"name": "parentId", "type": "number", "optional": false, "description": ""}, {"name": "childrenCount", "type": "number", "optional": false, "description": ""}, {"name": "children", "type": "Array<{", "optional": false, "description": ""}, {"name": "id", "type": "number", "optional": false, "description": ""}, {"name": "title", "type": "string", "optional": false, "description": ""}, {"name": "step", "type": "number", "optional": false, "description": ""}, {"name": "template", "type": "number | null", "optional": false, "description": ""}]}, {"name": "QuestionStructureAnalysis", "properties": [{"name": "totalQuestions", "type": "number", "optional": false, "description": ""}, {"name": "stepAnalysis", "type": "Array<{", "optional": false, "description": ""}, {"name": "step", "type": "number", "optional": false, "description": ""}, {"name": "isGreenFlag", "type": "boolean", "optional": false, "description": ""}, {"name": "total", "type": "number", "optional": false, "description": ""}, {"name": "with<PERSON><PERSON>nt", "type": "number", "optional": false, "description": ""}, {"name": "uniqueParents", "type": "number", "optional": false, "description": ""}]}, {"name": "QuestionListResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "QuestionModel[]", "optional": false, "description": ""}, {"name": "step", "type": "number", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "count", "type": "number", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "CertificationStepsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "StepInfo[]", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "totalSteps", "type": "number", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "FormQuestionsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "{", "optional": false, "description": ""}, {"name": "questions", "type": "StepQuestionGroup", "optional": false, "description": ""}, {"name": "stepInfo", "type": "StepStatistics[]", "optional": false, "description": ""}]}, {"name": "QuestionDetailResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "QuestionModel | {", "optional": false, "description": ""}]}, {"name": "QuestionAnalysisResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "QuestionStructureAnalysis", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "QuestionQueryParams", "properties": [{"name": "step", "type": "string", "optional": true, "description": ""}, {"name": "level", "type": "string", "optional": true, "description": ""}]}, {"name": "CertificationStepsQueryParams", "properties": [{"name": "level", "type": "string", "optional": true, "description": ""}]}, {"name": "FormQuestionsQueryParams", "properties": [{"name": "certificationId", "type": "string", "optional": true, "description": ""}]}, {"name": "QuestionDetailParams", "properties": [{"name": "questionId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "template-answer", "file": "models/template-answer.ts", "interfaces": [{"name": "TemplateAnswerSaveRequest", "properties": [{"name": "certification_sid", "type": "string", "optional": false, "description": ""}, {"name": "question_sid", "type": "string", "optional": false, "description": ""}, {"name": "template_id", "type": "number", "optional": false, "description": ""}, {"name": "answer_data", "type": "Record<string, unknown>", "optional": false, "description": ""}, {"name": "question_title", "type": "string", "optional": true, "description": ""}]}, {"name": "TemplateAnswerValidateRequest", "properties": [{"name": "template_id", "type": "number", "optional": false, "description": ""}, {"name": "answer_data", "type": "Record<string, unknown>", "optional": false, "description": ""}]}, {"name": "TemplateAnswerValidationResult", "properties": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "boolean", "optional": false, "description": ""}, {"name": "errors", "type": "string[]", "optional": false, "description": ""}]}, {"name": "TemplateAnswerSaveResult", "properties": [{"name": "certification_sid", "type": "string", "optional": false, "description": ""}, {"name": "question_sid", "type": "string", "optional": false, "description": ""}, {"name": "template_id", "type": "number", "optional": false, "description": ""}, {"name": "question_title", "type": "string", "optional": false, "description": ""}, {"name": "answer_json", "type": "string", "optional": false, "description": ""}, {"name": "raw_answer_data", "type": "Record<string, unknown>", "optional": false, "description": ""}, {"name": "validation_result", "type": "TemplateAnswerValidationResult", "optional": false, "description": ""}, {"name": "timestamp", "type": "string", "optional": false, "description": ""}, {"name": "status", "type": "string", "optional": false, "description": ""}]}, {"name": "YesNoAnswerData", "properties": [{"name": "is_yes_no", "type": "string", "optional": false, "description": ""}]}, {"name": "TeamMemberAnswerData", "properties": [{"name": "student_list", "type": "Array<{", "optional": false, "description": ""}, {"name": "input_1", "type": "string", "optional": false, "description": ""}, {"name": "input_2", "type": "string", "optional": false, "description": ""}, {"name": "input_3", "type": "string", "optional": false, "description": ""}]}, {"name": "MeetingRecordAnswerData", "properties": [{"name": "meeting_date_and_theme", "type": "Array<{", "optional": false, "description": ""}, {"name": "input_1", "type": "string", "optional": false, "description": ""}, {"name": "input_2", "type": "string", "optional": false, "description": ""}]}, {"name": "ShareMeetingAnswerData", "properties": [{"name": "is_yes_no", "type": "string", "optional": false, "description": ""}, {"name": "share_people", "type": "{", "optional": false, "description": ""}, {"name": "checkbox", "type": "string[]", "optional": false, "description": ""}]}, {"name": "RecruitMemberAnswerData", "properties": [{"name": "is_yes_no", "type": "string", "optional": false, "description": ""}, {"name": "textarea", "type": "string", "optional": false, "description": ""}]}, {"name": "PhotoRecordAnswerData", "properties": [{"name": "photo_record", "type": "Array<{", "optional": false, "description": ""}, {"name": "photo_url", "type": "string", "optional": false, "description": ""}, {"name": "photo_name", "type": "string", "optional": false, "description": ""}, {"name": "description", "type": "string", "optional": true, "description": ""}]}, {"name": "EnvironmentPathAnswerData", "properties": [{"name": "improve_path_list", "type": "Array<{", "optional": false, "description": ""}, {"name": "path", "type": "string", "optional": false, "description": ""}, {"name": "cname", "type": "string", "optional": false, "description": ""}, {"name": "date", "type": "{", "optional": false, "description": ""}, {"name": "input_1", "type": "string", "optional": false, "description": ""}, {"name": "input_2", "type": "string", "optional": false, "description": ""}]}, {"name": "TextAreaAnswerData", "properties": [{"name": "textarea", "type": "string", "optional": false, "description": ""}]}, {"name": "SummaryAnswerData", "properties": [{"name": "textarea_1", "type": "string", "optional": false, "description": ""}, {"name": "textarea_2", "type": "string", "optional": false, "description": ""}, {"name": "textarea_3", "type": "string", "optional": false, "description": ""}, {"name": "textarea_4", "type": "string", "optional": false, "description": ""}, {"name": "textarea_5", "type": "string", "optional": false, "description": ""}]}, {"name": "RecertificationMeetingAnswerData", "properties": [{"name": "textarea_1", "type": "string", "optional": false, "description": ""}, {"name": "textarea_2", "type": "string", "optional": false, "description": ""}, {"name": "textarea_3", "type": "string", "optional": false, "description": ""}, {"name": "textarea_4", "type": "string", "optional": false, "description": ""}, {"name": "textarea_5", "type": "string", "optional": false, "description": ""}, {"name": "textarea_6", "type": "string", "optional": false, "description": ""}, {"name": "textarea_7", "type": "string", "optional": false, "description": ""}]}, {"name": "TemplateAnswerSaveResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}, {"name": "data", "type": "TemplateAnswerSaveResult", "optional": true, "description": ""}, {"name": "required", "type": "string[]", "optional": true, "description": ""}, {"name": "errors", "type": "string[]", "optional": true, "description": ""}, {"name": "error", "type": "string", "optional": true, "description": ""}]}, {"name": "TemplateAnswerValidateResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}, {"name": "data", "type": "TemplateAnswerValidationResult", "optional": true, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}, {"name": "user", "file": "models/user.ts", "interfaces": [{"name": "UserProfile", "properties": [{"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "account", "type": "string", "optional": false, "description": ""}, {"name": "nick<PERSON><PERSON>", "type": "string", "optional": false, "description": ""}, {"name": "email", "type": "string", "optional": true, "description": ""}, {"name": "phone", "type": "string", "optional": true, "description": ""}, {"name": "avatar", "type": "string", "optional": true, "description": ""}, {"name": "roleType", "type": "string", "optional": false, "description": ""}, {"name": "isActive", "type": "boolean", "optional": false, "description": ""}, {"name": "createdTime", "type": "Date", "optional": false, "description": ""}, {"name": "updatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "remark", "type": "string", "optional": true, "description": ""}, {"name": "permissions", "type": "string[]", "optional": false, "description": ""}, {"name": "permissionGroups", "type": "string[]", "optional": false, "description": ""}, {"name": "school", "type": "SchoolInfo", "optional": true, "description": ""}, {"name": "certifications", "type": "CertificationInfo[]", "optional": false, "description": ""}]}, {"name": "SchoolInfo", "properties": [{"name": "id", "type": "number", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": false, "description": ""}, {"name": "englishName", "type": "string", "optional": true, "description": ""}, {"name": "code", "type": "string", "optional": true, "description": ""}, {"name": "address", "type": "string", "optional": true, "description": ""}, {"name": "phone", "type": "string", "optional": true, "description": ""}, {"name": "email", "type": "string", "optional": true, "description": ""}, {"name": "website", "type": "string", "optional": true, "description": ""}, {"name": "<PERSON><PERSON><PERSON>", "type": "string", "optional": true, "description": ""}, {"name": "contactPhone", "type": "string", "optional": true, "description": ""}, {"name": "contactEmail", "type": "string", "optional": true, "description": ""}]}, {"name": "CertificationInfo", "properties": [{"name": "id", "type": "string", "optional": false, "description": ""}, {"name": "certificationType", "type": "string", "optional": false, "description": ""}, {"name": "level", "type": "number", "optional": false, "description": ""}, {"name": "status", "type": "string", "optional": false, "description": ""}, {"name": "applyDate", "type": "Date", "optional": false, "description": ""}, {"name": "reviewDate", "type": "Date", "optional": true, "description": ""}, {"name": "passDate", "type": "Date", "optional": true, "description": ""}, {"name": "expiredDate", "type": "Date", "optional": true, "description": ""}, {"name": "certificateNumber", "type": "string", "optional": true, "description": ""}]}, {"name": "UserProfileUpdateRequest", "properties": [{"name": "nick<PERSON><PERSON>", "type": "string", "optional": true, "description": ""}, {"name": "email", "type": "string", "optional": true, "description": ""}, {"name": "phone", "type": "string", "optional": true, "description": ""}, {"name": "avatar", "type": "string", "optional": true, "description": ""}, {"name": "school", "type": "{", "optional": true, "description": ""}, {"name": "name", "type": "string", "optional": true, "description": ""}, {"name": "englishName", "type": "string", "optional": true, "description": ""}, {"name": "address", "type": "string", "optional": true, "description": ""}, {"name": "phone", "type": "string", "optional": true, "description": ""}, {"name": "email", "type": "string", "optional": true, "description": ""}, {"name": "website", "type": "string", "optional": true, "description": ""}, {"name": "<PERSON><PERSON><PERSON>", "type": "string", "optional": true, "description": ""}, {"name": "contactPhone", "type": "string", "optional": true, "description": ""}, {"name": "contactEmail", "type": "string", "optional": true, "description": ""}]}, {"name": "PermissionInfo", "properties": [{"name": "code", "type": "string", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": false, "description": ""}, {"name": "description", "type": "string", "optional": true, "description": ""}]}, {"name": "PermissionGroupInfo", "properties": [{"name": "code", "type": "string", "optional": false, "description": ""}, {"name": "name", "type": "string", "optional": false, "description": ""}, {"name": "description", "type": "string", "optional": true, "description": ""}]}, {"name": "UserPermissions", "properties": [{"name": "permissions", "type": "PermissionInfo[]", "optional": false, "description": ""}, {"name": "permissionGroups", "type": "PermissionGroupInfo[]", "optional": false, "description": ""}]}, {"name": "Account<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": [{"name": "Id", "type": "string", "optional": false, "description": ""}, {"name": "Account", "type": "string", "optional": false, "description": ""}, {"name": "<PERSON><PERSON><PERSON>", "type": "string", "optional": false, "description": ""}, {"name": "Email", "type": "string", "optional": true, "description": ""}, {"name": "Phone", "type": "string", "optional": true, "description": ""}, {"name": "Avatar", "type": "string", "optional": true, "description": ""}, {"name": "RoleType", "type": "string", "optional": false, "description": ""}, {"name": "IsActive", "type": "boolean", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "UpdatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "Remark", "type": "string", "optional": true, "description": ""}, {"name": "CmsUserId", "type": "number", "optional": true, "description": ""}]}, {"name": "SchoolQueryResult", "properties": [{"name": "Id", "type": "number", "optional": false, "description": ""}, {"name": "Name", "type": "string", "optional": false, "description": ""}, {"name": "EnglishName", "type": "string", "optional": true, "description": ""}, {"name": "Code", "type": "string", "optional": true, "description": ""}, {"name": "Address", "type": "string", "optional": true, "description": ""}, {"name": "Phone", "type": "string", "optional": true, "description": ""}, {"name": "Email", "type": "string", "optional": true, "description": ""}, {"name": "Website", "type": "string", "optional": true, "description": ""}, {"name": "<PERSON><PERSON><PERSON>", "type": "string", "optional": true, "description": ""}, {"name": "ContactPhone", "type": "string", "optional": true, "description": ""}, {"name": "ContactEmail", "type": "string", "optional": true, "description": ""}, {"name": "IsActive", "type": "boolean", "optional": false, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": false, "description": ""}, {"name": "UpdatedTime", "type": "Date", "optional": false, "description": ""}]}, {"name": "CertificationQueryResult", "properties": [{"name": "Id", "type": "number", "optional": false, "description": ""}, {"name": "Level", "type": "number", "optional": true, "description": ""}, {"name": "ReviewStatus", "type": "number", "optional": true, "description": ""}, {"name": "CreatedTime", "type": "Date", "optional": true, "description": ""}, {"name": "ReviewDate", "type": "Date", "optional": true, "description": ""}, {"name": "ApprovedDate", "type": "Date", "optional": true, "description": ""}, {"name": "ExpiredDate", "type": "Date", "optional": true, "description": ""}, {"name": "CertificateNumber", "type": "string", "optional": true, "description": ""}]}, {"name": "PermissionQueryResult", "properties": [{"name": "Code", "type": "string", "optional": false, "description": ""}, {"name": "Name", "type": "string", "optional": false, "description": ""}, {"name": "Description", "type": "string", "optional": true, "description": ""}]}, {"name": "PermissionGroupQueryResult", "properties": [{"name": "Code", "type": "string", "optional": false, "description": ""}, {"name": "Name", "type": "string", "optional": false, "description": ""}, {"name": "Description", "type": "string", "optional": true, "description": ""}]}, {"name": "Account<PERSON><PERSON><PERSON><PERSON><PERSON>s", "properties": [{"name": "<PERSON><PERSON><PERSON>", "type": "string", "optional": true, "description": ""}, {"name": "Email", "type": "string", "optional": true, "description": ""}, {"name": "Phone", "type": "string", "optional": true, "description": ""}, {"name": "Avatar", "type": "string", "optional": true, "description": ""}]}, {"name": "SchoolUpdateFields", "properties": [{"name": "Name", "type": "string", "optional": true, "description": ""}, {"name": "EnglishName", "type": "string", "optional": true, "description": ""}, {"name": "Address", "type": "string", "optional": true, "description": ""}, {"name": "Phone", "type": "string", "optional": true, "description": ""}, {"name": "Email", "type": "string", "optional": true, "description": ""}, {"name": "Website", "type": "string", "optional": true, "description": ""}, {"name": "<PERSON><PERSON><PERSON>", "type": "string", "optional": true, "description": ""}, {"name": "ContactPhone", "type": "string", "optional": true, "description": ""}, {"name": "ContactEmail", "type": "string", "optional": true, "description": ""}]}, {"name": "UserProfileResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "UserProfile", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": true, "description": ""}]}, {"name": "UserProfileUpdateResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "UserProfile", "optional": false, "description": ""}, {"name": "message", "type": "string", "optional": false, "description": ""}]}, {"name": "UserCertificationsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "CertificationQueryResult[]", "optional": false, "description": ""}]}, {"name": "UserSchoolResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "SchoolQueryResult", "optional": false, "description": ""}]}, {"name": "UserPermissionsResponse", "properties": [{"name": "success", "type": "boolean", "optional": false, "description": ""}, {"name": "data", "type": "UserPermissions", "optional": false, "description": ""}]}, {"name": "UserProfileParams", "properties": [{"name": "userId", "type": "string", "optional": false, "description": ""}]}], "types": [], "exports": ["interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface", "interface"]}], "constants": [{"name": "admin", "file": "constants/admin.ts", "constants": [{"name": "ANSWER_STATUS", "value": "{\n  NOT_FILLED: 0, // 未填寫\n  FILLED: 1, // 已填寫\n  NEEDS_SUPPLEMENT: 2, // 待補件\n  REJECTED: 3, // 退件\n  COMPLETED: 4, // 已完成\n} as const"}, {"name": "REVIEW_STATUS", "value": "{\n  IN_REVIEW: 0, // 審核中\n  APPROVED: 1, // 已通過\n  REJECTED: 2, // 已拒絕\n  NEEDS_SUPPLEMENT: 3, // 需要補件\n  NOT_SUBMITTED: 4, // 未提交\n} as const"}, {"name": "CERTIFICATION_STATUS", "value": "{\n  INACTIVE: 0, // 非活躍\n  ACTIVE: 1, // 活躍\n  DELETED: 2, // 已刪除\n} as const"}, {"name": "ADMIN_ACTION_TYPES", "value": "{\n  REQUEST_SUPPLEMENT: \"request_supplement\", // 要求補件\n  APPROVE: \"approve\", // 批准\n  REJECT: \"reject\", // 拒絕\n  COMMENT: \"comment\", // 評論\n} as const"}, {"name": "ANSWER_STATUS_DESCRIPTIONS", "value": "{\n  [ANSWER_STATUS.NOT_FILLED]: \"未填寫\",\n  [ANSWER_STATUS.FILLED]: \"已填寫\",\n  [ANSWER_STATUS.NEEDS_SUPPLEMENT]: \"待補件\",\n  [ANSWER_STATUS.REJECTED]: \"退件\",\n  [ANSWER_STATUS.COMPLETED]: \"已審核\",\n} as const"}, {"name": "ADMIN_ROLES", "value": "{\n  GOVERNMENT: \"Government\",\n  ADMIN: \"admin\",\n} as const"}, {"name": "ADMIN_PERMISSIONS", "value": "{\n  ADMIN: \"admin\",\n  REVIEW: \"review\",\n  MANAGE: \"manage\",\n} as const"}, {"name": "ADMIN_PERMISSION_GROUPS", "value": "{\n  ADMIN: \"admin\",\n  RE<PERSON>EW<PERSON>: \"reviewer\",\n  MANAGER: \"manager\",\n} as const"}, {"name": "COMMENT_LIMITS", "value": "{\n  MAX_LENGTH: 20, // 評審意見最大長度\n  MIN_LENGTH: 1, // 評審意見最小長度\n} as const"}, {"name": "ADMIN_ERROR_MESSAGES", "value": "{\n  MISSING_PARAMETERS: \"Missing required parameters\",\n  INSUFFICIENT_PERMISSIONS: \"Insufficient permissions. Admin role required.\",\n  CERTIFICATION_NOT_FOUND: \"Certification not found\",\n  ANSWER_NOT_FOUND: \"Answer not found for the specified question\",\n  NOT_IN_REVIEW_STATUS: \"Can only mark answers for certifications in review status\",\n  INVALID_ACTION: \"Invalid action\",\n  COMMENT_TOO_LONG: \"評審意見不得超過20個字\",\n  COMMENT_TOO_SHORT: \"評審意見不能為空\",\n  INTERNAL_SERVER_ERROR: \"Internal server error\",\n  CERTIFICATION_NOT_IN_REVIEW: \"Certification is not in review status\",\n} as const"}, {"name": "ADMIN_SUCCESS_MESSAGES", "value": "{\n  ANSWER_STATUS_MARKED: \"Answer status marked successfully\",\n  ACTION_LOGS_RETRIEVED: \"Action logs retrieved successfully\",\n  REVIEW_COMMENT_SAVED: \"評審意見保存成功\",\n  REVIEW_COMMENT_UPDATED: \"評審意見更新成功\",\n  REVIEW_COMMENTS_RETRIEVED: \"Review comments retrieved successfully\",\n} as const"}, {"name": "SQL_QUERIES", "value": "{\n  GET_CERTIFICATION: `\n    SELECT CertificationId, ReviewStatus, Status\n    FROM Certifications \n    WHERE CertificationId = @certificationId AND Status = @activeStatus\n  `,\n  GET_ANSWER: `\n    SELECT AnswerId, QuestionId, AnswerStatus\n    FROM CertificationAnswers \n    WHERE CertificationId = @certificationId AND QuestionId = @questionId\n  `,\n  UPDATE_ANSWER_STATUS: `\n    UPDATE CertificationAnswers \n    SET AnswerStatus = @answerStatus, UpdatedTime = GETDATE()\n    WHERE AnswerId = @answerId\n  `,\n  INSERT_ACTION_LOG: `\n    INSERT INTO AdminActionLogs (\n      CertificationId, QuestionId, AnswerId, AdminUserId, Action, \n      PreviousStatus, NewStatus, ActionTime, Notes\n    ) VALUES (\n      @certificationId, @questionId, @answerId, @adminUserId, @action,\n      @previousStatus, @newStatus, GETDATE(), @notes\n    )\n  `,\n  GET_ACTION_LOGS: `\n    SELECT \n      l.ActionLogId,\n      l.CertificationId,\n      l.QuestionId,\n      l.AnswerId,\n      l.Action,\n      l.PreviousStatus,\n      l.New<PERSON>tatus,\n      l.ActionTime,\n      l.Notes,\n      a.Username as AdminUsername,\n      q.Title as QuestionTitle\n    FROM AdminActionLogs l\n    LEFT JOIN Accounts a ON l.AdminUserId = a.AccountId\n    LEFT JOIN Questions q ON l.QuestionId = q.QuestionId\n    WHERE l.CertificationId = @certificationId\n    ORDER BY l.ActionTime DESC\n  `,\n  GET_EXISTING_STEP_RECORD: `\n    SELECT CertificationStepRecordId \n    FROM CertificationStepRecords \n    WHERE CertificationId = @certificationId AND StepNumber = @stepNumber\n  `,\n  UPDATE_STEP_RECORD: `\n    UPDATE CertificationStepRecords \n    SET StepOpinion = @comment,\n        UpdatedUserId = @adminUserId,\n        UpdatedTime = GETDATE()\n    WHERE CertificationStepRecordId = @recordId\n  `,\n  INSERT_STEP_RECORD: `\n    INSERT INTO CertificationStepRecords (\n      CertificationId, StepNumber, StepOpinion, CreatedUserId, UpdatedUserId, UpdatedTime\n    ) VALUES (\n      @certificationId, @stepNumber, @comment, @adminUserId, @adminUserId, GETDATE()\n    )\n  `,\n  GET_REVIEW_COMMENTS: `\n    SELECT \n      csr.CertificationStepRecordId,\n      csr.CertificationId,\n      csr.StepNumber,\n      csr.StepOpinion as Comment,\n      csr.CreatedTime,\n      csr.UpdatedTime,\n      a.Username as AdminUsername\n    FROM CertificationStepRecords csr\n    LEFT JOIN Accounts a ON csr.UpdatedUserId = a.AccountId\n    WHERE csr.CertificationId = @certificationId \n      AND csr.StepOpinion IS NOT NULL \n      AND csr.StepOpinion != ''\n    ORDER BY csr.StepNumber, csr.CreatedTime DESC\n  `,\n} as const"}, {"name": "isAdmin", "value": "(user: { roleType?: string"}, {"name": "getAnswerStatusInfo", "value": "(action: string): { status: number"}, {"name": "isValidAction", "value": "(action: string): boolean => {\n  return Object.values(ADMIN_ACTION_TYPES).includes(action as any)"}, {"name": "parseStepId", "value": "(stepId: string): number => {\n  return parseInt(stepId.replace(\"step_\", \"\")) || parseInt(stepId)"}, {"name": "validateComment", "value": "(comment: string): { isValid: boolean"}, {"name": "formatActionLog", "value": "(log: any): AdminActionLog => {\n  return {\n    actionLogId: log.ActionLogId,\n    certificationId: log.CertificationId,\n    questionId: log.QuestionId,\n    answerId: log.AnswerId,\n    action: log.Action,\n    previousStatus: log.PreviousStatus,\n    newStatus: log.NewStatus,\n    actionTime: log.ActionTime,\n    notes: log.Notes,\n    adminUsername: log.AdminUsername,\n    questionTitle: log.QuestionTitle,\n  }"}, {"name": "formatReviewComment", "value": "(comment: any): ReviewComment => {\n  return {\n    certificationStepRecordId: comment.CertificationStepRecordId,\n    certificationId: comment.CertificationId,\n    stepNumber: comment.StepNumber,\n    comment: comment.Comment,\n    createdTime: comment.CreatedTime,\n    updatedTime: comment.UpdatedTime,\n    adminUsername: comment.AdminUsername,\n  }"}, {"name": "canAdminOperateCertification", "value": "(certification: { ReviewStatus: number }): boolean => {\n  return certification.ReviewStatus === REVIEW_STATUS.IN_REVIEW"}, {"name": "generateActionNote", "value": "(action: string, statusDescription: string): string => {\n  return `管理員標示答案為${statusDescription}`"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "answer", "file": "constants/answer.ts", "constants": [{"name": "ANSWER_STATUS", "value": "{\n  NOT_FILLED: 0, // 未填寫/已刪除\n  FILLED: 1, // 已填寫\n  NEEDS_SUPPLEMENT: 2, // 待補件\n  REJECTED: 3, // 退件\n  COMPLETED: 4, // 已完成\n} as const"}, {"name": "REVIEW_STATUS", "value": "{\n  IN_REVIEW: 0, // 審核中\n  APPROVED: 1, // 已通過\n  REJECTED: 2, // 已拒絕\n  NEEDS_SUPPLEMENT: 3, // 需要補件\n  NOT_SUBMITTED: 4, // 未提交\n} as const"}, {"name": "QUESTION_STATUS", "value": "{\n  INACTIVE: 0, // 非活躍\n  ACTIVE: 1, // 活躍\n} as const"}, {"name": "CERTIFICATION_STATUS", "value": "{\n  INACTIVE: 0, // 非活躍\n  ACTIVE: 1, // 活躍\n  DELETED: 2, // 已刪除\n} as const"}, {"name": "ANSWER_ERROR_MESSAGES", "value": "{\n  MISSING_PARAMETERS: \"缺少必要參數：certificationId, questionId, answerData\",\n  MISSING_ANSWER_ID: \"缺少答案ID\",\n  MISSING_CERTIFICATION_ID: \"缺少認證ID\",\n  MISSING_QUESTION_OR_CERTIFICATION_ID: \"缺少問題ID或認證ID\",\n  USER_NOT_ASSIGNED_SCHOOL: \"用戶未分配學校，無權限修改認證\",\n  NO_PERMISSION_MODIFY: \"無權限修改此認證\",\n  NO_PERMISSION_VIEW: \"無權限查看此認證\",\n  NO_PERMISSION_DELETE: \"無權限刪除此答案\",\n  CERTIFICATION_UNDER_REVIEW: \"認證正在審核中，無法修改答案\",\n  QUESTION_NOT_FOUND: \"問題不存在或已被刪除\",\n  ANSWER_NOT_FOUND: \"答案不存在\",\n  ANSWER_ALREADY_DELETED: \"答案不存在或已被刪除\",\n  SAVE_FAILED: \"保存答案失敗，請稍後再試\",\n  QUERY_FAILED: \"查詢答案失敗，請稍後再試\",\n  DELETE_FAILED: \"刪除答案失敗，請稍後再試\",\n  JSON_PARSE_ERROR: \"JSON 解析失敗\",\n} as const"}, {"name": "ANSWER_SUCCESS_MESSAGES", "value": "{\n  ANSWER_SAVED: \"答案已保存\",\n  ANSWER_UPDATED: \"答案已更新\",\n  ANSWER_DELETED: \"答案已刪除\",\n  ANSWERS_RETRIEVED: \"答案查詢成功\",\n  NO_ANSWER_FOUND: \"尚未找到此問題的答案\",\n} as const"}, {"name": "SQL_QUERIES", "value": "{\n  GET_USER_SCHOOL: \"SELECT SchoolId FROM Accounts WHERE AccountId = @userId\",\n  GET_CERTIFICATION_INFO: \"SELECT SchoolId, ReviewStatus FROM Certifications WHERE CertificationId = @certificationId\",\n  GET_CERTIFICATION_SCHOOL: \"SELECT SchoolId FROM Certifications WHERE CertificationId = @certificationId\",\n  GET_QUESTION_INFO: \"SELECT QuestionId, QuestionTemplate FROM Questions WHERE QuestionId = @questionId AND Status = @activeStatus\",\n  GET_EXISTING_ANSWER: `\n    SELECT CertificationAnswerId \n    FROM CertificationAnswers \n    WHERE CertificationId = @certificationId AND QuestionId = @questionId\n  `,\n  UPDATE_ANSWER: `\n    UPDATE CertificationAnswers \n    SET AnswerText = @answerText,\n        AnswerStatus = @answerStatus,\n        UpdatedTime = GETDATE(),\n        UpdatedUserId = @userId\n    WHERE CertificationAnswerId = @answerId\n  `,\n  INSERT_ANSWER: `\n    INSERT INTO CertificationAnswers (\n      CertificationId,\n      QuestionId,\n      AnswerText,\n      AnswerStatus,\n      SubmittedDate,\n      CreatedTime,\n      CreatedUserId,\n      UpdatedTime,\n      UpdatedUserId,\n      SortOrder\n    ) \n    OUTPUT INSERTED.CertificationAnswerId\n    VALUES (\n      @certificationId,\n      @questionId,\n      @answerText,\n      @answerStatus,\n      NULL,\n      GETDATE(),\n      @userId,\n      GETDATE(),\n      @userId,\n      0\n    )\n  `,\n  GET_CERTIFICATION_ANSWERS: `\n    SELECT \n      ca.CertificationAnswerId,\n      ca.CertificationId,\n      ca.QuestionId,\n      ca.AnswerText,\n      ca.AnswerStatus,\n      ca.SubmittedDate,\n      ca.ReviewedDate,\n      ca.CreatedTime,\n      ca.UpdatedTime,\n      q.Title as QuestionTitle,\n      q.QuestionTemplate,\n      q.StepNumber,\n      q.ParentQuestionId\n    FROM CertificationAnswers ca\n    LEFT JOIN Questions q ON ca.QuestionId = q.QuestionId\n    WHERE ca.CertificationId = @certificationId\n      AND q.Status = @activeStatus\n    ORDER BY q.StepNumber, q.SortOrder, q.QuestionId\n  `,\n  GET_SINGLE_ANSWER: `\n    SELECT \n      ca.CertificationAnswerId,\n      ca.CertificationId,\n      ca.QuestionId,\n      ca.AnswerText,\n      ca.AnswerStatus,\n      ca.SubmittedDate,\n      ca.ReviewedDate,\n      ca.CreatedTime,\n      ca.UpdatedTime,\n      q.Title as QuestionTitle,\n      q.QuestionTemplate,\n      q.StepNumber,\n      q.ParentQuestionId\n    FROM CertificationAnswers ca\n    LEFT JOIN Questions q ON ca.QuestionId = q.QuestionId\n    WHERE ca.CertificationId = @certificationId \n      AND ca.QuestionId = @questionId\n      AND q.Status = @activeStatus\n  `,\n  GET_ANSWER_FOR_DELETE: `\n    SELECT \n      ca.CertificationAnswerId,\n      ca.CertificationId,\n      c.SchoolId\n    FROM CertificationAnswers ca\n    LEFT JOIN Certifications c ON ca.CertificationId = c.CertificationId\n    WHERE ca.CertificationAnswerId = @answerId\n  `,\n  DELETE_ANSWER: `\n    UPDATE CertificationAnswers \n    SET AnswerStatus = @deletedStatus,\n        UpdatedTime = GETDATE(),\n        UpdatedUserId = @userId\n    WHERE CertificationAnswerId = @answerId\n  `,\n} as const"}, {"name": "validateSaveParameters", "value": "(request: {\n  certificationId?: any"}, {"name": "canModifyCertification", "value": "(reviewStatus: number): boolean => {\n  return reviewStatus !== REVIEW_STATUS.IN_REVIEW"}, {"name": "hasPermissionForCertification", "value": "(userSchoolId: number, certificationSchoolId: number): boolean => {\n  return userSchoolId === certificationSchoolId"}, {"name": "safeJSONParse", "value": "(jsonString: string | null): any => {\n  if (!jsonString) return null"}, {"name": "formatAnswerRecord", "value": "(record: any): any => {\n  return {\n    answerId: record.CertificationAnswerId,\n    certificationId: record.CertificationId,\n    questionId: record.QuestionId,\n    questionTitle: record.QuestionTitle,\n    questionTemplate: record.QuestionTemplate,\n    stepNumber: record.StepNumber,\n    parentQuestionId: record.ParentQuestionId,\n    answerData: safeJSONParse(record.AnswerText),\n    answerStatus: record.AnswerStatus,\n    submittedDate: record.SubmittedDate,\n    reviewedDate: record.ReviewedDate,\n    createdTime: record.CreatedTime,\n    updatedTime: record.UpdatedTime,\n  }"}, {"name": "generateDebugInfo", "value": "(request: any): any => {\n  return {\n    certificationId: request.certificationId,\n    questionId: request.questionId,\n    hasAnswerData: !!request.answerData,\n    userId: request.userId,\n  }"}, {"name": "isValidId", "value": "(id: any): boolean => {\n  return id != null && !isNaN(parseInt(id))"}, {"name": "getOperationMessage", "value": "(isUpdate: boolean): string => {\n  return isUpdate ? ANSWER_SUCCESS_MESSAGES.ANSWER_UPDATED : ANSWER_SUCCESS_MESSAGES.ANSWER_SAVED"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "auth", "file": "constants/auth.ts", "constants": [{"name": "ALLOWED_ROLE_TYPES", "value": "[\"School\", \"Government\", \"Tutor\"] as const"}, {"name": "ROLE_TYPE_MAP", "value": "{\n  SCHOOL: \"School\",\n  GOVERNMENT: \"Government\",\n  TUTOR: \"<PERSON>tor\",\n} as const"}, {"name": "ACCOUNT_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n  DELETED: 2,\n} as const"}, {"name": "TOKEN_TYPE", "value": "{\n  LOGIN: \"Login\",\n  SCHOOL_IDENTITY: \"school_identity\",\n  EPA_IDENTITY: \"epa_identity\",\n  TUTOR_IDENTITY: \"tutor_identity\",\n} as const"}, {"name": "TOKEN_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n} as const"}, {"name": "PASSWORD_CONFIG", "value": "{\n  MIN_LENGTH: 8,\n  SALT_LENGTH: 32,\n  HASH_ITERATIONS: 10000,\n  HMAC_ALGORITHM: \"sha256\",\n} as const"}, {"name": "DEFAULT_TOKEN_VALIDITY_DAYS", "value": "{\n  LOGIN: 30,\n  IDENTITY: 3000,\n  ADMIN: 1,\n} as const"}, {"name": "AUTH_ERROR_MESSAGES", "value": "{\n  TOKEN_REQUIRED: \"Token is required\",\n  CREDENTIALS_REQUIRED: \"Account and password are required\",\n  INVALID_TOKEN: \"Invalid token\",\n  INVALID_CREDENTIALS: \"Invalid account or password\",\n  ACCOUNT_DISABLED: \"Access denied. Your account has been deactivated.\",\n  ROLE_NOT_ALLOWED: \"Access denied. Your account role is not authorized to access this system.\",\n  PASSWORD_TOO_SHORT: `Password must be at least ${PASSWORD_CONFIG.MIN_LENGTH} characters long`,\n  PASSWORD_REQUIREMENTS: \"Password must contain at least one uppercase letter, one lowercase letter, and one number\",\n  OLD_PASSWORD_INCORRECT: \"Old password is incorrect\",\n  ACCOUNT_NOT_FOUND: \"Account not found\",\n  TOKEN_GENERATION_FAILED: \"Failed to generate token\",\n  PASSWORD_UPDATE_FAILED: \"Failed to update password\",\n  UNAUTHORIZED: \"Unauthorized access\",\n  INTERNAL_ERROR: \"Internal server error\",\n} as const"}, {"name": "AUTH_SUCCESS_MESSAGES", "value": "{\n  TOKEN_LOGIN_SUCCESS: \"Token login successful\",\n  PASSWORD_LOGIN_SUCCESS: \"Password login successful\",\n  PASSWORD_CHANGED: \"Password changed successfully\",\n  TOKEN_CREATED: \"Token created successfully\",\n  LOGOUT_SUCCESS: \"Logout successful\",\n  PASSWORDS_RESET: \"Passwords reset successfully\",\n} as const"}, {"name": "determineRoleTypeFromBooleanFields", "value": "(is_school_partner?: boolean, is_epa_user?: boolean, is_guidance_team?: boolean): string => {\n  if (is_school_partner) return ROLE_TYPE_MAP.SCHOOL"}, {"name": "determineRoleTypeFromIntegerFields", "value": "(IsSchoolPartner?: number, IsEpaUser?: number, IsGuidanceTeam?: number): string => {\n  if (IsSchoolPartner === 1) return ROLE_TYPE_MAP.SCHOOL"}, {"name": "mapRoleTypeToFrontendRole", "value": "(roleType: string): string => {\n  return FRONTEND_ROLE_MAP[roleType] || \"school\""}, {"name": "validatePasswordStrength", "value": "(password: string): { valid: boolean"}, {"name": "isAllowedRole", "value": "(roleType: string): boolean => {\n  return ALLOWED_ROLE_TYPES.includes(roleType as (typeof ALLOWED_ROLE_TYPES)[number])"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "campus-submission", "file": "constants/campus-submission.ts", "constants": [{"name": "SUBMISSION_STATUS", "value": "{\n  NOT_SUBMITTED: -1,\n  UNDER_REVIEW: 0,\n  PUBLISHED: 1,\n  DELETED: 2,\n  REJECTED: 3,\n} as const"}, {"name": "REVIEW_STATUS", "value": "{\n  WITHDRAWN: 0,\n  PUBLISHED: 1,\n  REJECTED: 3,\n} as const"}, {"name": "BADGE_TYPE", "value": "{\n  NONE: 0,\n  BRONZE: 1,\n  SILVER: 2,\n  GOLD: 3,\n  GREEN_FLAG: 4,\n} as const"}, {"name": "FEATURED_STATUS", "value": "{\n  NORMAL: 0,\n  FEATURED: 1,\n} as const"}, {"name": "CONTENT_TYPE_CODE", "value": "{\n  IMAGE: \"image\",\n  VIDEO: \"video\",\n  DOCUMENT: \"document\",\n  LINK: \"link\",\n} as const"}, {"name": "LOCALE_CODE", "value": "{\n  ZH_TW: \"zh-TW\",\n  EN: \"en\",\n} as const"}, {"name": "DEFAULT_PAGINATION", "value": "{\n  LIMIT: 50,\n  PAGE: 1,\n} as const"}, {"name": "DESCRIPTION_SUMMARY_LENGTH", "value": "200"}, {"name": "getStatusText", "value": "(status: number): string => {\n  return STATUS_TEXT_MAP[status] || \"未知狀態\""}, {"name": "getFinalStatus", "value": "(\n  submissionStatus: number,\n  reviewStatus?: number,\n  hasReview?: boolean,\n  reviewComment?: string\n): { status: number"}, {"name": "<PERSON><PERSON><PERSON>d<PERSON>eview", "value": "(reviewComment?: string): boolean => {\n  return reviewComment !== undefined && reviewComment !== null && reviewComment !== \"\""}, {"name": "formatDateToISOString", "value": "(date: Date | string): string => {\n  return new Date(date).toISOString().split(\"T\")[0]"}, {"name": "generateDescriptionSummary", "value": "(description: string): string => {\n  if (!description) return \"\""}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "certificate", "file": "constants/certificate.ts", "constants": [{"name": "CERTIFICATE_STATUS", "value": "{\n  VALID: \"valid\",\n  INVALID: \"invalid\",\n  EXPIRED: \"expired\",\n} as const"}, {"name": "TOKEN_TYPES", "value": "{\n  CERTIFICATE_LOGIN: \"CERTIFICATE_LOGIN\",\n  NORMAL_LOGIN: \"NORMAL_LOGIN\",\n} as const"}, {"name": "ACCOUNT_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n  SUSPENDED: 2,\n} as const"}, {"name": "CERTIFICATE_ACTIONS", "value": "{\n  BIND: \"CERTIFICATE_BIND\",\n  UNBIND: \"CERTIFICATE_UNBIND\",\n  LOGIN: \"CERTIFICATE_LOGIN\",\n  VALIDATE: \"CERTIFICATE_VALIDATE\",\n} as const"}, {"name": "CERTIFICATE_REQUIREMENTS", "value": "{\n  MIN_DATA_LENGTH: 10,\n  MIN_PASSWORD_LENGTH: 6,\n  TOKEN_EXPIRY_DAYS: 30,\n} as const"}, {"name": "CERTIFICATE_ERROR_MESSAGES", "value": "{\n  MISSING_CERTIFICATE_DATA: \"憑證資料不能為空\",\n  INVALID_CERTIFICATE: \"憑證格式無效或已過期\",\n  MISSING_BIND_INFO: \"憑證資訊和帳號ID不能為空\",\n  PERMISSION_DENIED_BIND: \"只能綁定自己的帳號\",\n  PERMISSION_DENIED_UNBIND: \"只能解除自己的憑證綁定\",\n  ACCOUNT_NOT_FOUND: \"帳號不存在\",\n  NO_CERTIFICATE_BOUND: \"此帳號未綁定任何憑證\",\n  UNBIND_FAILED: \"解除憑證綁定失敗\",\n  MISSING_LOGIN_CREDENTIALS: \"憑證資料和密碼不能為空\",\n  INVALID_PASSWORD: \"憑證密碼錯誤\",\n  NO_BOUND_ACCOUNT: \"未找到綁定此憑證的有效帳號\",\n  VALIDATION_ERROR: \"憑證驗證過程發生錯誤\",\n  BIND_ERROR: \"憑證綁定過程發生錯誤\",\n  UNBIND_ERROR: \"解除憑證綁定過程發生錯誤\",\n  QUERY_ERROR: \"查詢憑證綁定狀態發生錯誤\",\n  LOGIN_ERROR: \"憑證登入過程發生錯誤\",\n  TEST_FAILED: \"憑證測試失敗\",\n} as const"}, {"name": "CERTIFICATE_SUCCESS_MESSAGES", "value": "{\n  VALIDATION_SUCCESS: \"憑證驗證成功\",\n  BIND_SUCCESS: \"憑證綁定成功\",\n  UNBIND_SUCCESS: \"憑證綁定已成功解除\",\n  LOGIN_SUCCESS: \"憑證登入成功\",\n  TEST_SUCCESS: \"憑證API測試成功\",\n} as const"}, {"name": "SQL_QUERIES", "value": "{\n  CHECK_ACCOUNT_CERTIFICATE: \"SELECT citizen_digital_number FROM Account WHERE sid = @accountId\",\n  UPDATE_UNBIND_CERTIFICATE: `\n    UPDATE Account SET \n      citizen_digital_number = NULL,\n      UpdateTime = GETDATE(),\n      UpdateUser = @userId\n    WHERE sid = @accountId\n  `,\n  GET_USER_BINDINGS: `\n    SELECT \n      sid as accountId,\n      citizen_digital_number as certificateId,\n      UpdateTime as lastUpdateTime\n    FROM Account \n    WHERE sid = @userId\n  `,\n  GET_ACCOUNT_BY_CERTIFICATE: `\n    SELECT \n      a.sid as accountId,\n      a.account as username,\n      a.cname as displayName,\n      a.isuse as status,\n      mp.member_role as role\n    FROM Account a\n    LEFT JOIN MemberProfiles mp ON a.sid = mp.account_sid\n    WHERE a.citizen_digital_number = @certificateId AND a.isuse = @activeStatus\n  `,\n  INSERT_TOKEN: `\n    INSERT INTO UserToken (AccountSid, Token, TokenType, ExpiredTime, IsActive, CreatedTime)\n    VALUES (@accountId, @token, @tokenType, @expiredTime, 1, GETDATE())\n  `,\n  INSERT_ACTION_LOG: `\n    INSERT INTO sys_logs (account_sid, action, description, ip_address, user_agent, create_time)\n    VALUES (@accountId, @action, @description, @ipAddress, @userAgent, GETDATE())\n  `,\n} as const"}, {"name": "CERTIFICATE_PATTERNS", "value": "{\n  BEGIN_CERTIFICATE: \"BEGIN CERTIFICATE\",\n  COMMON_NAME: \"CN=\",\n  MIN_CERT_LENGTH: 100,\n} as const"}, {"name": "DEMO_CERTIFICATE", "value": "{\n  SUBJECT: \"CN=測試用戶,OU=臺灣自然人憑證,O=政府憑證管理中心,C=TW\",\n  ISSUER: \"CN=政府憑證管理中心,O=行政院,C=TW\",\n  VALID_FROM: \"2023-01-01\",\n  VALID_TO: \"2026-12-31\",\n  SERIAL_NUMBER_LENGTH: 16,\n} as const"}, {"name": "validateCertificateData", "value": "(certificateData: string): boolean => {\n  try {\n    if (!certificateData || certificateData.length < CERTIFICATE_REQUIREMENTS.MIN_DATA_LENGTH) {\n      return false"}, {"name": "parseCertificateInfo", "value": "(certificateData: string): any => {\n  const crypto = require(\"crypto\")"}, {"name": "validateCertificatePassword", "value": "(certificateData: string, password: string): boolean => {\n  try {\n    if (!password || password.length < CERTIFICATE_REQUIREMENTS.MIN_PASSWORD_LENGTH) {\n      return false"}, {"name": "hasPermissionForAccount", "value": "(userId: string, targetAccountId: string): boolean => {\n  return userId === targetAccountId"}, {"name": "generateTokenExpiry", "value": "(): Date => {\n  const expiryDate = new Date()"}, {"name": "formatBindingInfo", "value": "(binding: any): any => {\n  const hasCertificate = !!binding.certificateId"}, {"name": "formatLoginResult", "value": "(account: any, token: string, expiryDate: Date): any => {\n  return {\n    token,\n    user: {\n      id: account.accountId,\n      username: account.username,\n      displayName: account.displayName,\n      role: account.role || \"user\",\n    },\n    expiryDate: expiryDate.toISOString(),\n  }"}, {"name": "generateLogDescription", "value": "(action: string): string => {\n  switch (action) {\n    case CERTIFICATE_ACTIONS.BIND:\n      return \"綁定自然人憑證\""}, {"name": "isV<PERSON>dAccount", "value": "(account: any): boolean => {\n  return account && account.status === ACCOUNT_STATUS.ACTIVE"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "certification", "file": "constants/certification.ts", "constants": [{"name": "CERTIFICATION_LEVEL", "value": "{\n  BRONZE: 1,\n  <PERSON><PERSON><PERSON><PERSON>: 2,\n  G<PERSON><PERSON>_FLAG: 3,\n  G<PERSON><PERSON>_FLAG_R1: 4,\n  GREEN_FLAG_R2: 5,\n  GREEN_FLAG_R3: 6,\n} as const"}, {"name": "REVIEW_STATUS", "value": "{\n  UNDER_REVIEW: 0,\n  APPROVED: 1,\n  REJECTED: 2,\n  NEEDS_SUPPLEMENT: 3,\n  NOT_SUBMITTED: 4,\n} as const"}, {"name": "CERTIFICATION_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n  DELETED: 2,\n} as const"}, {"name": "CERTIFICATION_TYPES", "value": "[\n  { id: \"Bronze\", name: \"銅牌\", level: CERTIFICATION_LEVEL.BRONZE, requiresGreenFlag: false },\n  { id: \"Silver\", name: \"銀牌\", level: CERTIFICATION_LEVEL.SILVER, requiresGreenFlag: false },\n  { id: \"GreenFlag\", name: \"綠旗\", level: CERTIFICATION_LEVEL.GREEN_FLAG, requiresGreenFlag: false },\n  { id: \"GreenFlag\", name: \"綠旗R1\", level: CERTIFICATION_LEVEL.GREEN_FLAG_R1, requiresGreenFlag: true },\n  { id: \"GreenFlag\", name: \"綠旗R2\", level: CERTIFICATION_LEVEL.GREEN_FLAG_R2, requiresGreenFlag: true },\n  { id: \"GreenFlag\", name: \"綠旗R3\", level: CERTIFICATION_LEVEL.GREEN_FLAG_R3, requiresGreenFlag: true },\n] as const"}, {"name": "CERTIFICATION_ERROR_MESSAGES", "value": "{\n  USER_NOT_AUTHENTICATED: \"使用者未認證\",\n  SCHOOL_NOT_ASSIGNED: \"該帳號尚未分配學校\",\n  CERTIFICATION_NOT_FOUND: \"找不到認證資料\",\n  DUPLICATE_CERTIFICATION: \"已存在相同類型的認證申請\",\n  GREEN_FLAG_REQUIRED: \"申請延續認證需要先通過綠旗認證\",\n  INVALID_CERTIFICATION_TYPE: \"認證類型和等級為必填項目\",\n  CREATE_FAILED: \"建立認證失敗\",\n  UPDATE_FAILED: \"更新認證失敗\",\n  DELETE_FAILED: \"刪除認證失敗\",\n  INVALID_PERMISSIONS: \"沒有權限執行此操作\",\n  TIME_CONSTRAINT_NOT_MET: \"不符合時間限制要求\",\n} as const"}, {"name": "CERTIFICATION_SUCCESS_MESSAGES", "value": "{\n  LIST_RETRIEVED: \"認證清單獲取成功\",\n  AVAILABILITY_CHECKED: \"認證可用性檢查完成\",\n  CERTIFICATION_CREATED: \"認證申請已建立\",\n  CERTIFICATION_UPDATED: \"認證資料已更新\",\n  CERTIFICATION_DELETED: \"認證申請已刪除\",\n  CERTIFICATION_SUBMITTED: \"認證已提交審核\",\n} as const"}, {"name": "TIME_CONSTRAINTS", "value": "{\n  GREEN_FLAG_R1_WAIT: 2, // 綠旗通過滿兩年才能申請R1\n  GREEN_FLAG_R2_WAIT: 2, // R1通過滿兩年才能申請R2\n  GREEN_FLAG_R3_WAIT: 2, // R2通過滿兩年才能申請R3\n} as const"}, {"name": "getReviewStatusInfo", "value": "(reviewStatus: number) => {\n  return {\n    label: REVIEW_STATUS_LABELS[reviewStatus] || \"未知狀態\",\n    icon: REVIEW_STATUS_ICONS[reviewStatus] || \"img/license-icon-no-status.svg\",\n    description: REVIEW_STATUS_DESCRIPTIONS[reviewStatus] || \"狀態未知\",\n    color: REVIEW_STATUS_COLORS[reviewStatus] || \"text-gray-700\",\n    bgColor: REVIEW_STATUS_BG_COLORS[reviewStatus] || \"bg-gray-100\",\n  }"}, {"name": "mapCertificationType", "value": "(levelName: string, level: number) => {\n  const levelMap: Record<number, { name: string"}, {"name": "getYearsSinceApproval", "value": "(approvedDate?: Date): number => {\n  if (!approvedDate) return 0"}, {"name": "isEditable", "value": "(reviewStatus: number): boolean => {\n  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED || reviewStatus === REVIEW_STATUS.NEEDS_SUPPLEMENT"}, {"name": "isDeletable", "value": "(reviewStatus: number): boolean => {\n  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED"}, {"name": "canSubmit", "value": "(reviewStatus: number): boolean => {\n  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED"}, {"name": "getFrontendId", "value": "(level: number): string => {\n  return FRONTEND_ID_MAP[level] || \"unknown\""}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "dashboard", "file": "constants/dashboard.ts", "constants": [{"name": "DASHBOARD_REVIEW_STATUS", "value": "{\n  UNDER_REVIEW: 0,\n  APPROVED: 1,\n  REJECTED: 2,\n  NEEDS_SUPPLEMENT: 3,\n  NOT_SUBMITTED: 4,\n} as const"}, {"name": "DASHBOARD_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n  DELETED: 2,\n} as const"}, {"name": "AUTHORIZED_ROLES", "value": "[\"Government\", \"Tutor\"] as const"}, {"name": "DEFAULT_LIMITS", "value": "{\n  LATEST_CERTIFICATIONS: 6,\n  SCHOOL_ARTICLES: 5,\n  PASSED_CERTIFICATIONS: 10,\n} as const"}, {"name": "LOCALE_CODE", "value": "{\n  ZH_TW: \"zh-TW\",\n  EN: \"en\",\n} as const"}, {"name": "DASHBOARD_ERROR_MESSAGES", "value": "{\n  INVALID_TOKEN: \"無效的使用者令牌\",\n  INSUFFICIENT_PERMISSIONS: \"權限不足\",\n  EPA_TUTOR_ONLY: \"僅限縣市政府和輔導人員查看統計資料\",\n  SCHOOL_ONLY: \"僅限學校身份查看此資料\",\n  CITY_NOT_FOUND: \"找不到指定的縣市\",\n  USER_CITY_NOT_FOUND: \"找不到用戶的縣市資料\",\n  SCHOOL_NOT_FOUND: \"找不到學校資料或您沒有權限\",\n  USER_NOT_AUTHENTICATED: \"使用者未認證\",\n  NO_CURRENT_CERTIFICATION: \"目前沒有申請中的認證\",\n  NO_SCHOOL_ASSIGNED: \"該帳號尚未分配學校\",\n  INTERNAL_ERROR: \"內部系統錯誤\",\n} as const"}, {"name": "DASHBOARD_SUCCESS_MESSAGES", "value": "{\n  TEST_SUCCESS: \"測試成功\",\n  CITY_STATISTICS_SUCCESS: \"縣市統計資料獲取成功\",\n  LATEST_CERTIFICATIONS_SUCCESS: \"最新認證資料獲取成功\",\n  SCHOOL_CERTIFICATION_SUCCESS: \"學校申請中認證獲取成功\",\n  SCHOOL_ARTICLES_SUCCESS: \"學校文章獲取成功\",\n  SCHOOL_PASSED_CERTIFICATIONS_SUCCESS: \"學校已通過認證獲取成功\",\n} as const"}, {"name": "getCertificationLevelName", "value": "(level: number): string => {\n  return CERTIFICATION_LEVEL_NAMES[level] || \"未知\""}, {"name": "isAuthorizedRole", "value": "(roleType: string): boolean => {\n  return AUTHORIZED_ROLES.includes(roleType as (typeof AUTHORIZED_ROLES)[number])"}, {"name": "isSchoolRole", "value": "(roleType: string): boolean => {\n  return roleType === \"School\""}, {"name": "formatDate", "value": "(date: Date | string): string => {\n  if (!date) return \"\""}, {"name": "formatStatisticsResult", "value": "(\n  cityInfo: { cityId: number"}, {"name": "getTestData", "value": "() => {\n  return {\n    cityId: 1,\n    cityName: \"基隆市\",\n    bronzeCount: 1,\n    silverCount: 1,\n    greenFlagCount: 0,\n    totalSchools: 2,\n  }"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "file", "file": "constants/file.ts", "constants": [{"name": "FILE_SIZE_LIMITS", "value": "{\n  GENERAL_FILE: 10 * 1024 * 1024, // 10MB\n  SCHOOL_LOGO: 5 * 1024 * 1024, // 5MB\n} as const"}, {"name": "FILE_COUNT_LIMITS", "value": "{\n  MULTIPLE_UPLOAD: 5,\n  SINGLE_UPLOAD: 1,\n} as const"}, {"name": "ALLOWED_MIME_TYPES", "value": "{\n  IMAGES: [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/gif\"],\n  DOCUMENTS: [\n    \"application/pdf\",\n    \"application/msword\",\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n    \"application/vnd.ms-excel\",\n    \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n  ],\n  VIDEOS: [\"video/mp4\", \"video/avi\", \"video/mov\"],\n  SCHOOL_LOGO: [\"image/jpeg\", \"image/jpg\", \"image/png\"],\n} as const"}, {"name": "ALL_ALLOWED_TYPES", "value": "[...ALLOWED_MIME_TYPES.IMAGES, ...ALLOWED_MIME_TYPES.DOCUMENTS, ...ALLOWED_MIME_TYPES.VIDEOS] as const"}, {"name": "SUPPORTED_EXTENSIONS", "value": "{\n  IMAGES: [\"jpg\", \"jpeg\", \"png\", \"gif\"],\n  DOCUMENTS: [\"pdf\", \"doc\", \"docx\", \"xls\", \"xlsx\"],\n  VIDEOS: [\"mp4\", \"avi\", \"mov\"],\n} as const"}, {"name": "FILE_TYPES", "value": "{\n  GENERAL: \"general\",\n  SCHOOL_LOGO: \"school-logo\",\n  CERTIFICATION: \"certification\",\n  QUESTION: \"question\",\n} as const"}, {"name": "LOCALE_CODES", "value": "{\n  ZH_TW: \"zh-TW\",\n  EN: \"en\",\n} as const"}, {"name": "DATA_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n  DELETED: 2,\n} as const"}, {"name": "FILE_ERROR_MESSAGES", "value": "{\n  NO_FILE_UPLOADED: \"沒有上傳檔案\",\n  UNSUPPORTED_FILE_TYPE: \"不支援的檔案類型\",\n  FILE_TOO_LARGE: \"檔案大小超過限制\",\n  TOO_MANY_FILES: \"檔案數量超過限制\",\n  INVALID_FILENAME: \"非法的檔案名稱\",\n  FILE_NOT_FOUND: \"找不到檔案\",\n  SCHOOL_NOT_FOUND: \"找不到學校資料\",\n  INVALID_USER_AUTH: \"無效的用戶認證\",\n  FILE_RECORD_CREATE_FAILED: \"檔案記錄建立失敗\",\n  UPLOAD_DIR_CREATE_FAILED: \"無法創建上傳目錄\",\n  NO_WRITE_PERMISSION: \"目錄沒有寫入權限\",\n  FILE_PROCESSING_FAILED: \"檔案處理失敗\",\n  LOGO_FORMAT_ERROR: \"校徽圖檔請上傳jpg、png格式\",\n  LOGO_SIZE_ERROR: \"檔案大小不可超過 5MB\",\n  PLEASE_SELECT_LOGO: \"請選擇校徽圖檔\",\n} as const"}, {"name": "FILE_SUCCESS_MESSAGES", "value": "{\n  FILE_UPLOADED: \"檔案上傳成功\",\n  FILE_DELETED: \"檔案刪除成功\",\n  FILE_INFO_RETRIEVED: \"檔案資訊獲取成功\",\n  SCHOOL_LOGO_UPLOADED: \"校徽上傳成功\",\n  SCHOOL_LOGO_RETRIEVED: \"校徽獲取成功\",\n  UPLOAD_DIAGNOSTICS_COMPLETE: \"上傳診斷完成\",\n} as const"}, {"name": "MULTER_ERROR_CODES", "value": "{\n  LIMIT_FILE_SIZE: \"LIMIT_FILE_SIZE\",\n  LIMIT_FILE_COUNT: \"LIMIT_FILE_COUNT\",\n  LIMIT_UNEXPECTED_FILE: \"LIMIT_UNEXPECTED_FILE\",\n} as const"}, {"name": "DEFAULT_DIRECTORIES", "value": "{\n  UPLOADS: \"uploads\",\n  SCHOOL_LOGOS: \"school-logos\",\n} as const"}, {"name": "isAllowedFileType", "value": "(mimetype: string): boolean => {\n  return ALL_ALLOWED_TYPES.includes(mimetype as any)"}, {"name": "isValidLogoFormat", "value": "(mimetype: string): boolean => {\n  return ALLOWED_MIME_TYPES.SCHOOL_LOGO.includes(mimetype as any)"}, {"name": "isValidFileSize", "value": "(size: number, fileType: string = FILE_TYPES.GENERAL): boolean => {\n  const limit = fileType === FILE_TYPES.SCHOOL_LOGO ? FILE_SIZE_LIMITS.SCHOOL_LOGO : FILE_SIZE_LIMITS.GENERAL_FILE"}, {"name": "isValidFilename", "value": "(filename: string): boolean => {\n  return !filename.includes(\"..\") && !filename.includes(\"/\") && !filename.includes(\"\\\\\")"}, {"name": "generateFilename", "value": "(originalName: string, accountId?: string | number): string => {\n  const timestamp = Date.now()"}, {"name": "generateLogoFilename", "value": "(originalName: string, accountId: string | number): string => {\n  const timestamp = Date.now()"}, {"name": "parseOriginalName", "value": "(filename: string): string => {\n  return filename.split(\"_\").slice(1).join(\"_\")"}, {"name": "getMulterErrorMessage", "value": "(errorCode: string): string => {\n  switch (errorCode) {\n    case MULTER_ERROR_CODES.LIMIT_FILE_SIZE:\n      return `檔案大小超過限制 (最大 ${FILE_SIZE_LIMITS.GENERAL_FILE / (1024 * 1024)}MB)`"}, {"name": "getSupportedFileTypes", "value": "() => {\n  return {\n    images: SUPPORTED_EXTENSIONS.IMAGES,\n    documents: SUPPORTED_EXTENSIONS.DOCUMENTS,\n    videos: SUPPORTED_EXTENSIONS.VIDEOS,\n  }"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "location", "file": "constants/location.ts", "constants": [{"name": "LOCALE_CODES", "value": "{\n  ZH_TW: \"zh-TW\",\n  EN_US: \"en-US\",\n} as const"}, {"name": "LOCATION_ERROR_MESSAGES", "value": "{\n  INVALID_CITY_ID: \"無效的縣市ID\",\n  GET_CITIES_FAILED: \"獲取縣市列表失敗\",\n  GET_AREAS_FAILED: \"獲取區域列表失敗\",\n  GET_HIERARCHY_FAILED: \"獲取地區層級資料失敗\",\n  UNKNOWN_ERROR: \"未知錯誤\",\n} as const"}, {"name": "LOCATION_SUCCESS_MESSAGES", "value": "{\n  CITIES_RETRIEVED: \"縣市列表獲取成功\",\n  AREAS_RETRIEVED: \"區域列表獲取成功\",\n  HIERARCHY_RETRIEVED: \"地區層級資料獲取成功\",\n} as const"}, {"name": "SQL_QUERIES", "value": "{\n  GET_CITIES: `\n    SELECT \n      c.CountyId,\n      ct.Name\n    FROM Counties c\n    INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId\n    WHERE ct.LocaleCode = @localeCode\n    ORDER BY c.CountyId\n  `,\n  GET_AREAS_BY_CITY: `\n    SELECT \n      d.DistrictId,\n      dt.Name,\n      d.CountyId\n    FROM Districts d\n    INNER JOIN DistrictTranslations dt ON d.DistrictId = dt.DistrictId\n    WHERE d.CountyId = @cityId AND dt.LocaleCode = @localeCode\n    ORDER BY d.DistrictId\n  `,\n  GET_HIERARCHY: `\n    SELECT \n      c.CountyId,\n      ct.Name as CountyName,\n      d.DistrictId,\n      dt.Name as DistrictName\n    FROM Counties c\n    INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId\n    LEFT JOIN Districts d ON c.CountyId = d.CountyId\n    LEFT JOIN DistrictTranslations dt ON d.DistrictId = dt.DistrictId AND dt.LocaleCode = @localeCode\n    WHERE ct.LocaleCode = @localeCode\n    ORDER BY c.CountyId, d.DistrictId\n  `,\n} as const"}, {"name": "isValidCityId", "value": "(cityId: string): boolean => {\n  const parsedId = parseInt(cityId)"}, {"name": "formatCityData", "value": "(cityRow: any): any => {\n  return {\n    id: cityRow.CountyId,\n    name: cityRow.Name,\n  }"}, {"name": "formatAreaData", "value": "(areaRow: any): any => {\n  return {\n    id: areaRow.DistrictId,\n    name: areaRow.Name,\n    cityId: areaRow.CountyId,\n  }"}, {"name": "organizeHierarchyData", "value": "(hierarchyRows: any[]): any[] => {\n  const cities: Record<number, any> = {}"}, {"name": "generateHierarchyStats", "value": "(hierarchyData: any[]): { totalCities: number"}, {"name": "generateSuccessMessage", "value": "(type: string, stats?: { totalCities?: number"}, {"name": "parseCityId", "value": "(cityIdStr: string): number => {\n  const cityId = parseInt(cityIdStr)"}, {"name": "DEFAULT_LOCALE", "value": "LOCALE_CODES.ZH_TW"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "profile", "file": "constants/profile.ts", "constants": [{"name": "MEMBER_ROLE_TYPE", "value": "{\n  SCHOOL: \"school\",\n  EPA: \"epa\",\n  TUTOR: \"tutor\",\n} as const"}, {"name": "ROLE_FIELD_MAP", "value": "{\n  IS_SCHOOL_PARTNER: 1,\n  IS_EPA_USER: 1,\n  IS_GUIDANCE_TEAM: 1,\n} as const"}, {"name": "LOCALE_CODE", "value": "{\n  ZH_TW: \"zh-TW\",\n  EN: \"en\",\n} as const"}, {"name": "DATA_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n  DELETED: 2,\n} as const"}, {"name": "DEFAULT_VALUES", "value": "{\n  MEMBER_ROLE: MEMBER_ROLE_TYPE.SCHOOL,\n  LOCALE: LOCALE_CODE.ZH_TW,\n  STATUS: DATA_STATUS.ACTIVE,\n} as const"}, {"name": "PROFILE_ERROR_MESSAGES", "value": "{\n  USER_NOT_AUTHENTICATED: \"使用者未認證\",\n  TOKEN_MISSING: \"缺少 Token\",\n  MEMBER_NOT_FOUND: \"找不到會員資料\",\n  SCHOOL_NOT_FOUND: \"找不到學校資料\",\n  INVALID_DATA: \"資料格式不正確\",\n  UPDATE_FAILED: \"更新失敗\",\n  ACCESS_DENIED: \"沒有權限存取此資料\",\n  INVALID_COUNTY: \"無效的縣市\",\n  INVALID_DISTRICT: \"無效的區域\",\n  CONTACT_LIMIT_EXCEEDED: \"聯絡人數量超過限制\",\n  STATISTICS_INVALID: \"統計資料格式不正確\",\n} as const"}, {"name": "PROFILE_SUCCESS_MESSAGES", "value": "{\n  PROFILE_RETRIEVED: \"會員資料獲取成功\",\n  PROFILE_UPDATED: \"會員資料更新成功\",\n  SCHOOL_BASIC_UPDATED: \"學校基本資料更新成功\",\n  PRINCIPAL_UPDATED: \"校長資料更新成功\",\n  CONTACTS_UPDATED: \"聯絡人資料更新成功\",\n  STATISTICS_UPDATED: \"學校統計資料更新成功\",\n} as const"}, {"name": "FIELD_LIMITS", "value": "{\n  MEMBER_NAME: 50,\n  JOB_TITLE: 100,\n  EMAIL: 255,\n  PHONE: 20,\n  ADDRESS: 500,\n  URL: 500,\n  INTRODUCTION: 2000,\n  SCHOOL_NAME: 200,\n  PRINCIPAL_NAME: 50,\n  CONTACT_NAME: 50,\n} as const"}, {"name": "CONTACT_LIMITS", "value": "{\n  MAX_CONTACTS: 10,\n  MIN_CONTACTS: 0,\n} as const"}, {"name": "STATISTICS_RANGES", "value": "{\n  MIN_VALUE: 0,\n  MAX_VALUE: 99999,\n} as const"}, {"name": "determineMemberRole", "value": "(isSchoolPartner: number, isEpaUser: number, isGuidanceTeam: number): string => {\n  if (isEpaUser === ROLE_FIELD_MAP.IS_EPA_USER) {\n    return MEMBER_ROLE_TYPE.EPA"}, {"name": "isSchoolMember", "value": "(roleType: string): boolean => {\n  return roleType === MEMBER_ROLE_TYPE.SCHOOL"}, {"name": "isEpaMember", "value": "(roleType: string): boolean => {\n  return roleType === MEMBER_ROLE_TYPE.EPA"}, {"name": "isTutorMember", "value": "(roleType: string): boolean => {\n  return roleType === MEMBER_ROLE_TYPE.TUTOR"}, {"name": "validateStatisticsValue", "value": "(value: number): boolean => {\n  return value >= STATISTICS_RANGES.MIN_VALUE && value <= STATISTICS_RANGES.MAX_VALUE"}, {"name": "validateContactsCount", "value": "(count: number): boolean => {\n  return count >= CONTACT_LIMITS.MIN_CONTACTS && count <= CONTACT_LIMITS.MAX_CONTACTS"}, {"name": "validateEmail", "value": "(email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/"}, {"name": "validatePhone", "value": "(phone: string): boolean => {\n  const phoneRegex = /^[\\d\\-\\+\\(\\)\\s]+$/"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "question", "file": "constants/question.ts", "constants": [{"name": "MAX_CERTIFICATION_STEPS", "value": "7"}, {"name": "EXCLUDED_QUESTION_TEMPLATES", "value": "[12, 15]"}, {"name": "GREEN_FLAG_STEPS", "value": "[8, 9]"}, {"name": "DEFAULT_CERTIFICATION_LEVEL", "value": "1"}, {"name": "QUESTION_STATUS", "value": "{\n  ACTIVE: 1,\n  INACTIVE: 0,\n} as const"}, {"name": "getStepTitle", "value": "(stepNumber: number): string => {\n  return STEP_TITLES[stepNumber] || `步驟 ${stepNumber}`"}, {"name": "isGreenFlagStep", "value": "(stepNumber: number): boolean => {\n  return GREEN_FLAG_STEPS.includes(stepNumber)"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "template-answer", "file": "constants/template-answer.ts", "constants": [{"name": "SUPPORTED_TEMPLATE_IDS", "value": "{\n  YES_NO: 1, // 是非選擇題\n  TEAM_MEMBER: 2, // 團隊成員表單\n  MEETING_RECORD: 3, // 會議記錄\n  SHARE_MEETING: 4, // 分享會議資訊\n  RECRUIT_MEMBER: 5, // 招募新成員\n  PHOTO_RECORD: 6, // 照片上傳\n  ENVIRONMENT_PATH: 8, // 環境路徑選擇\n  TEXTAREA: 16, // 文字區域\n  SUMMARY: 19, // 總結申論\n  RECERTIFICATION_MEETING: 21, // 會議記錄（再認證）\n} as const"}, {"name": "ANSWER_STATUS", "value": "{\n  DRAFT: \"draft\", // 草稿狀態\n  SUBMITTED: \"submitted\", // 已提交\n  VALIDATED: \"validated\", // 已驗證\n  REJECTED: \"rejected\", // 已拒絕\n} as const"}, {"name": "YES_NO_VALUES", "value": "[\"0\", \"1\", \"\"] as const"}, {"name": "DATE_FORMAT_REGEX", "value": "/^\\d{4}-\\d{2}-\\d{2}$/"}, {"name": "TEMPLATE_ANSWER_ERROR_MESSAGES", "value": "{\n  MISSING_PARAMETERS: \"缺少必要參數\",\n  VALIDATION_FAILED: \"答案格式驗證失敗\",\n  SAVE_FAILED: \"模板答案儲存失敗\",\n  VALIDATE_FAILED: \"答案驗證失敗\",\n  UNKNOWN_ERROR: \"未知錯誤\",\n  VALIDATION_ERROR: \"驗證過程中發生錯誤\",\n  UNKNOWN_TEMPLATE: \"未知的模板ID\",\n\n  // 欄位特定錯誤\n  MISSING_YES_NO: \"缺少 is_yes_no 欄位\",\n  INVALID_YES_NO: \"is_yes_no 必須是 '0', '1' 或空字串\",\n  INVALID_ARRAY: \"必須是陣列\",\n  INVALID_STRING: \"必須是字串\",\n  MISSING_FIELD: \"缺少必要欄位\",\n  INVALID_DATE_FORMAT: \"日期格式錯誤，應為 YYYY-MM-DD\",\n  MISSING_DATE_OR_THEME: \"缺少日期或主題\",\n  INVALID_MEMBER_FORMAT: \"的欄位格式錯誤\",\n  MISSING_PATH_OR_NAME: \"缺少路徑或名稱\",\n  MISSING_DATE_INFO: \"缺少日期資訊\",\n} as const"}, {"name": "TEMPLATE_ANSWER_SUCCESS_MESSAGES", "value": "{\n  VALIDATION_SUCCESS: \"答案格式驗證成功\",\n  SAVE_SUCCESS: \"模板答案保存成功\",\n  FORMAT_VALID: \"答案格式有效\",\n} as const"}, {"name": "REQUIRED_PARAMETERS", "value": "{\n  SAVE: [\"certification_sid\", \"question_sid\", \"template_id\", \"answer_data\"],\n  VALIDATE: [\"template_id\", \"answer_data\"],\n} as const"}, {"name": "TEMPLATE_VALIDATION_RULES", "value": "{\n  [SUPPORTED_TEMPLATE_IDS.YES_NO]: {\n    required_fields: [\"is_yes_no\"],\n    valid_values: {\n      is_yes_no: YES_NO_VALUES,\n    },\n  },\n  [SUPPORTED_TEMPLATE_IDS.TEAM_MEMBER]: {\n    required_lists: [\"student_list\", \"teacher_list\", \"community_member_list\"],\n    member_fields: [\"input_1\", \"input_2\", \"input_3\"],\n  },\n  [SUPPORTED_TEMPLATE_IDS.MEETING_RECORD]: {\n    required_arrays: [\"meeting_date_and_theme\", \"file\"],\n    meeting_fields: [\"input_1\", \"input_2\"],\n    date_validation: true,\n  },\n  [SUPPORTED_TEMPLATE_IDS.SHARE_MEETING]: {\n    required_fields: [\"is_yes_no\"],\n    required_objects: [\"share_people\", \"how_share_meeting\"],\n    checkbox_fields: [\"share_people.checkbox\", \"how_share_meeting.checkbox\"],\n  },\n  [SUPPORTED_TEMPLATE_IDS.RECRUIT_MEMBER]: {\n    required_fields: [\"is_yes_no\", \"textarea\"],\n    string_fields: [\"textarea\"],\n  },\n  [SUPPORTED_TEMPLATE_IDS.PHOTO_RECORD]: {\n    required_arrays: [\"photo_record\"],\n  },\n  [SUPPORTED_TEMPLATE_IDS.ENVIRONMENT_PATH]: {\n    required_arrays: [\"improve_path_list\"],\n    path_fields: [\"path\", \"cname\"],\n    date_fields: [\"date.input_1\", \"date.input_2\"],\n  },\n  [SUPPORTED_TEMPLATE_IDS.TEXTAREA]: {\n    required_fields: [\"textarea\"],\n    string_fields: [\"textarea\"],\n  },\n  [SUPPORTED_TEMPLATE_IDS.SUMMARY]: {\n    required_textareas: 5,\n  },\n  [SUPPORTED_TEMPLATE_IDS.RECERTIFICATION_MEETING]: {\n    required_textareas: 7,\n  },\n} as const"}, {"name": "validateTemplateAnswer", "value": "(templateId: number, answerData: Record<string, unknown>): { isValid: boolean"}, {"name": "standardizeAnswerFormat", "value": "(templateId: number, answerData: Record<string, unknown>): Record<string, unknown> => {\n  switch (templateId) {\n    case SUPPORTED_TEMPLATE_IDS.YES_NO:\n      return {\n        is_yes_no: String(answerData.is_yes_no || \"\"),\n      }"}, {"name": "checkRequiredParameters", "value": "(params: Record<string, unknown>, required: string[]): string[] => {\n  const missing: string[] = []"}, {"name": "generateQuestionTitle", "value": "(questionSid: string, customTitle?: string): string => {\n  return customTitle || `問題${questionSid}`"}, {"name": "formatSaveResult", "value": "(request: any, standardizedAnswer: Record<string, unknown>, validationResult: { isValid: boolean"}, {"name": "isSupportedTemplate", "value": "(templateId: number): boolean => {\n  return Object.values(SUPPORTED_TEMPLATE_IDS).includes(templateId as any)"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}, {"name": "user", "file": "constants/user.ts", "constants": [{"name": "ROLE_TYPES", "value": "{\n  SCHOOL: \"School\",\n  GOVERNMENT: \"Government\",\n  TUTOR: \"<PERSON>tor\",\n  ADMIN: \"Admin\",\n} as const"}, {"name": "USER_STATUS", "value": "{\n  INACTIVE: 0,\n  ACTIVE: 1,\n  SUSPENDED: 2,\n  DELETED: 3,\n} as const"}, {"name": "CERTIFICATION_STATUS", "value": "{\n  DRAFT: \"Draft\",\n  IN_REVIEW: \"InReview\",\n  PASSED: \"Passed\",\n  REJECTED: \"Rejected\",\n  EXPIRED: \"Expired\",\n} as const"}, {"name": "CERTIFICATION_TYPES", "value": "{\n  BRONZE: \"Bronze\",\n  SILVER: \"Silver\",\n  GREEN_FLAG: \"GreenFlag\",\n} as const"}, {"name": "CERTIFICATION_LEVELS", "value": "{\n  BRONZE: 2,\n  <PERSON><PERSON><PERSON><PERSON>: 3,\n  <PERSON><PERSON><PERSON>_FLAG: 4,\n  G<PERSON><PERSON>_FLAG_R1: 5,\n  GREEN_FLAG_R2: 6,\n  GREEN_FLAG_R3: 7,\n} as const"}, {"name": "REVIEW_STATUS", "value": "{\n  IN_REVIEW: 0,\n  APPROVED: 1,\n  REJECTED: 2,\n  NEEDS_SUPPLEMENT: 3,\n  NOT_SUBMITTED: 4,\n} as const"}, {"name": "DATA_STATUS", "value": "{\n  ACTIVE: 0,\n  INACTIVE: 1,\n  DELETED: 2,\n} as const"}, {"name": "PERMISSION_FLAGS", "value": "{\n  IS_USE: 1,\n  NOT_USE: 0,\n} as const"}, {"name": "ALLOWED_ACCOUNT_FIELDS", "value": "{\n  [ROLE_TYPES.SCHOOL]: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"Avatar\"],\n  [ROLE_TYPES.GOVERNMENT]: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON>tar\"],\n  [ROLE_TYPES.TUTOR]: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"Avatar\"],\n  [ROLE_TYPES.ADMIN]: [\"Nick<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Phone\", \"Avatar\"],\n} as const"}, {"name": "ALLOWED_SCHOOL_FIELDS", "value": "[\"Name\", \"EnglishName\", \"Address\", \"Phone\", \"Email\", \"Website\", \"ContactPerson\", \"ContactPhone\", \"ContactEmail\"] as const"}, {"name": "USER_ERROR_MESSAGES", "value": "{\n  USER_NOT_AUTHENTICATED: \"使用者未認證\",\n  USER_NOT_FOUND: \"找不到使用者資料\",\n  NO_PERMISSION: \"無權限訪問此資料\",\n  SCHOOL_ONLY: \"只有學校身份可以訪問學校資料\",\n  SCHOOL_NOT_FOUND: \"找不到對應的學校資料\",\n  INVALID_EMAIL: \"電子郵件格式不正確\",\n  INVALID_PHONE: \"電話號碼格式不正確\",\n  UPDATE_FAILED: \"更新使用者資料失敗\",\n  NO_VALID_FIELDS: \"沒有提供有效的更新欄位\",\n  TRANSACTION_FAILED: \"資料庫事務處理失敗\",\n} as const"}, {"name": "USER_SUCCESS_MESSAGES", "value": "{\n  PROFILE_RETRIEVED: \"使用者資料獲取成功\",\n  PROFILE_UPDATED: \"使用者資料更新成功\",\n  CERTIFICATIONS_RETRIEVED: \"認證資料獲取成功\",\n  SCHOOL_INFO_RETRIEVED: \"學校資料獲取成功\",\n  PERMISSIONS_RETRIEVED: \"權限資料獲取成功\",\n} as const"}, {"name": "DEFAULT_PERMISSIONS", "value": "{\n  ADMIN: [\"admin\"],\n  USER: [\"user\"],\n  SCHOOL: [\"school\"],\n  GOVERNMENT: [\"government\"],\n  TUTOR: [\"tutor\"],\n} as const"}, {"name": "SQL_QUERIES", "value": "{\n  GET_ACCOUNT: \"SELECT * FROM Account WHERE Id = @userId AND IsActive = 1\",\n  GET_ACCOUNT_WITH_PERMISSIONS: `\n    SELECT a.*, \n           STRING_AGG(DISTINCT p.ename, ',') as Permissions,\n           STRING_AGG(DISTINCT pg.ename, ',') as PermissionGroups\n    FROM Account a\n    LEFT JOIN account_permission_group apg ON a.Id = apg.accountSid AND apg.dataStatus = 0\n    LEFT JOIN permission_group pg ON apg.groupSid = pg.sid AND pg.isuse = 1\n    LEFT JOIN permission_group_map pgm ON pg.sid = pgm.groupSid AND pgm.dataStatus = 0\n    LEFT JOIN permission p ON pgm.permissionSid = p.sid AND p.isuse = 1\n    WHERE a.Id = @userId AND a.IsActive = 1\n    GROUP BY a.Id, a.Account, a.<PERSON>, a.<PERSON><PERSON>, a.Phone, a.Avatar, a.RoleType, a.IsActive, a.CreatedTime, a.UpdatedTime, a.Remark\n  `,\n  GET_SCHOOL_BY_ACCOUNT: `\n    SELECT s.*\n    FROM School s\n    INNER JOIN Account a ON s.Id = a.CmsUserId\n    WHERE a.Id = @accountId AND s.IsActive = 1\n  `,\n  GET_CERTIFICATIONS: `\n    SELECT c.*\n    FROM Certifications c\n    WHERE c.SchoolId = @userId AND c.Status = 1 \n    ORDER BY c.CreatedTime DESC\n  `,\n  GET_PERMISSIONS: `\n    SELECT DISTINCT p.ename as Code, p.cname as Name, p.remark as Description\n    FROM account_permission_group apg\n    INNER JOIN permission_group pg ON apg.groupSid = pg.sid\n    INNER JOIN permission_group_map pgm ON pg.sid = pgm.groupSid\n    INNER JOIN permission p ON pgm.permissionSid = p.sid\n    WHERE apg.accountSid = @accountId \n      AND apg.dataStatus = 0\n      AND pg.isuse = 1\n      AND pgm.dataStatus = 0\n      AND p.isuse = 1\n    ORDER BY p.cname\n  `,\n  GET_PERMISSION_GROUPS: `\n    SELECT DISTINCT pg.ename as Code, pg.cname as Name, pg.remark as Description\n    FROM account_permission_group apg\n    INNER JOIN permission_group pg ON apg.groupSid = pg.sid\n    WHERE apg.accountSid = @accountId \n      AND apg.dataStatus = 0\n      AND pg.isuse = 1\n    ORDER BY pg.cname\n  `,\n} as const"}, {"name": "isValidEmail", "value": "(email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/"}, {"name": "isValidPhone", "value": "(phone: string): boolean => {\n  // 允許多種電話號碼格式\n  const phoneRegex = /^[\\d\\-+() ]{8,20}$/"}, {"name": "hasPermission", "value": "(userRole: string, requiredRole: string): boolean => {\n  if (userRole === ROLE_TYPES.ADMIN) return true"}, {"name": "isSchoolRole", "value": "(roleType: string): boolean => {\n  return roleType === ROLE_TYPES.SCHOOL"}, {"name": "isAdminRole", "value": "(roleType: string): boolean => {\n  return roleType === ROLE_TYPES.ADMIN"}, {"name": "getAllowed<PERSON><PERSON><PERSON><PERSON><PERSON>s", "value": "(roleType: string): string[] => {\n  return ALLOWED_ACCOUNT_FIELDS[roleType as keyof typeof ALLOWED_ACCOUNT_FIELDS] || []"}, {"name": "getAllowed<PERSON><PERSON>ol<PERSON><PERSON>s", "value": "(): string[] => {\n  return [...ALLOWED_SCHOOL_FIELDS]"}, {"name": "mapCertificationLevelToType", "value": "(level: number): string => {\n  switch (level) {\n    case CERTIFICATION_LEVELS.BRONZE:\n      return CERTIFICATION_TYPES.BRONZE"}, {"name": "mapReviewStatusToCertificationStatus", "value": "(reviewStatus: number): string => {\n  switch (reviewStatus) {\n    case REVIEW_STATUS.APPROVED:\n      return CERTIFICATION_STATUS.PASSED"}, {"name": "canAccessUserData", "value": "(currentUserId: string, targetUserId: string, permissions: string[]): boolean => {\n  // 如果是同一個用戶，允許訪問\n  if (currentUserId === targetUserId) return true"}, {"name": "validateUpdateData", "value": "(data: any): { isValid: boolean"}], "functions": [], "exports": ["const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const", "const"]}], "summary": {"totalRoutes": 14, "totalModels": 13, "totalConstants": 13, "routeCategories": {"admin": 4, "answer": 4, "auth": 6, "campus-submissions": 7, "certificate": 6, "certification": 6, "dashboard": 8, "file": 9, "location": 3, "metrics": 4, "question": 5, "simple-profile": 2, "template-answer": 2, "user": 6}}}