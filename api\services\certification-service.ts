// ========== 認證服務 - 業務邏輯和資料庫查詢 ==========

import { executeQuery, executeQuerySingle, executeInsert, executeUpdate } from "../config/database-mssql.js";
import {
  Certification,
  CertificationListItem,
  CertificationAvailability,
  CertificationRequest,
  SchoolAccount,
  ExistingCertification,
  CertificationQueryResult,
  CertificationStatistics,
} from "../models/certification.js";
import {
  CERTIFICATION_LEVEL,
  REVIEW_STATUS,
  CERTIFICATION_STATUS,
  CERTIFICATION_TYPES,
  CERTIFICATION_ERROR_MESSAGES,
  CERTIFICATION_SUCCESS_MESSAGES,
  getReviewStatusInfo,
  mapCertificationType,
  getYearsSinceApproval,
  isEditable,
  isDeletable,
  canSubmit,
  getFrontendId,
  TIME_CONSTRAINTS,
} from "../constants/certification.js";

export class CertificationService {
  // 獲取認證清單
  static async getCertificationList(accountId: number): Promise<{
    all: CertificationListItem[];
    drafts: CertificationListItem[];
    pending: CertificationListItem[];
    passed: CertificationListItem[];
    statistics: CertificationStatistics;
  }> {
    try {
      // 查詢認證資料，包含申請人姓名
      const query = `
        SELECT c.*, mp.MemberName as ApplicantName
        FROM Certifications c
        LEFT JOIN Accounts a ON a.SchoolId = c.SchoolId
        LEFT JOIN MemberProfiles mp ON mp.AccountId = a.AccountId AND mp.LocaleCode = 'zh-TW'
        WHERE a.AccountId = @accountId AND c.Status = @activeStatus
        ORDER BY c.CreatedTime DESC
      `;

      const certifications = await executeQuery<CertificationQueryResult>(query, {
        accountId,
        activeStatus: CERTIFICATION_STATUS.ACTIVE,
      });

      const transformedCerts = certifications.map(this.transformCertificationToListItem);

      // 過濾和分類
      const activeCerts = transformedCerts.filter((cert) => cert.status === CERTIFICATION_STATUS.ACTIVE);

      return {
        all: activeCerts,
        drafts: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.NOT_SUBMITTED || cert.reviewStatus === REVIEW_STATUS.NEEDS_SUPPLEMENT),
        pending: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.UNDER_REVIEW),
        passed: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.APPROVED),
        statistics: this.calculateStatistics(transformedCerts),
      };
    } catch (error) {
      console.error("❌ [CertificationService] 獲取認證清單失敗:", error);
      throw new Error(CERTIFICATION_ERROR_MESSAGES.CREATE_FAILED);
    }
  }

  // 檢查認證可用性
  static async checkCertificationAvailability(accountId: number): Promise<{
    availability: CertificationAvailability[];
    hasPassedGreenFlag: boolean;
    greenFlagApprovedYearsAgo: number;
    greenFlagR1ApprovedYearsAgo: number;
    greenFlagR2ApprovedYearsAgo: number;
  }> {
    try {
      // 獲取學校ID
      const schoolAccount = await this.getSchoolIdByAccountId(accountId);
      if (!schoolAccount) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.SCHOOL_NOT_ASSIGNED);
      }

      // 查詢現有認證
      const existingCertifications = await executeQuery<ExistingCertification>(
        `SELECT level, reviewStatus, approvedDate FROM Certifications 
         WHERE SchoolId = @schoolId AND Status = @activeStatus`,
        {
          schoolId: schoolAccount.schoolId,
          activeStatus: CERTIFICATION_STATUS.ACTIVE,
        }
      );

      // 分析綠旗系列認證
      const greenFlagCert = existingCertifications.find(
        (cert) => cert.level === CERTIFICATION_LEVEL.GREEN_FLAG && cert.reviewStatus === REVIEW_STATUS.APPROVED
      );
      const greenFlagR1Cert = existingCertifications.find(
        (cert) => cert.level === CERTIFICATION_LEVEL.GREEN_FLAG_R1 && cert.reviewStatus === REVIEW_STATUS.APPROVED
      );
      const greenFlagR2Cert = existingCertifications.find(
        (cert) => cert.level === CERTIFICATION_LEVEL.GREEN_FLAG_R2 && cert.reviewStatus === REVIEW_STATUS.APPROVED
      );

      // 檢查每個認證類型的可用性
      const availability = CERTIFICATION_TYPES.map((certType) => {
        const existingCert = existingCertifications.find((cert) => cert.level === certType.level);
        let available = true;
        let reason: string | undefined;

        // 如果已有相同等級的認證，檢查狀態
        if (existingCert) {
          if (existingCert.reviewStatus === REVIEW_STATUS.APPROVED) {
            available = false;
            reason = "已通過此認證";
          } else if (existingCert.reviewStatus === REVIEW_STATUS.UNDER_REVIEW) {
            available = false;
            reason = "認證審核中";
          } else if (existingCert.reviewStatus === REVIEW_STATUS.NEEDS_SUPPLEMENT) {
            available = false;
            reason = "需要補件，無法重新申請";
          } else if (existingCert.reviewStatus === REVIEW_STATUS.NOT_SUBMITTED) {
            // 未送審的認證也視為不可申請（避免重複建立）
            available = false;
            reason = "已有未送審的認證申請";
          }
        }

        // 檢查綠旗延續認證的特殊條件
        if (available && certType.requiresGreenFlag) {
          if (certType.level === CERTIFICATION_LEVEL.GREEN_FLAG_R1) {
            if (!greenFlagCert) {
              available = false;
              reason = "需要先通過綠旗認證";
            } else if (getYearsSinceApproval(greenFlagCert.approvedDate) < TIME_CONSTRAINTS.GREEN_FLAG_R1_WAIT) {
              available = false;
              reason = "需要綠旗通過滿兩年";
            }
          } else if (certType.level === CERTIFICATION_LEVEL.GREEN_FLAG_R2) {
            if (!greenFlagR1Cert) {
              available = false;
              reason = "需要先通過綠旗R1認證";
            } else if (getYearsSinceApproval(greenFlagR1Cert.approvedDate) < TIME_CONSTRAINTS.GREEN_FLAG_R2_WAIT) {
              available = false;
              reason = "需要綠旗R1通過滿兩年";
            }
          } else if (certType.level === CERTIFICATION_LEVEL.GREEN_FLAG_R3) {
            if (!greenFlagR2Cert) {
              available = false;
              reason = "需要先通過綠旗R2認證";
            } else if (getYearsSinceApproval(greenFlagR2Cert.approvedDate) < TIME_CONSTRAINTS.GREEN_FLAG_R3_WAIT) {
              available = false;
              reason = "需要綠旗R2通過滿兩年";
            }
          }
        }

        return {
          id: certType.id,
          name: certType.name,
          level: certType.level,
          available,
          reason,
          frontendId: getFrontendId(certType.level),
        };
      });

      return {
        availability,
        hasPassedGreenFlag: !!greenFlagCert,
        greenFlagApprovedYearsAgo: greenFlagCert ? getYearsSinceApproval(greenFlagCert.approvedDate) : 0,
        greenFlagR1ApprovedYearsAgo: greenFlagR1Cert ? getYearsSinceApproval(greenFlagR1Cert.approvedDate) : 0,
        greenFlagR2ApprovedYearsAgo: greenFlagR2Cert ? getYearsSinceApproval(greenFlagR2Cert.approvedDate) : 0,
      };
    } catch (error) {
      console.error("❌ [CertificationService] 檢查認證可用性失敗:", error);
      throw error;
    }
  }

  // 獲取單個認證資訊
  static async getCertificationById(certificationId: number): Promise<Certification | null> {
    try {
      const certification = await executeQuerySingle<CertificationQueryResult>(
        "SELECT * FROM Certifications WHERE CertificationId = @certificationId AND Status = @activeStatus",
        {
          certificationId,
          activeStatus: CERTIFICATION_STATUS.ACTIVE,
        }
      );

      return certification ? this.mapQueryResultToCertification(certification) : null;
    } catch (error) {
      console.error("❌ [CertificationService] 獲取認證失敗:", error);
      throw new Error(CERTIFICATION_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND);
    }
  }

  // 創建新認證
  static async createCertification(accountId: number, request: CertificationRequest): Promise<Certification> {
    try {
      // 獲取學校ID
      const schoolAccount = await this.getSchoolIdByAccountId(accountId);
      if (!schoolAccount) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.SCHOOL_NOT_ASSIGNED);
      }

      // 檢查是否已有相同等級的認證
      const existingCert = await executeQuerySingle<CertificationQueryResult>(
        `SELECT * FROM Certifications 
         WHERE SchoolId = @schoolId AND Level = @level AND Status = @activeStatus`,
        {
          schoolId: schoolAccount.schoolId,
          level: request.level,
          activeStatus: CERTIFICATION_STATUS.ACTIVE,
        }
      );

      if (existingCert) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.DUPLICATE_CERTIFICATION);
      }

      // 如果是R1-R3，檢查是否有已通過的綠旗認證
      if (request.level >= CERTIFICATION_LEVEL.GREEN_FLAG_R1) {
        const passedGreenFlag = await executeQuerySingle<CertificationQueryResult>(
          `SELECT * FROM Certifications 
           WHERE SchoolId = @schoolId AND Level = @greenFlagLevel AND Status = @activeStatus`,
          {
            schoolId: schoolAccount.schoolId,
            greenFlagLevel: CERTIFICATION_LEVEL.GREEN_FLAG,
            activeStatus: CERTIFICATION_STATUS.ACTIVE,
          }
        );

        if (!passedGreenFlag) {
          throw new Error(CERTIFICATION_ERROR_MESSAGES.GREEN_FLAG_REQUIRED);
        }
      }

      const now = new Date();
      const insertParams = {
        schoolId: schoolAccount.schoolId,
        level: request.level,
        reviewStatus: REVIEW_STATUS.NOT_SUBMITTED,
        createdTime: now,
        updatedTime: now,
        createdUserId: accountId,
        addType: "Frontend",
        status: CERTIFICATION_STATUS.ACTIVE,
      };

      const certificationId = await executeInsert(
        `INSERT INTO Certifications 
         (SchoolId, Level, ReviewStatus, CreatedTime, UpdatedTime, CreatedUserId, AddType, Status) 
         OUTPUT INSERTED.CertificationId
         VALUES (@schoolId, @level, @reviewStatus, @createdTime, @updatedTime, @createdUserId, @addType, @status)`,
        insertParams
      );

      if (!certificationId || certificationId === 0) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.CREATE_FAILED);
      }

      const newCertification = await this.getCertificationById(certificationId);
      if (!newCertification) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.CREATE_FAILED);
      }

      return newCertification;
    } catch (error) {
      console.error("❌ [CertificationService] 建立認證失敗:", error);
      throw error;
    }
  }

  // 更新認證
  static async updateCertification(certificationId: number, accountId: number, updateData: Partial<Certification>): Promise<Certification> {
    try {
      // 檢查認證是否存在且屬於該用戶
      const certification = await this.getCertificationById(certificationId);
      if (!certification) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND);
      }

      // 檢查權限
      const schoolAccount = await this.getSchoolIdByAccountId(accountId);
      if (!schoolAccount || schoolAccount.schoolId !== certification.schoolId) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.INVALID_PERMISSIONS);
      }

      // 構建更新欄位
      const updateFields: string[] = [];
      const updateValues: Record<string, number | Date | string | undefined> = { certificationId };

      if (updateData.reviewStatus !== undefined) {
        updateFields.push("ReviewStatus = @reviewStatus");
        updateValues.reviewStatus = updateData.reviewStatus;
      }

      if (updateData.reviewDate !== undefined) {
        updateFields.push("ReviewDate = @reviewDate");
        updateValues.reviewDate = updateData.reviewDate;
      }

      if (updateData.approvedDate !== undefined) {
        updateFields.push("ApprovedDate = @approvedDate");
        updateValues.approvedDate = updateData.approvedDate;
      }

      if (updateFields.length > 0) {
        updateFields.push("UpdatedTime = GETDATE()");

        const updateQuery = `
          UPDATE Certifications 
          SET ${updateFields.join(", ")}
          WHERE CertificationId = @certificationId
        `;

        await executeUpdate(updateQuery, updateValues);
      }

      const updatedCertification = await this.getCertificationById(certificationId);
      if (!updatedCertification) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.UPDATE_FAILED);
      }

      return updatedCertification;
    } catch (error) {
      console.error("❌ [CertificationService] 更新認證失敗:", error);
      throw error;
    }
  }

  // 刪除認證
  static async deleteCertification(certificationId: number, accountId: number): Promise<void> {
    try {
      // 檢查認證是否存在且屬於該用戶
      const certification = await this.getCertificationById(certificationId);
      if (!certification) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND);
      }

      // 檢查權限
      const schoolAccount = await this.getSchoolIdByAccountId(accountId);

      if (!schoolAccount || schoolAccount.schoolId !== certification.schoolId) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.INVALID_PERMISSIONS);
      }

      // 檢查是否可刪除
      if (!isDeletable(certification.reviewStatus)) {
        throw new Error(CERTIFICATION_ERROR_MESSAGES.INVALID_PERMISSIONS);
      }

      // 軟刪除
      await executeUpdate("UPDATE Certifications SET Status = @inactiveStatus, UpdatedTime = GETDATE() WHERE CertificationId = @certificationId", {
        certificationId,
        inactiveStatus: CERTIFICATION_STATUS.INACTIVE,
      });
    } catch (error) {
      console.error("❌ [CertificationService] 刪除認證失敗:", error);
      throw error;
    }
  }

  // ========== 私有輔助方法 ==========

  // 根據帳號ID獲取學校ID
  private static async getSchoolIdByAccountId(accountId: number): Promise<SchoolAccount | null> {
    return await executeQuerySingle<SchoolAccount>("SELECT schoolId FROM Accounts WHERE AccountId = @accountId", { accountId });
  }

  // 轉換認證查詢結果為清單項目
  private static transformCertificationToListItem(cert: CertificationQueryResult): CertificationListItem {
    const level = cert.Level || CERTIFICATION_LEVEL.BRONZE;
    const levelName = level === CERTIFICATION_LEVEL.BRONZE ? "Bronze" : level === CERTIFICATION_LEVEL.SILVER ? "Silver" : "GreenFlag";
    const typeInfo = mapCertificationType(levelName, level);
    const statusInfo = getReviewStatusInfo(cert.ReviewStatus);

    return {
      id: cert.CertificationId,
      status: cert.Status,
      statusInfo,
      typeInfo: {
        name: typeInfo.name,
        fullName: typeInfo.fullName,
        level: level,
        isRenewal: typeInfo.isRenewal,
        icon: typeInfo.icon,
      },
      applicantName: cert.ApplicantName || "未設定",
      applyDate: cert.CreatedTime,
      reviewStatus: cert.ReviewStatus,
      reviewDate: cert.ReviewDate,
      passDate: cert.ApprovedDate,
      isEditable: isEditable(cert.ReviewStatus),
      isDeletable: isDeletable(cert.ReviewStatus),
      canSubmit: canSubmit(cert.ReviewStatus),
      canView: true,
    };
  }

  // 計算統計資料
  private static calculateStatistics(certifications: CertificationListItem[]): CertificationStatistics {
    const activeCerts = certifications.filter((cert) => cert.status === CERTIFICATION_STATUS.ACTIVE);

    return {
      total: activeCerts.length,
      drafts: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.NOT_SUBMITTED || cert.reviewStatus === REVIEW_STATUS.NEEDS_SUPPLEMENT).length,
      pending: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.UNDER_REVIEW).length,
      passed: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.APPROVED).length,
      inReview: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.REJECTED).length,
      returned: activeCerts.filter((cert) => cert.reviewStatus === REVIEW_STATUS.NEEDS_SUPPLEMENT).length,
    };
  }

  // 映射查詢結果為認證模型
  private static mapQueryResultToCertification(result: CertificationQueryResult): Certification {
    return {
      certificationId: result.CertificationId,
      schoolId: result.SchoolId,
      level: result.Level,
      reviewStatus: result.ReviewStatus,
      reviewDate: result.ReviewDate,
      approvedDate: result.ApprovedDate,
      createdTime: result.CreatedTime,
      updatedTime: result.UpdatedTime,
      createdUserId: result.CreatedUserId,
      reviewerId: result.ReviewerId,
      addType: result.AddType,
      status: result.Status,
    };
  }
}
