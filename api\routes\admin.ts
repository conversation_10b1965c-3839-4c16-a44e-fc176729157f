import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import { AdminService } from "../services/admin-service.js";
import { APILogger } from "../utils/logger.js";
import {
  AnswerStatusMarkParams,
  ActionLogsParams,
  ReviewCommentSaveParams,
  ReviewCommentsParams,
  AnswerStatusMarkQueryParams,
  ActionLogsQueryParams,
  ReviewCommentSaveQueryParams,
  ReviewCommentsQueryParams,
  AnswerStatusMarkResponse,
  ActionLogsResponse,
  ReviewCommentSaveResponse,
  ReviewCommentsResponse,
  AnswerStatusMarkRequest,
  ReviewCommentRequest,
  AuthenticatedUser,
} from "../models/admin.js";
import { ADMIN_ERROR_MESSAGES, ADMIN_SUCCESS_MESSAGES } from "../constants/admin.js";

const router = express.Router();

// 標示答案狀態 (管理員操作)
router.post(
  "/mark-answer-status",
  authenticateToken,
  async (
    req: express.Request<AnswerStatusMarkParams, AnswerStatusMarkResponse, AnswerStatusMarkRequest, AnswerStatusMarkQueryParams>,
    res: express.Response<AnswerStatusMarkResponse>
  ) => {
    try {
      APILogger.logRequest(req, "Admin", "標示答案狀態");
      const user = req.user as AuthenticatedUser;

      if (!user) {
        return res.status(401).json({
          success: false,
          message: ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
          data: {} as any,
        });
      }

      const answerStatusInfo = await AdminService.markAnswerStatus(req.body, user);

      APILogger.logSuccess(
        "Admin",
        "標示答案狀態",
        {
          certificationId: req.body.certificationId,
          questionId: req.body.questionId,
          action: req.body.action,
          newStatus: answerStatusInfo.newStatus,
          adminUser: user.account,
        },
        `Answer marked as ${answerStatusInfo.statusDescription}`
      );

      res.json({
        success: true,
        message: `Answer marked as ${answerStatusInfo.statusDescription} successfully`,
        data: answerStatusInfo,
      });
    } catch (error: unknown) {
      APILogger.logError("Admin", "標示答案狀態", error, 500);

      if (error instanceof Error) {
        if ([ADMIN_ERROR_MESSAGES.MISSING_PARAMETERS, ADMIN_ERROR_MESSAGES.INVALID_ACTION, ADMIN_ERROR_MESSAGES.NOT_IN_REVIEW_STATUS].includes(error.message)) {
          return res.status(400).json({
            success: false,
            message: error.message,
            data: {} as any,
          });
        }

        if (error.message === ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS) {
          return res.status(403).json({
            success: false,
            message: error.message,
            data: {} as any,
          });
        }

        if ([ADMIN_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND, ADMIN_ERROR_MESSAGES.ANSWER_NOT_FOUND].includes(error.message)) {
          return res.status(404).json({
            success: false,
            message: error.message,
            data: {} as any,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: "Internal server error during answer status marking",
        data: {} as any,
      });
    }
  }
);

// 獲取管理員操作歷史
router.get(
  "/action-logs/:certificationId",
  authenticateToken,
  async (req: express.Request<ActionLogsParams, ActionLogsResponse, {}, ActionLogsQueryParams>, res: express.Response<ActionLogsResponse>) => {
    try {
      APILogger.logRequest(req, "Admin", "獲取管理員操作歷史");
      const { certificationId } = req.params;
      const user = req.user as AuthenticatedUser;

      if (!user) {
        return res.status(401).json({
          success: false,
          message: ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
          data: [],
        });
      }

      const logs = await AdminService.getActionLogs(parseInt(certificationId), user);

      APILogger.logSuccess(
        "Admin",
        "獲取管理員操作歷史",
        {
          certificationId,
          logCount: logs.length,
        },
        ADMIN_SUCCESS_MESSAGES.ACTION_LOGS_RETRIEVED
      );

      res.json({
        success: true,
        message: ADMIN_SUCCESS_MESSAGES.ACTION_LOGS_RETRIEVED,
        data: logs,
      });
    } catch (error: unknown) {
      APILogger.logError("Admin", "獲取管理員操作歷史", error, 500);

      if (error instanceof Error && error.message === ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS) {
        return res.status(403).json({
          success: false,
          message: error.message,
          data: [],
        });
      }

      res.status(500).json({
        success: false,
        message: "Internal server error during action logs retrieval",
        data: [],
      });
    }
  }
);

// 保存評審意見 (管理員功能)
router.post(
  "/save-review-comment",
  authenticateToken,
  async (
    req: express.Request<ReviewCommentSaveParams, ReviewCommentSaveResponse, ReviewCommentRequest, ReviewCommentSaveQueryParams>,
    res: express.Response<ReviewCommentSaveResponse>
  ) => {
    try {
      APILogger.logRequest(req, "Admin", "保存評審意見");
      const user = req.user as AuthenticatedUser;

      if (!user) {
        return res.status(401).json({
          success: false,
          message: ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
          data: {} as any,
        });
      }

      const result = await AdminService.saveReviewComment(req.body, user);

      const successMessage = result.action === "update" ? ADMIN_SUCCESS_MESSAGES.REVIEW_COMMENT_UPDATED : ADMIN_SUCCESS_MESSAGES.REVIEW_COMMENT_SAVED;

      APILogger.logSuccess(
        "Admin",
        "保存評審意見",
        {
          certificationId: req.body.certificationId,
          stepId: req.body.stepId,
          action: result.action,
          adminUser: user.account,
        },
        successMessage
      );

      res.json({
        success: true,
        message: successMessage,
        data: result,
      });
    } catch (error: unknown) {
      APILogger.logError("Admin", "保存評審意見", error, 500);

      if (error instanceof Error) {
        if ([ADMIN_ERROR_MESSAGES.MISSING_PARAMETERS, ADMIN_ERROR_MESSAGES.COMMENT_TOO_LONG, ADMIN_ERROR_MESSAGES.COMMENT_TOO_SHORT].includes(error.message)) {
          return res.status(400).json({
            success: false,
            message: error.message,
            data: {} as any,
          });
        }

        if (error.message === ADMIN_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS) {
          return res.status(403).json({
            success: false,
            message: error.message,
            data: {} as any,
          });
        }

        if (error.message === ADMIN_ERROR_MESSAGES.CERTIFICATION_NOT_FOUND) {
          return res.status(404).json({
            success: false,
            message: error.message,
            data: {} as any,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: "Internal server error during review comment saving",
        data: {} as any,
      });
    }
  }
);

// 獲取評審意見 (管理員和申請者都可查看)
router.get(
  "/review-comments/:certificationId",
  authenticateToken,
  async (req: express.Request<ReviewCommentsParams, ReviewCommentsResponse, {}, ReviewCommentsQueryParams>, res: express.Response<ReviewCommentsResponse>) => {
    try {
      APILogger.logRequest(req, "Admin", "獲取評審意見");
      const { certificationId } = req.params;

      const comments = await AdminService.getReviewComments(parseInt(certificationId));

      APILogger.logSuccess(
        "Admin",
        "獲取評審意見",
        {
          certificationId,
          commentCount: comments.length,
        },
        ADMIN_SUCCESS_MESSAGES.REVIEW_COMMENTS_RETRIEVED
      );

      res.json({
        success: true,
        message: ADMIN_SUCCESS_MESSAGES.REVIEW_COMMENTS_RETRIEVED,
        data: comments,
      });
    } catch (error: unknown) {
      APILogger.logError("Admin", "獲取評審意見", error, 500);

      res.status(500).json({
        success: false,
        message: "Internal server error during review comments retrieval",
        data: [],
      });
    }
  }
);

export default router;
