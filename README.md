# EcoCampus 認證與審查系統

> 🌱 環保署校園環境認證管理平台

## 📋 專案概況

**EcoCampus** 是專為環保署校園環境認證管理設計的全端應用，支援三種身份：學校、環保署、輔導員的認證申請與審查流程。

### 🏗️ 技術架構
- **前端**: React + TypeScript + Vite (http://localhost:8080)
- **後端**: Node.js + Express + TypeScript (http://localhost:3001)
- **資料庫**: Microsoft SQL Server (VM-MSSQL-2022:1433)
- **UI**: shadcn/ui + Tailwind CSS

### 📁 專案結構
```
├── src/                    # 前端原始碼
├── api/                    # 後端 API
├── docs/                   # 文檔資料
└── public/                 # 靜態資源
```

## ⚡ 快速開始

```bash
# 1. 安裝與設定
git clone <GIT_URL>
cd ecocampus-certification-and-review-site
npm install
cp env.example .env

# 2. 啟動服務
npm run dev      # 前端 (8080)
npm run server   # 後端 (3001)
```

## 🔑 開發測試

### 快速測試連結
```bash
# 學校身份
http://localhost:8080/login?token=A0B0D18E-CFA0-4BEF-96EE-F157407E85A7

# 環保署身份  
http://localhost:8080/login?token=850D03CB-927A-45E6-AFDE-173C45F93E99

# 輔導員身份
http://localhost:8080/login?token=D1433FC9-85C6-4054-A97D-7D12B7AFD5BB
```

### Token 生成 API
```bash
# 生成測試 Token
curl -X POST http://localhost:3001/api/auth/create-token \
  -H "Content-Type: application/json" \
  -d '{"userId":"5","tokenType":"Login","expiredDays":365}'

# 身份對應：5=學校, 6=環保署, 7=輔導員
```

## 📚 開發文檔

- **[AI 代理操作規範](./AI_AGENT_OPERATION_RULES.md)** - 完整的開發流程和規範 ⭐
- **[開發快速參考](./DEV_QUICK_REFERENCE.md)** - 常用指令和操作一覽 ⭐

## ⚠️ 重要提醒

- **Token 撤銷**：登出會永久撤銷 Token，測試時建議直接關閉瀏覽器
- **開發專用**：Token 生成 API 僅供開發使用
- **服務依賴**：前端需要後端 API 正常運行

## 🤝 參與開發

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/new-feature`)
3. 提交變更 (`git commit -am 'Add new feature'`)
4. 推送分支 (`git push origin feature/new-feature`)
5. 建立 Pull Request

## 🔧 環境變數配置

### 📂 檔案上傳設定

在 `.env` 檔案中新增以下配置：

```bash
# 📁 檔案上傳配置
UPLOAD_MAX_SIZE=10485760          # 最大檔案大小 (bytes)，預設 10MB
UPLOAD_MAX_FILES=5                # 最大檔案數量
UPLOAD_BASE_PATH=/path/to/uploads # 上傳基礎路徑，留空使用預設路徑
SCHOOL_LOGO_PATH=                 # 校徽上傳路徑，留空使用預設路徑
GENERAL_UPLOAD_PATH=              # 一般檔案上傳路徑，留空使用預設路徑
```

### 🌍 不同環境配置

#### 開發環境（預設）
```bash
# 開發環境使用相對路徑
UPLOAD_BASE_PATH=./public/uploads
SCHOOL_LOGO_PATH=./public/uploads/school-logos
GENERAL_UPLOAD_PATH=./api/uploads
```

#### 測試環境
```bash
# 測試環境使用絕對路徑
UPLOAD_BASE_PATH=/var/www/ecocampus-test/uploads
SCHOOL_LOGO_PATH=/var/www/ecocampus-test/uploads/school-logos
GENERAL_UPLOAD_PATH=/var/www/ecocampus-test/api/uploads
```

#### 正式環境
```bash
# 正式環境使用正式路徑
UPLOAD_BASE_PATH=/var/www/ecocampus/uploads
SCHOOL_LOGO_PATH=/var/www/ecocampus/uploads/school-logos
GENERAL_UPLOAD_PATH=/var/www/ecocampus/api/uploads
```

### 📋 設定說明

- **UPLOAD_BASE_PATH**: 上傳檔案的基礎目錄
- **SCHOOL_LOGO_PATH**: 校徽圖檔專用目錄
- **GENERAL_UPLOAD_PATH**: 一般檔案上傳目錄

### ⚠️ 注意事項

- 確保目錄具有寫入權限
- 路徑可以是相對路徑或絕對路徑
- 如果路徑不存在，系統會自動創建
- 在 Docker 環境中請使用絕對路徑

## 📄 授權條款

此專案採用 MIT 授權條款，詳見 [LICENSE](LICENSE) 檔案。
