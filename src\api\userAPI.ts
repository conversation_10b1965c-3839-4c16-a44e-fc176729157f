import { Base<PERSON><PERSON>, ApiResponse } from "./BaseAPI";

// 聯絡人資訊
export interface ContactPerson {
  contact_sid: string;
  contact_cname: string;
  contact_job_title: string;
  contact_tel: string;
  contact_phone: string;
  contact_email: string;
}

// EPA 會員記錄
export interface EPAMemberRecord {
  member_role: "epa";
  member_sid: string;
  sid: string;
  place_sid: string;
  member_cname: string;
  member_cname_en: string;
  member_tel: string;
  member_phone: string;
  member_email: string;
}

// School 會員記錄
export interface SchoolMemberRecord {
  member_role: "school";
  member_sid: string;
  sid: string;
  member_cname: string;
  member_cname_en: string;
  member_address: string;
  member_tel: string;
  member_email: string;
  member_url: string;
  file_token_member_pohto?: string;
  member_Introduction: string;
  member_exchange: string;

  // 校長資訊
  school_principal_sid: string;
  principal_cname: string;
  principal_tel: string;
  principal_phone: string;
  principal_email: string;

  // 聯絡人資訊
  contact_num: string;
  contact: ContactPerson[];

  // 學校統計資料
  school_statistics_sid: string;
  write_date: string;
  staff_total: string;
  elementary1: string;
  elementary2: string;
  elementary3: string;
  elementary4: string;
  elementary5: string;
  elementary6: string;
  middle7: string;
  middle8: string;
  middle9: string;
  hight10: string;
  hight11: string;
  hight12: string;
}

// Tutor 會員記錄
export interface TutorMemberRecord {
  member_role: "tutor";
  member_sid: string;
  sid: string;
  place_cname: string;
  job_cname: string;
  member_cname: string;
  member_cname_en: string;
  member_tel: string;
  member_phone: string;
  member_email: string;
}

// 統一的會員資料接口
export interface MemberProfile {
  // 基本會員資料
  sid: number;
  account: string;
  member_role: "school" | "epa" | "tutor";
  member_cname: string;
  member_cname_en?: string;
  member_email: string;
  member_tel?: string;
  member_phone?: string;
  member_address?: string;
  member_url?: string;

  // 地區資訊
  city_sid?: number;
  area_sid?: number;

  // 職業資訊（EPA & Tutor）
  place_cname?: string;
  job_cname?: string;

  // 學校特有（School）
  code?: string;
  member_Introduction?: string;
  member_exchange?: string;

  // 系統資訊
  register_review: "尚未審核" | "未通過" | "已通過";
  member_passdate?: string;
  isuse: number;
  createdate: number;
  updatedate: number;

  // 詳細記錄（JSON格式）
  member_record?: EPAMemberRecord | SchoolMemberRecord | TutorMemberRecord;
  certification_levels?: MemberLevel[];
  environment_paths?: MemberPath[];
}

// 會員認證等級
export interface MemberLevel {
  sid: number;
  member_sid: number;
  certification_sid: number;
  level: number;
  effectiveness: number;
  cert_level?: number;
  review?: string;
  passdate?: string;
  createdate: number;
  updatedate: number;
}

// 會員環境路徑
export interface MemberPath {
  sid: number;
  member_sid: number;
  level_sid: number;
  water?: number;
  food?: number;
  biological?: number;
  traffic?: number;
  weather?: number;
  consume?: number;
  energy?: number;
  life?: number;
  school?: number;
  habitat?: number;
  forest?: number;
  protection?: number;
  createdate: number;
  updatedate: number;
}

// 環境路徑統計
export interface PathStatistics {
  water: number;
  food: number;
  biological: number;
  traffic: number;
  weather: number;
  consume: number;
  energy: number;
  life: number;
  school: number;
  habitat: number;
  forest: number;
  protection: number;
}

// 身份統計資訊
export interface RoleStatistics {
  role_statistics: Array<{
    member_role: string;
    count: number;
  }>;
  review_statistics: Array<{
    register_review: string;
    count: number;
  }>;
}

// 縣市資料
export interface CityData {
  id: number;
  name: string;
}

// 區域資料
export interface AreaData {
  id: number;
  name: string;
  cityId: number;
}

// 用戶 API 類別
export class UserAPI extends BaseAPI {
  constructor() {
    super();
  }

  // 獲取當前使用者完整資料
  async getCurrentUserProfile(): Promise<ApiResponse<MemberProfile>> {
    return this.get<MemberProfile>("/simple-profile");
  }

  // 獲取縣市列表
  async getCities(): Promise<ApiResponse<CityData[]>> {
    return this.get<CityData[]>("/location/cities");
  }

  // 獲取指定縣市的區域列表
  async getAreas(cityId: number): Promise<ApiResponse<AreaData[]>> {
    return this.get<AreaData[]>(`/location/areas/${cityId}`);
  }

  // 依 ID 獲取使用者完整資料
  async getUserProfile(userId: string): Promise<ApiResponse<MemberProfile>> {
    return this.get<MemberProfile>(`/profile/${userId}`);
  }

  // 更新使用者基本資料
  async updateUserProfile(
    updateData: Partial<MemberProfile> & {
      member_record_data?: EPAMemberRecord | SchoolMemberRecord | TutorMemberRecord;
    }
  ): Promise<ApiResponse<MemberProfile>> {
    return this.put<MemberProfile>("/simple-profile", updateData);
  }

  // 獲取會員認證等級
  async getCertificationLevels(): Promise<ApiResponse<MemberLevel[]>> {
    return this.get<MemberLevel[]>("/user/certification-levels");
  }

  // 獲取會員環境路徑
  async getEnvironmentPaths(): Promise<
    ApiResponse<{
      paths: MemberPath[];
      statistics: PathStatistics;
    }>
  > {
    return this.get<{
      paths: MemberPath[];
      statistics: PathStatistics;
    }>("/user/environment-paths");
  }

  // 更新環境路徑
  async updateEnvironmentPaths(level_sid: number, paths: Record<string, boolean>): Promise<ApiResponse<{ path_id: number }>> {
    return this.put<{ path_id: number }>("/user/environment-paths", { level_sid, paths });
  }

  // 獲取身份統計資訊（管理員功能）
  async getRoleStatistics(): Promise<ApiResponse<RoleStatistics>> {
    return this.get<RoleStatistics>("/user/role-statistics");
  }
}

// 創建全域用戶 API 實例
export const userAPI = new UserAPI();
