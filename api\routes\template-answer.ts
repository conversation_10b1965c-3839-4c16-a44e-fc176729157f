import express from "express";
import { TemplateAnswerService } from "../services/template-answer-service.js";
import {
  TemplateAnswerSaveParams,
  TemplateAnswerValidateParams,
  TemplateAnswerSaveQueryParams,
  TemplateAnswerValidateQueryParams,
  TemplateAnswerSaveResponse,
  TemplateAnswerValidateResponse,
  TemplateAnswerSaveRequest,
  TemplateAnswerValidateRequest,
} from "../models/template-answer.js";
import { TEMPLATE_ANSWER_ERROR_MESSAGES, TEMPLATE_ANSWER_SUCCESS_MESSAGES, REQUIRED_PARAMETERS } from "../constants/template-answer.js";

const router = express.Router();

// 模板答案儲存 API（暫不實際儲存到資料庫）
router.post(
  "/save",
  async (
    req: express.Request<TemplateAnswerSaveParams, TemplateAnswerSaveResponse, TemplateAnswerSaveRequest, TemplateAnswerSaveQueryParams>,
    res: express.Response<TemplateAnswerSaveResponse>
  ) => {
    try {
      const result = await TemplateAnswerService.saveTemplateAnswer(req.body);

      res.json({
        success: true,
        message: TEMPLATE_ANSWER_SUCCESS_MESSAGES.VALIDATION_SUCCESS,
        data: result,
      });
    } catch (error: unknown) {
      console.error("模板答案儲存失敗:", error);

      if (error instanceof Error) {
        // 處理缺少必要參數的錯誤
        if (error.message.includes(TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS)) {
          return res.status(400).json({
            success: false,
            message: TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS,
            required: REQUIRED_PARAMETERS.SAVE,
          });
        }

        // 處理驗證失敗的錯誤
        if (error.message === TEMPLATE_ANSWER_ERROR_MESSAGES.VALIDATION_FAILED) {
          return res.status(400).json({
            success: false,
            message: TEMPLATE_ANSWER_ERROR_MESSAGES.VALIDATION_FAILED,
            errors: (error as any).validationErrors || [],
          });
        }

        // 處理未知模板ID的錯誤
        if (error.message.includes(TEMPLATE_ANSWER_ERROR_MESSAGES.UNKNOWN_TEMPLATE)) {
          return res.status(400).json({
            success: false,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: TEMPLATE_ANSWER_ERROR_MESSAGES.SAVE_FAILED,
        error: error instanceof Error ? error.message : TEMPLATE_ANSWER_ERROR_MESSAGES.UNKNOWN_ERROR,
      });
    }
  }
);

// 驗證答案格式
router.post(
  "/validate",
  async (
    req: express.Request<TemplateAnswerValidateParams, TemplateAnswerValidateResponse, TemplateAnswerValidateRequest, TemplateAnswerValidateQueryParams>,
    res: express.Response<TemplateAnswerValidateResponse>
  ) => {
    try {
      const result = await TemplateAnswerService.validateTemplateAnswerFormat(req.body);

      res.json({
        success: true,
        data: result,
      });
    } catch (error: unknown) {
      console.error("答案驗證失敗:", error);

      if (error instanceof Error) {
        // 處理缺少必要參數的錯誤
        if (error.message.includes(TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS)) {
          return res.status(400).json({
            success: false,
            message: TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS,
          });
        }

        // 處理未知模板ID的錯誤
        if (error.message.includes(TEMPLATE_ANSWER_ERROR_MESSAGES.UNKNOWN_TEMPLATE)) {
          return res.status(400).json({
            success: false,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: TEMPLATE_ANSWER_ERROR_MESSAGES.VALIDATE_FAILED,
      });
    }
  }
);

export default router;
