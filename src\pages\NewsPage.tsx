import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import campusSubmissionService from "@/services/campusSubmissionService";
import { CampusSubmission } from "@/api/campusSubmissionAPI";

const NewsPage = () => {
  const navigate = useNavigate();

  // 狀態管理
  const [submissions, setSubmissions] = useState<CampusSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [submissionToDelete, setSubmissionToDelete] = useState<CampusSubmission | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 頁面變數
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  // 載入投稿列表
  const loadSubmissions = async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);

      const response = await campusSubmissionService.getSubmissions({
        page,
        limit: 20,
      });

      if (response.success) {
        setSubmissions(response.data);
        setCurrentPage(response.pagination.page);
        setTotalPages(response.pagination.totalPages);
        setTotal(response.pagination.total);
      } else {
        setError(response.message || "載入投稿列表失敗");
      }
    } catch (err) {
      console.error("載入投稿列表失敗:", err);
      setError(err instanceof Error ? err.message : "載入投稿列表時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 初始載入
  useEffect(() => {
    loadSubmissions();
  }, []);

  // 處理查看投稿
  const handleViewSubmission = (submission: CampusSubmission) => {
    // 導向詳情頁面
    navigate(`/news/detail/${submission.submissionId}`);
  };

  // 開啟刪除確認對話框
  const openDeleteDialog = (submission: CampusSubmission) => {
    setSubmissionToDelete(submission);
    setDeleteDialogOpen(true);
  };

  // 關閉刪除對話框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSubmissionToDelete(null);
    setDeleteLoading(false);
  };

  // 確認刪除投稿
  const handleDeleteSubmission = async () => {
    if (!submissionToDelete) return;

    try {
      setDeleteLoading(true);

      const response = await campusSubmissionService.deleteSubmission(submissionToDelete.submissionId);

      if (response.success) {
        // 從列表中移除已刪除的投稿
        setSubmissions((prev) => prev.filter((s) => s.submissionId !== submissionToDelete.submissionId));
        setTotal((prev) => prev - 1);

        // 關閉對話框
        closeDeleteDialog();

        // 顯示成功訊息
        alert(`投稿「${submissionToDelete.title}」已成功刪除`);
      } else {
        alert(response.message || "刪除投稿失敗");
      }
    } catch (err) {
      console.error("刪除投稿失敗:", err);
      alert(err instanceof Error ? err.message : "刪除投稿時發生錯誤");
    } finally {
      setDeleteLoading(false);
    }
  };

  // 處理頁面變更
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      loadSubmissions(page);
    }
  };

  // 導向投稿說明
  const handleGuidelinesClick = () => {
    navigate("/news/guidelines");
  };

  // 新增投稿
  const handleNewSubmission = () => {
    navigate("/news/create");
  };

  // 載入狀態
  if (loading) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
        <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" style={{ minHeight: "500px" }}>
          <h1 className="font-size-3xl font-bold text-primary mb-6 text-center">校園新聞投稿</h1>
          <div className="mt-8 flex flex-col gap-6">
            <div className="flex justify-end gap-3 mb-4">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
            <div className="bg-gray-50 p-6 rounded shadow">
              <Skeleton className="h-6 w-32 mb-4" />
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center justify-between p-4 bg-white rounded">
                    <div className="flex-1">
                      <Skeleton className="h-5 w-48 mb-2" />
                      <Skeleton className="h-4 w-96" />
                    </div>
                    <div className="flex gap-4">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-12" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>
    );
  }

  // 錯誤狀態
  if (error) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
        <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" style={{ minHeight: "500px" }}>
          <h1 className="font-size-3xl font-bold text-primary mb-6 text-center">校園新聞投稿</h1>
          <Alert variant="destructive" className="mt-8">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="mt-4 text-center">
            <Button onClick={() => loadSubmissions()} variant="outline">
              重新載入
            </Button>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
      <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" aria-label="校園新聞投稿" style={{ minHeight: "500px" }}>
        <h1 className="font-size-3xl font-bold text-primary mb-6 text-center" tabIndex={0}>
          校園新聞投稿
        </h1>

        <div className="mt-8 flex flex-col gap-6">
          {/* 操作按鈕 */}
          <div className="flex justify-between items-center mb-4">
            <div className="text-gray-600">共 {total} 筆投稿記錄</div>
            <div className="flex gap-3">
              <Button variant="secondary" type="button" onClick={handleGuidelinesClick}>
                投稿說明
              </Button>
              <Button variant="default" type="button" onClick={handleNewSubmission}>
                新增投稿
              </Button>
            </div>
          </div>

          {/* 投稿列表 */}
          <div className="bg-gray-50 p-6 rounded shadow">
            <div className="font-bold font-size-xl mb-4">投稿紀錄</div>

            {submissions.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                <div className="mb-4">📝</div>
                <div>暫無投稿記錄</div>
                <div className="text-sm mt-2">點擊「新增投稿」開始您的第一篇投稿</div>
              </div>
            ) : (
              <div className="divide-y">
                {submissions.map((submission) => (
                  <div key={submission.submissionId} className="py-4 flex items-center justify-between text-gray-800">
                    <div className="flex-1">
                      <div className="font-semibold text-lg mb-1">{submission.title}</div>
                      <div className="font-size-xs text-gray-500">
                        投稿日期 {campusSubmissionService.formatDate(submission.submissionDate)} │ 建立日期{" "}
                        {campusSubmissionService.formatDate(submission.createdTime)}
                        {submission.updatedTime && <> │ 更新日期 {campusSubmissionService.formatDate(submission.updatedTime)}</>}
                      </div>
                    </div>
                    <div className="flex items-center gap-6 font-size-sm ml-6">
                      <span className={`font-semibold ${campusSubmissionService.getStatusColorClass(submission.status)}`}>{submission.statusText}</span>

                      {campusSubmissionService.canOperate(submission.status) && (
                        <>
                          <span className="border-l border-gray-300 mx-2 h-4"></span>
                          <Button
                            variant="link"
                            className="text-green-900 underline underline-offset-2 px-1 hover:text-green-700"
                            type="button"
                            onClick={() => handleViewSubmission(submission)}>
                            查看內容
                          </Button>
                          {campusSubmissionService.canDelete(submission.status) && (
                            <>
                              <span className="border-l border-gray-300 mx-2 h-4"></span>
                              <Button
                                variant="link"
                                className="text-red-600 underline underline-offset-2 px-1 hover:text-red-700"
                                type="button"
                                onClick={() => openDeleteDialog(submission)}>
                                刪除
                              </Button>
                            </>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 分頁控制 */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center gap-4 mt-6 pt-4 border-t">
                <Button variant="outline" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage <= 1} className="px-3 py-2">
                  上一頁
                </Button>

                <div className="flex items-center gap-2">
                  {/* 頁面數字 */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNumber;
                    if (totalPages <= 5) {
                      pageNumber = i + 1;
                    } else if (currentPage <= 3) {
                      pageNumber = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i;
                    } else {
                      pageNumber = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNumber}
                        variant={currentPage === pageNumber ? "default" : "outline"}
                        onClick={() => handlePageChange(pageNumber)}
                        className="w-10 h-10 p-0">
                        {pageNumber}
                      </Button>
                    );
                  })}
                </div>

                <Button variant="outline" onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage >= totalPages} className="px-3 py-2">
                  下一頁
                </Button>

                <span className="text-sm text-gray-500 ml-4">
                  第 {currentPage} 頁，共 {totalPages} 頁
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 刪除確認對話框 */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>確認刪除投稿</DialogTitle>
              <DialogDescription>
                您確定要刪除投稿「{submissionToDelete?.title}」嗎？
                <br />
                <span className="text-red-600 font-medium">此操作無法復原。</span>
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={closeDeleteDialog} disabled={deleteLoading}>
                取消
              </Button>
              <Button variant="primary" onClick={handleDeleteSubmission} disabled={deleteLoading} className="bg-red-600 hover:bg-red-700 text-white">
                {deleteLoading ? "刪除中..." : "確定刪除"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </section>
    </main>
  );
};

export default NewsPage;
