import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import { DashboardService } from "../services/dashboard-service.js";
import {
  CityStatisticsParams,
  LatestCertificationsParams,
  LatestCertificationsQueryParams,
  SchoolCertificationQueryParams,
  TestResponse,
  CityStatisticsResponse,
  LatestCertificationsResponse,
  SchoolCurrentCertificationResponse,
  SchoolArticlesResponse,
  SchoolPassedCertificationsResponse,
} from "../models/dashboard.js";
import { DEFAULT_LIMITS, DASHBOARD_ERROR_MESSAGES, DASHBOARD_SUCCESS_MESSAGES } from "../constants/dashboard.js";
import { APILogger } from "../utils/logger.js";
import { Certification, CertificationListResponse } from "../models/certification.js";

const router = express.Router();

// 測試端點（無需認證）
router.get("/test", async (req: express.Request<{}, TestResponse>, res: express.Response<TestResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "測試端點");

    const result = await DashboardService.getTestData();

    APILogger.logSuccess("Dashboard", "測試端點", result, DASHBOARD_SUCCESS_MESSAGES.TEST_SUCCESS);

    res.json({
      success: true,
      data: result,
      message: DASHBOARD_SUCCESS_MESSAGES.TEST_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "測試端點", error, 500);
    res.status(500).json({
      success: false,
      data: {
        cityId: 0,
        cityName: "",
        bronzeCount: 0,
        silverCount: 0,
        greenFlagCount: 0,
        totalSchools: 0,
      },
      message: "測試失敗",
    });
  }
});

// 獲取指定縣市的生態學校統計
router.get("/city-statistics/:cityId", authenticateToken, async (req: express.Request, res: express.Response<CityStatisticsResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "獲取縣市統計");
    const { cityId } = req.params;
    const token = req.headers["x-user-token"] as string;

    const result = await DashboardService.getCityStatistics(parseInt(cityId), token);

    APILogger.logSuccess("Dashboard", "獲取縣市統計", result, `縣市 ${cityId} 統計資料獲取成功`);

    res.json({
      success: true,
      data: result,
      message: DASHBOARD_SUCCESS_MESSAGES.CITY_STATISTICS_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "獲取縣市統計", error, 500);

    if (error instanceof Error) {
      if (error.message === DASHBOARD_ERROR_MESSAGES.INVALID_TOKEN) {
        return res.status(401).json({
          success: false,
          data: {
            cityId: 0,
            cityName: "",
            bronzeCount: 0,
            silverCount: 0,
            greenFlagCount: 0,
            totalSchools: 0,
          },
          message: error.message,
        });
      }

      if (error.message === DASHBOARD_ERROR_MESSAGES.EPA_TUTOR_ONLY) {
        return res.status(403).json({
          success: false,
          data: {
            cityId: 0,
            cityName: "",
            bronzeCount: 0,
            silverCount: 0,
            greenFlagCount: 0,
            totalSchools: 0,
          },
          message: error.message,
        });
      }

      if (error.message === DASHBOARD_ERROR_MESSAGES.CITY_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          data: {
            cityId: 0,
            cityName: "",
            bronzeCount: 0,
            silverCount: 0,
            greenFlagCount: 0,
            totalSchools: 0,
          },
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      data: {
        cityId: 0,
        cityName: "",
        bronzeCount: 0,
        silverCount: 0,
        greenFlagCount: 0,
        totalSchools: 0,
      },
      message: "獲取縣市統計時發生錯誤",
    });
  }
});

// 獲取當前用戶所屬縣市的統計資料
router.get("/my-city-statistics", authenticateToken, async (req: express.Request, res: express.Response<CityStatisticsResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "獲取我的縣市統計");
    const token = req.headers["x-user-token"] as string;

    const result = await DashboardService.getMyCityStatistics(token);

    APILogger.logSuccess("Dashboard", "獲取我的縣市統計", result, "用戶縣市統計資料獲取成功");

    res.json({
      success: true,
      data: result,
      message: DASHBOARD_SUCCESS_MESSAGES.CITY_STATISTICS_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "獲取我的縣市統計", error, 500);

    if (error instanceof Error && error.message === DASHBOARD_ERROR_MESSAGES.USER_CITY_NOT_FOUND) {
      return res.status(404).json({
        success: false,
        data: {
          cityId: 0,
          cityName: "",
          bronzeCount: 0,
          silverCount: 0,
          greenFlagCount: 0,
          totalSchools: 0,
        },
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      data: {
        cityId: 0,
        cityName: "",
        bronzeCount: 0,
        silverCount: 0,
        greenFlagCount: 0,
        totalSchools: 0,
      },
      message: "獲取用戶縣市統計時發生錯誤",
    });
  }
});

router.get("/school/passed-certifications", authenticateToken, async (req: express.Request, res: express.Response<SchoolPassedCertificationsResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "獲取學校已通過認證");
    const accountId = parseInt(req.user?.accountId as string);
    const limit = parseInt(req.query.limit as string) || DEFAULT_LIMITS.PASSED_CERTIFICATIONS;

    if (!accountId) {
      return res.status(401).json({
        success: false,
        data: [],
        message: DASHBOARD_ERROR_MESSAGES.INVALID_TOKEN,
      });
    }

    const certifications = await DashboardService.getSchoolPassedCertifications(accountId, limit);

    APILogger.logSuccess(
      "Dashboard",
      "獲取學校已通過認證",
      { count: certifications.length, limit },
      DASHBOARD_SUCCESS_MESSAGES.SCHOOL_PASSED_CERTIFICATIONS_SUCCESS
    );

    res.json({
      success: true,
      data: certifications,
      message: DASHBOARD_SUCCESS_MESSAGES.SCHOOL_PASSED_CERTIFICATIONS_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "獲取學校已通過認證", error, 500);

    if (error instanceof Error) {
      if (error.message === DASHBOARD_ERROR_MESSAGES.SCHOOL_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          data: [],
          message: error.message,
        });
      }

      if (error.message === DASHBOARD_ERROR_MESSAGES.SCHOOL_ONLY) {
        return res.status(403).json({
          success: false,
          data: [],
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      data: [],
      message: "獲取學校已通過認證時發生錯誤",
    });
  }
});

// 獲取學校最新投稿文章
router.get("/school/latest-articles", authenticateToken, async (req: express.Request, res: express.Response<SchoolArticlesResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "獲取學校最新投稿文章");
    const accountId = parseInt(req.user?.accountId as string);
    const limit = parseInt(req.query.limit as string) || DEFAULT_LIMITS.SCHOOL_ARTICLES;

    if (!accountId) {
      return res.status(401).json({
        success: false,
        data: [],
        message: DASHBOARD_ERROR_MESSAGES.INVALID_TOKEN,
      });
    }

    const articles = await DashboardService.getSchoolLatestArticles(accountId, limit);

    APILogger.logSuccess("Dashboard", "獲取學校最新投稿文章", { count: articles.length, limit }, DASHBOARD_SUCCESS_MESSAGES.SCHOOL_ARTICLES_SUCCESS);

    res.json({
      success: true,
      data: articles,
      message: DASHBOARD_SUCCESS_MESSAGES.SCHOOL_ARTICLES_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "獲取學校最新投稿文章", error, 500);

    if (error instanceof Error) {
      if (error.message === DASHBOARD_ERROR_MESSAGES.SCHOOL_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          data: [],
          message: error.message,
        });
      }

      if (error.message === DASHBOARD_ERROR_MESSAGES.SCHOOL_ONLY) {
        return res.status(403).json({
          success: false,
          data: [],
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      data: [],
      message: "獲取學校最新投稿文章時發生錯誤",
    });
  }
});

// 獲取指定縣市的最新認證（最多6則）
router.get("/latest-certifications/:cityId", authenticateToken, async (req: express.Request, res: express.Response<LatestCertificationsResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "獲取最新認證");
    const { cityId } = req.params;
    const limit = parseInt(req.query.limit as string) || DEFAULT_LIMITS.LATEST_CERTIFICATIONS;
    const token = req.headers["x-user-token"] as string;

    const certifications = await DashboardService.getLatestCertifications(parseInt(cityId), token, limit);

    APILogger.logSuccess(
      "Dashboard",
      "獲取最新認證",
      { cityId, count: certifications.length, limit },
      DASHBOARD_SUCCESS_MESSAGES.LATEST_CERTIFICATIONS_SUCCESS
    );

    res.json({
      success: true,
      data: certifications,
      message: DASHBOARD_SUCCESS_MESSAGES.LATEST_CERTIFICATIONS_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "獲取最新認證", error, 500);

    if (error instanceof Error) {
      if (error.message === DASHBOARD_ERROR_MESSAGES.INVALID_TOKEN) {
        return res.status(401).json({
          success: false,
          data: [],
          message: error.message,
        });
      }

      if (error.message === DASHBOARD_ERROR_MESSAGES.EPA_TUTOR_ONLY) {
        return res.status(403).json({
          success: false,
          data: [],
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      data: [],
      message: "獲取最新認證時發生錯誤",
    });
  }
});

// 獲取當前用戶所屬縣市的最新認證
router.get("/my-latest-certifications", authenticateToken, async (req: express.Request, res: express.Response<LatestCertificationsResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "獲取我的最新認證");
    const token = req.headers["x-user-token"] as string;
    const limit = parseInt(req.query.limit as string) || DEFAULT_LIMITS.LATEST_CERTIFICATIONS;

    const certifications = await DashboardService.getMyLatestCertifications(token, limit);

    APILogger.logSuccess("Dashboard", "獲取我的最新認證", { count: certifications.length, limit }, "用戶最新認證資料獲取成功");

    res.json({
      success: true,
      data: certifications,
      message: DASHBOARD_SUCCESS_MESSAGES.LATEST_CERTIFICATIONS_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "獲取我的最新認證", error, 500);

    if (error instanceof Error && error.message === DASHBOARD_ERROR_MESSAGES.USER_CITY_NOT_FOUND) {
      return res.status(404).json({
        success: false,
        data: [],
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      data: [],
      message: "獲取用戶最新認證時發生錯誤",
    });
  }
});

// 獲取學校當前申請中的認證
router.get("/school/current-certification", authenticateToken, async (req: express.Request, res: express.Response<SchoolCurrentCertificationResponse>) => {
  try {
    APILogger.logRequest(req, "Dashboard", "獲取學校申請中認證");
    const token = req.headers["x-user-token"] as string;

    const certification = await DashboardService.getSchoolCurrentCertification(token);

    if (!certification) {
      APILogger.logSuccess("Dashboard", "獲取學校申請中認證", { hasCertification: false }, "學校目前沒有申請中的認證");

      return res.json({
        success: true,
        data: null,
        message: DASHBOARD_ERROR_MESSAGES.NO_CURRENT_CERTIFICATION,
      });
    }

    APILogger.logSuccess("Dashboard", "獲取學校申請中認證", certification, DASHBOARD_SUCCESS_MESSAGES.SCHOOL_CERTIFICATION_SUCCESS);

    res.json({
      success: true,
      data: certification,
      message: DASHBOARD_SUCCESS_MESSAGES.SCHOOL_CERTIFICATION_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Dashboard", "獲取學校申請中認證", error, 500);

    if (error instanceof Error && error.message === DASHBOARD_ERROR_MESSAGES.SCHOOL_NOT_FOUND) {
      return res.status(404).json({
        success: false,
        data: null,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      data: null,
      message: "獲取學校申請中認證時發生錯誤",
    });
  }
});

// TODO: 其他學校相關端點
// - GET /school/articles - 獲取學校文章

export default router;
