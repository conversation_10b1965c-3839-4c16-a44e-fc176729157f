/**
 * 統一驗證正則表達式模式
 * 整合原本分散在前後端的各種驗證規則
 * 
 * @description 提供一致性的資料驗證邏輯，避免前後端驗證不一致的問題
 * @version 1.0.0
 */

/**
 * 驗證模式常數集合
 * 所有系統使用的正則表達式模式都在此統一定義
 */
export const ValidationPatterns = {
  /**
   * Email 驗證模式
   * 支援標準的 email 格式驗證
   */
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  /**
   * 台灣電話號碼驗證（嚴格版本）
   * 支援格式：
   * - 手機：09xxxxxxxx, +886-9xxxxxxxx
   * - 市話：02-xxxxxxxx, 04-xxxxxxxx 等
   */
  PHONE_TW: /^(\+886|0)?([2-9]\d{7,8}|[2-9]\d{2}-\d{3}-\d{3})$/,
  
  /**
   * 簡單電話驗證（寬鬆版本）
   * 向後相容用，僅檢查 9-10 位數字
   */
  PHONE_SIMPLE: /^\d{9,10}$/,
  
  /**
   * 強密碼驗證
   * 要求：至少 8 位，包含大小寫英文字母和數字
   */
  PASSWORD_STRONG: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  
  /**
   * 中文姓名驗證
   * 允許 2-10 個中文字元
   */
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,10}$/,
  
  /**
   * 英文姓名驗證
   * 允許英文字母、空格、連字號和撇號
   */
  ENGLISH_NAME: /^[a-zA-Z\s\-']{2,50}$/,
  
  /**
   * 學校代碼驗證
   * 6 位數字格式
   */
  SCHOOL_CODE: /^\d{6}$/,
  
  /**
   * Token 格式驗證
   * UUID 格式：xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
   */
  UUID_TOKEN: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
  
  /**
   * URL 驗證
   * 基本的 HTTP/HTTPS URL 格式驗證
   */
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
} as const;

/**
 * 驗證結果介面
 */
export interface ValidationResult {
  /** 是否通過驗證 */
  isValid: boolean;
  /** 錯誤訊息（若驗證失敗）*/
  message?: string;
  /** 清理後的值（若需要）*/
  cleanedValue?: string;
}

/**
 * 驗證輔助類別
 * 提供常用的驗證方法和工具函數
 */
export class ValidationHelper {
  /**
   * Email 驗證
   * @param email 要驗證的 email 地址
   * @returns 驗證結果
   */
  static isValidEmail(email: string): ValidationResult {
    if (!email || typeof email !== 'string') {
      return {
        isValid: false,
        message: 'Email 不能為空'
      };
    }
    
    const trimmedEmail = email.trim().toLowerCase();
    const isValid = ValidationPatterns.EMAIL.test(trimmedEmail);
    
    return {
      isValid,
      message: isValid ? undefined : '請輸入有效的 Email 格式',
      cleanedValue: isValid ? trimmedEmail : undefined
    };
  }
  
  /**
   * 電話號碼驗證
   * @param phone 要驗證的電話號碼
   * @param strict 是否使用嚴格驗證（預設 true）
   * @returns 驗證結果
   */
  static isValidPhone(phone: string, strict: boolean = true): ValidationResult {
    if (!phone || typeof phone !== 'string') {
      return {
        isValid: false,
        message: '電話號碼不能為空'
      };
    }
    
    // 清理電話號碼（移除空格、破折號等）
    const cleaned = phone.replace(/[-\s()]/g, '');
    
    let isValid: boolean;
    let message: string;
    
    if (strict) {
      isValid = ValidationPatterns.PHONE_TW.test(cleaned);
      message = isValid ? undefined : '請輸入有效的台灣電話號碼格式';
    } else {
      // 寬鬆驗證：僅檢查是否為 9-10 位數字
      const numbersOnly = cleaned.replace(/\D/g, '');
      isValid = ValidationPatterns.PHONE_SIMPLE.test(numbersOnly);
      message = isValid ? undefined : '電話號碼必須為 9-10 位數字';
    }
    
    return {
      isValid,
      message,
      cleanedValue: isValid ? cleaned : undefined
    };
  }
  
  /**
   * 密碼強度驗證
   * @param password 要驗證的密碼
   * @returns 驗證結果
   */
  static isValidPassword(password: string): ValidationResult {
    if (!password || typeof password !== 'string') {
      return {
        isValid: false,
        message: '密碼不能為空'
      };
    }
    
    if (password.length < 8) {
      return {
        isValid: false,
        message: '密碼長度至少需要 8 個字元'
      };
    }
    
    if (!/(?=.*[a-z])/.test(password)) {
      return {
        isValid: false,
        message: '密碼必須包含至少一個小寫英文字母'
      };
    }
    
    if (!/(?=.*[A-Z])/.test(password)) {
      return {
        isValid: false,
        message: '密碼必須包含至少一個大寫英文字母'
      };
    }
    
    if (!/(?=.*\d)/.test(password)) {
      return {
        isValid: false,
        message: '密碼必須包含至少一個數字'
      };
    }
    
    const isValid = ValidationPatterns.PASSWORD_STRONG.test(password);
    return {
      isValid,
      message: isValid ? undefined : '密碼格式不符合要求'
    };
  }
  
  /**
   * 中文姓名驗證
   * @param name 要驗證的姓名
   * @returns 驗證結果
   */
  static isValidChineseName(name: string): ValidationResult {
    if (!name || typeof name !== 'string') {
      return {
        isValid: false,
        message: '姓名不能為空'
      };
    }
    
    const trimmedName = name.trim();
    const isValid = ValidationPatterns.CHINESE_NAME.test(trimmedName);
    
    return {
      isValid,
      message: isValid ? undefined : '請輸入 2-10 個中文字元的姓名',
      cleanedValue: isValid ? trimmedName : undefined
    };
  }
  
  /**
   * Token 格式驗證
   * @param token 要驗證的 token
   * @returns 驗證結果
   */
  static isValidToken(token: string): ValidationResult {
    if (!token || typeof token !== 'string') {
      return {
        isValid: false,
        message: 'Token 不能為空'
      };
    }
    
    const trimmedToken = token.trim();
    const isValid = ValidationPatterns.UUID_TOKEN.test(trimmedToken);
    
    return {
      isValid,
      message: isValid ? undefined : 'Token 格式不正確',
      cleanedValue: isValid ? trimmedToken : undefined
    };
  }
  
  /**
   * URL 格式驗證
   * @param url 要驗證的 URL
   * @returns 驗證結果
   */
  static isValidUrl(url: string): ValidationResult {
    if (!url || typeof url !== 'string') {
      return {
        isValid: false,
        message: 'URL 不能為空'
      };
    }
    
    const trimmedUrl = url.trim();
    const isValid = ValidationPatterns.URL.test(trimmedUrl);
    
    return {
      isValid,
      message: isValid ? undefined : '請輸入有效的 URL 格式',
      cleanedValue: isValid ? trimmedUrl : undefined
    };
  }
  
  /**
   * 批量驗證
   * @param validations 驗證規則陣列
   * @returns 整體驗證結果
   */
  static validateAll(validations: Array<() => ValidationResult>): ValidationResult {
    const results = validations.map(validate => validate());
    const failed = results.find(result => !result.isValid);
    
    if (failed) {
      return {
        isValid: false,
        message: failed.message
      };
    }
    
    return {
      isValid: true
    };
  }
}

/**
 * 常用驗證規則組合
 */
export const CommonValidationRules = {
  /**
   * 用戶註冊驗證規則
   */
  userRegistration: {
    email: (email: string) => ValidationHelper.isValidEmail(email),
    password: (password: string) => ValidationHelper.isValidPassword(password),
    phone: (phone: string) => ValidationHelper.isValidPhone(phone, true),
    name: (name: string) => ValidationHelper.isValidChineseName(name)
  },
  
  /**
   * 登入驗證規則
   */
  userLogin: {
    email: (email: string) => ValidationHelper.isValidEmail(email),
    token: (token: string) => ValidationHelper.isValidToken(token)
  }
};