import { parse, isValid, differenceInDays } from "date-fns";

// 日期間隔驗證函數
export const validateDateIntervals = (dates: string[]): { isValid: boolean; message?: string } => {
  const validDates = dates
    .filter((dateStr) => dateStr.trim())
    .map((dateStr) => {
      const formats = ["yyyy-MM-dd", "yyyy/MM/dd", "yyyy.MM.dd"];
      for (const fmt of formats) {
        try {
          const parsed = parse(dateStr, fmt, new Date());
          if (isValid(parsed)) return parsed;
        } catch (e) {
          continue;
        }
      }
      const date = new Date(dateStr);
      return isValid(date) ? date : null;
    })
    .filter((date) => date !== null) as Date[];

  if (validDates.length < 2) {
    return { isValid: true }; // 少於2個日期時不需驗證間隔
  }

  // 排序日期
  validDates.sort((a, b) => a.getTime() - b.getTime());

  // 檢查相鄰日期間隔
  for (let i = 1; i < validDates.length; i++) {
    const daysDifference = differenceInDays(validDates[i], validDates[i - 1]);
    if (daysDifference < 30) {
      // 少於30天（約一個月）
      return {
        isValid: false,
        message: `會議日期間隔至少需要一個月，第${i}次與第${i + 1}次會議間隔僅${daysDifference}天`,
      };
    }
  }

  return { isValid: true };
};
