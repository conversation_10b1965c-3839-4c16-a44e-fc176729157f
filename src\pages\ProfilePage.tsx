import React, { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ProfileEditForm } from "@/components/ProfileEditForm";
import { useAuth, useCurrentUser } from "@/hooks/useAuth";
import userService from "@/services/userService";
import { useToast } from "@/hooks/use-toast";
import { MemberProfile } from "@/api/userAPI";

// 基本資料維護
const ProfilePage = () => {
  const [profile, setProfile] = useState<MemberProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const hasLoadedRef = useRef(false); // 追蹤是否已經載入過資料

  const { authState } = useAuth();
  const currentUser = useCurrentUser();
  const { toast } = useToast();

  // 處理 API 錯誤的輔助函數
  const handleApiError = (error: unknown): string => {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === "string") {
      return error;
    }
    return "發生未知錯誤";
  };

  // 載入使用者資料
  useEffect(() => {
    const loadProfile = async () => {
      // 如果已經載入過資料且認證狀態沒有變化，則不重複載入
      if (hasLoadedRef.current && authState.isAuthenticated && currentUser) {
        return;
      }

      if (!authState.isAuthenticated || !currentUser) {
        setError("使用者未認證");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // 🆕 使用真實的 API 調用
        const response = await userService.getCurrentUserProfile();

        if (response.success && response.data) {
          setProfile(response.data);
          setError(null);
          hasLoadedRef.current = true; // 標記已載入
        } else {
          throw new Error(response.message || "載入資料失敗");
        }
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);

        console.error("載入使用者資料失敗:", err);

        toast({
          variant: "destructive",
          title: "載入失敗",
          description: errorMessage,
        });
      } finally {
        setLoading(false);
      }
    };

    loadProfile();
  }, [authState.isAuthenticated, currentUser, toast]); // 恢復依賴項，但添加了重複載入檢查

  // 處理個人資料更新
  const handleProfileUpdate = (updatedProfile: MemberProfile) => {
    setProfile(updatedProfile);
    hasLoadedRef.current = true; // 確保更新後標記為已載入
    toast({
      title: "更新成功",
      description: "個人資料已成功更新",
    });
  };

  // 手動重新載入資料（可選功能）
  const handleRefreshProfile = async () => {
    hasLoadedRef.current = false; // 重置載入標記
    setLoading(true);
    setError(null);

    try {
      const response = await userService.getCurrentUserProfile();
      if (response.success && response.data) {
        setProfile(response.data);
        hasLoadedRef.current = true;
      } else {
        throw new Error(response.message || "重新載入資料失敗");
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "重新載入失敗",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  // 如果使用者未認證，顯示錯誤
  if (!authState.isAuthenticated || !currentUser) {
    return (
      <div className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <p className="text-destructive">請先登入才能訪問個人資料頁面。</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 載入中狀態
  if (loading) {
    return (
      <div className="min-h-[calc(100vh-60px)] bg-muted py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-96" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // 如果沒有資料，使用基本模擬資料
  if (!profile) {
    const fallbackProfile: MemberProfile = {
      sid: parseInt(currentUser.id),
      account: currentUser.account || "mockAccount",
      member_role: currentUser.role as "school" | "epa" | "tutor",
      member_cname: currentUser.name,
      member_cname_en: "",
      member_email: currentUser.email,
      member_tel: "",
      member_phone: "",
      member_address: "",
      member_url: "",
      city_sid: 1,
      area_sid: 1,
      place_cname: currentUser.organization || "",
      job_cname: currentUser.position || "",
      code: "",
      member_Introduction: "",
      member_exchange: "0",
      register_review: "已通過",
      member_passdate: "",
      isuse: 1,
      createdate: Math.floor(Date.now() / 1000),
      updatedate: Math.floor(Date.now() / 1000),
      member_record: undefined,
      certification_levels: [],
      environment_paths: [],
    };
    setProfile(fallbackProfile);
  }

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted py-12">
      <div className="container mx-auto px-4">
        <section className="max-w-6xl mx-auto" aria-label="基本資料維護">
          {/* 基本資料編輯表單 */}
          {profile && <ProfileEditForm profile={profile} onProfileUpdate={handleProfileUpdate} />}
        </section>
      </div>
    </main>
  );
};

export default ProfilePage;
