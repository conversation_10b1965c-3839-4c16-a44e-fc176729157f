import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import { UserService } from "../services/user-service.js";
import {
  UserProfile,
  UserProfileParams,
  UserProfileQueryParams,
  UserCertificationsQueryParams,
  UserSchoolQueryParams,
  UserPermissionsQueryParams,
  UserProfileResponse,
  UserProfileUpdateResponse,
  UserCertificationsResponse,
  UserSchoolResponse,
  UserPermissionsResponse,
  UserProfileUpdateRequest,
  SchoolQueryResult,
} from "../models/user.js";
import { USER_ERROR_MESSAGES, USER_SUCCESS_MESSAGES } from "../constants/user.js";

const router = express.Router();

// 獲取當前使用者基本資料
router.get(
  "/profile",
  authenticateToken,
  async (req: express.Request<{}, UserProfileResponse, {}, UserProfileQueryParams>, res: express.Response<UserProfileResponse>) => {
    try {
      const token = req.headers["x-user-token"] as string;

      if (!req.user || !token) {
        return res.status(401).json({
          success: false,
          data: {} as UserProfile,
          message: USER_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
        });
      }

      const memberProfile = await UserService.getCurrentUserProfile(token);

      res.json({
        success: true,
        data: memberProfile,
        message: USER_SUCCESS_MESSAGES.PROFILE_RETRIEVED,
      });
    } catch (error: unknown) {
      console.error("獲取使用者資料失敗:", error);

      if (error instanceof Error && error.message === USER_ERROR_MESSAGES.USER_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          data: {} as UserProfile,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        data: {} as UserProfile,
        message: "獲取使用者資料失敗",
      });
    }
  }
);

// 獲取使用者基本資料 (依 ID)
router.get("/profile/:userId", authenticateToken, async (req: express.Request<{ userId: string }>, res: express.Response<UserProfileResponse>) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user?.id;
    const currentUserPermissions = req.user?.permissions || [];

    if (!currentUserId) {
      return res.status(401).json({
        success: false,
        data: {} as UserProfile,
        message: USER_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
      });
    }

    const memberProfile = await UserService.getUserProfileById(userId, currentUserId, currentUserPermissions);

    res.json({
      success: true,
      data: memberProfile,
      message: USER_SUCCESS_MESSAGES.PROFILE_RETRIEVED,
    });
  } catch (error: unknown) {
    console.error("獲取使用者資料失敗:", error);

    if (error instanceof Error) {
      if (error.message === USER_ERROR_MESSAGES.NO_PERMISSION) {
        return res.status(403).json({
          success: false,
          data: {} as UserProfile,
          message: error.message,
        });
      }

      if (error.message === USER_ERROR_MESSAGES.USER_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          data: {} as UserProfile,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      data: {} as UserProfile,
      message: "獲取使用者資料失敗",
    });
  }
});

// 更新使用者基本資料
router.put(
  "/profile",
  authenticateToken,
  async (req: express.Request<{}, UserProfileUpdateResponse, UserProfileUpdateRequest>, res: express.Response<UserProfileUpdateResponse>) => {
    try {
      const userId = req.user?.id;
      const updateData: UserProfileUpdateRequest = req.body;
      const token = req.headers["x-user-token"] as string;

      if (!userId || !token) {
        return res.status(401).json({
          success: false,
          data: {} as UserProfile,
          message: USER_ERROR_MESSAGES.USER_NOT_AUTHENTICATED,
        });
      }

      const updatedProfile = await UserService.updateUserProfile(userId, updateData, token);

      res.json({
        success: true,
        data: updatedProfile,
        message: USER_SUCCESS_MESSAGES.PROFILE_UPDATED,
      });
    } catch (error: unknown) {
      console.error("更新使用者資料失敗:", error);

      if (error instanceof Error) {
        if (error.message === USER_ERROR_MESSAGES.USER_NOT_FOUND) {
          return res.status(404).json({
            success: false,
            data: {} as UserProfile,
            message: error.message,
          });
        }

        if ([USER_ERROR_MESSAGES.INVALID_EMAIL as string, USER_ERROR_MESSAGES.INVALID_PHONE as string].includes(error.message)) {
          return res.status(400).json({
            success: false,
            data: {} as UserProfile,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        data: {} as UserProfile,
        message: USER_ERROR_MESSAGES.UPDATE_FAILED,
      });
    }
  }
);

// 獲取會員認證資訊
router.get(
  "/certifications",
  authenticateToken,
  async (req: express.Request<{}, UserCertificationsResponse, {}, UserCertificationsQueryParams>, res: express.Response<UserCertificationsResponse>) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          data: [],
        });
      }

      const certifications = await UserService.getUserCertificationsOnly(userId);

      res.json({
        success: true,
        data: certifications,
      });
    } catch (error: unknown) {
      console.error("獲取認證資料失敗:", error);
      res.status(500).json({
        success: false,
        data: [],
      });
    }
  }
);

// 獲取使用者學校資訊 (如果是學校身份)
router.get(
  "/school",
  authenticateToken,
  async (req: express.Request<{}, UserSchoolResponse, {}, UserSchoolQueryParams>, res: express.Response<UserSchoolResponse>) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          data: {} as SchoolQueryResult,
        });
      }

      const school = await UserService.getSchoolInfo(userId);

      res.json({
        success: true,
        data: school,
      });
    } catch (error: unknown) {
      console.error("獲取學校資料失敗:", error);

      if (error instanceof Error) {
        if (error.message === USER_ERROR_MESSAGES.SCHOOL_ONLY) {
          return res.status(403).json({
            success: false,
            data: {} as SchoolQueryResult,
          });
        }

        if (error.message === USER_ERROR_MESSAGES.SCHOOL_NOT_FOUND) {
          return res.status(404).json({
            success: false,
            data: {} as SchoolQueryResult,
          });
        }
      }

      res.status(500).json({
        success: false,
        data: {} as SchoolQueryResult,
      });
    }
  }
);

// 獲取使用者權限資訊
router.get(
  "/permissions",
  authenticateToken,
  async (req: express.Request<{}, UserPermissionsResponse, {}, UserPermissionsQueryParams>, res: express.Response<UserPermissionsResponse>) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          data: { permissions: [], permissionGroups: [] },
        });
      }

      const permissionsData = await UserService.getUserPermissionsInfo(userId);

      res.json({
        success: true,
        data: permissionsData,
      });
    } catch (error: unknown) {
      console.error("獲取權限資料失敗:", error);
      res.status(500).json({
        success: false,
        data: { permissions: [], permissionGroups: [] },
      });
    }
  }
);

export default router;
