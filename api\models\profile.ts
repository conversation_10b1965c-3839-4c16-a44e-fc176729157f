// ========== 個人資料相關資料模型 ==========

export interface MemberProfile {
  accountId: number;
  account: string;
  email: string;
  telephone?: string;
  phone?: string;
  address?: string;
  countyId?: number;
  isSchoolPartner: number;
  isEpaUser: number;
  isGuidanceTeam: number;
  memberName?: string;
  memberEmail?: string;
  memberTelephone?: string;
  memberPhone?: string;
  memberAddress?: string;
  memberUrl?: string;
  jobTitle?: string;
  placeName?: string;
  memberRole?: string;
  memberIntroduction?: string;
  memberExchange?: string;
  memberNameEn?: string;
  countyNameZh?: string;
  countyNameEn?: string;
}

export interface SchoolInfo {
  schoolId: number;
  schoolCode?: string;
  schoolPhone?: string;
  schoolMobile?: string;
  schoolEmail?: string;
  schoolCountyId?: number;
  schoolDistrictId?: number;
  schoolName?: string;
  schoolAddress?: string;
  schoolDepartment?: string;
  schoolJobTitle?: string;
  schoolIntroduction?: string;
  schoolWebsite?: string;
  schoolNameEn?: string;
  schoolAddressEn?: string;
  schoolCountyNameZh?: string;
  schoolCountyNameEn?: string;
  logoPath?: string;
}

export interface PrincipalInfo {
  principalName?: string;
  principalTelephone?: string;
  principalPhone?: string;
  principalEmail?: string;
}

export interface ContactInfo {
  contactId: number;
  contactName: string;
  jobTitle: string;
  contactTelephone?: string;
  contactPhone?: string;
  contactEmail?: string;
}

export interface SchoolStatistics {
  schoolStatisticsId: number;
  staffTotal: number;
  elementary1: number;
  elementary2: number;
  elementary3: number;
  elementary4: number;
  elementary5: number;
  elementary6: number;
  middle7: number;
  middle8: number;
  middle9: number;
  high10: number;
  high11: number;
  high12: number;
  writeDate: Date;
}

export interface FullProfile extends MemberProfile {
  school?: SchoolInfo;
  principal?: PrincipalInfo;
  contacts?: ContactInfo[];
  statistics?: SchoolStatistics;
}

// 資料庫查詢結果介面
export interface MemberQueryResult {
  AccountId: number;
  account: string;
  email: string;
  tel: string;
  phone: string;
  address: string;
  account_county_id: number;
  is_school_partner: number;
  is_epa_user: number;
  is_guidance_team: number;
  member_cname_zh: string;
  member_email: string;
  member_tel: string;
  member_phone: string;
  member_address: string;
  member_url: string;
  job_cname: string;
  place_cname: string;
  member_role: string;
  member_Introduction: string;
  member_exchange: string;
  member_cname_en: string;
  county_name_zh: string;
  county_name_en: string;
  school_county_name_zh: string;
  school_county_name_en: string;
  school_id: number;
  school_code: string;
  school_phone: string;
  school_mobile: string;
  school_email: string;
  school_county_id: number;
  school_district_id: number;
  school_name: string;
  school_address: string;
  school_department: string;
  school_job_title: string;
  school_introduction: string;
  school_website: string;
  school_name_en: string;
  school_address_en: string;
  principal_cname: string;
  principal_tel: string;
  principal_phone: string;
  principal_email: string;
  school_logo_path?: string;
}

export interface ContactQueryResult {
  contact_cname: string;
  contact_job_title: string;
  contact_tel: string;
  contact_phone: string;
  contact_email: string;
  contact_sid: number;
}

export interface StatisticsQueryResult {
  staff_total: number;
  elementary1: number;
  elementary2: number;
  elementary3: number;
  elementary4: number;
  elementary5: number;
  elementary6: number;
  middle7: number;
  middle8: number;
  middle9: number;
  hight10: number;
  hight11: number;
  hight12: number;
  write_date: Date;
  school_statistics_sid: number;
}

// 更新請求介面
export interface UpdateMemberProfileRequest {
  jobTitle?: string;
  memberName?: string;
  memberNameEn?: string;
  officePhone?: string;
  mobilePhone?: string;
  email?: string;
}

export interface UpdateSchoolBasicRequest {
  schoolName?: string;
  schoolNameEn?: string;
  countyId?: number;
  districtId?: number;
  address?: string;
  addressEn?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  website?: string;
}

export interface UpdatePrincipalRequest {
  principalName?: string;
  principalPhone?: string;
  principalMobile?: string;
  principalEmail?: string;
}

export interface UpdateContactsRequest {
  contacts: Array<{
    contactId?: number;
    contactName: string;
    jobTitle: string;
    contactPhone?: string;
    contactMobile?: string;
    contactEmail?: string;
  }>;
}

export interface UpdateStatisticsRequest {
  staffTotal?: number;
  elementary1?: number;
  elementary2?: number;
  elementary3?: number;
  elementary4?: number;
  elementary5?: number;
  elementary6?: number;
  middle7?: number;
  middle8?: number;
  middle9?: number;
  high10?: number;
  high11?: number;
  high12?: number;
}

// API 回應介面
export interface ProfileResponse {
  success: boolean;
  data: FullProfile;
  message?: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  data?: {
    updated: boolean;
    profile?: FullProfile;
  };
}

// 路由參數類型
export interface ProfileParams {}
export interface UpdateProfileParams {}
