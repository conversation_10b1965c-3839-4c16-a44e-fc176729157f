import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Trash2, Plus, Upload, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import userService, { SchoolMemberRecord, EPAMemberRecord, TutorMemberRecord } from "@/services/userService";
import { MemberProfile } from "@/api";
import authService from "@/services/authService";
import { buildAssetUrl } from "@/utils/pathUtils";
// 基本資料維護的表格

// 聯絡人接口
interface ContactPerson {
  contact_sid: string;
  contact_cname: string;
  contact_job_title: string;
  contact_tel: string;
  contact_phone: string;
  contact_email: string;
}

// 學校統計資料接口
interface SchoolStatistics {
  staff_total: string;
  elementary1: string;
  elementary2: string;
  elementary3: string;
  elementary4: string;
  elementary5: string;
  elementary6: string;
  middle7: string;
  middle8: string;
  middle9: string;
  hight10: string;
  hight11: string;
  hight12: string;
  write_date: string;
}

// 擴展表單資料接口
interface ExtendedFormData {
  // 基本資料
  member_cname: string;
  member_cname_en: string;
  member_email: string;
  member_tel: string;
  member_phone?: string;
  member_address?: string; // 保留原始地址欄位以保持兼容性
  member_url?: string;
  member_Introduction?: string;
  member_exchange?: string;
  place_cname?: string;
  place_cname_en?: string; // 🆕 學校英文名稱
  job_cname?: string;

  // 🆕 地址資訊（學校專用）
  city_name?: string; // 縣市
  area_name?: string; // 區域
  detail_address?: string; // 詳細地址

  // 地區資訊（保留以兼容非學校身份）
  city_sid?: string;
  area_sid?: string;

  // 🆕 校徽資訊（學校專用）
  school_logo?: string; // 校徽圖檔路徑

  // 校長資訊 (學校專用)
  principal_cname?: string;
  principal_tel?: string;
  principal_phone?: string;
  principal_email?: string;

  // 聯絡人資訊 (學校專用)
  contact?: ContactPerson[];
  contact_num?: string;

  // 學校統計資料 (學校專用)
  statistics?: SchoolStatistics;

  // 學校照片
  file_token_member_pohto?: string;
}

interface ProfileEditFormProps {
  profile: MemberProfile;
  onProfileUpdate: (updatedProfile: MemberProfile) => void;
}

export const ProfileEditForm: React.FC<ProfileEditFormProps> = ({ profile, onProfileUpdate }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ExtendedFormData>({
    member_cname: "",
    member_cname_en: "",
    member_email: "",
    member_tel: "",
    contact: [],
    statistics: {
      staff_total: "",
      elementary1: "",
      elementary2: "",
      elementary3: "",
      elementary4: "",
      elementary5: "",
      elementary6: "",
      middle7: "",
      middle8: "",
      middle9: "",
      hight10: "",
      hight11: "",
      hight12: "",
      write_date: new Date().toISOString().split("T")[0],
    },
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false); // 🆕 追蹤表單是否有變更
  const [originalFormData, setOriginalFormData] = useState<ExtendedFormData | null>(null); // 🆕 保存原始資料
  const [logoModalOpen, setLogoModalOpen] = useState(false); // 🆕 校徽放大檢視模態框

  // 🆕 載入縣市和區域資料
  useEffect(() => {
    const loadCityData = async () => {
      try {
        // 使用 userService 獲取縣市資料
        const result = await userService.getCities();
        if (result.success) {
          // 🔧 修正：轉換API資料格式為前端期望格式
          const formattedOptions = result.data.map((city: { id: number; name: string }) => ({
            value: String(city.id),
            label: city.name,
          }));
          setCityOptions(formattedOptions);
          console.log("✅ [CityData] 載入成功:", formattedOptions.length, "個縣市");

          // 🆕 縣市資料載入完成後，立即初始化已選縣市和區域
          if (formData.city_sid && !selectedCityId) {
            console.log("🔄 [Init] 縣市資料載入完成，開始初始化已選縣市:", formData.city_sid);

            // 驗證縣市ID是否存在於選項中
            const cityExists = formattedOptions.some((city) => city.value === formData.city_sid);
            if (cityExists) {
              console.log("✅ [Init] 確認縣市選項存在，設置選中狀態");
              setSelectedCityId(formData.city_sid);

              // 自動載入對應的區域資料
              loadAreaData(formData.city_sid, formData.area_sid);
            } else {
              console.warn(
                "⚠️ [Init] 縣市ID不存在於選項中:",
                formData.city_sid,
                "可用選項:",
                formattedOptions.map((o) => o.value)
              );
              // 清空無效的縣市選擇
              handleFieldChange("city_sid", "");
            }
          }
        }
      } catch (error) {
        console.error("❌ [CityData] 載入縣市資料失敗:", error);
      }
    };

    loadCityData();
  }, []); // 🔧 移除formData依賴，避免重複載入

  // 🆕 載入當前校徽
  useEffect(() => {
    const loadCurrentLogo = async () => {
      if (profile.member_role !== "school") return;

      try {
        const result = await userService.getSchoolLogo(profile.sid || 5);

        console.log("🔄 [Logo] 載入校徽:", result);
        if (result.success && result.data.logoUrl) {
          setFormData((prev) => ({
            ...prev,
            school_logo: result.data.logoUrl,
          }));

          // 🆕 同時更新 originalFormData 以避免被重置
          setOriginalFormData((prev) =>
            prev
              ? {
                  ...prev,
                  school_logo: result.data.logoUrl,
                }
              : null
          );
        }
      } catch (error) {
        console.log("📸 [Logo] 載入校徽失敗或尚未上傳:", error);
      }
    };

    loadCurrentLogo();
  }, [profile.sid, profile.member_role]);

  // 🆕 載入區域資料的通用函數
  const loadAreaData = async (cityId: string, preSelectedAreaId?: string) => {
    if (!cityId) return;

    setLoadingAreas(true);
    console.log("🔄 [AreaData] 載入區域資料，縣市ID:", cityId, "預選區域ID:", preSelectedAreaId);

    try {
      // 使用 userService 獲取區域資料
      const result = await userService.getAreas(Number(cityId));
      if (result.success) {
        // 🔧 修正：轉換區域API資料格式為前端期望格式
        const formattedAreaOptions = result.data.map((area: { id: number; name: string }) => ({
          value: String(area.id),
          label: area.name,
        }));
        setAreaOptions(formattedAreaOptions);
        console.log("✅ [AreaData] 載入成功:", formattedAreaOptions.length, "個區域");

        // 🆕 如果有預選的區域ID，驗證是否存在於選項中
        if (preSelectedAreaId) {
          const areaExists = formattedAreaOptions.some((area) => area.value === preSelectedAreaId);
          if (areaExists) {
            console.log("✅ [AreaData] 確認預選區域存在:", preSelectedAreaId);
          } else {
            console.warn("⚠️ [AreaData] 預選區域不存在，將清空選擇:", preSelectedAreaId);
            handleFieldChange("area_sid", "");
          }
        }
      }
    } catch (error) {
      console.error("❌ [AreaData] 載入區域資料失敗:", error);
    } finally {
      setLoadingAreas(false);
    }
  };

  // 🆕 處理縣市選擇變更
  const handleCityChange = async (cityId: string) => {
    console.log("🔄 [CityChange] 縣市選擇變更:", cityId);
    setSelectedCityId(cityId);
    handleFieldChange("city_sid", cityId);

    // 清空區域選擇
    setAreaOptions([]);
    handleFieldChange("area_sid", "");

    // 載入新縣市的區域資料
    if (cityId) {
      loadAreaData(cityId);
    }
  };

  // 🆕 檢查表單是否有變更
  const checkForChanges = (currentData: ExtendedFormData, originalData: ExtendedFormData | null) => {
    if (!originalData) return false;

    // 🔧 智能比較：分別處理不同類型的資料
    const { contact: currentContacts, ...currentBasic } = currentData;
    const { contact: originalContacts, ...originalBasic } = originalData;

    // 1. 比較基本欄位（排除聯絡人）
    if (JSON.stringify(currentBasic) !== JSON.stringify(originalBasic)) {
      return true;
    }

    // 2. 比較聯絡人資料（忽略 contact_sid 差異）
    const currentContactCount = currentContacts?.length || 0;
    const originalContactCount = originalContacts?.length || 0;

    // 聯絡人數量不同
    if (currentContactCount !== originalContactCount) {
      return true;
    }

    // 比較每個聯絡人的內容（忽略 contact_sid）
    for (let i = 0; i < currentContactCount; i++) {
      const current = currentContacts?.[i];
      const original = originalContacts?.[i];

      if (!current || !original) continue;

      // 只比較實際內容，忽略 contact_sid
      const currentContent = {
        contact_cname: current.contact_cname,
        contact_job_title: current.contact_job_title,
        contact_tel: current.contact_tel,
        contact_phone: current.contact_phone,
        contact_email: current.contact_email,
      };

      const originalContent = {
        contact_cname: original.contact_cname,
        contact_job_title: original.contact_job_title,
        contact_tel: original.contact_tel,
        contact_phone: original.contact_phone,
        contact_email: original.contact_email,
      };

      if (JSON.stringify(currentContent) !== JSON.stringify(originalContent)) {
        return true;
      }
    }

    // 沒有發現變更
    return false;
  };

  // 初始化表單資料
  useEffect(() => {
    // 從 member_record 中獲取詳細資料
    const memberRecord = profile.member_record as SchoolMemberRecord | EPAMemberRecord | TutorMemberRecord | undefined;

    const initialData: ExtendedFormData = {
      member_cname: profile.member_cname || "",
      member_cname_en: profile.member_cname_en || "",
      member_email: profile.member_email || "",
      member_tel: profile.member_tel || "",
      member_phone: profile.member_phone || "",
      member_address: profile.member_address || "",
      member_url: profile.member_url || "",
      member_Introduction: profile.member_Introduction || "",
      member_exchange: profile.member_exchange || "0",
      place_cname: profile.place_cname || "",
      place_cname_en: (profile as MemberProfile & { place_cname_en?: string }).place_cname_en || "", // 🆕 學校英文名稱
      job_cname: profile.job_cname || "",
      city_sid: profile.city_sid ? String(profile.city_sid) : "",
      area_sid: profile.area_sid ? String(profile.area_sid) : "",
      // 🆕 地址資訊初始化
      city_name: "",
      area_name: "",
      detail_address: profile.member_address || "",
      school_logo: formData.school_logo || "", // 🔧 保持現有校徽，不要重置為空
      principal_cname: memberRecord && "principal_cname" in memberRecord ? memberRecord.principal_cname : "",
      principal_tel: memberRecord && "principal_tel" in memberRecord ? memberRecord.principal_tel : "",
      principal_phone: memberRecord && "principal_phone" in memberRecord ? memberRecord.principal_phone : "",
      principal_email: memberRecord && "principal_email" in memberRecord ? memberRecord.principal_email : "",
      contact: memberRecord && "contact" in memberRecord ? memberRecord.contact : [],
      contact_num: memberRecord && "contact_num" in memberRecord ? memberRecord.contact_num : "0",
      statistics: {
        staff_total: memberRecord && "staff_total" in memberRecord ? memberRecord.staff_total : "",
        elementary1: memberRecord && "elementary1" in memberRecord ? memberRecord.elementary1 : "",
        elementary2: memberRecord && "elementary2" in memberRecord ? memberRecord.elementary2 : "",
        elementary3: memberRecord && "elementary3" in memberRecord ? memberRecord.elementary3 : "",
        elementary4: memberRecord && "elementary4" in memberRecord ? memberRecord.elementary4 : "",
        elementary5: memberRecord && "elementary5" in memberRecord ? memberRecord.elementary5 : "",
        elementary6: memberRecord && "elementary6" in memberRecord ? memberRecord.elementary6 : "",
        middle7: memberRecord && "middle7" in memberRecord ? memberRecord.middle7 : "",
        middle8: memberRecord && "middle8" in memberRecord ? memberRecord.middle8 : "",
        middle9: memberRecord && "middle9" in memberRecord ? memberRecord.middle9 : "",
        hight10: memberRecord && "hight10" in memberRecord ? memberRecord.hight10 : "",
        hight11: memberRecord && "hight11" in memberRecord ? memberRecord.hight11 : "",
        hight12: memberRecord && "hight12" in memberRecord ? memberRecord.hight12 : "",
        write_date: memberRecord && "write_date" in memberRecord ? memberRecord.write_date : new Date().toISOString().split("T")[0],
      },
      file_token_member_pohto: memberRecord && "file_token_member_pohto" in memberRecord ? memberRecord.file_token_member_pohto : "",
    };

    setFormData(initialData);
    setOriginalFormData({ ...initialData }); // 🆕 保存原始資料的副本
    setHasChanges(false); // 🆕 重置變更狀態
  }, [profile]);

  // 🆕 監聽表單資料變更
  useEffect(() => {
    const changes = checkForChanges(formData, originalFormData);
    setHasChanges(changes);
  }, [formData, originalFormData]);

  // 格式化身份類型顯示
  const formatMemberRole = (role: "school" | "epa" | "tutor"): string => {
    const roleMap = {
      school: "生態學校",
      epa: "縣市政府",
      tutor: "輔導人員",
    };
    return roleMap[role] || role;
  };

  // 驗證函數
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // const isValidPhone = (phone: string): boolean => {
  //   const phoneRegex = /^(\+886|0)?([2-9]\d{7,8}|[2-9]\d{2}-\d{3}-\d{3})$/;
  //   return phoneRegex.test(phone.replace(/[-\s]/g, ""));
  // };

  //  改為：手機號碼驗證（09 開頭 + 8 碼）
  const isValidMobile = (phone: string): boolean => {
    const cleaned = phone.replace(/[-\s]/g, "");
    return /^09\d{8}$/.test(cleaned);
  };

  //  改為：辦公室電話驗證（允許 # 或 * 後綴分機）
  const isValidOfficePhone = (phone: string): boolean => {
    const cleaned = phone.replace(/[-\s]/g, "");
    return /^(0|\+886)\d{6,10}([#*]\d+)?$/.test(cleaned);
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // 表單驗證
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // 基本驗證
    if (!formData.member_cname.trim()) {
      newErrors.member_cname = "名稱為必填欄位";
    }

    if (formData.member_email && !isValidEmail(formData.member_email)) {
      newErrors.member_email = "請輸入正確的電子郵件格式";
    }

    if (formData.member_tel && !isValidOfficePhone(formData.member_tel)) {
      newErrors.member_tel = "請輸入正確的電話號碼格式";
    }

    if (formData.member_phone && !isValidMobile(formData.member_phone)) {
      newErrors.member_phone = "請輸入正確的手機號碼格式";
    }

    if (formData.member_url && !isValidUrl(formData.member_url)) {
      newErrors.member_url = "請輸入正確的網址格式";
    }

    // 校長資訊驗證 (學校身份)
    if (profile.member_role === "school") {
      if (formData.principal_email && !isValidEmail(formData.principal_email)) {
        newErrors.principal_email = "請輸入正確的校長電子郵件格式";
      }
      if (formData.principal_tel && !isValidOfficePhone(formData.principal_tel)) {
        newErrors.principal_tel = "請輸入正確的校長電話格式";
      }
      if (formData.principal_phone && !isValidMobile(formData.principal_phone)) {
        newErrors.principal_phone = "請輸入正確的校長手機格式";
      }
    }

    // 聯絡人驗證
    formData.contact?.forEach((contact, index) => {
      if (contact.contact_email && !isValidEmail(contact.contact_email)) {
        newErrors[`contact_${index}_email`] = "請輸入正確的聯絡人電子郵件格式";
      }
      if (contact.contact_tel && !isValidOfficePhone(contact.contact_tel)) {
        newErrors[`contact_${index}_tel`] = "請輸入正確的聯絡人電話格式";
      }
      if (contact.contact_phone && !isValidMobile(contact.contact_phone)) {
        newErrors[`contact_${index}_phone`] = "請輸入正確的聯絡人手機格式";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 處理欄位變更
  const handleFieldChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // 清除該欄位的錯誤
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // 處理統計資料變更
  const handleStatisticsChange = (field: keyof SchoolStatistics, value: string) => {
    setFormData((prev) => ({
      ...prev,
      statistics: {
        ...prev.statistics!,
        [field]: value,
      },
    }));
  };

  // 添加聯絡人
  const addContact = () => {
    const newContact: ContactPerson = {
      contact_sid: "",
      contact_cname: "",
      contact_job_title: "",
      contact_tel: "",
      contact_phone: "",
      contact_email: "",
    };

    setFormData((prev) => ({
      ...prev,
      contact: [...(prev.contact || []), newContact],
      contact_num: String((prev.contact?.length || 0) + 1),
    }));
  };

  // 移除聯絡人
  const removeContact = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      contact: prev.contact?.filter((_, i) => i !== index) || [],
      contact_num: String(Math.max(0, (prev.contact?.length || 1) - 1)),
    }));
  };

  // 更新聯絡人資料
  const updateContact = (index: number, field: keyof ContactPerson, value: string) => {
    setFormData((prev) => ({
      ...prev,
      contact: prev.contact?.map((contact, i) => (i === index ? { ...contact, [field]: value } : contact)) || [],
    }));
  };

  // 提交表單
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        variant: "destructive",
        title: "表單驗證失敗",
        description: "請檢查並修正表單中的錯誤",
      });
      return;
    }

    setLoading(true);

    try {
      // 🆕 準備更新資料
      const updateData: Record<string, unknown> = {
        // 基本資料欄位
        member_cname: formData.member_cname,
        member_cname_en: formData.member_cname_en,
        member_email: formData.member_email,
        member_tel: formData.member_tel,
        member_phone: formData.member_phone,
        member_address: formData.detail_address || formData.member_address, // 🆕 優先使用 detail_address
        member_url: formData.member_url,
        member_Introduction: formData.member_Introduction,
        member_exchange: formData.member_exchange,
        place_cname: formData.place_cname,
        place_cname_en: formData.place_cname_en,
        job_cname: formData.job_cname,
        city_sid: formData.city_sid ? parseInt(formData.city_sid) : null,
        area_sid: formData.area_sid ? parseInt(formData.area_sid) : null,
      };

      // 學校身份的詳細資料
      if (profile.member_role === "school") {
        updateData.member_record_data = {
          // 校長資訊
          principal_cname: formData.principal_cname,
          principal_tel: formData.principal_tel,
          principal_phone: formData.principal_phone,
          principal_email: formData.principal_email,

          // 學校統計資料
          statistics: formData.statistics,

          // 聯絡人資訊（如果需要的話）
          contact: formData.contact,
          contact_num: formData.contact_num,
        };
      }

      // 🆕 調用真實的 API
      const response = await userService.updateUserProfile(updateData);

      if (response.success) {
        console.log("🔄 [FormUpdate] 更新成功，同步表單資料:", response.data);

        // 🆕 強制重新載入校徽以確保不會消失
        let currentSchoolLogo = formData.school_logo;
        if (profile.member_role === "school") {
          try {
            console.log("🔄 [Logo] 強制重新載入校徽...");
            const logoResult = await userService.getSchoolLogo(profile.sid || 5);
            if (logoResult.success && logoResult.data.logoUrl) {
              currentSchoolLogo = logoResult.data.logoUrl;
              console.log("✅ [Logo] 重新載入校徽成功:", currentSchoolLogo);
            } else {
              console.log("📸 [Logo] 沒有找到校徽檔案");
              currentSchoolLogo = formData.school_logo; // 保持現有校徽
            }
          } catch (error) {
            console.log("📸 [Logo] 重新載入校徽失敗:", error);
            currentSchoolLogo = formData.school_logo; // 保持現有校徽
          }
        }

        // 立即使用更新後的資料重新初始化表單
        if (response.data) {
          const updatedProfile = response.data;
          const memberRecord = updatedProfile.member_record as SchoolMemberRecord | EPAMemberRecord | TutorMemberRecord | undefined;

          const refreshedFormData: ExtendedFormData = {
            member_cname: updatedProfile.member_cname || "",
            member_cname_en: updatedProfile.member_cname_en || "",
            member_email: updatedProfile.member_email || "",
            member_tel: updatedProfile.member_tel || "",
            member_phone: updatedProfile.member_phone || "",
            member_address: updatedProfile.member_address || "",
            member_url: updatedProfile.member_url || "",
            member_Introduction: updatedProfile.member_Introduction || "",
            member_exchange: updatedProfile.member_exchange || "0",
            place_cname: updatedProfile.place_cname || "",
            place_cname_en: (updatedProfile as MemberProfile & { place_cname_en?: string }).place_cname_en || "", // 🆕 學校英文名稱
            job_cname: updatedProfile.job_cname || "",
            city_sid: String(updatedProfile.city_sid || ""),
            area_sid: String(updatedProfile.area_sid || ""),
            // 🆕 地址資訊更新後
            city_name: (updatedProfile as MemberProfile & { city_name?: string }).city_name || "",
            area_name: (updatedProfile as MemberProfile & { area_name?: string }).area_name || "",
            detail_address: (updatedProfile as MemberProfile & { detail_address?: string }).detail_address || updatedProfile.member_address || "",
            school_logo: currentSchoolLogo || formData.school_logo || "", // 🔧 使用重新載入的校徽或保持原有校徽
            principal_cname: memberRecord && "principal_cname" in memberRecord ? memberRecord.principal_cname : "",
            principal_tel: memberRecord && "principal_tel" in memberRecord ? memberRecord.principal_tel : "",
            principal_phone: memberRecord && "principal_phone" in memberRecord ? memberRecord.principal_phone : "",
            principal_email: memberRecord && "principal_email" in memberRecord ? memberRecord.principal_email : "",
            contact: memberRecord && "contact" in memberRecord ? memberRecord.contact : [],
            contact_num: memberRecord && "contact_num" in memberRecord ? memberRecord.contact_num : "0",
            statistics: {
              staff_total: memberRecord && "staff_total" in memberRecord ? memberRecord.staff_total : "",
              elementary1: memberRecord && "elementary1" in memberRecord ? memberRecord.elementary1 : "",
              elementary2: memberRecord && "elementary2" in memberRecord ? memberRecord.elementary2 : "",
              elementary3: memberRecord && "elementary3" in memberRecord ? memberRecord.elementary3 : "",
              elementary4: memberRecord && "elementary4" in memberRecord ? memberRecord.elementary4 : "",
              elementary5: memberRecord && "elementary5" in memberRecord ? memberRecord.elementary5 : "",
              elementary6: memberRecord && "elementary6" in memberRecord ? memberRecord.elementary6 : "",
              middle7: memberRecord && "middle7" in memberRecord ? memberRecord.middle7 : "",
              middle8: memberRecord && "middle8" in memberRecord ? memberRecord.middle8 : "",
              middle9: memberRecord && "middle9" in memberRecord ? memberRecord.middle9 : "",
              hight10: memberRecord && "hight10" in memberRecord ? memberRecord.hight10 : "",
              hight11: memberRecord && "hight11" in memberRecord ? memberRecord.hight11 : "",
              hight12: memberRecord && "hight12" in memberRecord ? memberRecord.hight12 : "",
              write_date: memberRecord && "write_date" in memberRecord ? memberRecord.write_date : new Date().toISOString().split("T")[0],
            },
            file_token_member_pohto: memberRecord && "file_token_member_pohto" in memberRecord ? memberRecord.file_token_member_pohto : "",
          };

          // 立即更新表單資料，避免顯示空值
          setFormData(refreshedFormData);
          setOriginalFormData({ ...refreshedFormData }); // 🆕 更新原始資料
          setHasChanges(false); // 🆕 清除變更狀態
          console.log("📋 [FormState] 表單資料已同步更新");

          // 🆕 同步更新 AuthService 中的用戶名稱（左上角顯示）
          const refreshSuccess = await authService.refreshUserProfile();
          if (refreshSuccess) {
            console.log("🔄 [UserNameSync] 左上角使用者名稱已同步更新");
          } else {
            console.warn("⚠️ [UserNameSync] 使用者名稱同步失敗，使用備用方法");
            // 備用方法：直接更新名稱
            if (refreshedFormData.member_cname || refreshedFormData.member_cname_en || refreshedFormData.job_cname) {
              authService.updateUserName(refreshedFormData.member_cname || "", refreshedFormData.member_cname_en || "", refreshedFormData.job_cname || "");
            }
          }

          // 通知父組件資料已更新
          onProfileUpdate(updatedProfile);
        }

        // 🆕 刷新使用者資料以更新 Navbar 顯示
        try {
          console.log("🔄 [NavbarSync] 刷新使用者資料以更新 Navbar 顯示...");
          const refreshSuccess = await authService.refreshUserProfile(true); // 強制刷新
          if (refreshSuccess) {
            console.log("✅ [NavbarSync] Navbar 使用者名稱已同步更新");
          } else {
            console.warn("⚠️ [NavbarSync] 使用者名稱同步失敗");
          }
        } catch (error) {
          console.error("❌ [NavbarSync] 刷新使用者資料失敗:", error);
        }

        toast({
          title: "更新成功",
          description: "基本資料已成功更新，頁面資訊將自動更新",
        });
      } else {
        throw new Error(response.message || "更新失敗");
      }
    } catch (error) {
      console.error("更新使用者資料失敗:", error);
      toast({
        variant: "destructive",
        title: "更新失敗",
        description: error instanceof Error ? error.message : "更新基本資料時發生錯誤",
      });
    } finally {
      setLoading(false);
    }
  };

  // 重置表單
  const handleReset = () => {
    // 從 member_record 中獲取詳細資料
    const memberRecord = profile.member_record as SchoolMemberRecord | EPAMemberRecord | TutorMemberRecord | undefined;

    // 重置為原始資料
    const initialData: ExtendedFormData = {
      member_cname: profile.member_cname || "",
      member_cname_en: profile.member_cname_en || "",
      member_email: profile.member_email || "",
      member_tel: profile.member_tel || "",
      member_phone: profile.member_phone || "",
      member_address: profile.member_address || "",
      member_url: profile.member_url || "",
      member_Introduction: profile.member_Introduction || "",
      member_exchange: profile.member_exchange || "0",
      place_cname: profile.place_cname || "",
      place_cname_en: (profile as MemberProfile & { place_cname_en?: string }).place_cname_en || "", // 🆕 學校英文名稱
      job_cname: profile.job_cname || "",
      city_sid: String(profile.city_sid || ""),
      area_sid: String(profile.area_sid || ""),
      // 🆕 地址資訊重置
      city_name: "",
      area_name: "",
      detail_address: profile.member_address || "",
      school_logo: formData.school_logo || (profile as MemberProfile & { school_logo?: string }).school_logo || "", // 🔧 保持重置時的校徽，優先使用當前校徽
      principal_cname: memberRecord && "principal_cname" in memberRecord ? memberRecord.principal_cname : "",
      principal_tel: memberRecord && "principal_tel" in memberRecord ? memberRecord.principal_tel : "",
      principal_phone: memberRecord && "principal_phone" in memberRecord ? memberRecord.principal_phone : "",
      principal_email: memberRecord && "principal_email" in memberRecord ? memberRecord.principal_email : "",
      contact: memberRecord && "contact" in memberRecord ? memberRecord.contact : [],
      contact_num: memberRecord && "contact_num" in memberRecord ? memberRecord.contact_num : "0",
      statistics: {
        staff_total: memberRecord && "staff_total" in memberRecord ? memberRecord.staff_total : "",
        elementary1: memberRecord && "elementary1" in memberRecord ? memberRecord.elementary1 : "",
        elementary2: memberRecord && "elementary2" in memberRecord ? memberRecord.elementary2 : "",
        elementary3: memberRecord && "elementary3" in memberRecord ? memberRecord.elementary3 : "",
        elementary4: memberRecord && "elementary4" in memberRecord ? memberRecord.elementary4 : "",
        elementary5: memberRecord && "elementary5" in memberRecord ? memberRecord.elementary5 : "",
        elementary6: memberRecord && "elementary6" in memberRecord ? memberRecord.elementary6 : "",
        middle7: memberRecord && "middle7" in memberRecord ? memberRecord.middle7 : "",
        middle8: memberRecord && "middle8" in memberRecord ? memberRecord.middle8 : "",
        middle9: memberRecord && "middle9" in memberRecord ? memberRecord.middle9 : "",
        hight10: memberRecord && "hight10" in memberRecord ? memberRecord.hight10 : "",
        hight11: memberRecord && "hight11" in memberRecord ? memberRecord.hight11 : "",
        hight12: memberRecord && "hight12" in memberRecord ? memberRecord.hight12 : "",
        write_date: memberRecord && "write_date" in memberRecord ? memberRecord.write_date : new Date().toISOString().split("T")[0],
      },
      file_token_member_pohto: memberRecord && "file_token_member_pohto" in memberRecord ? memberRecord.file_token_member_pohto : "",
    };

    setFormData(initialData);
    setOriginalFormData({ ...initialData }); // 🆕 重置原始資料
    setHasChanges(false); // 🆕 清除變更狀態
    setErrors({});

    toast({
      title: "表單已重置",
      description: "所有變更已還原為原始值",
    });
  };

  // 渲染基本輸入欄位
  const renderField = (field: keyof ExtendedFormData, label: string, type: "text" | "email" | "tel" | "url" = "text", required = false) => {
    return (
      <div className="space-y-2">
        <Label htmlFor={field}>
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
        <Input
          id={field}
          type={type}
          value={(formData[field] as string) || ""}
          onChange={(e) => handleFieldChange(field, e.target.value)}
          className={errors[field] ? "border-red-500" : ""}
          placeholder={`請輸入${label}`}
          required={required}
        />
        {errors[field] && (
          <Alert variant="destructive" className="py-2">
            <AlertDescription>{errors[field]}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  // 渲染文字區域欄位
  const renderTextareaField = (field: keyof ExtendedFormData, label: string, rows = 3) => {
    return (
      <div className="space-y-2">
        <Label htmlFor={field}>{label}</Label>
        <Textarea
          id={field}
          value={(formData[field] as string) || ""}
          onChange={(e) => handleFieldChange(field, e.target.value)}
          className={errors[field] ? "border-red-500" : ""}
          placeholder={`請輸入${label}`}
          rows={rows}
        />
        {errors[field] && (
          <Alert variant="destructive" className="py-2">
            <AlertDescription>{errors[field]}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  // 渲染下拉選單欄位
  const renderSelectField = (field: keyof ExtendedFormData, label: string, options: { value: string; label: string }[], placeholder = "請選擇...") => {
    return (
      <div className="space-y-2">
        <Label htmlFor={field}>{label}</Label>
        <Select value={(formData[field] as string) || ""} onValueChange={(value) => handleFieldChange(field, value)}>
          <SelectTrigger className={errors[field] ? "border-red-500" : ""}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {options
              .filter((option) => option.value !== undefined && option.label)
              .map((option, index) => (
                <SelectItem key={`${field}-${option.value || "undefined"}-${index}-${option.label?.length || 0}`} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
        {errors[field] && (
          <Alert variant="destructive" className="py-2">
            <AlertDescription>{errors[field]}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  // 🆕 校徽上傳狀態
  const [logoUploading, setLogoUploading] = useState(false);

  // 🆕 地區資料狀態
  const [cityOptions, setCityOptions] = useState<{ value: string; label: string }[]>([]);
  const [areaOptions, setAreaOptions] = useState<{ value: string; label: string }[]>([]);
  const [selectedCityId, setSelectedCityId] = useState<string>("");
  const [loadingAreas, setLoadingAreas] = useState(false);

  // 🆕 監聽formData變更，同步初始化狀態
  useEffect(() => {
    // 當formData.city_sid變更且縣市選項已載入時，同步selectedCityId
    if (formData.city_sid && cityOptions.length > 0 && formData.city_sid !== selectedCityId) {
      console.log("🔄 [Sync] 同步縣市選擇狀態:", formData.city_sid);

      // 驗證縣市選項是否存在
      const cityExists = cityOptions.some((city) => city.value === formData.city_sid);
      if (cityExists) {
        console.log("✅ [Sync] 縣市選項驗證通過，更新選中狀態");
        setSelectedCityId(formData.city_sid);

        // 如果區域選項為空且有選中的區域，載入區域資料
        if (formData.area_sid && areaOptions.length === 0) {
          console.log("🔄 [Sync] 載入區域資料:", formData.area_sid);
          loadAreaData(formData.city_sid, formData.area_sid);
        }
      } else {
        console.warn(
          "⚠️ [Sync] 縣市選項不存在:",
          formData.city_sid,
          "可用:",
          cityOptions.map((o) => o.value)
        );
      }
    }
  }, [formData.city_sid, formData.area_sid, cityOptions.length, selectedCityId, areaOptions.length]);

  // 🆕 處理校徽上傳
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 檢查檔案格式
    if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
      toast({
        variant: "destructive",
        title: "檔案格式錯誤",
        description: "校徽圖檔請上傳jpg、png格式",
      });
      return;
    }

    // 檢查檔案大小（5MB限制）
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "檔案太大",
        description: "檔案大小不可超過5MB",
      });
      return;
    }

    setLogoUploading(true);
    try {
      // 🚀 使用 userService 上傳校徽
      const result = await userService.uploadSchoolLogo(profile.sid || 5, file);

      if (result.success) {
        handleFieldChange("school_logo", result.data.logoUrl);

        toast({
          title: "校徽上傳成功",
          description: "校徽圖檔已成功上傳並自動調整為150x150尺寸",
        });
      } else {
        throw new Error(result.message || "上傳失敗");
      }
    } catch (error) {
      console.error("校徽上傳錯誤:", error);
      toast({
        variant: "destructive",
        title: "上傳失敗",
        description: error instanceof Error ? error.message : "校徽圖檔上傳失敗，請稍後再試",
      });
    } finally {
      setLogoUploading(false);
    }
  };

  // 渲染學校基本資料
  const renderSchoolBasicInfo = () => (
    <div className="space-y-6">
      {/* 🆕 負責人個人基本資料區塊 */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <h5 className="font-medium text-lg">使用者個人資料</h5>
          <Badge variant="outline" className="text-xs">
            個人資訊
          </Badge>
        </div>
        <div className="bg-blue-50 p-4 rounded-lg space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
            {renderField("job_cname", "職稱")}
            {renderField("member_cname", "中文姓名", "text", true)}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderField("member_cname_en", "英文姓名")}
            {renderField("member_tel", "辦公室電話", "tel")}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderField("member_phone", "手機號碼", "tel")}
            {renderField("member_email", "電子郵件", "email", true)}
          </div>
        </div>
      </div>

      {/* 🆕 學校基本資料區塊 */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <h5 className="font-medium text-lg">學校基本資料</h5>
          <Badge variant="outline" className="text-xs">
            學校資訊
          </Badge>
        </div>
        <div className="bg-green-50 p-4 rounded-lg space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderField("place_cname", "學校中文名稱")}
            {renderField("place_cname_en", "學校英文名稱")}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{renderField("member_url", "學校網站", "url")}</div>
          {renderTextareaField("member_Introduction", "學校介紹")}

          {/* 🆕 學校地址 */}
          <div className="space-y-3">
            <h6 className="font-medium">學校地址</h6>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city_sid">縣市</Label>
                <Select key={`city-select-${cityOptions.length}-${formData.city_sid}`} value={formData.city_sid || ""} onValueChange={handleCityChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="請選擇縣市" />
                  </SelectTrigger>
                  <SelectContent>
                    {cityOptions
                      .filter((option) => option.value && option.label)
                      .map((option, index) => (
                        <SelectItem key={`city-${option.value || "undefined"}-${index}-${option.label?.length || 0}`} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="area_sid">區域</Label>
                <Select
                  key={`area-select-${areaOptions.length}-${formData.area_sid}`}
                  value={formData.area_sid || ""}
                  onValueChange={(value) => handleFieldChange("area_sid", value)}
                  disabled={!selectedCityId || loadingAreas}>
                  <SelectTrigger>
                    <SelectValue placeholder={loadingAreas ? "載入中..." : "請選擇區域"} />
                  </SelectTrigger>
                  <SelectContent>
                    {areaOptions
                      .filter((option) => option.value && option.label)
                      .map((option, index) => (
                        <SelectItem key={`area-${option.value || "undefined"}-${index}-${option.label?.length || 0}`} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {renderField("detail_address", "詳細地址")}
            </div>
          </div>

          {/* 🆕 校徽上傳與管理 */}
          <div className="space-y-3">
            <h6 className="font-medium">校徽圖檔</h6>

            {/* 當前校徽顯示 */}
            {formData.school_logo ? (
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  {/* 校徽縮圖 */}
                  <div className="flex-shrink-0 w-28">
                    <Label className="text-sm text-gray-600 ">當前校徽</Label>
                    <div
                      className="mt-1 border rounded-lg p-1 w-28 h-24 flex items-center justify-center bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => setLogoModalOpen(true)}
                      title="點擊放大檢視">
                      <img
                        src={formData.school_logo}
                        alt="校徽縮圖"
                        className="max-w-full max-h-full object-contain rounded"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          if (
                            // target.src !== buildAssetUrl("img/placeholder.svg")
                            target.src !== buildAssetUrl("img/NoLogo.png")
                          ) {
                            // target.src = buildAssetUrl("img/placeholder.svg");
                            target.src = buildAssetUrl("img/NoLogo.png");
                          }
                        }}
                      />
                    </div>
                    {/* <p className="text-xs text-gray-500 mt-1 text-center">
                      點擊放大
                    </p> */}
                  </div>

                  {/* 重新上傳功能 */}
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="school_logo_replace">重新上傳校徽</Label>
                    <Input
                      id="school_logo_replace"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={handleLogoUpload}
                      disabled={logoUploading}
                      className="h-14 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                    <p className="text-s text-gray-500">校徽圖檔請上傳 jpg、png 格式。建議尺寸 150×150px，檔案大小不超過 5MB。重新選擇檔案即可更新校徽</p>
                  </div>
                </div>
              </div>
            ) : (
              /* 沒有校徽時的上傳界面 */
              <div className="space-y-4">
                {/* 預設校徽顯示 */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-28">
                    <Label className="text-sm text-gray-600">預設校徽</Label>
                    <div className="mt-1 border rounded-lg p-1 w-28 h-24 flex items-center justify-center bg-gray-50">
                      <img src={buildAssetUrl("img/placeholder.svg")} alt="預設校徽" className="max-w-full max-h-full object-contain rounded opacity-50" />
                    </div>
                    <p className="text-xs text-gray-500 mt-1 text-center">尚未上傳校徽</p>
                  </div>

                  {/* 上傳功能 */}
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="school_logo">上傳校徽</Label>
                    <Input
                      id="school_logo"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={handleLogoUpload}
                      disabled={logoUploading}
                      className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                    <p className="text-xs text-gray-500">支援 JPG、PNG 格式，建議尺寸 150×150px，檔案大小不超過 5MB</p>
                  </div>
                </div>
              </div>
            )}

            {/* <p className="text-sm text-gray-500">
              校徽圖檔請上傳 jpg、png 格式。建議尺寸 150×150px，檔案大小不超過
              5MB。
            </p> */}

            {logoUploading && (
              <div className="flex items-center space-x-2 text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">上傳中...</span>
              </div>
            )}
          </div>

          {/* 🆕 交流活動設置 */}
          <div className="space-y-2">
            <h6 className="font-medium">交流活動</h6>
            <div className="space-y-2">
              <Label htmlFor="member_exchange ">是否參與交流活動</Label>
              <Select value={formData.member_exchange || "0"} onValueChange={(value) => handleFieldChange("member_exchange", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem key="exchange-option-no-0" value="0">
                    否
                  </SelectItem>
                  <SelectItem key="exchange-option-yes-1" value="1">
                    是
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* 🆕 校徽放大檢視模態框 */}
      {logoModalOpen && formData.school_logo && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" onClick={() => setLogoModalOpen(false)}>
          <div className="relative max-w-2xl max-h-2xl p-4">
            <img
              src={formData.school_logo}
              alt="校徽放大檢視"
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                if (target.src !== buildAssetUrl("img/placeholder.svg")) {
                  target.src = buildAssetUrl("img/placeholder.svg");
                }
              }}
            />
            <Button
              type="button"
              variant="secondary"
              size="sm"
              className="absolute top-2 right-2 bg-white hover:bg-gray-100"
              onClick={() => setLogoModalOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  // 渲染校長資訊
  const renderPrincipalInfo = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {renderField("principal_cname", "校長姓名")}
        {renderField("principal_email", "校長電子郵件", "email")}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {renderField("principal_tel", "校長辦公室電話", "tel")}
        {renderField("principal_phone", "校長手機", "tel")}
      </div>
    </div>
  );

  // 渲染聯絡人資訊
  const renderContactInfo = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-md font-semibold">聯絡人列表</h4>
        <Button type="button" variant="outline" size="sm" onClick={addContact}>
          <Plus className="h-4 w-4 mr-2" />
          新增聯絡人
        </Button>
      </div>

      {formData.contact && formData.contact.length > 0 ? (
        <div className="space-y-4">
          {formData.contact.map((contact, index) => (
            <Card key={`contact-item-${contact.contact_sid || `new-${index}`}-${index}`} className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h5 className="font-size-sm font-medium">聯絡人 {index + 1}</h5>
                <Button type="button" variant="outline" size="sm" onClick={() => removeContact(index)} className="text-red-600 hover:text-red-700">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                {/* 第一行：基本資訊 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>聯絡人姓名</Label>
                    <Input
                      value={contact.contact_cname}
                      onChange={(e) => updateContact(index, "contact_cname", e.target.value)}
                      placeholder="請輸入聯絡人姓名"
                    />
                  </div>
                  <div className="space-y-2 ">
                    <Label>職稱</Label>
                    <Input
                      value={contact.contact_job_title}
                      onChange={(e) => updateContact(index, "contact_job_title", e.target.value)}
                      placeholder="請輸入職稱"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>聯絡人電子郵件</Label>
                    <Input
                      type="email"
                      value={contact.contact_email}
                      onChange={(e) => updateContact(index, "contact_email", e.target.value)}
                      placeholder="請輸入聯絡人電子郵件"
                      className={errors[`contact_${index}_email`] ? "border-red-500" : ""}
                    />
                    {errors[`contact_${index}_email`] && (
                      <Alert variant="destructive" className="py-2">
                        <AlertDescription>{errors[`contact_${index}_email`]}</AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
                {/* 第二行：聯絡方式 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>聯絡人電話</Label>
                    <Input
                      type="tel"
                      value={contact.contact_tel}
                      onChange={(e) => updateContact(index, "contact_tel", e.target.value)}
                      placeholder="請輸入聯絡人電話"
                      className={errors[`contact_${index}_tel`] ? "border-red-500" : ""}
                    />
                    {errors[`contact_${index}_tel`] && (
                      <Alert variant="destructive" className="py-2">
                        <AlertDescription>{errors[`contact_${index}_tel`]}</AlertDescription>
                      </Alert>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>聯絡人手機</Label>
                    <Input
                      type="tel"
                      value={contact.contact_phone}
                      onChange={(e) => updateContact(index, "contact_phone", e.target.value)}
                      placeholder="請輸入聯絡人手機"
                      className={errors[`contact_${index}_phone`] ? "border-red-500" : ""}
                    />
                    {errors[`contact_${index}_phone`] && (
                      <Alert variant="destructive" className="py-2">
                        <AlertDescription>{errors[`contact_${index}_phone`]}</AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Alert>
          <AlertDescription>尚未新增聯絡人資訊。點擊「新增聯絡人」按鈕開始添加。</AlertDescription>
        </Alert>
      )}
    </div>
  );

  // 渲染學校統計資料
  const renderSchoolStatistics = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>填寫日期</Label>
          <Input type="date" value={formData.statistics?.write_date || ""} onChange={(e) => handleStatisticsChange("write_date", e.target.value)} />
        </div>
        <div className="space-y-2">
          <Label>教職員總數</Label>
          <Input
            type="number"
            value={formData.statistics?.staff_total || ""}
            onChange={(e) => handleStatisticsChange("staff_total", e.target.value)}
            placeholder="請輸入教職員總數"
            min="0"
          />
        </div>
      </div>

      <div className="space-y-3">
        <h5 className="font-size-sm font-medium">國小學生人數</h5>
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          {[1, 2, 3, 4, 5, 6].map((grade, index) => (
            <div key={`elementary-grade-${grade}-${index}`} className="space-y-2">
              <Label>{grade}年級</Label>
              <Input
                type="number"
                value={formData.statistics?.[`elementary${grade}` as keyof SchoolStatistics] || ""}
                onChange={(e) => handleStatisticsChange(`elementary${grade}` as keyof SchoolStatistics, e.target.value)}
                placeholder="人數"
                min="0"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-3">
        <h5 className="font-size-sm font-medium">國中學生人數</h5>
        <div className="grid grid-cols-3 gap-4">
          {[7, 8, 9].map((grade, index) => (
            <div key={`middle-grade-${grade}-${index}`} className="space-y-2">
              <Label>{grade}年級</Label>
              <Input
                type="number"
                value={formData.statistics?.[`middle${grade}` as keyof SchoolStatistics] || ""}
                onChange={(e) => handleStatisticsChange(`middle${grade}` as keyof SchoolStatistics, e.target.value)}
                placeholder="人數"
                min="0"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-3">
        <h5 className="font-size-sm font-medium">高中學生人數</h5>
        <div className="grid grid-cols-3 gap-4">
          {[10, 11, 12].map((grade, index) => (
            <div key={`high-grade-${grade}-${index}`} className="space-y-2">
              <Label>{grade === 10 ? "1年級" : grade === 11 ? "2年級" : "3年級"}</Label>
              <Input
                type="number"
                value={formData.statistics?.[`hight${grade}` as keyof SchoolStatistics] || ""}
                onChange={(e) => handleStatisticsChange(`hight${grade}` as keyof SchoolStatistics, e.target.value)}
                placeholder="人數"
                min="0"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // 渲染政府/輔導人員基本資料
  const renderGovernmentTutorInfo = () => {
    // 獲取縣市名稱
    const getCityName = (citySid: number | string | undefined): string => {
      if (!citySid) return "未設定";
      const cityId = String(citySid);
      const city = cityOptions.find((option) => option.value === cityId);
      return city ? city.label : `縣市 ID: ${cityId}`;
    };

    // 渲染只讀欄位的輔助函數
    const renderReadOnlyField = (label: string, value: string) => {
      return (
        <div className="space-y-2">
          <Label htmlFor={label}>{label}</Label>
          <div className="min-h-[2.5rem] px-3 py-2 border border-input bg-muted/50 rounded-md font-size-sm">{value}</div>
        </div>
      );
    };

    return (
      <div className="space-y-4">
        {/* 第一行：負責縣市、所屬單位 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {renderReadOnlyField("負責縣市", getCityName(profile.city_sid))}
          {renderReadOnlyField("所屬單位", profile.place_cname || "未設定")}
        </div>

        {/* 第二行：職稱、中文姓名 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
          {renderField("job_cname", "職稱")}
          {renderField("member_cname", "中文姓名", "text", true)}
        </div>

        {/* 第三行：英文姓名、辦公室電話 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {renderField("member_cname_en", "英文姓名")}
          {renderField("member_tel", "辦公室電話", "tel")}
        </div>

        {/* 第四行：手機號碼、電子郵件 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {renderField("member_phone", "手機號碼", "tel")}
          {renderField("member_email", "電子郵件", "email", true)}
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-size-2xl ">基本資料維護</CardTitle>
            <CardDescription>完整管理您的基本資料，支援多層級資訊編輯</CardDescription>
          </div>
          <Badge variant={profile.member_role === "school" ? "default" : profile.member_role === "epa" ? "secondary" : "outline"}>
            {formatMemberRole(profile.member_role)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {profile.member_role === "school" ? (
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4 h-18">
                <TabsTrigger value="basic">基本資料</TabsTrigger>
                <TabsTrigger value="principal">校長資訊</TabsTrigger>
                <TabsTrigger value="contacts">聯絡人</TabsTrigger>
                <TabsTrigger value="statistics">學校統計</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <h3 className="font-size-lg font-semibold">學校基本資料</h3>
                <Separator />
                {renderSchoolBasicInfo()}
              </TabsContent>

              <TabsContent value="principal" className="space-y-4">
                <h3 className="font-size-lg font-semibold">校長資訊</h3>
                <Separator />
                {renderPrincipalInfo()}
              </TabsContent>

              <TabsContent value="contacts" className="space-y-4">
                <h3 className="font-size-lg font-semibold">聯絡人管理</h3>
                <Separator />
                {renderContactInfo()}
              </TabsContent>

              <TabsContent value="statistics" className="space-y-4">
                <h3 className="font-size-lg font-semibold">學校統計資料</h3>
                <Separator />
                {renderSchoolStatistics()}
              </TabsContent>
            </Tabs>
          ) : (
            <div className="space-y-4">
              <h3 className="font-size-lg font-semibold">基本資料</h3>
              <Separator />
              {renderGovernmentTutorInfo()}
            </div>
          )}

          {/* 操作按鈕 */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={loading} className="flex-1">
              {loading ? "更新中..." : hasChanges ? "儲存變更 *" : "儲存變更"}
            </Button>
            <Button type="button" variant="outline" onClick={handleReset} disabled={loading} className="flex-1">
              重置
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
