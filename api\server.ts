import express from "express";
import cors from "cors";
import { fileURLToPath } from "url";
import path from "path";
import { FileConfigManager } from "./config/config-manager.js";
import { initDatabase } from "./config/database-mssql.js";

import certificationRoutes from "./routes/certification.js";
import questionRoutes from "./routes/question.js";
import answerRoutes from "./routes/answer.js";
import fileRoutes from "./routes/file.js";
import templateAnswerRoutes from "./routes/template-answer.js";
import authRoutes from "./routes/auth.js";
import userRoutes from "./routes/user.js";
import simpleProfileRoutes from "./routes/simple-profile.js";
import dashboardRoutes from "./routes/dashboard.js";
import locationRoutes from "./routes/location.js";
import campusSubmissionsRoutes from "./routes/campus-submissions.js";
import certificateRoutes from "./routes/certificate.js";
import adminRoutes from "./routes/admin.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// 資料庫初始化已移至 database-mssql.ts 的 initDatabase 函數

/**
 * 初始化純文件配置管理器
 */
async function initializePureFileConfig() {
  try {
    const config = FileConfigManager.getInstance();

    // 確保配置已載入
    if (!config.isLoaded()) {
      throw new Error("配置載入失敗");
    }

    return config;
  } catch (error) {
    console.error("❌ 純文件配置初始化失敗:", error);
    throw error;
  }
}

/**
 * 設置中間件
 */
function setupMiddleware(config: FileConfigManager) {
  const serverConfig = config.getServerConfig();
  const uploadConfig = config.getUploadConfig();

  // CORS 設置
  app.use(
    cors({
      origin: serverConfig.allowedOrigins,
      credentials: true,
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization", "X-User-Token"],
      optionsSuccessStatus: 200,
    })
  );

  // URL 編碼設置（用於非檔案上傳的 POST 請求）
  app.use(
    express.urlencoded({
      extended: true,
      limit: `${uploadConfig.maxSize}mb`,
    })
  );

  // JSON 解析中間件（排除檔案上傳路由）
  app.use((req, res, next) => {
    // 檢查是否為檔案上傳請求
    const isFileUpload = req.path.includes("/upload") || req.path.includes("/photos") || req.path.includes("/attachments") || req.path.includes("/file");

    // 如果是檔案上傳請求，跳過 JSON 解析
    if (isFileUpload && req.headers["content-type"]?.includes("multipart/form-data")) {
      return next();
    }

    // 否則使用 JSON 解析
    express.json({
      limit: `${uploadConfig.maxSize}mb`,
    })(req, res, next);
  });

  // 靜態文件服務
  app.use(express.static(path.join(__dirname, "../public")));

  // 中間件設置完成
}

/**
 * 設置路由
 */
function setupRoutes() {
  // API 路由
  app.use("/api/certification", certificationRoutes);
  app.use("/api/question", questionRoutes);
  app.use("/api/answer", answerRoutes);
  app.use("/api/file", fileRoutes);
  app.use("/api/template-answer", templateAnswerRoutes);
  app.use("/api/auth", authRoutes);
  app.use("/api/user", userRoutes);
  app.use("/api/simple-profile", simpleProfileRoutes);
  app.use("/api/dashboard", dashboardRoutes);
  app.use("/api/location", locationRoutes);
  app.use("/api/campus-submissions", campusSubmissionsRoutes);
  app.use("/api/certificate", certificateRoutes);
  app.use("/api/admin", adminRoutes);

  // 健康檢查端點
  app.get("/api/health", (req, res) => {
    const config = FileConfigManager.getInstance();
    const summary = config.getConfigSummary();

    res.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      environment: config.getEnvironment(),
      configMode: "pure-file",
      database: summary.database,
      server: summary.server,
      uptime: process.uptime(),
    });
  });

  // 404 處理
  app.use("*", (req, res) => {
    res.status(404).json({ error: "Not Found" });
  });

  // 路由設置完成
}

/**
 * 啟動服務器
 */
async function startServer() {
  try {
    // 1. 初始化配置
    const config = await initializePureFileConfig();

    // 2. 設置中間件
    setupMiddleware(config);

    // 3. 初始化資料庫連接
    await initDatabase();

    // 4. 設置路由
    setupRoutes();

    // 5. 啟動 HTTP 服務器
    const serverConfig = config.getServerConfig();
    const databaseConfig = config.getDatabaseConfig();

    app.listen(serverConfig.port, () => {
      console.log(`🎉 EcoCampus API 服務器啟動成功！http://localhost:${serverConfig.port}`);
    });
  } catch (error) {
    console.error("❌ 服務器啟動失敗:", error);

    // 錯誤詳細信息
    if (error instanceof Error) {
      console.error("詳細信息:", error.message);
    }

    process.exit(1);
  }
}

/**
 * 優雅關閉
 */
const gracefulShutdown = (signal: string) => {
  console.log(`🛑 收到 ${signal} 信號，正在關閉服務器...`);

  // 配置管理器清理
  FileConfigManager.resetInstance();

  console.log("✅ 服務器已安全關閉");
  process.exit(0);
};

// 信號處理
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// 未捕獲的異常處理
process.on("uncaughtException", (error) => {
  console.error("💥 未捕獲的異常:", error);
  console.error("堆棧追踪:", error.stack);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("💥 未處理的 Promise 拒絕:", reason);
  console.error("Promise:", promise);
  process.exit(1);
});

// 純文件配置模式已為默認模式

// 啟動服務器
startServer();
