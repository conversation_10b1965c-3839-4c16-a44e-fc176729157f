import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  console.log(`🚀 Vite 配置載入中... 環境模式: ${mode}`);

  // 根據環境模式設定不同的配置
  const isLocal = mode === "development";
  const isDev = mode === "testing";
  const isProd = mode === "production";

  // 設定 base 路徑
  const base = isLocal ? "/" : "/apply/";

  // 設定 API 代理目標
  const apiTarget = isLocal ? "http://localhost:3001" : isDev ? "http://ecocampus-v2-apply-api.sumire.com.tw" : "https://ecocampus.moenv.gov.tw";

  // 設定端口
  const port = isLocal ? 8080 : isDev ? 8081 : 8082;

  return {
    base,

    server: {
      host: "::",
      port,
      proxy: {
        // 代理 API 請求到對應環境的後端服務器
        "/api": {
          target: apiTarget,
          changeOrigin: true,
          secure: !isLocal,
        },
      },
    },

    plugins: [
      react({
        jsxRuntime: "classic",
        jsxImportSource: undefined,
      }),
      // 只在本地開發模式啟用 componentTagger
      isLocal && componentTagger(),
    ].filter(Boolean),

    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },

    // 根據環境定義不同的環境變數
    define: {
      "import.meta.env.VITE_APP_MODE": JSON.stringify(mode),
      "import.meta.env.VITE_APP_ENV": JSON.stringify(isLocal ? "local" : isDev ? "dev" : "prod"),
      "import.meta.env.VITE_API_BASE_URL": JSON.stringify(
        isLocal ? "http://localhost:3001/api" : isDev ? "http://ecocampus-v2-apply-api.sumire.com.tw/api" : "https://ecocampus.moenv.gov.tw/apply-api"
      ),
    },

    // 構建配置
    build: {
      outDir: "dist",
      sourcemap: isLocal || isDev,
      minify: isProd,
      rollupOptions: {
        output: {
          manualChunks: isProd
            ? {
                vendor: ["react", "react-dom"],
                ui: ["@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu"],
                utils: ["axios", "date-fns", "zod"],
              }
            : undefined,
        },
      },
    },

    // 預覽配置
    preview: {
      port: 4173,
      host: true,
    },
  };
});
