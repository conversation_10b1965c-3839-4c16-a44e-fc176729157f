/**
 * Token 來源管理介面定義
 * 定義 Token 的取得、儲存和管理操作
 * 
 * @description 抽象化 Token 的來源，支援不同環境下的 Token 管理策略
 * @version 1.0.0
 */

/**
 * Token 儲存選項介面
 */
export interface TokenStorageOptions {
  /** 過期時間（秒），null 表示永不過期 */
  expiresIn?: number | null;
  /** 是否為安全 Token（影響儲存方式）*/
  secure?: boolean;
  /** 儲存域（用於區分不同應用）*/
  domain?: string;
  /** 額外的中繼資料 */
  metadata?: Record<string, any>;
}

/**
 * Token 資訊介面
 */
export interface TokenInfo {
  /** Token 值 */
  token: string;
  /** 建立時間 */
  createdAt: string | Date;
  /** 過期時間（如果有的話）*/
  expiresAt?: string | Date | null;
  /** Token 來源 */
  source: 'storage' | 'header' | 'cookie' | 'query' | 'body';
  /** 是否為安全 Token */
  secure?: boolean;
  /** 額外資訊 */
  metadata?: Record<string, any>;
}

/**
 * Token 來源基礎介面
 * 定義基本的 Token 操作
 */
export interface ITokenSource {
  /**
   * 取得 Token
   * @returns Token 字串或 null（如果不存在）
   */
  getToken(): string | null;
  
  /**
   * 設定 Token
   * @param token Token 字串
   * @param options 儲存選項（可選）
   */
  setToken(token: string, options?: TokenStorageOptions): void;
  
  /**
   * 移除 Token
   */
  removeToken(): void;
  
  /**
   * 檢查 Token 是否存在
   * @returns 是否有 Token
   */
  hasToken(): boolean;
}

/**
 * 增強型 Token 來源介面
 * 提供額外的 Token 管理功能
 */
export interface IEnhancedTokenSource extends ITokenSource {
  /**
   * 取得 Token 詳細資訊
   * @returns TokenInfo 物件或 null
   */
  getTokenInfo(): TokenInfo | null;
  
  /**
   * 檢查 Token 是否過期
   * @returns 是否過期
   */
  isTokenExpired(): boolean;
  
  /**
   * 取得 Token 剩餘有效時間（秒）
   * @returns 剩餘秒數，-1 表示永不過期，0 表示已過期
   */
  getTokenTimeToLive(): number;
  
  /**
   * 清除所有過期的 Token（如果支援多個 Token）
   */
  clearExpiredTokens?(): void;
  
  /**
   * 取得所有儲存的 Token（除錯用）
   * @returns Token 資訊陣列
   */
  getAllTokens?(): TokenInfo[];
}

/**
 * 前端 Token 來源介面
 * 專門處理瀏覽器環境中的 Token 管理
 */
export interface IFrontendTokenSource extends IEnhancedTokenSource {
  /**
   * 從 sessionStorage 取得 Token
   * @returns Token 或 null
   */
  getFromSessionStorage(): string | null;
  
  /**
   * 從 localStorage 取得 Token
   * @returns Token 或 null
   */
  getFromLocalStorage(): string | null;
  
  /**
   * 從 Cookie 取得 Token
   * @param cookieName Cookie 名稱
   * @returns Token 或 null
   */
  getFromCookie(cookieName?: string): string | null;
  
  /**
   * 設定到 sessionStorage
   * @param token Token 字串
   * @param options 儲存選項
   */
  setToSessionStorage(token: string, options?: TokenStorageOptions): void;
  
  /**
   * 設定到 localStorage
   * @param token Token 字串
   * @param options 儲存選項
   */
  setToLocalStorage(token: string, options?: TokenStorageOptions): void;
  
  /**
   * 設定到 Cookie
   * @param token Token 字串
   * @param cookieName Cookie 名稱
   * @param options 儲存選項
   */
  setToCookie(token: string, cookieName?: string, options?: TokenStorageOptions): void;
}

/**
 * 後端 Token 來源介面
 * 專門處理伺服器環境中的 Token 取得
 */
export interface IBackendTokenSource extends ITokenSource {
  /**
   * 從 HTTP Headers 取得 Token
   * @param headers Headers 物件
   * @param headerNames 可能的 Header 名稱陣列
   * @returns Token 或 null
   */
  getFromHeaders(headers: Record<string, string | string[]>, headerNames?: string[]): string | null;
  
  /**
   * 從 Authorization Header 取得 Bearer Token
   * @param headers Headers 物件
   * @returns Token 或 null
   */
  getFromAuthorizationHeader(headers: Record<string, string | string[]>): string | null;
  
  /**
   * 從自訂 Header 取得 Token
   * @param headers Headers 物件
   * @param headerName 自訂 Header 名稱
   * @returns Token 或 null
   */
  getFromCustomHeader(headers: Record<string, string | string[]>, headerName: string): string | null;
  
  /**
   * 從 Query Parameters 取得 Token
   * @param query Query 物件
   * @param paramName 參數名稱
   * @returns Token 或 null
   */
  getFromQuery?(query: Record<string, any>, paramName?: string): string | null;
  
  /**
   * 從 Request Body 取得 Token
   * @param body Body 物件
   * @param fieldName 欄位名稱
   * @returns Token 或 null
   */
  getFromBody?(body: Record<string, any>, fieldName?: string): string | null;
}

/**
 * Token 來源工廠介面
 * 用於建立不同類型的 Token 來源
 */
export interface ITokenSourceFactory {
  /**
   * 建立前端 Token 來源
   * @param preferredStorage 偏好的儲存方式
   * @returns 前端 Token 來源實例
   */
  createFrontendSource(preferredStorage?: 'session' | 'local' | 'cookie'): IFrontendTokenSource;
  
  /**
   * 建立後端 Token 來源
   * @param request 請求物件
   * @param options 建立選項
   * @returns 後端 Token 來源實例
   */
  createBackendSource(request: any, options?: TokenStorageOptions): IBackendTokenSource;
  
  /**
   * 建立適應性 Token 來源（自動偵測環境）
   * @param context 上下文物件
   * @returns 適當的 Token 來源實例
   */
  createAdaptiveSource(context?: any): ITokenSource | IFrontendTokenSource | IBackendTokenSource;
}

/**
 * Token 快取介面
 * 用於 Token 的快取管理
 */
export interface ITokenCache {
  /**
   * 取得快取的 Token
   * @param key 快取鍵值
   * @returns Token 或 null
   */
  get(key: string): Promise<string | null>;
  
  /**
   * 設定 Token 快取
   * @param key 快取鍵值
   * @param token Token 值
   * @param ttl 存活時間（秒）
   */
  set(key: string, token: string, ttl?: number): Promise<void>;
  
  /**
   * 刪除 Token 快取
   * @param key 快取鍵值
   */
  delete(key: string): Promise<void>;
  
  /**
   * 檢查快取是否存在
   * @param key 快取鍵值
   * @returns 是否存在
   */
  exists(key: string): Promise<boolean>;
  
  /**
   * 清除所有快取
   */
  clear(): Promise<void>;
  
  /**
   * 清除過期快取
   */
  clearExpired(): Promise<void>;
}