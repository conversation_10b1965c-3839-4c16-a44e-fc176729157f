import express from "express";
import multer from "multer";
import path from "path";
import { authenticateToken } from "../middleware/auth.js";
import { CampusSubmissionService } from "../services/campus-submission-service.js";
import { CampusSubmissionFileService } from "../services/campus-submission-file-service.js";
import {
  CampusSubmissionListQueryParams,
  CampusSubmissionDetailParams,
  CampusSubmissionListResponse,
  CampusSubmissionDetailResponse,
  CampusSubmissionDeleteResponse,
} from "../models/campus-submission.js";
import { DEFAULT_PAGINATION } from "../constants/campus-submission.js";
import { APILogger } from "../utils/logger.js";
import { FILE_ERROR_MESSAGES, FILE_SUCCESS_MESSAGES } from "../constants/file.js";

const router = express.Router();

// 獲取學校的校園投稿列表
router.get(
  "/",
  authenticateToken,
  async (req: express.Request<{}, CampusSubmissionListResponse, {}, CampusSubmissionListQueryParams>, res: express.Response<CampusSubmissionListResponse>) => {
    try {
      APILogger.logRequest(req, "CampusSubmissions", "獲取投稿列表");
      const token = req.headers["x-user-token"] as string;
      const limit = parseInt(req.query.limit as string) || DEFAULT_PAGINATION.LIMIT;
      const page = parseInt(req.query.page as string) || DEFAULT_PAGINATION.PAGE;

      // 獲取學校資訊
      const schoolInfo = await CampusSubmissionService.getSchoolInfoByToken(token);
      if (!schoolInfo) {
        return res.status(404).json({
          success: false,
          message: "找不到學校資料或您沒有權限",
          data: [],
          schoolInfo: { schoolId: 0, schoolName: "" },
          pagination: { page: 0, limit: 0, total: 0, totalPages: 0 },
        });
      }

      // 獲取投稿列表
      const { submissions, total } = await CampusSubmissionService.getCampusSubmissionList(schoolInfo.schoolId, page, limit);

      const totalPages = Math.ceil(total / limit);

      APILogger.logSuccess(
        "CampusSubmissions",
        "獲取投稿列表",
        {
          schoolId: schoolInfo.schoolId,
          count: submissions.length,
          total,
          page,
          totalPages,
        },
        "校園投稿列表獲取成功"
      );

      res.json({
        success: true,
        data: submissions,
        schoolInfo: {
          schoolId: schoolInfo.schoolId,
          schoolName: schoolInfo.schoolName,
        },
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
        message: "校園投稿列表獲取成功",
      });
    } catch (error: unknown) {
      APILogger.logError("CampusSubmissions", "獲取投稿列表", error, 500);
      res.status(500).json({
        success: false,
        message: "獲取校園投稿列表時發生錯誤",
        data: [],
        schoolInfo: { schoolId: 0, schoolName: "" },
        pagination: { page: 0, limit: 0, total: 0, totalPages: 0 },
      });
    }
  }
);

// 獲取單一校園投稿詳細資料
router.get("/:id", authenticateToken, async (req: express.Request<CampusSubmissionDetailParams>, res: express.Response<CampusSubmissionDetailResponse>) => {
  try {
    APILogger.logRequest(req, "CampusSubmissions", "獲取投稿詳情");
    const token = req.headers["x-user-token"] as string;
    const submissionId = req.params.id;

    // 獲取學校資訊
    const schoolInfo = await CampusSubmissionService.getSchoolInfoByToken(token);
    if (!schoolInfo) {
      return res.status(404).json({
        success: false,
        message: "找不到學校資料或您沒有權限",
        data: {} as any,
      });
    }

    // 獲取投稿詳情
    const submissionDetail = await CampusSubmissionService.getCampusSubmissionDetail(submissionId, schoolInfo.schoolId);

    if (!submissionDetail) {
      return res.status(404).json({
        success: false,
        message: "找不到指定的投稿記錄",
        data: {} as any,
      });
    }

    APILogger.logSuccess(
      "CampusSubmissions",
      "獲取投稿詳情",
      {
        submissionId,
        schoolId: schoolInfo.schoolId,
        hasAttachments: (submissionDetail.attachments?.length || 0) > 0,
      },
      "校園投稿詳情獲取成功"
    );

    res.json({
      success: true,
      data: submissionDetail,
      message: "校園投稿詳情獲取成功",
    });
  } catch (error: unknown) {
    APILogger.logError("CampusSubmissions", "獲取投稿詳情", error, 500);
    res.status(500).json({
      success: false,
      message: "獲取校園投稿詳情時發生錯誤",
      data: {} as any,
    });
  }
});

// 軟刪除校園投稿
router.delete("/:id", authenticateToken, async (req: express.Request<CampusSubmissionDetailParams>, res: express.Response<CampusSubmissionDeleteResponse>) => {
  try {
    APILogger.logRequest(req, "CampusSubmissions", "刪除投稿");
    const token = req.headers["x-user-token"] as string;
    const submissionId = req.params.id;

    // 獲取學校資訊
    const schoolInfo = await CampusSubmissionService.getSchoolInfoByToken(token);
    if (!schoolInfo) {
      return res.status(404).json({
        success: false,
        message: "找不到學校資料或您沒有權限",
      });
    }

    // 刪除投稿
    const deleteResult = await CampusSubmissionService.deleteCampusSubmission(submissionId, schoolInfo.schoolId, schoolInfo.AccountId);

    if (!deleteResult.canDelete) {
      return res.status(400).json({
        success: false,
        message: deleteResult.message,
      });
    }

    APILogger.logSuccess(
      "CampusSubmissions",
      "刪除投稿",
      {
        submissionId,
        schoolId: schoolInfo.schoolId,
        title: deleteResult.submission?.Title,
      },
      "校園投稿刪除成功"
    );

    res.json({
      success: true,
      message: deleteResult.message,
    });
  } catch (error: unknown) {
    APILogger.logError("CampusSubmissions", "刪除投稿", error, 500);
    res.status(500).json({
      success: false,
      message: "刪除校園投稿時發生錯誤",
    });
  }
});

// 配置 multer 用於檔案上傳
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "./temp"); // 臨時目錄
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({ storage });

// 校園投稿照片上傳端點
router.post(
  "/:submissionId/photos",
  authenticateToken,
  upload.array("photos", 10), // 最多10張照片
  async (req: express.Request<{ submissionId: string }>, res: express.Response) => {
    try {
      APILogger.logRequest(req, "CampusSubmissions", "上傳照片");
      const token = req.headers["x-user-token"] as string;
      const submissionId = req.params.submissionId;
      const files = req.files as Express.Multer.File[];

      // 獲取學校資訊（暫時跳過，用於測試）
      let schoolInfo = null;
      if (token) {
        schoolInfo = await CampusSubmissionService.getSchoolInfoByToken(token);
        if (!schoolInfo) {
          return res.status(404).json({
            success: false,
            message: "找不到學校資料或您沒有權限",
            data: [],
          });
        }
      }

      // 處理照片上傳
      const photos = await CampusSubmissionFileService.processPhotoUpload(files, submissionId);

      APILogger.logSuccess(
        "CampusSubmissions",
        "上傳照片",
        {
          submissionId,
          schoolId: schoolInfo?.schoolId || "unknown",
          photoCount: photos.length,
        },
        "校園投稿照片上傳成功"
      );

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.FILE_UPLOADED,
        data: photos,
      });
    } catch (error: unknown) {
      APILogger.logError("CampusSubmissions", "上傳照片", error, 500);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
        data: [],
      });
    }
  }
);

// 校園投稿附件上傳端點
router.post(
  "/:submissionId/attachments",
  authenticateToken,
  upload.array("attachments", 5), // 最多5個附件
  async (req: express.Request<{ submissionId: string }>, res: express.Response) => {
    try {
      APILogger.logRequest(req, "CampusSubmissions", "上傳附件");
      const token = req.headers["x-user-token"] as string;
      const submissionId = req.params.submissionId;
      const files = req.files as Express.Multer.File[];

      // 獲取學校資訊（暫時跳過，用於測試）
      let schoolInfo = null;
      if (token) {
        schoolInfo = await CampusSubmissionService.getSchoolInfoByToken(token);
        if (!schoolInfo) {
          return res.status(404).json({
            success: false,
            message: "找不到學校資料或您沒有權限",
            data: [],
          });
        }
      }

      // 處理附件上傳
      const attachments = await CampusSubmissionFileService.processAttachmentUpload(files, submissionId);

      APILogger.logSuccess(
        "CampusSubmissions",
        "上傳附件",
        {
          submissionId,
          schoolId: schoolInfo?.schoolId || "unknown",
          attachmentCount: attachments.length,
        },
        "校園投稿附件上傳成功"
      );

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.FILE_UPLOADED,
        data: attachments,
      });
    } catch (error: unknown) {
      APILogger.logError("CampusSubmissions", "上傳附件", error, 500);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
        data: [],
      });
    }
  }
);

// 刪除校園投稿照片端點
router.delete(
  "/:submissionId/photos/:filename",
  authenticateToken,
  async (req: express.Request<{ submissionId: string; filename: string }>, res: express.Response) => {
    try {
      APILogger.logRequest(req, "CampusSubmissions", "刪除照片");
      const token = req.headers["x-user-token"] as string;
      const { submissionId, filename } = req.params;

      // 獲取學校資訊
      const schoolInfo = await CampusSubmissionService.getSchoolInfoByToken(token);
      if (!schoolInfo) {
        return res.status(404).json({
          success: false,
          message: "找不到學校資料或您沒有權限",
        });
      }

      // 刪除照片
      await CampusSubmissionFileService.deletePhoto(filename);

      APILogger.logSuccess(
        "CampusSubmissions",
        "刪除照片",
        {
          submissionId,
          schoolId: schoolInfo.schoolId,
          filename,
        },
        "校園投稿照片刪除成功"
      );

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.FILE_DELETED,
      });
    } catch (error: unknown) {
      APILogger.logError("CampusSubmissions", "刪除照片", error, 500);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
      });
    }
  }
);

// 刪除校園投稿附件端點
router.delete(
  "/:submissionId/attachments/:filename",
  authenticateToken,
  async (req: express.Request<{ submissionId: string; filename: string }>, res: express.Response) => {
    try {
      APILogger.logRequest(req, "CampusSubmissions", "刪除附件");
      const token = req.headers["x-user-token"] as string;
      const { submissionId, filename } = req.params;

      // 獲取學校資訊
      const schoolInfo = await CampusSubmissionService.getSchoolInfoByToken(token);
      if (!schoolInfo) {
        return res.status(404).json({
          success: false,
          message: "找不到學校資料或您沒有權限",
        });
      }

      // 刪除附件
      await CampusSubmissionFileService.deleteAttachment(filename);

      APILogger.logSuccess(
        "CampusSubmissions",
        "刪除附件",
        {
          submissionId,
          schoolId: schoolInfo.schoolId,
          filename,
        },
        "校園投稿附件刪除成功"
      );

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.FILE_DELETED,
      });
    } catch (error: unknown) {
      APILogger.logError("CampusSubmissions", "刪除附件", error, 500);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
      });
    }
  }
);

// TODO: 其他端點需要後續實現
// - POST /     創建校園投稿
// - PUT /:id   更新校園投稿
// - POST /:id/submit  送審投稿
// - POST /:id/withdraw 撤回投稿

export default router;
