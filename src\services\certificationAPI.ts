import { BaseService, BaseApiResponse, BaseServiceConfig } from "./BaseService";
import { getCurrentEnvironment, isFeatureEnabled, getApiBaseUrl } from "../config/environment";

// 認證申請 API 服務層
export interface CertificationData {
  sid?: number;
  member_sid: number;
  level: number; // 1=銅牌, 2=銀牌, 3=綠旗, 4=綠旗R1, 5=綠旗R2, 6=綠旗R3
  review: "審核中" | "通過" | "退件" | "補件" | "尚未審核";
  ReviewStatus?: number; // 0=審核中, 1=已通過, 2=已退件, 3=待補件, 4=尚未審核
  reviewdate?: string;
  passdate?: string;
  returndate?: string;
  additionaldate?: string;
  ApplyDate?: string; // 申請日期
  certificate_sid?: number;
  is_del: number;
  add_type: "front" | "backend";
  createdate: number;
  updatedate: number;
  CreatedTime?: string; // 創建時間（ISO 格式）
  UpdatedTime?: string; // 更新時間（ISO 格式）
}

// 認證列表項目類型
export interface CertificationListItem {
  id: string;
  certificationType: string;
  level: number;
  status: string;
  statusInfo: {
    label: string;
    icon: string;
    description: string;
    color: string;
    bgColor: string;
  };
  typeInfo: {
    name: string;
    fullName: string;
    level: number;
    isRenewal: boolean;
    icon: string;
  };
  applicantName: string;
  applyDate: string;
  reviewDate?: string;
  passDate?: string;
  expiredDate?: string;
  certificateNumber?: string;
  isEditable: boolean;
  isDeletable: boolean;
}

// 認證可用性類型
export interface CertificationAvailability {
  id: string;
  name: string;
  level: number;
  available: boolean;
  reason: string;
  frontendId: string;
}

export interface QuestionData {
  sid: number;
  parent_sid: number;
  title: string;
  step: number; // 1-9
  sequence: number;
  is_use: number;
  is_renew: number; // -1=初次, 0=通用, 1=更新
  question_tpl: number;
  lan: string;
  createdate: number;
  updatedate: number;
}

export interface AnswerRecord {
  sid?: number;
  certification_answer_sid: number;
  answer_json: string; // JSON格式的答案內容
  opinion?: string; // 審核意見
  status: "未完成" | "已完成" | "待補件" | "退件" | "已填寫";
  sequence: number;
  createdate: number;
  updatedate: number;
}

// Add new interfaces for the missing types
export interface CertificationStep {
  step: number;
  title: string;
  description: string;
  questionCount: number;
  isCompleted: boolean;
  questions?: QuestionData[];
}

export interface FormQuestionConfig {
  questions: QuestionData[];
  steps: CertificationStep[];
}

export interface ProgressData {
  completedSteps: number;
  totalSteps: number;
  completedQuestions: number;
  totalQuestions: number;
  progressPercentage: number;
}

export interface ApiResponse<T> extends BaseApiResponse<T> {}

// API 配置 (繼承自 BaseServiceConfig)
interface ApiConfig extends BaseServiceConfig {
  environment?: string;
}

class CertificationAPIService extends BaseService {
  private readonly environment: string;

  constructor(config?: Partial<ApiConfig>) {
    const currentEnv = getCurrentEnvironment();

    super({
      ...config,
      debugMode: config?.debugMode ?? isFeatureEnabled("debugMode"),
    });

    this.environment = config?.environment || currentEnv.name;
  }

  // 認證管理
  async getCertification(certificationId: number): Promise<ApiResponse<CertificationData>> {
    return this.request<CertificationData>(`/certification/${certificationId}`);
  }

  async createCertification(data: Partial<CertificationData>): Promise<ApiResponse<CertificationData>> {
    return this.request<CertificationData>("/certification", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateCertification(certificationId: number, data: Partial<CertificationData>): Promise<ApiResponse<CertificationData>> {
    return this.request<CertificationData>(`/certification/${certificationId}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async submitForReview(certificationId: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/certification/${certificationId}/submit`, {
      method: "POST",
    });
  }

  // 認證步驟和表單
  async getCertificationSteps(): Promise<ApiResponse<CertificationStep[]>> {
    return this.request<CertificationStep[]>("/question/certification-steps");
  }

  async getFormQuestions(): Promise<ApiResponse<FormQuestionConfig>> {
    return this.request<FormQuestionConfig>("/question/form-questions");
  }

  // 問題管理
  async getQuestionsByStep(step: number, certificationLevel: number): Promise<ApiResponse<QuestionData[]>> {
    return this.request<QuestionData[]>(`/question?step=${step}&level=${certificationLevel}`);
  }

  async getQuestionConfig(step: number, questionIndex: number, questionId: number): Promise<ApiResponse<QuestionData>> {
    return this.request<QuestionData>(`/question/config?step=${step}&question_index=${questionIndex}&question_sid=${questionId}`);
  }

  // 答案管理
  async getAnswer(certificationId: number, questionId: number): Promise<ApiResponse<AnswerRecord>> {
    return this.request<AnswerRecord>(`/answer/certification?certification_sid=${certificationId}&question_sid=${questionId}`);
  }

  async getAnswers(certificationId: number): Promise<ApiResponse<AnswerRecord[]>> {
    return this.request<AnswerRecord[]>(`/answer/certification/${certificationId}`);
  }

  async saveAnswer(certificationId: number, questionId: number, answerData: Record<string, unknown>): Promise<ApiResponse<AnswerRecord>> {
    return this.request<AnswerRecord>("/answer/save", {
      method: "POST",
      body: JSON.stringify({
        certification_sid: certificationId,
        question_sid: questionId,
        answer_json: JSON.stringify(answerData),
        status: "已填寫",
      }),
    });
  }

  // 保存認證問題答案 (匹配前端使用方式)
  async saveCertificationAnswer(data: { certificationId: number; questionId: number; answerData: Record<string, unknown>; templateId?: number }): Promise<
    ApiResponse<{
      answerId: number;
      questionId: number;
      templateId: number;
      message: string;
    }>
  > {
    return this.request<{
      answerId: number;
      questionId: number;
      templateId: number;
      message: string;
    }>("/answer/save", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateAnswerStatus(certificationId: number, questionId: number, status: AnswerRecord["status"]): Promise<ApiResponse<void>> {
    return this.request<void>("/answers/status", {
      method: "PUT",
      body: JSON.stringify({
        certification_sid: certificationId,
        question_sid: questionId,
        status,
      }),
    });
  }

  // 認證檔案上傳 (特化版本)
  async uploadCertificationFile(
    file: File,
    fileType: string,
    certificationId: number,
    questionId: number
  ): Promise<ApiResponse<{ fileUrl: string; fileName: string }>> {
    const additionalData = {
      file_type: fileType,
      certification_sid: certificationId.toString(),
      question_sid: questionId.toString(),
    };

    return this.uploadFile<{ fileUrl: string; fileName: string }>("/files/upload", file, additionalData);
  }

  async deleteFile(fileId: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/files/${fileId}`, {
      method: "DELETE",
    });
  }

  // 通用檔案上傳（單一檔案）
  async uploadSingleFile(file: File): Promise<
    ApiResponse<{
      id: string;
      url: string;
      fileName: string;
      fileType: string;
      fileSize: number;
    }>
  > {
    return this.uploadFile<{
      id: string;
      url: string;
      fileName: string;
      fileType: string;
      fileSize: number;
    }>("/file/upload-single", file);
  }

  // 自然人憑證綁定
  async bindNaturalPersonCertificate(data: {
    certificateInfo: {
      subject: string;
      issuer: string;
      serialNumber: string;
      notBefore: string;
      notAfter: string;
      fingerprint: string;
    };
    accountId?: string;
  }): Promise<ApiResponse<{ message: string; bindingId: string }>> {
    return this.request<{ message: string; bindingId: string }>("/certificate/bind", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 驗證相關
  async validateAnswer(
    certificationId: number,
    questionId: number,
    answerData: Record<string, unknown>
  ): Promise<ApiResponse<{ isValid: boolean; errors: Record<string, string> }>> {
    return this.request<{ isValid: boolean; errors: Record<string, string> }>("/answers/validate", {
      method: "POST",
      body: JSON.stringify({
        certification_sid: certificationId,
        question_sid: questionId,
        answer_data: answerData,
      }),
    });
  }

  // 特殊動態表單處理
  async getDynamicFormOptions(questionId: number, certificationId: number): Promise<ApiResponse<unknown>> {
    return this.request<unknown>(`/question/dynamic-options/${questionId}?certification_sid=${certificationId}`);
  }

  // 進度追蹤
  async getCertificationProgress(certificationId: number): Promise<ApiResponse<ProgressData>> {
    return this.request<ProgressData>(`/certification/${certificationId}/progress`);
  }

  // 審核歷程
  async getReviewHistory(certificationId: number): Promise<ApiResponse<AnswerRecord[]>> {
    return this.request<AnswerRecord[]>(`/answer/review-history/${certificationId}`);
  }

  // 證書生成
  async generateCertificate(certificationId: number): Promise<ApiResponse<{ certificateUrl: string }>> {
    return this.request<{ certificateUrl: string }>(`/certification/${certificationId}/certificate`, {
      method: "POST",
    });
  }

  // 認證列表管理
  async getCertificationList(): Promise<
    ApiResponse<{
      all: CertificationListItem[];
      drafts: CertificationListItem[];
      pending: CertificationListItem[];
      passed: CertificationListItem[];
      statistics: {
        total: number;
        drafts: number;
        pending: number;
        passed: number;
        inReview: number;
        returned: number;
      };
    }>
  > {
    return this.request<{
      all: CertificationListItem[];
      drafts: CertificationListItem[];
      pending: CertificationListItem[];
      passed: CertificationListItem[];
      statistics: {
        total: number;
        drafts: number;
        pending: number;
        passed: number;
        inReview: number;
        returned: number;
      };
    }>("/certification/list");
  }

  // 檢查認證可用性
  async getCertificationAvailability(): Promise<
    ApiResponse<{
      availability: CertificationAvailability[];
    }>
  > {
    return this.request<{
      availability: CertificationAvailability[];
    }>("/certification/availability");
  }

  // 創建認證申請
  async createCertificationApplication(data: { certificationType: string; level: number }): Promise<
    ApiResponse<{
      certificationId: number;
      message: string;
    }>
  > {
    return this.request<{
      certificationId: number;
      message: string;
    }>("/certification/create", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 刪除認證申請
  async deleteCertification(certificationId: string): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.request<{
      message: string;
    }>(`/certification/${certificationId}`, {
      method: "DELETE",
    });
  }

  // 提交認證送審
  async submitCertificationForReview(certificationId: string): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.request<{
      message: string;
    }>(`/certification/${certificationId}/submit`, {
      method: "POST",
      body: JSON.stringify({
        certificationId: certificationId,
      }),
    });
  }

  // 管理員權限驗證
  async verifyAdminPermission(token: string): Promise<
    ApiResponse<{
      isAdmin: boolean;
      userRole: string;
    }>
  > {
    return this.request<{
      isAdmin: boolean;
      userRole: string;
    }>("/auth/verify-admin", {
      method: "POST",
      body: JSON.stringify({ token }),
    });
  }

  // 管理員標記答案狀態
  async markAnswerStatus(data: { certificationId: number; questionId: string; status: string; action: string }): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.request<{
      message: string;
    }>("/admin/mark-answer-status", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 保存評審意見
  async saveReviewComment(data: { certificationId: number; stepId: string; comment: string }): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.request<{
      message: string;
    }>("/admin/save-review-comment", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 獲取評審意見
  async getReviewComments(certificationId: string): Promise<
    ApiResponse<
      Array<{
        CertificationStepRecordId: number;
        StepNumber: number;
        Comment: string;
        CreatedTime: string;
        AdminUsername: string;
      }>
    >
  > {
    return this.request<
      Array<{
        CertificationStepRecordId: number;
        StepNumber: number;
        Comment: string;
        CreatedTime: string;
        AdminUsername: string;
      }>
    >(`/admin/review-comments/${certificationId}`);
  }

  // 模板答案儲存（新增）
  async saveTemplateAnswer(
    certificationId: number,
    questionId: number,
    templateId: number,
    answerData: Record<string, unknown>,
    questionTitle?: string
  ): Promise<
    ApiResponse<{
      certification_sid: number;
      question_sid: number;
      template_id: number;
      question_title: string;
      answer_json: string;
      raw_answer_data: Record<string, unknown>;
      validation_result: { isValid: boolean; errors: string[] };
      timestamp: string;
      status: string;
    }>
  > {
    return this.request<{
      certification_sid: number;
      question_sid: number;
      template_id: number;
      question_title: string;
      answer_json: string;
      raw_answer_data: Record<string, unknown>;
      validation_result: { isValid: boolean; errors: string[] };
      timestamp: string;
      status: string;
    }>("/template-answers/save", {
      method: "POST",
      body: JSON.stringify({
        certification_sid: certificationId,
        question_sid: questionId,
        template_id: templateId,
        answer_data: answerData,
        question_title: questionTitle,
      }),
    });
  }

  // 驗證模板答案格式（新增）
  async validateTemplateAnswer(templateId: number, answerData: Record<string, unknown>): Promise<ApiResponse<{ isValid: boolean; errors: string[] }>> {
    return this.request<{ isValid: boolean; errors: string[] }>("/template-answers/validate", {
      method: "POST",
      body: JSON.stringify({
        template_id: templateId,
        answer_data: answerData,
      }),
    });
  }
}

// 單例模式
export const certificationAPI = new CertificationAPIService();

// 環境配置函數
export const configureAPI = () => {
  // API 已經配置為使用實際後端
  const backendUrl = getApiBaseUrl();

  console.log("🌐 使用實際後端API:", backendUrl);

  return certificationAPI;
};

// 導出類型和服務
export default certificationAPI;

// 輔助函數
export const mapCertificationLevelToNumber = (level: string): number => {
  const mapping: Record<string, number> = {
    bronze: 1,
    silver: 2,
    green_flag: 3,
    green_flag_r1: 4,
    green_flag_r2: 5,
    green_flag_r3: 6,
  };
  return mapping[level] || 1;
};

export const mapNumberToCertificationLevel = (level: number): string => {
  const mapping: Record<number, string> = {
    1: "bronze",
    2: "silver",
    3: "green_flag",
    4: "green_flag_r1",
    5: "green_flag_r2",
    6: "green_flag_r3",
  };
  return mapping[level] || "bronze";
};

// 驗證規則
export const ValidationRules = {
  required: (value: unknown): boolean => {
    return value !== null && value !== undefined && value !== "";
  },

  minLength:
    (min: number) =>
    (value: string): boolean => {
      return value && value.length >= min;
    },

  maxLength:
    (max: number) =>
    (value: string): boolean => {
      return value && value.length <= max;
    },

  fileType:
    (allowedTypes: string[]) =>
    (file: File): boolean => {
      return allowedTypes.includes(file.type);
    },

  pathRequirement:
    (level: string) =>
    (paths: unknown[]): boolean => {
      const coreEnvironments = ["交通", "氣候變遷", "消耗與廢棄物", "永續食物"];
      const pathCount = paths.length;
      const corePathCount = paths.filter((path: { path: string }) => coreEnvironments.includes(path.path)).length;

      switch (level) {
        case "bronze":
          return pathCount >= 1;
        case "silver":
          return pathCount >= 2 && corePathCount >= 1;
        case "green_flag":
        default:
          return pathCount >= 3 && corePathCount >= 1;
      }
    },
};
