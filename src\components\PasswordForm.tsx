import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle2 } from "lucide-react";
import authService from "@/services/authService";

// 表單資料介面
interface PasswordFormData {
  oldPassword: string;
  newPassword: string;
  repeatPassword: string;
}

// 修改密碼表單組件
const PasswordForm = ({ onSuccess }: { onSuccess?: () => void }) => {
  const [showOld, setShowOld] = useState(false);
  const [showNew, setShowNew] = useState(false);
  const [showRepeat, setShowRepeat] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<PasswordFormData>();

  const newPassword = watch("newPassword");

  // 密碼強度驗證
  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return "密碼必須至少8個字元";
    }
    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/.test(password)) {
      return "密碼必須包含大寫字母、小寫字母和數字";
    }
    return true;
  };

  // 處理成功確認
  const handleSuccessConfirm = () => {
    setIsSuccess(false);
    setMessage(null);
    reset();
    onSuccess?.();
  };

  // 提交表單
  const onSubmit = async (data: PasswordFormData) => {
    setLoading(true);
    setMessage(null);

    try {
      console.log("提交修改密碼表單");

      // 檢查新密碼和確認密碼是否一致
      if (data.newPassword !== data.repeatPassword) {
        setMessage({
          type: "error",
          text: "新密碼和確認密碼不一致",
        });
        setLoading(false);
        return;
      }

      // 呼叫 API 修改密碼
      const result = await authService.changePassword(data.oldPassword, data.newPassword);

      if (result.success) {
        setMessage({
          type: "success",
          text: result.message || "密碼修改成功！",
        });
        setIsSuccess(true);
        console.log("密碼修改成功");
      } else {
        setMessage({
          type: "error",
          text: result.message,
        });
        console.log("密碼修改失敗:", result.message);
      }
    } catch (error) {
      console.error("修改密碼時發生錯誤:", error);
      setMessage({
        type: "error",
        text: "修改密碼時發生未知錯誤，請稍後再試",
      });
    } finally {
      setLoading(false);
    }
  };

  // 如果修改成功，顯示成功確認界面
  if (isSuccess) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
          <CheckCircle2 className="h-8 w-8 text-green-600" />
        </div>

        <h3 className="font-size-lg font-semibold text-green-800 mb-2">密碼修改成功！</h3>

        <p className="font-size-sm text-gray-600 text-center mb-6">
          您的密碼已成功更新。
          <br />
          請使用新密碼進行下次登入。
        </p>

        <Button onClick={handleSuccessConfirm} className="w-full bg-[#38513A] font-size-lg hover:bg-[#2d4330]" aria-label="確認並關閉">
          確認
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} aria-label="修改密碼表單" tabIndex={0}>
      <div className="flex flex-col gap-6">
        {/* 舊密碼 */}
        <div>
          <label htmlFor="oldPassword" className="font-bold">
            <span className="text-destructive">*</span>舊密碼
          </label>
          <div className="relative">
            <Input
              id="oldPassword"
              type={showOld ? "text" : "password"}
              aria-required="true"
              className="pr-24"
              disabled={loading}
              {...register("oldPassword", {
                required: "請輸入舊密碼",
              })}
            />
            <label className="absolute right-3 top-2 flex items-center gap-2 cursor-pointer select-none font-size-sm">
              <input type="checkbox" className="mr-1" checked={showOld} onChange={() => setShowOld((v) => !v)} aria-label="顯示密碼" disabled={loading} />
              顯示密碼
            </label>
          </div>
          {errors.oldPassword && <p className="font-size-sm text-destructive mt-1">{errors.oldPassword.message}</p>}
        </div>

        {/* 新密碼 */}
        <div>
          <label htmlFor="newPassword" className="font-bold">
            <span className="text-destructive">*</span>新密碼
          </label>
          <div className="relative">
            <Input
              id="newPassword"
              type={showNew ? "text" : "password"}
              aria-required="true"
              className="pr-24"
              disabled={loading}
              {...register("newPassword", {
                required: "請輸入新密碼",
                validate: validatePassword,
              })}
              aria-describedby="newpass-tip"
            />
            <label className="absolute right-3 top-2 flex items-center gap-2 cursor-pointer select-none font-size-sm">
              <input type="checkbox" className="mr-1" checked={showNew} onChange={() => setShowNew((v) => !v)} aria-label="顯示密碼" disabled={loading} />
              顯示密碼
            </label>
          </div>
          <span id="newpass-tip" className="block font-size-xs text-muted-foreground mt-1">
            密碼必須包含：至少8個字元、大寫與小寫字母、數字
          </span>
          {errors.newPassword && <p className="font-size-sm text-destructive mt-1">{errors.newPassword.message}</p>}
        </div>

        {/* 密碼確認 */}
        <div>
          <label htmlFor="repeatPassword" className="font-bold">
            <span className="text-destructive">*</span>密碼確認
          </label>
          <div className="relative">
            <Input
              id="repeatPassword"
              type={showRepeat ? "text" : "password"}
              aria-required="true"
              className="pr-24"
              disabled={loading}
              {...register("repeatPassword", {
                required: "請再次輸入新密碼",
                validate: (value) => value === newPassword || "密碼確認不一致",
              })}
            />
            <label className="absolute right-3 top-2 flex items-center gap-2 cursor-pointer select-none font-size-sm">
              <input type="checkbox" className="mr-1" checked={showRepeat} onChange={() => setShowRepeat((v) => !v)} aria-label="顯示密碼" disabled={loading} />
              顯示密碼
            </label>
          </div>
          {errors.repeatPassword && <p className="font-size-sm text-destructive mt-1">{errors.repeatPassword.message}</p>}
        </div>
      </div>

      {/* 錯誤訊息顯示 */}
      {message && message.type === "error" && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* 提交按鈕 */}
      <Button type="submit" disabled={loading} className="w-full mt-6 bg-[#38513A] font-size-lg hover:bg-[#2d4330]" aria-label="確認修改">
        {loading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            修改中...
          </>
        ) : (
          "確認修改"
        )}
      </Button>
    </form>
  );
};

export default PasswordForm;
