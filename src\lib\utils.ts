import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
/*
🧾 那這個 utils.ts 是幹嘛的？
根據內容判斷，你的 utils.ts 裡面有一個很常見的工具函數：cn()


export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(...inputs))
}
這是專門幫 Tailwind 使用者解決「className 衝突處理」的合併工具。

🔧 它整合了這些兩個套件：
clsx：條件式 className 合併工具

tailwind-merge：針對 Tailwind 的 class 衝突判斷器

✅ 用法範例

cn("bg-red-500", condition && "bg-green-500")
// 如果 condition 是 true → 結果會是 "bg-green-500"
// 如果是 false → 結果是 "bg-red-500"
這比純 clsx 更強：tailwind-merge 會確保「後者覆蓋前者」，不會出現兩個 bg-xxx 共存的問題。
*/
