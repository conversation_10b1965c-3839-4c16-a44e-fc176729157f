/**
 * 認證系統監控指標模組
 * 追蹤 Token 驗證成功率、API 回應時間、錯誤率等關鍵指標
 */

interface MetricData {
  timestamp: Date;
  value: number;
  metadata?: Record<string, any>;
}

interface AuthMetrics {
  tokenValidationSuccess: number;
  tokenValidationFailure: number;
  apiResponseTimes: number[];
  errorCount: number;
  totalRequests: number;
  lastReset: Date;
}

export class AuthMonitor {
  private static instance: AuthMonitor;
  private metrics: AuthMetrics;
  private readonly maxResponseTimes = 1000; // 保留最近 1000 次回應時間
  private errorRateThreshold: number;
  private autoRollbackEnabled: boolean;

  private constructor() {
    this.metrics = this.initializeMetrics();
    this.errorRateThreshold = parseInt(process.env.ERROR_RATE_THRESHOLD || '5');
    this.autoRollbackEnabled = process.env.AUTO_ROLLBACK_ENABLED === 'true';
  }

  /**
   * 獲取單例實例
   */
  public static getInstance(): AuthMonitor {
    if (!AuthMonitor.instance) {
      AuthMonitor.instance = new AuthMonitor();
    }
    return AuthMonitor.instance;
  }

  /**
   * 初始化指標
   */
  private initializeMetrics(): AuthMetrics {
    return {
      tokenValidationSuccess: 0,
      tokenValidationFailure: 0,
      apiResponseTimes: [],
      errorCount: 0,
      totalRequests: 0,
      lastReset: new Date()
    };
  }

  /**
   * 記錄 Token 驗證成功
   */
  public recordTokenValidationSuccess(): void {
    this.metrics.tokenValidationSuccess++;
    this.checkMetrics();
  }

  /**
   * 記錄 Token 驗證失敗
   */
  public recordTokenValidationFailure(): void {
    this.metrics.tokenValidationFailure++;
    this.metrics.errorCount++;
    this.checkMetrics();
  }

  /**
   * 記錄 API 回應時間
   */
  public recordApiResponseTime(responseTime: number): void {
    this.metrics.apiResponseTimes.push(responseTime);
    
    // 保持陣列大小在限制內
    if (this.metrics.apiResponseTimes.length > this.maxResponseTimes) {
      this.metrics.apiResponseTimes.shift();
    }
    
    this.metrics.totalRequests++;
    this.checkMetrics();
  }

  /**
   * 記錄錯誤
   */
  public recordError(error: Error, context?: Record<string, any>): void {
    this.metrics.errorCount++;
    
    // 記錄錯誤詳情（可以整合到日誌系統）
    if (process.env.ENABLE_AUTH_LOGGING === 'true') {
      console.error('[AuthMonitor] Error recorded:', {
        message: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString()
      });
    }
    
    this.checkMetrics();
  }

  /**
   * 獲取 Token 驗證成功率
   */
  public getTokenValidationSuccessRate(): number {
    const total = this.metrics.tokenValidationSuccess + this.metrics.tokenValidationFailure;
    if (total === 0) return 100;
    
    return (this.metrics.tokenValidationSuccess / total) * 100;
  }

  /**
   * 獲取平均 API 回應時間
   */
  public getAverageResponseTime(): number {
    if (this.metrics.apiResponseTimes.length === 0) return 0;
    
    const sum = this.metrics.apiResponseTimes.reduce((a, b) => a + b, 0);
    return sum / this.metrics.apiResponseTimes.length;
  }

  /**
   * 獲取 P95 回應時間
   */
  public getP95ResponseTime(): number {
    if (this.metrics.apiResponseTimes.length === 0) return 0;
    
    const sorted = [...this.metrics.apiResponseTimes].sort((a, b) => a - b);
    const index = Math.floor(sorted.length * 0.95);
    return sorted[index];
  }

  /**
   * 獲取錯誤率
   */
  public getErrorRate(): number {
    if (this.metrics.totalRequests === 0) return 0;
    
    return (this.metrics.errorCount / this.metrics.totalRequests) * 100;
  }

  /**
   * 獲取當前指標快照
   */
  public getMetricsSnapshot(): Record<string, any> {
    return {
      tokenValidation: {
        success: this.metrics.tokenValidationSuccess,
        failure: this.metrics.tokenValidationFailure,
        successRate: this.getTokenValidationSuccessRate().toFixed(2) + '%'
      },
      performance: {
        totalRequests: this.metrics.totalRequests,
        averageResponseTime: this.getAverageResponseTime().toFixed(2) + 'ms',
        p95ResponseTime: this.getP95ResponseTime().toFixed(2) + 'ms'
      },
      errors: {
        count: this.metrics.errorCount,
        rate: this.getErrorRate().toFixed(2) + '%'
      },
      system: {
        uptime: this.getUptime(),
        lastReset: this.metrics.lastReset.toISOString(),
        autoRollback: this.autoRollbackEnabled,
        errorThreshold: this.errorRateThreshold + '%'
      }
    };
  }

  /**
   * 檢查指標並觸發自動回滾（如果需要）
   */
  private checkMetrics(): void {
    // 至少要有 100 個請求才開始檢查
    if (this.metrics.totalRequests < 100) return;
    
    const errorRate = this.getErrorRate();
    
    if (errorRate > this.errorRateThreshold && this.autoRollbackEnabled) {
      this.triggerAutoRollback(errorRate);
    }
  }

  /**
   * 觸發自動回滾
   */
  private triggerAutoRollback(errorRate: number): void {
    console.error(`[AuthMonitor] 錯誤率過高 (${errorRate.toFixed(2)}%)，觸發自動回滾`);
    
    // 設置環境變數以停用新架構
    process.env.USE_NEW_AUTH = 'false';
    process.env.NEW_AUTH_RATIO = '0';
    
    // 發送通知（可以整合到通知系統）
    this.sendRollbackNotification(errorRate);
    
    // 重置指標
    this.resetMetrics();
  }

  /**
   * 發送回滾通知
   */
  private sendRollbackNotification(errorRate: number): void {
    // 這裡可以整合 Email、Slack、Discord 等通知服務
    console.warn('[AuthMonitor] 回滾通知已發送', {
      errorRate: errorRate.toFixed(2) + '%',
      threshold: this.errorRateThreshold + '%',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 重置指標
   */
  public resetMetrics(): void {
    this.metrics = this.initializeMetrics();
  }

  /**
   * 獲取運行時間
   */
  private getUptime(): string {
    const diff = Date.now() - this.metrics.lastReset.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  }

  /**
   * 導出指標為 Prometheus 格式
   */
  public exportPrometheusMetrics(): string {
    const metrics = [];
    
    // Token 驗證指標
    metrics.push(`# HELP auth_token_validation_total Total number of token validations`);
    metrics.push(`# TYPE auth_token_validation_total counter`);
    metrics.push(`auth_token_validation_total{status="success"} ${this.metrics.tokenValidationSuccess}`);
    metrics.push(`auth_token_validation_total{status="failure"} ${this.metrics.tokenValidationFailure}`);
    
    // 回應時間指標
    metrics.push(`# HELP auth_api_response_time_ms API response time in milliseconds`);
    metrics.push(`# TYPE auth_api_response_time_ms gauge`);
    metrics.push(`auth_api_response_time_ms{quantile="0.5"} ${this.getAverageResponseTime()}`);
    metrics.push(`auth_api_response_time_ms{quantile="0.95"} ${this.getP95ResponseTime()}`);
    
    // 錯誤率指標
    metrics.push(`# HELP auth_error_rate Current error rate percentage`);
    metrics.push(`# TYPE auth_error_rate gauge`);
    metrics.push(`auth_error_rate ${this.getErrorRate()}`);
    
    // 請求總數
    metrics.push(`# HELP auth_requests_total Total number of requests`);
    metrics.push(`# TYPE auth_requests_total counter`);
    metrics.push(`auth_requests_total ${this.metrics.totalRequests}`);
    
    return metrics.join('\n');
  }
}

// 匯出單例
export const authMonitor = AuthMonitor.getInstance();