import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";

const NewsGuidelinesPage = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1); // 返回上一頁
  };

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted">
      <div className="container mx-auto px-6 py-6">
        {/* Header Controls */}
        <div className="flex items-center mb-4">
          <Button variant="outline" onClick={handleGoBack}>
            ← 回到新聞投稿頁面
          </Button>
        </div>

        {/* Main Content */}
        <section className="bg-white w-full rounded-lg shadow-lg px-8 py-10" aria-label="投稿說明">
          <h1 className="font-size-3xl font-bold text-primary mb-8 text-center" tabIndex={0}>
            投稿說明
          </h1>

          <div className="text-left text-gray-700 space-y-6 leading-relaxed">
            {/* 主要說明內容 */}
            <p className="font-size-base">
              本網站內所連結之各學校網站內容，由該校自行維護管理，若有任何不當內容由該校自行負責，本網站僅定期確保網頁連結之正確性。
            </p>
          </div>
        </section>
      </div>
    </main>
  );
};

export default NewsGuidelinesPage;
