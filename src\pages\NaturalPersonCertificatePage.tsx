import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CreditCard, CheckCircle, AlertCircle, Usb } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { useFontSize } from "@/hooks/useFontSize";
import { getApiBaseUrl } from "@/config/environment";
import { useAuthService } from "@/services/authService";
import { certificationAPI } from "@/services/certificationAPI";

// 自然人憑證相關介面定義
interface CertificateInfo {
  subject: string;
  issuer: string;
  serialNumber: string;
  validFrom: string;
  validTo: string;
  fingerprint: string;
}

interface CardReader {
  id: string;
  name: string;
  connected: boolean;
}

// 憑證狀態類型
type CertificateStatus = "idle" | "detecting" | "reading" | "validating" | "binding" | "success" | "error";

const NaturalPersonCertificatePage: React.FC = () => {
  const auth = useAuth();
  const { fontSize } = useFontSize();
  const authService = useAuthService();
  const [certificatePassword, setCertificatePassword] = useState("");
  const [status, setStatus] = useState<CertificateStatus>("idle");
  const [error, setError] = useState<string>("");
  const [readers, setReaders] = useState<CardReader[]>([]);
  const [selectedReader, setSelectedReader] = useState<string>("");
  const [certificateInfo, setCertificateInfo] = useState<CertificateInfo | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // 檢測讀卡機
  const detectCardReaders = async (): Promise<CardReader[]> => {
    setStatus("detecting");
    setError("");

    try {
      // 檢查是否支援 WebUSB
      if (!("usb" in navigator)) {
        throw new Error("請使用 Chrome 或 Edge 瀏覽器進行操作");
      }

      // 請求 USB 設備權限
      const devices = await (navigator as { usb: { getDevices: () => Promise<unknown[]> } }).usb.getDevices();

      // 模擬讀卡機檢測（實際應用中需要根據廠商 ID 和產品 ID 識別）
      const mockReaders: CardReader[] = [
        { id: "reader_1", name: "虹堡 EZ100PU", connected: true },
        { id: "reader_2", name: "中強資訊 HiCOS PKI", connected: false },
      ];

      // 如果有 USB 設備，假設為讀卡機
      if (devices.length > 0) {
        mockReaders[0].connected = true;
      }

      setReaders(mockReaders);
      if (mockReaders.some((r) => r.connected)) {
        setSelectedReader(mockReaders.find((r) => r.connected)?.id || "");
      }

      return mockReaders;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "讀卡機檢測失敗";
      setError(errorMessage);
      throw err;
    } finally {
      setStatus("idle");
    }
  };

  // 讀取憑證資訊
  const readCertificate = async (readerId: string, password: string): Promise<CertificateInfo> => {
    setStatus("reading");
    setError("");

    try {
      // 模擬憑證讀取（實際應用中需要與讀卡機API整合）
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // 模擬憑證資訊（實際應用中需要解析真實憑證）
      const mockCertificate: CertificateInfo = {
        subject: "CN=王小明,OU=臺灣自然人憑證,O=政府憑證管理中心,C=TW",
        issuer: "CN=政府憑證管理中心,O=行政院,C=TW",
        serialNumber: "1234567890ABCDEF",
        validFrom: "2023-01-01",
        validTo: "2026-12-31",
        fingerprint: "SHA256:1234567890ABCDEF...",
      };

      // 驗證密碼（模擬）
      if (password.length < 6) {
        throw new Error("密碼錯誤，請重新輸入");
      }

      return mockCertificate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "憑證讀取失敗";
      setError(errorMessage);
      throw err;
    }
  };

  // 驗證憑證
  const validateCertificate = async (certificate: CertificateInfo): Promise<boolean> => {
    setStatus("validating");

    try {
      // 檢查憑證有效期
      const now = new Date();
      const validFrom = new Date(certificate.validFrom);
      const validTo = new Date(certificate.validTo);

      if (now < validFrom || now > validTo) {
        throw new Error("憑證已過期，請更新憑證");
      }

      // 模擬憑證鏈驗證
      await new Promise((resolve) => setTimeout(resolve, 1000));

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "憑證驗證失敗";
      setError(errorMessage);
      throw err;
    }
  };

  // 綁定憑證到帳號
  const bindCertificateToAccount = async (certificate: CertificateInfo): Promise<void> => {
    setStatus("binding");

    try {
      // 檢查認證狀態
      if (!authService.isAuthenticated()) {
        throw new Error("用戶未登入，請先登入");
      }

      // 使用 certificationAPI 服務進行憑證綁定
      const result = await certificationAPI.bindNaturalPersonCertificate({
        certificateInfo: certificate,
        accountId: auth.authState.user?.id,
      });

      if (!result.success) {
        throw new Error(result.message || "憑證綁定失敗");
      }

      setStatus("success");
      toast({
        title: "綁定完成",
        description: "您的自然人憑證已成功綁定",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "憑證綁定失敗";
      setError(errorMessage);
      setStatus("error");
      throw err;
    }
  };

  // 處理綁定流程
  const handleBind = async (e?: React.MouseEvent<HTMLButtonElement>) => {
    // 防止表單提交和頁面刷新
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    console.log("開始憑證綁定流程");

    // 清除之前的錯誤訊息
    setError("");

    // 驗證輸入
    if (!certificatePassword.trim()) {
      const errorMsg = "請輸入憑證密碼";
      setError(errorMsg);
      toast({
        title: "請輸入密碼",
        description: "請輸入您的自然人憑證密碼",
        variant: "destructive",
      });
      return;
    }

    if (certificatePassword.trim().length < 6) {
      const errorMsg = "密碼長度不足，請輸入完整密碼";
      setError(errorMsg);
      toast({
        title: "密碼長度不足",
        description: "請輸入完整的憑證密碼",
        variant: "destructive",
      });
      return;
    }

    if (!selectedReader) {
      const errorMsg = "請選擇讀卡機";
      setError(errorMsg);
      toast({
        title: "請選擇讀卡機",
        description: "請先選擇已連接的讀卡機",
        variant: "destructive",
      });
      return;
    }

    if (status !== "idle") {
      const errorMsg = "系統忙碌中，請稍後再試";
      setError(errorMsg);
      console.log("驗證失敗:", errorMsg);
      return;
    }

    try {
      // 1. 讀取憑證
      const certificate = await readCertificate(selectedReader, certificatePassword);
      setCertificateInfo(certificate);

      // 2. 驗證憑證
      await validateCertificate(certificate);

      // 3. 綁定憑證
      await bindCertificateToAccount(certificate);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "未知錯誤";
      setError(errorMessage);
      setStatus("error");
      toast({
        title: "操作失敗",
        description: "請檢查憑證和密碼後重新嘗試",
        variant: "destructive",
      });
    }
  };

  // 重置狀態
  const resetStatus = (e?: React.MouseEvent<HTMLButtonElement>) => {
    // 防止表單提交和頁面刷新
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    setStatus("idle");
    setError("");
    setCertificateInfo(null);
    setCertificatePassword("");
  };

  // 頁面加載時檢測讀卡機
  useEffect(() => {
    detectCardReaders().catch(error => {
      console.warn('讀卡機檢測失敗:', error.message);
      // 不拋出未處理的 rejection
    });
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case "detecting":
      case "reading":
      case "validating":
      case "binding":
        return <Loader2 className="h-5 w-5 animate-spin" />;
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case "detecting":
        return "檢測讀卡機中...";
      case "reading":
        return "讀取憑證中...";
      case "validating":
        return "驗證憑證中...";
      case "binding":
        return "綁定憑證中...";
      case "success":
        return "綁定完成";
      case "error":
        return "請重新操作";
      default:
        return "準備就緒";
    }
  };

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-start py-12">
      <div className="w-full max-w-4xl px-4 space-y-6">
        {/* 頁面標題 */}
        <div className="text-center">
          <h1 className="font-bold text-primary mb-4" style={{ fontSize: "var(--font-3xl)" }}>
            自然人憑證綁定
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto" style={{ fontSize: "var(--font-base)" }}>
            使用自然人憑證快速安全登入，只需插入憑證卡片並輸入密碼即可完成綁定。
          </p>
        </div>

        <div className="flex justify-center">
          <div className="w-full max-w-md">
            {/* 綁定表單 */}
            <Card>
              <CardHeader>
                <CardTitle>憑證綁定</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 讀卡機選擇 */}
                <div className="space-y-2">
                  <Label htmlFor="reader">讀卡機</Label>
                  <div className="flex gap-2">
                    <select
                      id="reader"
                      value={selectedReader}
                      onChange={(e) => setSelectedReader(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-600"
                      disabled={status !== "idle"}>
                      <option value="">請選擇讀卡機</option>
                      {readers
                        .filter((r) => r.connected)
                        .map((reader) => (
                          <option key={reader.id} value={reader.id}>
                            {reader.name}
                          </option>
                        ))}
                    </select>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        detectCardReaders();
                      }}
                      disabled={status === "detecting"}>
                      <Usb className="h-4 w-4" />
                    </Button>
                  </div>
                  {readers.length === 0 && (
                    <p className="text-gray-500" style={{ fontSize: "var(--font-sm)" }}>
                      未檢測到讀卡機
                    </p>
                  )}
                </div>

                {/* 憑證密碼 */}
                <div className="space-y-2">
                  <Label htmlFor="password">憑證密碼</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={certificatePassword}
                      onChange={(e) => setCertificatePassword(e.target.value)}
                      placeholder="請輸入自然人憑證密碼"
                      disabled={status !== "idle"}
                      className="pr-20"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={(e) => {
                        e.preventDefault();
                        setShowPassword(!showPassword);
                      }}>
                      {showPassword ? "隱藏" : "顯示"}
                    </Button>
                  </div>
                </div>

                {/* 錯誤訊息 */}
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {/* 操作按鈕 */}
                <div className="flex gap-2">
                  <Button type="button" onClick={handleBind} disabled={status !== "idle"} className="flex-1">
                    {status === "idle" ? "開始綁定" : "處理中..."}
                  </Button>
                  {(status === "success" || status === "error") && (
                    <Button type="button" variant="outline" onClick={resetStatus}>
                      重新綁定
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 成功狀態顯示 */}
            {certificateInfo && (
              <div className="mt-6">
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <p className="text-green-800 font-medium text-center" style={{ fontSize: "var(--font-base)" }}>
                    ✅ 您的自然人憑證已成功綁定
                  </p>
                  <p className="text-green-600 text-center mt-1" style={{ fontSize: "var(--font-sm)" }}>
                    現在可以使用憑證進行快速登入
                  </p>
                  <div className="border-t border-green-200 mt-3 pt-3">
                    <div className="flex justify-between" style={{ fontSize: "var(--font-sm)" }}>
                      <span className="text-gray-500">憑證有效期至</span>
                      <span className="font-medium">{certificateInfo.validTo}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
};

export default NaturalPersonCertificatePage;
