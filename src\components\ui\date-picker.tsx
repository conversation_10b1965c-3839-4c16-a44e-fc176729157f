import React, { useState } from "react";
import { format, parse, isValid } from "date-fns";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "lucide-react";

// 日期選擇器組件
interface DatePickerProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export const DatePicker: React.FC<DatePickerProps> = ({ value, onChange, placeholder = "請選擇日期", disabled = false }) => {
  const [isOpen, setIsOpen] = useState(false);

  // 解析輸入的日期字串
  const parseInputDate = (dateString: string): Date | null => {
    if (!dateString.trim()) return null;

    // 嘗试多種日期格式
    const formats = ["yyyy-MM-dd", "yyyy/MM/dd", "yyyy.MM.dd", "yyyy-M-d", "yyyy/M/d"];

    for (const fmt of formats) {
      try {
        const parsed = parse(dateString, fmt, new Date());
        if (isValid(parsed)) {
          return parsed;
        }
      } catch (e) {
        continue;
      }
    }

    // 嘗試原生解析
    const date = new Date(dateString);
    return isValid(date) ? date : null;
  };

  const selectedDate = parseInputDate(value);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      onChange(format(date, "yyyy-MM-dd"));
      setIsOpen(false);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full justify-start text-left font-normal" disabled={disabled}>
          <Calendar className="mr-2 h-4 w-4" />
          {selectedDate ? format(selectedDate, "yyyy-MM-dd") : placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <CalendarComponent mode="single" selected={selectedDate || undefined} onSelect={handleDateSelect} initialFocus />
      </PopoverContent>
    </Popover>
  );
};
