# EcoCampus 前端環境腳本使用指南

## 🚀 快速開始

### 開發環境腳本

#### **本地開發環境 (Local Development)**
```bash
npm run dev
```
- **模式**: `development`
- **端口**: 8080
- **API**: `http://localhost:3001/api`
- **Base Path**: `/`
- **特點**: 本地開發，支援熱重載，啟用除錯工具

#### **測試環境 (Testing Environment)**
```bash
npm run testing
```
- **模式**: `testing`
- **端口**: 8081
- **API**: `http://ecocampus-v2-apply-api.sumire.com.tw/api`
- **Base Path**: `/apply/`
- **特點**: 測試環境，連接到測試 API

#### **生產環境 (Production Environment)**
```bash
npm run prod
```
- **模式**: `production`
- **端口**: 8082
- **API**: `https://ecocampus.moenv.gov.tw/apply-api`
- **Base Path**: `/apply/`
- **特點**: 生產環境模擬，連接到正式 API

### 構建腳本

#### **測試環境構建**
```bash
npm run build-testing
```
- **模式**: `testing`
- **輸出**: `dist/` 目錄
- **Source Map**: ✅ 啟用
- **Minify**: ❌ 不壓縮
- **用途**: 部署到測試環境

#### **生產環境構建**
```bash
npm run build-prod
```
- **模式**: `production`
- **輸出**: `dist/` 目錄
- **Source Map**: ❌ 不生成
- **Minify**: ✅ 壓縮
- **Code Splitting**: ✅ 啟用
- **用途**: 部署到生產環境

#### **預設構建**
```bash
npm run build
```
- **模式**: `production` (預設)
- **用途**: 標準生產構建

### 其他腳本

#### **預覽構建結果**
```bash
npm run preview
```
- **端口**: 4173
- **用途**: 預覽構建後的靜態檔案

#### **程式碼檢查**
```bash
npm run lint          # 檢查程式碼
npm run lint:fix      # 自動修復程式碼問題
```

## 🌍 環境配置詳解

### 環境變數

每個環境都會自動注入以下環境變數：

```typescript
// 環境模式
import.meta.env.VITE_APP_MODE        // "development" | "testing" | "production"

// 環境名稱
import.meta.env.VITE_APP_ENV         // "local" | "dev" | "prod"

// API 基礎 URL
import.meta.env.VITE_API_BASE_URL    // 對應環境的 API URL
```

### 環境差異對比

| 配置項目 | Dev | Testing | Prod |
|---------|-----|---------|------|
| **模式** | `development` | `testing` | `production` |
| **端口** | 8080 | 8081 | 8082 |
| **API URL** | `http://localhost:3001/api` | `http://ecocampus-v2-apply-api.sumire.com.tw/api` | `https://ecocampus.moenv.gov.tw/apply-api` |
| **Base Path** | `/` | `/apply/` | `/apply/` |
| **Source Map** | ✅ | ✅ | ❌ |
| **Minify** | ❌ | ❌ | ✅ |
| **Code Splitting** | ❌ | ❌ | ✅ |
| **Debug Tools** | ✅ | ❌ | ❌ |

## 🔧 使用場景

### 開發階段
```bash
# 本地開發，連接本地 API
npm run dev

# 測試環境開發，連接測試 API
npm run testing
```

### 測試階段
```bash
# 構建測試版本
npm run build-testing

# 預覽測試版本
npm run preview
```

### 部署階段
```bash
# 構建生產版本
npm run build-prod

# 或使用預設構建
npm run build
```

## 📁 輸出目錄

所有構建結果都會輸出到 `dist/` 目錄：

```
dist/
├── index.html          # 主頁面
├── assets/             # 靜態資源
│   ├── css/           # 樣式檔案
│   ├── js/            # JavaScript 檔案
│   └── images/        # 圖片資源
└── favicon.ico        # 網站圖標
```

## 🚨 注意事項

1. **API 連接**: 確保對應環境的 API 服務器正在運行
2. **端口衝突**: 如果端口被佔用，Vite 會自動選擇下一個可用端口
3. **環境變數**: 前端會根據啟動模式自動載入對應的環境配置
4. **構建優化**: 生產環境構建會啟用代碼分割和壓縮優化

## 🔄 環境切換

如果需要在前端應用中動態切換環境，可以使用 `src/config/environment.ts` 中的環境管理器：

```typescript
import { switchEnvironment, getCurrentEnvironment } from '@/config/environment';

// 切換到測試環境
switchEnvironment('testing');

// 獲取當前環境
const env = getCurrentEnvironment();
console.log(`當前環境: ${env.displayName}`);
```

## 📋 環境配置文件

### 前端專用環境配置

前端環境配置已移至 `config/environments/` 目錄，包含：

- `env.development.example` - 本地開發環境配置
- `env.testing.example` - 測試環境配置  
- `env.production.example` - 生產環境配置

### 使用方式

```bash
# 本地開發環境
cp config/environments/env.development.example .env

# 測試環境
cp config/environments/env.testing.example .env

# 生產環境
cp config/environments/env.production.example .env
```

### 配置內容

前端環境配置包含：

- **環境設定**: NODE_ENV, APP_ENV
- **前端服務器配置**: FRONTEND_PORT, FRONTEND_URL
- **API 服務器配置**: API_BASE_URL
- **外部連結配置**: 各種 VITE_* 變數
- **開發工具配置**: ENABLE_LOGGING, DEBUG_MODE
- **測試 Token**: 各環境的測試用 Token

### 後端配置分離

後端相關配置已移至 `api/.env`，包含：

- 資料庫配置
- 認證配置
- 檔案上傳配置
- 服務器配置
- 監控和日誌配置
