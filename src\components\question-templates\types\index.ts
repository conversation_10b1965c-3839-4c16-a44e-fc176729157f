// ========== 模板資料結構定義 ==========

// 模板1 - 是非選擇題資料結構
export interface YesNoData {
  is_yes_no: string;
}

// 模板2 - 團隊成員資料結構
export interface TeamMemberData {
  student_list: Array<{ input_1: string; input_2: string; input_3: string }>;
  teacher_list: Array<{ input_1: string; input_2: string; input_3: string }>;
  community_member_list: Array<{ input_1: string; input_2: string; input_3: string }>;
}

// 模板3 - 會議記錄（初次認證）資料結構
export interface MeetingRecordData {
  meeting_date_and_theme: Array<{
    input_1: string; // 會議日期
    input_2: string; // 會議主題
  }>;
  file: Array<{
    file_url: string; // 檔案路徑
    file_name: string; // 檔案名稱
  }>;
}

// 模板4 - 分享會議資訊資料結構
export interface ShareMeetingInfoData {
  is_yes_no: string;
  share_people: { checkbox: string[] };
  how_share_meeting: {
    checkbox: string[];
    text: { text_1: string; text_2: string; text_3: string };
  };
}

// 模板5 - 招募新成員資料結構
export interface RecruitmentData {
  is_yes_no: string;
  textarea: string;
}

// 模板6 - 照片上傳資料結構
export interface PhotoUploadData {
  photo_record: Array<{
    photo_url: string;
    photo_date: string;
    photo_des: string;
  }>;
}

// 模板8 - 環境路徑選擇資料結構
export interface PathSelectionData {
  improve_path_list: Array<{
    path: string;
    cname: string;
    who_make: { checkbox: string[]; text: { text_3: string } };
    date: { input_1: string; input_2: string };
    execution_short_aims: string;
    execution_mid_aims: string;
    execution_long_aims: string;
    how_share: {
      checkbox: string[];
      text: { text_1: string; text_2: string; text_3: string };
    };
    photo_record: Array<{
      photo_url: string;
      photo_date: string;
      photo_des: string;
    }>;
  }>;
}

// 模板9 - 生態監控記錄資料結構
export interface MonitoringRecordData {
  monitor_list: Array<{
    path: string;
    monitor_topic: Array<{
      topic: { input_1: string; input_2: string };
      table: Array<{
        table_name: string;
        table_contant: Array<{
          input_1: string; // 日期
          input_2: string; // 數量
          input_3: string; // 單位
          input_4: string; // 備註
        }>;
      }>;
    }>;
  }>;
}

// 模板11 - 第四步申論資料結構
export interface Template11Data {
  question_1: string; // 在執行生態學校計畫時是如何監測執行進度？
  question_2: string; // 是否有現成的資料可輔助說明每項行動方案之目標均有達成
  question_3: string; // 請問貴校在執行生態學校計畫時，最大的成果為何？
  question_4: string; // 請問貴校在執行生態學校計畫時所面臨最大的挑戰為何？
}

// 模板12 - 教學案例資料結構
export interface TeachingCaseData {
  teaching_sample_list: Array<{
    teaching_sample_name: string;
    teaching_sample_designer: string;
    grade: { checkbox: string[] };
    path: { checkbox: string[] };
    subject: { checkbox: string[] };
    teaching_sample_hour: string;
    teaching_sample_aim: string;
    teaching_sample_content: string;
    teaching_sample_method: string;
    teaching_sample_evaluation: string;
    teaching_sample_resource: string;
    teaching_sample_reference: string;
    file: File[];
  }>;
}

// 模板13 - 第五步申論資料結構
export interface Template13Data {
  question_1: string; // 簡要說明如何透過學生參與生態學校計畫來強化貴校的教學成果？
  question_2: string; // 與兩年前申請綠旗認證時相比，貴校的教育課程與生態學校計畫間的融合度是否更高？請說明之。
}

// 模板16 - 文字區域資料結構
export interface TextAreaData {
  textarea: string;
}

// 模板19 - 總結申論資料結構
export interface SummaryEssayData {
  textarea_1: string;
  textarea_2: string;
  textarea_3: string;
  textarea_4: string;
  textarea_5: string;
}

// 模板21 - 會議記錄（再認證）資料結構
export interface RenewalMeetingData {
  textarea_1: string;
  textarea_2: string;
  textarea_3: string;
  textarea_4: string;
  textarea_5: string;
  textarea_6: string;
  textarea_7: string;
}

// 模板22 - 教學案例整合資料結構
export interface Template22Data {
  case_1: {
    grade: string; // 年級
    course: string; // 課程
    activity: string; // 課程活動
  };
  case_2: {
    grade: string;
    course: string;
    activity: string;
  };
  case_3: {
    grade: string;
    course: string;
    activity: string;
  };
}

// 通用介面
export interface TemplateProps<T> {
  data: T;
  onChange: (data: T) => void;
  disabled?: boolean;
}
