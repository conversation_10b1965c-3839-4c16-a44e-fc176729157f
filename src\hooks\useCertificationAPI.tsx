import React from "react";
import { useState, useEffect, useCallback } from "react";
import { certificationAPI } from "@/services/certificationAPI";

export interface ApiCertificationData {
  sid?: number;
  member_sid: number;
  level: number;
  review: string;
  createdate?: number;
  updatedate?: number;
}

export interface ApiStepData {
  step: number;
  title: string;
  description: string;
  questionCount: number;
  isCompleted: boolean;
}

export interface ApiProgressData {
  completedSteps: number;
  totalSteps: number;
  completedQuestions: number;
  totalQuestions: number;
  progressPercentage: number;
}

export interface useCertificationAPIProps {
  certType: string;
}

interface ApiAnswerResponse {
  value: unknown;
  status: string;
  opinion?: string;
  updatedate: number;
}

interface ApiAnswerData {
  questionId: number;
  value: unknown;
  status: string;
  step: number;
  sequence: number;
}

export const useCertificationAPI = ({ certType }: useCertificationAPIProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificationId, setCertificationId] = useState<number | null>(null);
  const [certificationData, setCertificationData] = useState<ApiCertificationData | null>(null);
  const [steps, setSteps] = useState<ApiStepData[]>([]);
  const [progress, setProgress] = useState<ApiProgressData | null>(null);
  const [saving, setSaving] = useState(false);

  // 獲取認證等級
  const getCertificationLevel = (certType: string): number => {
    switch (certType) {
      case "bronze":
        return 1;
      case "silver":
        return 2;
      case "green_flag":
        return 3;
      case "green-flag-r1":
        return 4;
      case "green-flag-r2":
        return 5;
      case "green-flag-r3":
        return 6;
      default:
        return 1;
    }
  };

  // 初始化認證數據
  const initializeCertification = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 1. 獲取或創建認證 ID
      let currentCertificationId = parseInt(localStorage.getItem("certification_sid") || "0");

      if (!currentCertificationId) {
        const certLevel = getCertificationLevel(certType);
        const createResponse = await certificationAPI.createCertification({
          member_sid: 1, // 實際應從登入狀態獲取
          level: certLevel,
          review: "尚未審核",
          add_type: "front",
        });

        if (createResponse.success && createResponse.data?.sid) {
          currentCertificationId = createResponse.data.sid;
          localStorage.setItem("certification_sid", currentCertificationId.toString());
        } else {
          throw new Error("創建認證失敗");
        }
      }

      setCertificationId(currentCertificationId);

      // 2. 載入認證資料
      const certResponse = await certificationAPI.getCertification(currentCertificationId);
      if (certResponse.success && certResponse.data) {
        setCertificationData(certResponse.data);
      }

      // 3. 載入認證步驟
      const certLevel = getCertificationLevel(certType);
      const stepsResponse = await certificationAPI.getCertificationSteps();
      if (stepsResponse.success && stepsResponse.data) {
        setSteps(stepsResponse.data);
      }

      // 4. 載入進度
      const progressResponse = await certificationAPI.getCertificationProgress(currentCertificationId);
      if (progressResponse.success && progressResponse.data) {
        setProgress(progressResponse.data);
      }

      setIsLoading(false);
    } catch (error) {
      console.error("初始化認證失敗:", error);
      setError(String(error));
      setIsLoading(false);
    }
  }, [certType]);

  // 儲存答案
  const saveAnswer = useCallback(
    async (questionId: number, answerData: Record<string, unknown>) => {
      if (!certificationId) return;

      try {
        setSaving(true);
        const response = await certificationAPI.saveAnswer(certificationId, questionId, answerData);
        if (response.success) {
          console.log("答案已儲存:", questionId);
          return true;
        } else {
          throw new Error("儲存失敗");
        }
      } catch (error) {
        console.error("儲存答案失敗:", error);
        return false;
      } finally {
        setSaving(false);
      }
    },
    [certificationId]
  );

  // 載入答案
  const loadAnswer = useCallback(
    async (questionId: number) => {
      if (!certificationId) return null;

      try {
        const response = await certificationAPI.getAnswer(certificationId, questionId);
        if (response.success && response.data) {
          return (response.data as unknown as ApiAnswerResponse).value;
        }
      } catch (error) {
        console.error("載入答案失敗:", error);
      }
      return null;
    },
    [certificationId]
  );

  // 提交認證申請
  const submitForReview = useCallback(async () => {
    if (!certificationId) return false;

    try {
      const response = await certificationAPI.submitForReview(certificationId);
      if (response.success) {
        // 重新載入認證資料
        await initializeCertification();
        return true;
      }
    } catch (error) {
      console.error("提交審核失敗:", error);
    }
    return false;
  }, [certificationId, initializeCertification]);

  // 載入所有答案
  const loadAllAnswers = useCallback(async () => {
    if (!certificationId) return {};

    try {
      const response = await certificationAPI.getAnswers(certificationId);
      if (response.success && response.data) {
        const answersMap: Record<string, unknown> = {};
        (response.data as unknown as ApiAnswerData[]).forEach((answer: ApiAnswerData) => {
          answersMap[`question_${answer.questionId}`] = answer.value;
        });
        return answersMap;
      }
    } catch (error) {
      console.error("載入所有答案失敗:", error);
    }
    return {};
  }, [certificationId]);

  // 初始化
  useEffect(() => {
    initializeCertification();
  }, [initializeCertification]);

  return {
    // 狀態
    isLoading,
    error,
    certificationId,
    certificationData,
    steps,
    progress,
    saving,

    // 方法
    saveAnswer,
    loadAnswer,
    loadAllAnswers,
    submitForReview,
    initializeCertification,
  };
};
