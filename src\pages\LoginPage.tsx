import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, AlertCircle, Eye, EyeOff, Lock, User, Settings } from "lucide-react";
import authService from "@/services/authService";
import EnvironmentSelector from "@/components/EnvironmentSelector";
import { getCurrentEnvironment, EnvironmentConfig, getOfficialLoginUrl } from "@/config/environment";

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // ✅ 無 token 才轉去根站 /login；有 token 就在本頁完成登入
  useEffect(() => {
    // 若已登入就不用轉
    if (authService.isAuthenticated()) return;

    const params = new URLSearchParams(location.search);
    const hasToken = params.get("token") || params.get("userToken") || params.get("authToken");

    if (hasToken) return; // 交給既有的 token 登入流程處理（你檔案裡已經有）

    // 準備 redirect，優先取 ?redirect=，否則取剛才想去的頁面或預設
    const fallback = "/apply/dashboard";
    const redirect = params.get("redirect") || (window.history.state && window.history.state.usr && window.history.state.usr.from?.pathname) || fallback;

    // 轉去官方網站登入頁，登入後會根據 redirect 參數回到指定頁面
    // onst officialLoginUrl = getOfficialLoginUrl();
    // onst redirectUrl = `${officialLoginUrl}?redirect=${encodeURIComponent(redirect)}`;

    // onsole.log(`🔄 重定向到官方登入頁面: ${redirectUrl}`);
    // indow.location.replace(redirectUrl);
  }, [location.search]);

  // Token 登入相關狀態
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tokenInfo, setTokenInfo] = useState<string | null>(null);
  const [hasProcessed, setHasProcessed] = useState(false);

  // 密碼登入相關狀態
  const [loginForm, setLoginForm] = useState({
    account: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [passwordLoginLoading, setPasswordLoginLoading] = useState(false);
  const [passwordLoginError, setPasswordLoginError] = useState<string | null>(null);

  // 登入方式狀態
  const [loginMethod, setLoginMethod] = useState<"token" | "password" | "environment">("token");

  // 環境管理狀態
  const [currentEnvironment, setCurrentEnvironment] = useState<EnvironmentConfig>(getCurrentEnvironment());

  // 檢查 URL 中的 reason 參數
  const urlParams = new URLSearchParams(location.search);
  const logoutReason = urlParams.get("reason");

  // 根據登出原因顯示不同訊息
  const getReasonMessage = (reason: string | null): string | null => {
    switch (reason) {
      case "token_expired":
        return "您的登入狀態已過期，請重新登入";
      case "permission_denied":
        return "權限不足，請聯繫系統管理員";
      case "role_denied":
        return "您的帳號角色不被允許存取此系統";
      default:
        return null;
    }
  };

  const reasonMessage = getReasonMessage(logoutReason);

  // 處理來自其他網站的 POST 請求或 URL 參數
  useEffect(() => {
    // 避免重複處理
    if (hasProcessed) return;

    const handleExternalLogin = async () => {
      try {
        // 檢查是否已經登入
        if (authService.isAuthenticated()) {
          setHasProcessed(true);
          const user = authService.getUser();
          const userRole = user?.role;

          // 縣市政府和輔導人員導向 profile 頁面
          if (userRole === "epa" || userRole === "tutor") {
            navigate("/profile", { replace: true });
          } else {
            // 學校身份導向 dashboard
            navigate("/dashboard", { replace: true });
          }
          return;
        }

        // 檢查 URL 參數中的 token（支援多種參數名稱）
        const urlParams = new URLSearchParams(location.search);
        const tokenFromUrl =
          urlParams.get("token") || // 支援 ?token=xxx
          urlParams.get("userToken") || // 支援 ?userToken=xxx
          urlParams.get("authToken"); // 支援 ?authToken=xxx

        // 檢查 sessionStorage 中是否有其他網站儲存的 token
        const tokenFromStorage = sessionStorage.getItem("externalUserToken");

        const userToken = tokenFromUrl || tokenFromStorage;

        if (userToken) {
          setTokenInfo(`正在驗證 Token: ${userToken.substring(0, 20)}...`);

          console.log("嘗試使用 Token 登入:", userToken);
          const result = await authService.handleExternalLogin(userToken);

          if (result.success) {
            // 清除外部 token
            sessionStorage.removeItem("externalUserToken");
            setHasProcessed(true);

            // 根據角色決定跳轉頁面
            const user = authService.getUser();
            const userRole = user?.role;
            let defaultRedirect = "/dashboard";

            // 縣市政府和輔導人員預設導向 profile 頁面
            if (userRole === "epa" || userRole === "tutor") {
              defaultRedirect = "/profile";
            }

            const redirectTo = urlParams.get("redirect") || defaultRedirect;
            navigate(redirectTo, { replace: true });
          } else {
            console.error("登入失敗:", result);
            setError(`登入失敗: ${result.message || "請檢查您的憑證是否有效"}`);
          }
        } else {
          // 沒有找到 token，切換到密碼登入模式
          setError(null);
          setLoginMethod("password");
        }
      } catch (error) {
        console.error("External login error:", error);
        const errorMessage = error instanceof Error ? error.message : "未知錯誤";
        setError(`登入處理過程中發生錯誤: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    handleExternalLogin();
  }, [location.search, navigate, hasProcessed]);

  // 重新嘗試登入
  const handleRetry = () => {
    setLoading(true);
    setError(null);
    setHasProcessed(false);
    setTokenInfo(null);
  };

  // 🔐 處理密碼登入
  const handlePasswordLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!loginForm.account || !loginForm.password) {
      setPasswordLoginError("請輸入帳號和密碼");
      return;
    }

    setPasswordLoginLoading(true);
    setPasswordLoginError(null);

    try {
      const result = await authService.loginWithPassword(loginForm.account, loginForm.password);

      if (result.success) {
        // 根據角色決定跳轉頁面
        const user = authService.getUser();
        const userRole = user?.role;
        let defaultRedirect = "/dashboard";

        // 縣市政府和輔導人員預設導向 profile 頁面
        if (userRole === "epa" || userRole === "tutor") {
          defaultRedirect = "/profile";
        }

        const urlParams = new URLSearchParams(location.search);
        const redirectTo = urlParams.get("redirect") || defaultRedirect;
        navigate(redirectTo, { replace: true });
      } else {
        console.error("密碼登入失敗:", result);
        setPasswordLoginError(result.message || "帳號或密碼錯誤，請重新嘗試");
      }
    } catch (error) {
      console.error("密碼登入處理錯誤:", error);
      const errorMessage = error instanceof Error ? error.message : "未知錯誤";
      setPasswordLoginError(`登入處理過程中發生錯誤: ${errorMessage}`);
    } finally {
      setPasswordLoginLoading(false);
    }
  };

  // 處理表單輸入
  const handleInputChange = (field: "account" | "password") => (e: React.ChangeEvent<HTMLInputElement>) => {
    setLoginForm((prev) => ({
      ...prev,
      [field]: e.target.value,
    }));
    // 清除錯誤訊息
    if (passwordLoginError) {
      setPasswordLoginError(null);
    }
  };

  // 處理環境變化
  const handleEnvironmentChange = (newEnv: EnvironmentConfig) => {
    console.log(`🌍 [LoginPage] 環境已變更: ${newEnv.displayName}`);
    setCurrentEnvironment(newEnv);

    // 如果正在 token 登入過程中，需要重置狀態
    if (loading && tokenInfo) {
      setLoading(false);
      setTokenInfo(null);
      setError(null);
      setHasProcessed(false);
    }
  };

  // 如果正在載入
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin text-green-600" />
              處理登入中
            </CardTitle>
            {tokenInfo && <CardDescription className="font-size-sm text-muted-foreground">{tokenInfo}</CardDescription>}
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600">請稍候...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 如果有錯誤
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              登入失敗
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Button onClick={handleRetry} className="w-full">
                重新嘗試
              </Button>

              <div className="font-size-xs text-muted-foreground space-y-1">
                <p>
                  <strong>調試資訊：</strong>
                </p>
                <p>當前 URL: {window.location.href}</p>
                <p>查找的參數: token, userToken, authToken</p>
                <p>
                  找到的參數值:{" "}
                  {new URLSearchParams(location.search).get("token") ||
                    new URLSearchParams(location.search).get("userToken") ||
                    new URLSearchParams(location.search).get("authToken") ||
                    "無"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 顯示登入界面
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>EcoCampus 生態學校系統</CardTitle>
          <CardDescription>請選擇登入方式</CardDescription>
        </CardHeader>
        <CardContent>
          {/* 顯示登出原因訊息 */}
          {reasonMessage && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{reasonMessage}</AlertDescription>
            </Alert>
          )}

          {/* 環境指示器 */}
          <div className="mb-4 p-2 bg-muted rounded-lg">
            <EnvironmentSelector compact={true} showQuickLinks={false} onEnvironmentChange={handleEnvironmentChange} />
          </div>

          <Tabs value={loginMethod} onValueChange={(value) => setLoginMethod(value as "token" | "password" | "environment")} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="token">Token 登入</TabsTrigger>
              <TabsTrigger value="password">密碼登入</TabsTrigger>
              <TabsTrigger value="environment">
                <Settings className="h-4 w-4 mr-1" />
                環境
              </TabsTrigger>
            </TabsList>

            <TabsContent value="token" className="space-y-4">
              <Alert>
                <AlertDescription>此系統支援從主系統跳轉登入，或在 URL 中提供有效的 token 參數。</AlertDescription>
              </Alert>

              <div className="font-size-xs text-muted-foreground space-y-1">
                <p>
                  <strong>Token 登入說明：</strong>
                </p>
                <p>URL 格式: ?token=your_token_here</p>
                <p>範例: /login?token=275512E3-F589-4134-96F7-25B50EBF14AE</p>
                <p>當前 URL: {window.location.href}</p>
              </div>

              <div className="space-y-2">
                <Button
                  onClick={() => {
                    setHasProcessed(false);
                    setLoading(true);
                    setError(null);
                  }}
                  variant="outline"
                  className="w-full">
                  重新檢查 Token
                </Button>

                <Button onClick={() => navigate("/")} variant="outline" className="w-full">
                  返回首頁
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="password" className="space-y-4">
              <form onSubmit={handlePasswordLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="account">帳號</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="account"
                      type="text"
                      placeholder="請輸入您的帳號"
                      value={loginForm.account}
                      onChange={handleInputChange("account")}
                      className="pl-10"
                      disabled={passwordLoginLoading}
                      autoComplete="username"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">密碼</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="請輸入您的密碼"
                      value={loginForm.password}
                      onChange={handleInputChange("password")}
                      className="pl-10 pr-10"
                      disabled={passwordLoginLoading}
                      autoComplete="current-password"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={passwordLoginLoading}>
                      {showPassword ? <EyeOff className="h-4 w-4 text-muted-foreground" /> : <Eye className="h-4 w-4 text-muted-foreground" />}
                    </Button>
                  </div>
                </div>

                {passwordLoginError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{passwordLoginError}</AlertDescription>
                  </Alert>
                )}

                <Button type="submit" className="w-full" disabled={passwordLoginLoading || !loginForm.account || !loginForm.password}>
                  {passwordLoginLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      登入中...
                    </>
                  ) : (
                    "登入"
                  )}
                </Button>
              </form>

              {currentEnvironment.features.showTestAccounts && (
                <div className="font-size-xs text-muted-foreground text-center">
                  <p>使用已創建的測試帳號登入：</p>
                  <p>school_user, epa_user, tutor_user</p>
                  <p>密碼: 測試密碼</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="environment" className="space-y-4">
              <EnvironmentSelector compact={false} showQuickLinks={true} onEnvironmentChange={handleEnvironmentChange} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginPage;
