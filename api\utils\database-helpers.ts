import { executeQuery, executeQuerySingle, executeUpdate } from "../config/database-mssql.js";
import { APILogger } from "./logger.js";

// 查詢結果介面
export interface QueryResult<T = unknown> {
  success: boolean;
  data?: T;
  count?: number;
  error?: string;
}

// 分頁查詢參數
export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

/**
 * 執行帶錯誤處理的查詢
 */
export async function safeExecuteQuery<T>(query: string, params?: Record<string, unknown>, context?: string): Promise<QueryResult<T[]>> {
  try {
    if (context) {
      APILogger.logDatabase(context, query, params);
    }

    const data = await executeQuery<T>(query, params);

    if (context) {
      APILogger.logDatabase(context, "查詢成功", undefined, data.length);
    }

    return {
      success: true,
      data,
      count: data.length,
    };
  } catch (error) {
    if (context) {
      APILogger.logDatabase(context, "查詢失敗", params);
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 執行帶錯誤處理的單行查詢
 */
export async function safeExecuteQuerySingle<T>(query: string, params?: Record<string, unknown>, context?: string): Promise<QueryResult<T>> {
  try {
    if (context) {
      APILogger.logDatabase(context, query, params);
    }

    const data = await executeQuerySingle<T>(query, params);

    if (context) {
      APILogger.logDatabase(context, data ? "查詢成功" : "未找到記錄", undefined, data ? 1 : 0);
    }

    return {
      success: true,
      data: data || undefined,
      count: data ? 1 : 0,
    };
  } catch (error) {
    if (context) {
      APILogger.logDatabase(context, "查詢失敗", params);
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 執行帶錯誤處理的更新操作
 */
export async function safeExecuteUpdate(query: string, params?: Record<string, unknown>, context?: string): Promise<QueryResult<number>> {
  try {
    if (context) {
      APILogger.logDatabase(context, query, params);
    }

    const affectedRows = await executeUpdate(query, params);

    if (context) {
      APILogger.logDatabase(context, "更新成功", undefined, affectedRows);
    }

    return {
      success: true,
      data: affectedRows,
      count: affectedRows,
    };
  } catch (error) {
    if (context) {
      APILogger.logDatabase(context, "更新失敗", params);
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 建構分頁查詢
 */
export function buildPaginatedQuery(
  baseQuery: string,
  pagination: PaginationParams,
  orderBy?: string
): { query: string; countQuery: string; params: Record<string, unknown> } {
  const offset = pagination.offset ?? (pagination.page - 1) * pagination.limit;

  // 建構計數查詢
  const countQuery = `SELECT COUNT(*) as total FROM (${baseQuery}) as countTable`;

  // 建構分頁查詢
  const orderClause = orderBy ? `ORDER BY ${orderBy}` : "";
  const paginatedQuery = `
    ${baseQuery}
    ${orderClause}
    OFFSET @offset ROWS
    FETCH NEXT @limit ROWS ONLY
  `;

  return {
    query: paginatedQuery,
    countQuery,
    params: {
      offset,
      limit: pagination.limit,
    },
  };
}

/**
 * 執行分頁查詢
 */
export async function executePaginatedQuery<T>(
  baseQuery: string,
  pagination: PaginationParams,
  queryParams?: Record<string, unknown>,
  orderBy?: string,
  context?: string
): Promise<{
  success: boolean;
  data?: T[];
  total?: number;
  error?: string;
}> {
  try {
    const { query, countQuery, params } = buildPaginatedQuery(baseQuery, pagination, orderBy);
    const allParams = { ...queryParams, ...params };

    // 執行計數查詢
    const countResult = await safeExecuteQuerySingle<{ total: number }>(countQuery, queryParams, context ? `${context} - 計數查詢` : undefined);

    if (!countResult.success || !countResult.data) {
      return {
        success: false,
        error: countResult.error || "計數查詢失敗",
      };
    }

    // 執行分頁查詢
    const dataResult = await safeExecuteQuery<T>(query, allParams, context ? `${context} - 分頁查詢` : undefined);

    if (!dataResult.success) {
      return {
        success: false,
        error: dataResult.error,
      };
    }

    return {
      success: true,
      data: dataResult.data,
      total: countResult.data.total,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 建構 WHERE 條件
 */
export function buildWhereConditions(conditions: Record<string, unknown>, prefix: string = ""): { whereClause: string; params: Record<string, unknown> } {
  const clauses: string[] = [];
  const params: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(conditions)) {
    if (value !== undefined && value !== null && value !== "") {
      const paramName = prefix ? `${prefix}_${key}` : key;
      clauses.push(`${key} = @${paramName}`);
      params[paramName] = value;
    }
  }

  return {
    whereClause: clauses.length > 0 ? `WHERE ${clauses.join(" AND ")}` : "",
    params,
  };
}

/**
 * 建構動態更新語句
 */
export function buildUpdateStatement(
  tableName: string,
  updateData: Record<string, unknown>,
  whereConditions: Record<string, unknown>,
  prefix: string = ""
): { query: string; params: Record<string, unknown> } {
  const updateClauses: string[] = [];
  const params: Record<string, unknown> = {};

  // 建構 SET 子句
  for (const [key, value] of Object.entries(updateData)) {
    if (value !== undefined) {
      const paramName = `update_${prefix ? prefix + "_" : ""}${key}`;
      updateClauses.push(`${key} = @${paramName}`);
      params[paramName] = value;
    }
  }

  // 建構 WHERE 子句
  const { whereClause, params: whereParams } = buildWhereConditions(whereConditions, `where_${prefix}`);

  Object.assign(params, whereParams);

  const query = `
    UPDATE ${tableName}
    SET ${updateClauses.join(", ")}
    ${whereClause}
  `;

  return { query, params };
}

/**
 * 檢查記錄是否存在
 */
export async function recordExists(tableName: string, conditions: Record<string, unknown>, context?: string): Promise<boolean> {
  const { whereClause, params } = buildWhereConditions(conditions);
  const query = `SELECT COUNT(*) as count FROM ${tableName} ${whereClause}`;

  const result = await safeExecuteQuerySingle<{ count: number }>(query, params, context);

  return result.success && result.data ? result.data.count > 0 : false;
}

/**
 * 批量插入助手
 */
export async function batchInsert<T extends Record<string, unknown>>(tableName: string, records: T[], context?: string): Promise<QueryResult<number>> {
  if (records.length === 0) {
    return { success: true, data: 0, count: 0 };
  }

  try {
    const columns = Object.keys(records[0]);
    const values: string[] = [];
    const params: Record<string, unknown> = {};

    records.forEach((record, index) => {
      const recordParams: string[] = [];
      columns.forEach((column) => {
        const paramName = `${column}_${index}`;
        recordParams.push(`@${paramName}`);
        params[paramName] = record[column];
      });
      values.push(`(${recordParams.join(", ")})`);
    });

    const query = `
      INSERT INTO ${tableName} (${columns.join(", ")})
      VALUES ${values.join(", ")}
    `;

    return await safeExecuteUpdate(query, params, context);
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 安全刪除（軟刪除）
 */
export async function safeSoftDelete(
  tableName: string,
  conditions: Record<string, unknown>,
  statusColumn: string = "Status",
  context?: string
): Promise<QueryResult<number>> {
  const updateData = { [statusColumn]: 0 };
  const { query, params } = buildUpdateStatement(tableName, updateData, conditions);

  return await safeExecuteUpdate(query, params, context);
}

/**
 * 驗證外鍵約束
 */
export async function validateForeignKey(referencedTable: string, referencedColumn: string, value: unknown, context?: string): Promise<boolean> {
  const conditions = { [referencedColumn]: value };
  return await recordExists(referencedTable, conditions, context);
}
