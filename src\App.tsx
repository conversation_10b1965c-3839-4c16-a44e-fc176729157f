import React, { <PERSON>rrorInfo, Component } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { FontSizeProvider } from "@/hooks/useFontSize";
import Navbar from "@/components/Navbar";
import RolePermissionAlert from "@/components/RolePermissionAlert";
import CertificatePage from "@/pages/CertificatePage";
import NewsPage from "@/pages/NewsPage";
import NewsGuidelinesPage from "@/pages/NewsGuidelinesPage";
import CampusSubmissionCreatePage from "@/pages/CampusSubmissionCreatePage";
import CampusSubmissionDetailPage from "@/pages/CampusSubmissionDetailPage";
import GuidancePage from "@/pages/GuidancePage";
import GuidanceApplyPage from "@/pages/GuidanceApplyPage";
import BindPinPage from "@/pages/BindPinPage";
import NaturalPersonCertificatePage from "@/pages/NaturalPersonCertificatePage";
import DashboardPage from "./pages/DashboardPage";
import CertificationTypePage from "@/pages/CertificationTypePage";
import CertificationApplicationPage from "./pages/CertificationApplicationPage";
import CertificationApplicationIframePage from "./pages/CertificationApplicationIframePage";
import LoginPage from "./pages/LoginPage";
import ProfilePage from "./pages/ProfilePage";
import NotFound from "./pages/NotFound";
import Index from "./pages/Index";
import SchoolManagementPage from "./pages/SchoolManagementPage";
import SchoolDetailPage from "./pages/SchoolDetailPage";
import { AuthProvider } from "@/hooks/useAuth";
import { TokenProvider } from "@/contexts/TokenContext";
import {
  RoleBasedRoute,
  SchoolOnlyRoute,
  EPAOnlyRoute,
  TutorOnlyRoute,
  GovernmentRoute,
  AllMemberRoute,
} from "@/components/RoleBasedRoutes";
import { getFrontendBasePath } from "@/config/environment"; // 加這行

const queryClient = new QueryClient();

// 錯誤邊界組件
class ErrorBoundary extends Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("React Error Boundary caught an error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: "20px", textAlign: "center" }}>
          <h1>系統錯誤</h1>
          <p>應用程式發生錯誤，請刷新頁面或聯繫管理員。</p>
          <button onClick={() => window.location.reload()}>重新載入頁面</button>
          <details style={{ marginTop: "20px", textAlign: "left" }}>
            <summary>錯誤詳情</summary>
            <pre>{this.state.error?.stack}</pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

// 受保護的路由佈局組件
const ProtectedLayout = ({ children }: { children: React.ReactNode }) => (
  <>
    <Navbar />
    {children}
  </>
);

const App = () => {
  console.log("App component rendering...");

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <FontSizeProvider>
            <TokenProvider
              enableAutoRefresh={false}
              enableValidationCache={true}
              onTokenChange={(token) => {
                console.log('🔑 [App] Token changed in Context:', token ? '***已設定***' : '未設定');
              }}
              onTokenError={(error) => {
                console.error('❌ [App] Token error in Context:', error);
              }}
            >
              <AuthProvider>
                <RolePermissionAlert />
                <Toaster />
                <Sonner />
                <BrowserRouter basename={getFrontendBasePath()}>
                {/* <BrowserRouter> */}
                <Routes>
                  {/* 公開路由 - 不需要認證 */}
                  <Route path="/login" element={<LoginPage />} />

                  {/* Iframe 獨立頁面路由 - 不包含 Navbar */}
                  <Route
                    path="/certification-application-iframe"
                    element={<CertificationApplicationIframePage />}
                  />

                  {/* 根目錄重定向 */}
                  <Route path="/" element={<Index />} />

                  {/* 受保護的路由 - 需要認證且有 Navbar */}

                  {/* 所有會員都可以訪問的路由 */}
                  <Route
                    path="/dashboard"
                    element={
                      <AllMemberRoute>
                        <ProtectedLayout>
                          <DashboardPage />
                        </ProtectedLayout>
                      </AllMemberRoute>
                    }
                  />

                  {/* 基本資料維護 - 所有會員都可以訪問 */}
                  <Route
                    path="/profile"
                    element={
                      <AllMemberRoute>
                        <ProtectedLayout>
                          <ProfilePage />
                        </ProtectedLayout>
                      </AllMemberRoute>
                    }
                  />

                  {/* 學校專用路由 */}

                  {/* 自然人憑證 */}
                  <Route
                    path="/ein"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <NaturalPersonCertificatePage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 認證申請 */}
                  <Route
                    path="/certificate"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <CertificatePage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 新增認證 */}
                  <Route
                    path="/certificate/select-type"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <CertificationTypePage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 認證申請 有iframe的頁面 */}
                  <Route
                    path="/certificate/application/:certificationId"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <CertificationApplicationPage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 校園新聞投稿 */}
                  <Route
                    path="/news"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <NewsPage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 校園新聞投稿 - 新增投稿 */}
                  <Route
                    path="/news/create"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <CampusSubmissionCreatePage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 校園新聞投稿 - 編輯 / 查看 已投稿內容 */}
                  <Route
                    path="/news/detail/:id"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <CampusSubmissionDetailPage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 投稿說明 */}
                  <Route
                    path="/news/guidelines"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <NewsGuidelinesPage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 輔導申請 */}
                  <Route
                    path="/guidance-apply"
                    element={
                      <SchoolOnlyRoute>
                        <ProtectedLayout>
                          <GuidanceApplyPage />
                        </ProtectedLayout>
                      </SchoolOnlyRoute>
                    }
                  />

                  {/* 政府人員專用路由（EPA + Tutor）*/}
                  <Route
                    path="/school-management"
                    element={
                      <GovernmentRoute>
                        <ProtectedLayout>
                          <SchoolManagementPage />
                        </ProtectedLayout>
                      </GovernmentRoute>
                    }
                  />

                  {/* 生態學校 查看紀錄 */}
                  <Route
                    path="/school-management/detail/:schoolId"
                    element={
                      <GovernmentRoute>
                        <ProtectedLayout>
                          <SchoolDetailPage />
                        </ProtectedLayout>
                      </GovernmentRoute>
                    }
                  />

                  {/* 縣市政府專用路由 */}

                  {/* 認證審核 */}
                  <Route
                    path="/certification/review"
                    element={
                      <EPAOnlyRoute>
                        <ProtectedLayout>
                          <div className="p-8 text-center">
                            <h1 className="font-size-2xl font-bold">
                              認證審核
                            </h1>
                            <p className="text-muted-foreground mt-2">
                              認證申請審核功能（開發中）
                            </p>
                          </div>
                        </ProtectedLayout>
                      </EPAOnlyRoute>
                    }
                  />

                  {/* 區域管理 */}
                  <Route
                    path="/area-management"
                    element={
                      <EPAOnlyRoute>
                        <ProtectedLayout>
                          <div className="p-8 text-center">
                            <h1 className="font-size-2xl font-bold">
                              區域管理
                            </h1>
                            <p className="text-muted-foreground mt-2">
                              縣市區域管理功能（開發中）
                            </p>
                          </div>
                        </ProtectedLayout>
                      </EPAOnlyRoute>
                    }
                  />

                  {/* 輔導人員專用路由 */}
                  <Route
                    path="/guidance"
                    element={
                      <TutorOnlyRoute>
                        <ProtectedLayout>
                          <GuidancePage />
                        </ProtectedLayout>
                      </TutorOnlyRoute>
                    }
                  />

                  {/* 認證支援 */}
                  <Route
                    path="/certification/support"
                    element={
                      <TutorOnlyRoute>
                        <ProtectedLayout>
                          <div className="p-8 text-center">
                            <h1 className="font-size-2xl font-bold">
                              認證支援
                            </h1>
                            <p className="text-muted-foreground mt-2">
                              認證支援功能（開發中）
                            </p>
                          </div>
                        </ProtectedLayout>
                      </TutorOnlyRoute>
                    }
                  />

                  {/* 政府人員共用路由（EPA + Tutor）*/}
                  <Route
                    path="/reports"
                    element={
                      <GovernmentRoute>
                        <ProtectedLayout>
                          <div className="p-8 text-center">
                            <h1 className="font-size-2xl font-bold">
                              統計報表
                            </h1>
                            <p className="text-muted-foreground mt-2">
                              各類統計報表功能（開發中）
                            </p>
                          </div>
                        </ProtectedLayout>
                      </GovernmentRoute>
                    }
                  />

                  {/* 會員審核 */}
                  <Route
                    path="/member-review"
                    element={
                      <GovernmentRoute>
                        <ProtectedLayout>
                          <div className="p-8 text-center">
                            <h1 className="font-size-2xl font-bold">
                              會員審核
                            </h1>
                            <p className="text-muted-foreground mt-2">
                              會員申請審核功能（開發中）
                            </p>
                          </div>
                        </ProtectedLayout>
                      </GovernmentRoute>
                    }
                  />

                  {/* 404 頁面 - 必須放在最後 */}
                  <Route
                    path="*"
                    element={
                      <ProtectedLayout>
                        <NotFound />
                      </ProtectedLayout>
                    }
                  />
                </Routes>
              </BrowserRouter>
            </AuthProvider>
          </TokenProvider>
          </FontSizeProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
