import React from "react";
import { YesNoTemplate } from "../templates/template-01-yes-no";
import { TeamMemberTemplate } from "../templates/template-02-team-member";
import { MeetingRecordTemplate } from "../templates/template-03-meeting-record";
import { TextAreaTemplate } from "../templates/template-16-textarea";
import type {
  YesNoData,
  TeamMemberData,
  MeetingRecordData,
  ShareMeetingInfoData,
  RecruitmentData,
  PhotoUploadData,
  PathSelectionData,
  MonitoringRecordData,
  TeachingCaseData,
  Template11Data,
  Template13Data,
  Template22Data,
  TextAreaData,
  SummaryEssayData,
  RenewalMeetingData,
} from "../types";

// ========== 問題模板工廠 ==========

interface QuestionTemplateFactoryProps {
  templateId: number;
  data: unknown;
  onChange: (data: unknown) => void;
  disabled?: boolean;
}

export const QuestionTemplateFactory: React.FC<QuestionTemplateFactoryProps> = ({ templateId, data, onChange, disabled = false }) => {
  // 通用屬性
  const commonProps = {
    disabled,
  };

  switch (templateId) {
    case 1:
      return <YesNoTemplate data={data as YesNoData} onChange={onChange} {...commonProps} />;
    case 2:
      return <TeamMemberTemplate data={data as TeamMemberData} onChange={onChange} {...commonProps} />;
    case 3:
      return <MeetingRecordTemplate data={data as MeetingRecordData} onChange={onChange} {...commonProps} />;
    case 16:
      return <TextAreaTemplate data={data as TextAreaData} onChange={onChange} {...commonProps} />;

    // 其他模板暫時使用文字區域模板
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    default:
      return <TextAreaTemplate data={data as TextAreaData} onChange={onChange} {...commonProps} />;
  }
};

// 用於檢查問題是否已完成的輔助函數
export const isQuestionCompleted = (templateId: number, data: unknown): boolean => {
  try {
    switch (templateId) {
      case 1: {
        const yesNoData = data as YesNoData;
        return Boolean(yesNoData?.is_yes_no?.trim());
      }
      case 2: {
        const teamData = data as TeamMemberData;
        return Boolean(
          teamData?.student_list?.some((item) => item.input_1?.trim() || item.input_2?.trim() || item.input_3?.trim()) ||
            teamData?.teacher_list?.some((item) => item.input_1?.trim() || item.input_2?.trim() || item.input_3?.trim()) ||
            teamData?.community_member_list?.some((item) => item.input_1?.trim() || item.input_2?.trim() || item.input_3?.trim())
        );
      }
      case 3: {
        const meetingData = data as MeetingRecordData;
        return Boolean(meetingData?.meeting_date_and_theme?.some((item) => item.input_1?.trim() && item.input_2?.trim()));
      }
      case 16: {
        const textData = data as TextAreaData;
        return Boolean(textData?.textarea?.trim());
      }
      default: {
        const textData = data as TextAreaData;
        return Boolean(textData?.textarea?.trim());
      }
    }
  } catch (error) {
    console.warn("檢查問題完成狀態時出現錯誤:", error);
    return false;
  }
};
