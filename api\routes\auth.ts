import express from "express";
import { AuthService } from "../services/auth-service.js";
import {
  TokenValidationRequest,
  TokenLoginRequest,
  PasswordLoginRequest,
  ChangePasswordRequest,
  AdminResetPasswordRequest,
  CreateTokenRequest,
  TokenValidationResponse,
  PasswordLoginResponse,
  ChangePasswordResponse,
  AdminResetPasswordResponse,
  CreateTokenResponse,
  LogoutResponse,
  RoleCheckResponse,
} from "../models/auth.js";
import { ALLOWED_ROLE_TYPES, AUTH_ERROR_MESSAGES, AUTH_SUCCESS_MESSAGES, validatePasswordStrength } from "../constants/auth.js";
import { APILogger } from "../utils/logger.js";

const router = express.Router();

router.get("/token-status", async (req: express.Request, res: express.Response) => {
  const token = req.headers["x-user-token"] as string;
  const validationResult = await AuthService.validateToken(token);
  return res.json(validationResult);
});

// Token 直接登入 API
router.post("/token-login", async (req: express.Request<{}, TokenValidationResponse, TokenLoginRequest>, res: express.Response<TokenValidationResponse>) => {
  try {
    APILogger.logRequest(req, "Auth", "Token 直接登入");
    const { token } = req.body;

    if (!token) {
      APILogger.logError("Auth", "Token 直接登入", AUTH_ERROR_MESSAGES.TOKEN_REQUIRED, 400);
      return res.status(400).json({
        success: false,
        data: {
          valid: false,
        },
        message: AUTH_ERROR_MESSAGES.TOKEN_REQUIRED,
      });
    }

    // 驗證 Token
    const validationResult = await AuthService.validateToken(token);

    if (validationResult.valid && validationResult.user) {
      const response: TokenValidationResponse = {
        success: true,
        data: {
          valid: true,
          user: validationResult.user,
          details: validationResult.user
            ? {
                userRole: validationResult.user.roleType,
                allowedRoles: [...ALLOWED_ROLE_TYPES],
                action: "account_logout",
              }
            : undefined,
        },
        message: AUTH_SUCCESS_MESSAGES.TOKEN_LOGIN_SUCCESS,
      };

      APILogger.logSuccess(
        "Auth",
        "Token 直接登入",
        {
          account: validationResult.user.account,
          roleType: validationResult.user.roleType,
        },
        AUTH_SUCCESS_MESSAGES.TOKEN_LOGIN_SUCCESS
      );

      return res.json(response);
    } else {
      APILogger.logError("Auth", "Token 直接登入", validationResult.message || AUTH_ERROR_MESSAGES.INVALID_TOKEN, 401);
      return res.status(401).json({
        success: false,
        data: {
          valid: false,
        },
        message: validationResult.message || AUTH_ERROR_MESSAGES.INVALID_TOKEN,
      });
    }
  } catch (error: unknown) {
    APILogger.logError("Auth", "Token 直接登入", error, 500);
    return res.status(500).json({
      success: false,
      data: {
        valid: false,
      },
      message: AUTH_ERROR_MESSAGES.INTERNAL_ERROR,
    });
  }
});

// 密碼登入 API
router.post("/password-login", async (req: express.Request<{}, PasswordLoginResponse, PasswordLoginRequest>, res: express.Response<PasswordLoginResponse>) => {
  try {
    APILogger.logRequest(req, "Auth", "密碼登入");
    const { account, password } = req.body;

    if (!account || !password) {
      APILogger.logError("Auth", "密碼登入", AUTH_ERROR_MESSAGES.CREDENTIALS_REQUIRED, 400);
      return res.status(400).json({
        success: false,
        data: {
          valid: false,
          token: "",
        },
        message: AUTH_ERROR_MESSAGES.CREDENTIALS_REQUIRED,
      });
    }

    // 驗證帳號密碼
    const loginResult = await AuthService.validateAccountPassword(account, password);

    if (loginResult.valid && loginResult.user) {
      // 生成新的登入 Token
      try {
        const newToken = await AuthService.createLoginToken(loginResult.user.id, 30);

        const response: PasswordLoginResponse = {
          success: true,
          data: {
            valid: true,
            user: loginResult.user,
            token: newToken,
          },
          message: AUTH_SUCCESS_MESSAGES.PASSWORD_LOGIN_SUCCESS,
        };

        APILogger.logSuccess(
          "Auth",
          "密碼登入",
          {
            account: loginResult.user.account,
            roleType: loginResult.user.roleType,
          },
          AUTH_SUCCESS_MESSAGES.PASSWORD_LOGIN_SUCCESS
        );

        return res.json(response);
      } catch (tokenError) {
        APILogger.logError("Auth", "密碼登入", `生成 Token 失敗: ${tokenError}`, 500);
        return res.status(500).json({
          success: false,
          data: {
            valid: false,
            token: "",
            user: loginResult.user,
          },
          message: AUTH_ERROR_MESSAGES.TOKEN_GENERATION_FAILED,
        });
      }
    } else {
      APILogger.logError("Auth", "密碼登入", loginResult.message || AUTH_ERROR_MESSAGES.INVALID_CREDENTIALS, 401);
      return res.status(401).json({
        success: false,
        data: {
          valid: false,
          token: "",
          user: loginResult.user,
          details: loginResult.user
            ? {
                userRole: loginResult.user.roleType,
                allowedRoles: [...ALLOWED_ROLE_TYPES],
                action: "account_logout",
              }
            : undefined,
        },
        message: loginResult.message || AUTH_ERROR_MESSAGES.INVALID_CREDENTIALS,
      });
    }
  } catch (error: unknown) {
    APILogger.logError("Auth", "密碼登入", error, 500);
    return res.status(500).json({
      success: false,
      data: {
        valid: false,
        token: "",
      },
      message: AUTH_ERROR_MESSAGES.INTERNAL_ERROR,
    });
  }
});

// 修改密碼 API
router.post(
  "/change-password",
  async (req: express.Request<{}, ChangePasswordResponse, ChangePasswordRequest>, res: express.Response<ChangePasswordResponse>) => {
    try {
      APILogger.logRequest(req, "Auth", "修改密碼");
      const token = req.headers["x-user-token"] as string;
      const { oldPassword, newPassword } = req.body;

      if (!token) {
        return res.status(401).json({
          success: false,
          message: AUTH_ERROR_MESSAGES.UNAUTHORIZED,
        });
      }

      if (!oldPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          message: "舊密碼和新密碼為必填項目",
        });
      }

      // 驗證新密碼強度
      const passwordValidation = validatePasswordStrength(newPassword);
      if (!passwordValidation.valid) {
        return res.status(400).json({
          success: false,
          message: passwordValidation.message || AUTH_ERROR_MESSAGES.PASSWORD_REQUIREMENTS,
        });
      }

      // 驗證當前用戶 Token
      const validationResult = await AuthService.validateToken(token);
      if (!validationResult.valid || !validationResult.user) {
        return res.status(401).json({
          success: false,
          message: "無效的登入狀態，請重新登入",
        });
      }

      const userId = validationResult.user.id;
      const userAccount = validationResult.user.account;

      // 驗證舊密碼
      const oldPasswordValidation = await AuthService.validateAccountPassword(userAccount, oldPassword);
      if (!oldPasswordValidation.valid) {
        return res.status(400).json({
          success: false,
          message: AUTH_ERROR_MESSAGES.OLD_PASSWORD_INCORRECT,
        });
      }

      // 更新密碼
      const updateResult = await AuthService.updatePassword(userId, newPassword);
      if (updateResult.success) {
        APILogger.logSuccess("Auth", "修改密碼", { userAccount }, AUTH_SUCCESS_MESSAGES.PASSWORD_CHANGED);
        return res.json({
          success: true,
          message: updateResult.message,
          user: {
            id: userId,
            account: userAccount,
          },
        });
      } else {
        return res.status(500).json({
          success: false,
          message: updateResult.message,
        });
      }
    } catch (error: unknown) {
      APILogger.logError("Auth", "修改密碼", error, 500);
      return res.status(500).json({
        success: false,
        message: "修改密碼時發生系統錯誤",
      });
    }
  }
);

// 登出 API
router.post("/logout", async (req: express.Request<{}, LogoutResponse>, res: express.Response<LogoutResponse>) => {
  try {
    APILogger.logRequest(req, "Auth", "用戶登出");
    const token = req.headers["x-user-token"] as string;

    if (token) {
      try {
        await AuthService.revokeToken(token);
        APILogger.logSuccess("Auth", "用戶登出", { token: token.substring(0, 8) + "..." }, "Token 已撤銷");
      } catch (error) {
        APILogger.logError("Auth", "用戶登出", `撤銷 Token 失敗: ${error}`, 500);
      }
    }

    return res.json({
      success: true,
      message: AUTH_SUCCESS_MESSAGES.LOGOUT_SUCCESS,
    });
  } catch (error: unknown) {
    APILogger.logError("Auth", "用戶登出", error, 500);
    return res.status(500).json({
      success: false,
      message: AUTH_ERROR_MESSAGES.INTERNAL_ERROR,
    });
  }
});

// 管理員重設密碼 API
router.post(
  "/admin-reset-password",
  async (req: express.Request<{}, AdminResetPasswordResponse, AdminResetPasswordRequest>, res: express.Response<AdminResetPasswordResponse>) => {
    try {
      APILogger.logRequest(req, "Auth", "管理員重設密碼");
      const { accountIds, newPassword } = req.body;

      if (!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: "帳號 ID 列表為必填項目",
          results: [],
        });
      }

      if (!newPassword) {
        return res.status(400).json({
          success: false,
          message: "新密碼為必填項目",
          results: [],
        });
      }

      // 驗證密碼強度
      const passwordValidation = validatePasswordStrength(newPassword);
      if (!passwordValidation.valid) {
        return res.status(400).json({
          success: false,
          message: passwordValidation.message || AUTH_ERROR_MESSAGES.PASSWORD_REQUIREMENTS,
          results: [],
        });
      }

      // 執行批量密碼重設
      const results = await AuthService.resetPasswordsForAccounts(accountIds, newPassword);

      const successCount = results.filter((r) => r.success).length;
      APILogger.logSuccess(
        "Auth",
        "管理員重設密碼",
        {
          totalAccounts: accountIds.length,
          successCount,
        },
        `成功重設 ${successCount}/${accountIds.length} 個帳號的密碼`
      );

      return res.json({
        success: true,
        message: `成功重設 ${successCount}/${accountIds.length} 個帳號的密碼`,
        results,
      });
    } catch (error: unknown) {
      APILogger.logError("Auth", "管理員重設密碼", error, 500);
      return res.status(500).json({
        success: false,
        message: AUTH_ERROR_MESSAGES.INTERNAL_ERROR,
        results: [],
      });
    }
  }
);

// TODO: 其他端點需要後續實現
// - POST /validate     Token 驗證
// - POST /external-login 外部登入
// - POST /create-token  創建 Token
// - GET /role-check    角色檢查

export default router;
