/**
 * 共用模組主入口點
 * 統一匯出所有共用的常數、介面和工具
 * 
 * @description 此檔案為 shared 目錄的主要入口，
 * 提供整個專案一致性的共用資源存取點
 * @version 1.0.0
 */

// === 常數模組 ===
export * from './constants';

// === 介面模組 ===  
export * from './interfaces';

// === 服務模組 ===
export * from './services';

// === 適配器模組 ===
export * from './adapters/AuthAdapter';

/**
 * 共用模組命名空間
 * 提供結構化的存取方式
 */
export const Shared = {
  // 常數
  Constants: () => import('./constants'),
  
  // 介面
  Interfaces: () => import('./interfaces'),
  
  // 服務
  Services: () => import('./services'),
  
  // 適配器
  Adapters: () => import('./adapters/AuthAdapter')
} as const;

/**
 * 版本資訊
 */
export const SHARED_MODULE_VERSION = '1.0.0';

/**
 * 模組資訊
 */
export const SHARED_MODULE_INFO = {
  version: SHARED_MODULE_VERSION,
  description: 'EcoCampus 共用模組 - 統一管理常數、介面和工具',
  author: 'EcoCampus Development Team',
  createdAt: '2025-08-20',
  modules: [
    'constants/roles',
    'constants/error-codes', 
    'constants/validation-patterns',
    'interfaces/IAuthService',
    'interfaces/ITokenSource',
    'interfaces/IUserService',
    'services/TokenManager',
    'services/TokenValidator',
    'services/UserDataService',
    'adapters/AuthAdapter'
  ]
} as const;