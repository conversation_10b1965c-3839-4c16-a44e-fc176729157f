// API 攔截器 - 處理 Token 過期和統一錯誤管理
import authService from "./authService";
import { isFeatureEnabled } from "@/config/environment";

// API 響應類型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
  timestamp?: string;
}

// API 錯誤響應類型
export interface ApiErrorResponse {
  message?: string;
  code?: string;
  details?: {
    userRole?: string;
    allowedRoles?: string[];
    action?: string;
  };
}

// Token 過期相關的錯誤碼
const TOKEN_EXPIRED_CODES = [
  "TOKEN_EXPIRED",
  "TOKEN_INVALID",
  "TOKEN_REVOKED",
  "UNAUTHORIZED",
  "AUTH_FAILED",
  "TOKEN_NOT_FOUND", // 新增：Token 找不到
  "VALIDATION_FAILED", // 新增：驗證失敗
  "TOKEN_MISSING", // 新增：Token 遺失
  "AUTHENTICATION_ERROR", // 新增：認證錯誤
  "NO_TOKEN", // 新增：無 Token
];

// API 攔截器類
class ApiInterceptor {
  private isRedirecting = false;
  private readonly redirectDelay = 2000; // 2秒延遲重定向

  // 處理 API 響應
  async handleResponse<T>(response: Response, originalUrl: string): Promise<T> {
    console.log(`🌐 [API] 處理響應: ${response.status} ${originalUrl}`);

    try {
      // 檢查是否為成功響應
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ [API] 成功響應:`, data);
        return data;
      }

      // 處理錯誤響應
      const errorData: ApiErrorResponse = await response.json().catch(() => ({
        message: `HTTP ${response.status}: ${response.statusText}`,
        code: "HTTP_ERROR",
      }));

      console.error(`❌ [API] 錯誤響應:`, errorData);

      // 檢查是否為 Token 過期相關錯誤
      if (this.isTokenExpiredError(response.status, errorData)) {
        await this.handleTokenExpired(originalUrl, errorData);
        throw new Error("TOKEN_EXPIRED");
      }

      // 檢查是否為角色權限錯誤
      if (response.status === 403) {
        await this.handlePermissionDenied(originalUrl, errorData);
        throw new Error("PERMISSION_DENIED");
      }

      // 拋出其他錯誤
      throw new Error(errorData.message || `API 請求失敗: ${response.status}`);
    } catch (error) {
      if (error instanceof Error && (error.message === "TOKEN_EXPIRED" || error.message === "PERMISSION_DENIED")) {
        throw error;
      }

      console.error(`💥 [API] 響應處理失敗:`, error);
      throw new Error("API 響應處理失敗");
    }
  }

  // 發送 API 請求
  async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    console.log(`🚀 [API] 發送請求: ${options.method || "GET"} ${url}`);

    try {
      // 自動添加認證 headers
      const headers = {
        "Content-Type": "application/json",
        ...authService.getAuthHeaders(),
        ...options.headers,
      };

      const requestOptions: RequestInit = {
        ...options,
        headers,
      };

      console.log(`🔑 [API] 請求 Headers:`, headers);

      // 發送請求
      const response = await fetch(url, requestOptions);

      // 處理響應
      return await this.handleResponse<T>(response, url);
    } catch (error) {
      console.error(`💥 [API] 請求失敗:`, error);

      if (error instanceof Error) {
        if (error.message === "TOKEN_EXPIRED" || error.message === "PERMISSION_DENIED") {
          throw error;
        }
      }

      // 網路錯誤或其他問題
      throw new Error(`網路請求失敗: ${error instanceof Error ? error.message : "未知錯誤"}`);
    }
  }

  // 檢查是否為 Token 過期錯誤
  private isTokenExpiredError(status: number, errorData: ApiErrorResponse): boolean {
    // 檢查 HTTP 狀態碼
    if (status === 401) {
      return true;
    }

    // 檢查錯誤碼
    if (errorData.code && TOKEN_EXPIRED_CODES.includes(errorData.code)) {
      return true;
    }

    // 檢查錯誤訊息
    const message = (errorData.message || "").toLowerCase();
    const expiredKeywords = [
      "token expired",
      "token invalid",
      "unauthorized",
      "token過期",
      "認證失敗",
      "token not found in any validation source", // 新增：Token 在驗證來源中找不到
      "token not found", // 新增：Token 找不到
      "validation source", // 新增：驗證來源相關錯誤
      "no valid token", // 新增：無有效 Token
      "token missing", // 新增：Token 遺失
      "authentication failed", // 新增：認證失敗
    ];

    return expiredKeywords.some((keyword) => message.includes(keyword));
  }

  // 處理 Token 過期
  private async handleTokenExpired(url: string, errorData: ApiErrorResponse): Promise<void> {
    if (this.isRedirecting) {
      return;
    }

    // 特別處理 "Token not found in any validation source" 錯誤
    const message = (errorData.message || "").toLowerCase();
    if (message.includes("token not found in any validation source")) {
      console.warn(`🔍 [API] Token 在驗證來源中找不到: ${url}`, errorData);
      console.warn(`🔍 [API] 這通常表示 Token 已被撤銷、刪除或格式不正確`);
    } else {
      console.warn(`⏰ [API] Token 已過期或無效: ${url}`, errorData);
    }

    try {
      this.isRedirecting = true;

      // 檢查是否啟用 Token 過期處理
      if (!isFeatureEnabled("enableTokenExpiry")) {
        console.log("🔧 [API] Token 過期處理已停用");
        return;
      }

      // 觸發 authService 的 Token 過期回調
      authService.onTokenExpired?.();

      // 執行登出操作
      await authService.logout();

      // 顯示過期提示
      this.showExpiredMessage(errorData.message || "Token 已過期，請重新登入");

      // 延遲重定向到登入頁面
      setTimeout(() => {
        console.log("🔄 [API] 重定向到登入頁面");
        window.location.href = "/login?reason=token_expired";
      }, this.redirectDelay);
    } catch (error) {
      console.error("💥 [API] 處理 Token 過期失敗:", error);
    }
  }

  // 處理權限被拒絕
  private async handlePermissionDenied(url: string, errorData: ApiErrorResponse): Promise<void> {
    if (this.isRedirecting) {
      return;
    }

    console.warn(`🚫 [API] 權限被拒絕: ${url}`, errorData);

    try {
      this.isRedirecting = true;

      // 檢查是否為角色權限問題
      if (errorData.details?.action === "account_logout") {
        // 觸發角色權限被拒絕回調
        authService.onRolePermissionDenied?.(errorData.details.userRole || "Unknown", errorData.details.allowedRoles || []);
        return;
      }

      // 其他權限問題
      this.showPermissionDeniedMessage(errorData.message || "您沒有權限執行此操作");

      // 延遲重定向
      setTimeout(() => {
        console.log("🔄 [API] 重定向到登入頁面");
        window.location.href = "/login?reason=permission_denied";
      }, this.redirectDelay);
    } catch (error) {
      console.error("💥 [API] 處理權限拒絕失敗:", error);
    }
  }

  // 顯示 Token 過期訊息
  private showExpiredMessage(message: string): void {
    // 檢查是否在瀏覽器環境
    if (typeof window === "undefined") return;

    try {
      // 創建過期提示元素
      const alertDiv = document.createElement("div");
      alertDiv.id = "token-expired-alert";
      alertDiv.className = "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg";
      alertDiv.innerHTML = `
        <div class="flex items-center gap-2">
          <span>⏰</span>
          <span>${message}</span>
        </div>
      `;

      // 移除現有提示
      const existing = document.getElementById("token-expired-alert");
      if (existing) {
        existing.remove();
      }

      // 添加到頁面
      document.body.appendChild(alertDiv);

      // 自動移除
      setTimeout(() => {
        if (alertDiv.parentNode) {
          alertDiv.remove();
        }
      }, this.redirectDelay - 500);
    } catch (error) {
      console.error("💥 [API] 顯示過期訊息失敗:", error);
    }
  }

  // 顯示權限被拒絕訊息
  private showPermissionDeniedMessage(message: string): void {
    if (typeof window === "undefined") return;

    try {
      const alertDiv = document.createElement("div");
      alertDiv.id = "permission-denied-alert";
      alertDiv.className = "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-orange-500 text-white px-6 py-3 rounded-lg shadow-lg";
      alertDiv.innerHTML = `
        <div class="flex items-center gap-2">
          <span>🚫</span>
          <span>${message}</span>
        </div>
      `;

      const existing = document.getElementById("permission-denied-alert");
      if (existing) {
        existing.remove();
      }

      document.body.appendChild(alertDiv);

      setTimeout(() => {
        if (alertDiv.parentNode) {
          alertDiv.remove();
        }
      }, this.redirectDelay - 500);
    } catch (error) {
      console.error("💥 [API] 顯示權限拒絕訊息失敗:", error);
    }
  }

  // 重置重定向狀態（用於測試或手動重置）
  resetRedirectingState(): void {
    this.isRedirecting = false;
    console.log("🔄 [API] 已重置重定向狀態");
  }

  // 獲取當前狀態
  getStatus(): { isRedirecting: boolean } {
    return {
      isRedirecting: this.isRedirecting,
    };
  }
}

// 創建全域 API 攔截器實例
export const apiInterceptor = new ApiInterceptor();

// 便捷的 API 請求方法
export const api = {
  get: <T>(url: string, options?: RequestInit) => apiInterceptor.request<T>(url, { ...options, method: "GET" }),

  post: <T>(url: string, data?: unknown, options?: RequestInit) =>
    apiInterceptor.request<T>(url, {
      ...options,
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    }),

  put: <T>(url: string, data?: unknown, options?: RequestInit) =>
    apiInterceptor.request<T>(url, {
      ...options,
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    }),

  delete: <T>(url: string, options?: RequestInit) => apiInterceptor.request<T>(url, { ...options, method: "DELETE" }),

  // 直接使用攔截器
  request: <T>(url: string, options?: RequestInit) => apiInterceptor.request<T>(url, options),
};

export default apiInterceptor;
