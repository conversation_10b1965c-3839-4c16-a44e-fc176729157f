// ========== 問題相關資料模型 ==========

export interface QuestionModel {
  id: number;
  title: string;
  parentId: number | null;
  step: number;
  sequence: number;
  isRequired: boolean;
  templateId: number | null;
  isRenewed: boolean;
  status: number;
  createdTime?: Date;
  updatedTime?: Date;
}

export interface StepInfo {
  step: number;
  title: string;
  description: string;
  questionCount: number;
  isEnabled: boolean;
}

export interface FormQuestion {
  sid: number;
  title: string;
  question_tpl: number;
  is_renew: number;
  step: number;
  sequence: number;
  parent_id: number | null;
}

export interface QuestionGroup {
  [parentKey: string]: FormQuestion[];
}

export interface StepQuestionGroup {
  [stepKey: string]: QuestionGroup;
}

export interface StepStatistics {
  step: number;
  isGreenFlag: boolean;
  totalQuestions: number;
  parentGroups: number;
}

export interface ParentGroupAnalysis {
  parentId: number;
  childrenCount: number;
  children: Array<{
    id: number;
    title: string;
    step: number;
    template: number | null;
  }>;
}

export interface QuestionStructureAnalysis {
  totalQuestions: number;
  stepAnalysis: Array<{
    step: number;
    isGreenFlag: boolean;
    total: number;
    withParent: number;
    uniqueParents: number;
  }>;
  parentGroups: ParentGroupAnalysis[];
  greenFlagQuestions: Array<{
    id: number;
    title: string;
    step: number;
    parentId: number | null;
    template: number | null;
    isRenewed: boolean;
  }>;
}

// API 回應格式
export interface QuestionListResponse {
  success: boolean;
  data: QuestionModel[];
  step: number;
  level: number;
  count: number;
  message?: string;
}

export interface CertificationStepsResponse {
  success: boolean;
  data: StepInfo[];
  level: number;
  totalSteps: number;
  message?: string;
}

export interface FormQuestionsResponse {
  success: boolean;
  data: {
    questions: StepQuestionGroup;
    stepInfo: StepStatistics[];
  };
  totalQuestions: number;
  certificationId?: number;
  message?: string;
}

export interface QuestionDetailResponse {
  success: boolean;
  data: QuestionModel | {};
  message?: string;
}

export interface QuestionAnalysisResponse {
  success: boolean;
  data: QuestionStructureAnalysis;
  message?: string;
}

// 查詢參數類型
export interface QuestionQueryParams {
  step?: string;
  level?: string;
}

export interface CertificationStepsQueryParams {
  level?: string;
}

export interface FormQuestionsQueryParams {
  certificationId?: string;
}

export interface QuestionDetailParams {
  questionId: string;
}
