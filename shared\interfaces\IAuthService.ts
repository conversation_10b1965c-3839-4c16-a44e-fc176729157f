/**
 * 認證服務介面定義
 * 定義認證系統的核心合約和資料結構
 * 
 * @description 提供標準化的認證介面，支援前後端一致的認證邏輯
 * @version 1.0.0
 */

import { AllowedRole } from '../constants/roles';
import { AuthErrorCode } from '../constants/error-codes';

/**
 * 會員基本資料介面
 * 對應系統中的用戶資料結構
 */
export interface MemberProfile {
  /** 會員 ID */
  accountId: string | number;
  /** 會員姓名 */
  name: string;
  /** 電子郵件 */
  email: string;
  /** 電話號碼 */
  phone?: string;
  /** 角色類型（標準化後） */
  roleType: AllowedRole;
  /** 原始角色類型（保留相容性） */
  originalRoleType?: string | number;
  /** 帳號是否啟用 */
  isActive: boolean;
  /** 學校 ID（針對學校角色） */
  schoolId?: string | number;
  /** 學校名稱 */
  schoolName?: string;
  /** 縣市代碼 */
  countyCode?: string;
  /** 縣市名稱 */
  countyName?: string;
  /** 建立時間 */
  createdAt?: string | Date;
  /** 更新時間 */
  updatedAt?: string | Date;
  /** 最後登入時間 */
  lastLoginAt?: string | Date;
  /** 是否為系統管理員 */
  isSystemAdmin?: boolean;
  /** 額外的設定檔資料 */
  profile?: Record<string, any>;
}

/**
 * Token 驗證結果介面
 * 標準化驗證結果的回傳格式
 */
export interface TokenValidationResult {
  /** 驗證是否成功 */
  valid: boolean;
  /** 使用者資料（驗證成功時提供）*/
  user?: MemberProfile;
  /** 回應訊息 */
  message?: string;
  /** 錯誤碼（驗證失敗時提供）*/
  errorCode?: AuthErrorCode | string;
  /** 詳細錯誤資訊（除錯用）*/
  error?: any;
  /** 驗證時間戳 */
  timestamp?: string | Date;
  /** Token 來源（用於追蹤）*/
  tokenSource?: 'database' | 'test' | 'legacy' | 'cache';
}

/**
 * 角色權限檢查結果介面
 */
export interface RolePermissionResult {
  /** 是否有權限 */
  hasPermission: boolean;
  /** 使用者角色 */
  userRole: AllowedRole;
  /** 要求的權限 */
  requiredPermission?: string;
  /** 拒絕原因（無權限時提供）*/
  deniedReason?: string;
}

/**
 * 認證上下文介面
 * 提供認證過程中的額外資訊
 */
export interface AuthContext {
  /** 請求 IP 位址 */
  ipAddress?: string;
  /** 用戶代理字串 */
  userAgent?: string;
  /** 來源 URL */
  referer?: string;
  /** 認證時間 */
  authTime?: string | Date;
  /** 環境資訊 */
  environment?: 'development' | 'testing' | 'production';
  /** 額外的中繼資料 */
  metadata?: Record<string, any>;
}

/**
 * 登出結果介面
 */
export interface LogoutResult {
  /** 登出是否成功 */
  success: boolean;
  /** 回應訊息 */
  message?: string;
  /** 清理的資源清單 */
  clearedResources?: string[];
  /** 登出時間 */
  logoutTime?: string | Date;
}

/**
 * 認證服務主介面
 * 定義所有認證相關操作的標準合約
 */
export interface IAuthService {
  /**
   * 驗證 Token 的有效性
   * @param token 要驗證的 token
   * @param context 認證上下文（可選）
   * @returns Promise<TokenValidationResult> 驗證結果
   */
  validateToken(token: string, context?: AuthContext): Promise<TokenValidationResult>;
  
  /**
   * 檢查使用者角色權限
   * @param user 使用者資料
   * @param requiredPermission 所需權限（可選）
   * @returns boolean | RolePermissionResult 權限檢查結果
   */
  checkRolePermission(
    user: MemberProfile, 
    requiredPermission?: string
  ): boolean | RolePermissionResult;
  
  /**
   * 使用者登出
   * @param token 使用者 token（可選，從 context 中取得）
   * @param context 認證上下文（可選）
   * @returns Promise<LogoutResult> 登出結果
   */
  logout(token?: string, context?: AuthContext): Promise<LogoutResult>;
  
  /**
   * 取得目前認證使用者
   * @param token 使用者 token
   * @param context 認證上下文（可選）
   * @returns Promise<MemberProfile | null> 使用者資料或 null
   */
  getCurrentUser(token: string, context?: AuthContext): Promise<MemberProfile | null>;
  
  /**
   * 重新整理 Token（如果支援）
   * @param currentToken 目前的 token
   * @param context 認證上下文（可選）
   * @returns Promise<TokenValidationResult> 新的驗證結果
   */
  refreshToken?(currentToken: string, context?: AuthContext): Promise<TokenValidationResult>;
  
  /**
   * 檢查 Token 是否即將過期
   * @param token 要檢查的 token
   * @returns Promise<boolean> 是否即將過期
   */
  isTokenExpiringSoon?(token: string): Promise<boolean>;
}

/**
 * 認證中間件介面
 * 專門用於後端 API 中間件
 */
export interface IAuthMiddleware {
  /**
   * 認證中間件函數
   * @param req 請求物件
   * @param res 回應物件
   * @param next 下一個中間件函數
   */
  authenticate(req: any, res: any, next: any): Promise<void>;
  
  /**
   * 權限檢查中間件
   * @param requiredRoles 必要角色清單
   * @returns 中間件函數
   */
  requireRoles(requiredRoles: AllowedRole[]): (req: any, res: any, next: any) => Promise<void>;
  
  /**
   * 可選認證中間件（不強制要求認證）
   * @param req 請求物件
   * @param res 回應物件
   * @param next 下一個中間件函數
   */
  optionalAuthenticate?(req: any, res: any, next: any): Promise<void>;
}