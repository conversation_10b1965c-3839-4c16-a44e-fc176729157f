import React from "react";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { LogOut } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface RoleBasedRouteProps {
  children: React.ReactNode;
  allowedRoles: ("school" | "epa" | "tutor")[];
  requireAuth?: boolean;
  fallbackPath?: string;
}

export const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  allowedRoles,
  requireAuth = true,
  fallbackPath = "/login",
}) => {
  const { authState, isLoading, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // 🚪 處理登出功能
  const handleLogout = async () => {
    try {
      console.log("權限不足頁面執行登出...");
      await logout();
      console.log("登出成功，重定向到登入頁面");
      navigate("/login", { replace: true });
    } catch (error) {
      console.error("登出失敗:", error);
      // 即使登出 API 失敗，也要強制清除本地資料並重定向
      try {
        sessionStorage.removeItem("userToken");
        sessionStorage.removeItem("userData");
        console.log("強制清除本地認證資料");
        navigate("/login", { replace: true });
      } catch (localError) {
        console.error("強制清除失敗，使用 window.location:", localError);
        window.location.href = "/login";
      }
    }
  };

  // 如果正在載入，顯示載入狀態，避免重定向
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <div className="w-full max-w-md space-y-4">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </div>
    );
  }

  // 檢查是否需要認證
  if (requireAuth && !authState.isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // 如果已認證但沒有使用者資料
  if (requireAuth && authState.isAuthenticated && !authState.user) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Alert className="max-w-md">
          <AlertDescription>
            無法獲取使用者資料，請重新登入。
            <div className="mt-4 flex gap-2">
              <Button onClick={() => (window.location.href = "/login")}>
                重新登入
              </Button>
              <Button
                variant="outline"
                onClick={handleLogout}
                className="flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                登出
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // 檢查角色權限
  if (
    requireAuth &&
    authState.user &&
    !allowedRoles.includes(authState.user.role as "school" | "epa" | "tutor")
  ) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Alert variant="destructive" className="max-w-md">
          <AlertDescription>
            <div className="space-y-3">
              <div>
                <strong>訪問權限不足</strong>
              </div>
              <div>
                您的身份：
                <Badge variant="outline" className="ml-2">
                  {formatMemberRole(
                    authState.user.role as "school" | "epa" | "tutor"
                  )}
                </Badge>
              </div>
              <div>
                此頁面僅限以下身份訪問：
                <div className="flex gap-2 mt-2">
                  {allowedRoles.map((role) => (
                    <Badge key={role} variant="secondary">
                      {formatMemberRole(role)}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="pt-4 space-y-2">
                {/* 🔒 新增：登出按鈕 - 放在最上方，最顯眼的位置 */}
                <div className="flex flex-col gap-2">
                  <Button
                    variant="outline"
                    onClick={handleLogout}
                    className="w-full flex items-center justify-center gap-2 border-red-500 text-red-600 hover:bg-red-50 hover:border-red-600"
                  >
                    <LogOut className="h-4 w-4" />
                    登出並重新登入
                  </Button>

                  <div className="font-size-sm text-muted-foreground text-center">
                    或
                  </div>

                  <Button
                    variant="secondary"
                    onClick={() => window.history.back()}
                    className="w-full"
                  >
                    返回上頁
                  </Button>
                </div>

                {/* 🔒 新增：說明文字 */}
                <div className="font-size-xs text-muted-foreground bg-muted/20 p-3 rounded-md mt-3">
                  <p className="font-medium">💡 提示：</p>
                  <p>• 如果您認為這是錯誤，請登出後使用正確的帳號重新登入</p>
                  <p>• 允許存取的身份：生態學校、縣市政府、輔導人員</p>
                </div>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
};

// 特定身份的路由組件
export const SchoolOnlyRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return <RoleBasedRoute allowedRoles={["school"]}>{children}</RoleBasedRoute>;
};

export const EPAOnlyRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return <RoleBasedRoute allowedRoles={["epa"]}>{children}</RoleBasedRoute>;
};

export const TutorOnlyRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return <RoleBasedRoute allowedRoles={["tutor"]}>{children}</RoleBasedRoute>;
};

export const GovernmentRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <RoleBasedRoute allowedRoles={["epa", "tutor"]}>{children}</RoleBasedRoute>
  );
};

export const AllMemberRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <RoleBasedRoute allowedRoles={["school", "epa", "tutor"]}>
      {children}
    </RoleBasedRoute>
  );
};

// 工具函數
const formatMemberRole = (role: "school" | "epa" | "tutor"): string => {
  const roleMap = {
    school: "生態學校",
    epa: "縣市政府",
    tutor: "輔導人員",
  };
  return roleMap[role] || role;
};

// 路由配置介面
export interface RouteConfig {
  path: string;
  allowedRoles: ("school" | "epa" | "tutor")[];
  label: string;
  description: string;
}

// 預定義路由配置
export const ROLE_ROUTES: RouteConfig[] = [
  {
    path: "/dashboard",
    allowedRoles: ["school", "epa", "tutor"],
    label: "會員首頁",
    description: "所有會員都可以訪問的首頁",
  },
  {
    path: "/profile",
    allowedRoles: ["school", "epa", "tutor"],
    label: "基本資料維護",
    description: "所有會員都可以維護基本資料",
  },
  {
    path: "/certificate",
    allowedRoles: ["school"],
    label: "認證申請",
    description: "僅限生態學校申請認證",
  },
  {
    path: "/news",
    allowedRoles: ["school"],
    label: "校園新聞投稿",
    description: "僅限學校身份投稿新聞",
  },
  {
    path: "/guidance-apply",
    allowedRoles: ["school"],
    label: "輔導申請",
    description: "僅限學校身份申請輔導",
  },
  {
    path: "/ein",
    allowedRoles: ["school"],
    label: "自然人綁定",
    description: "僅限學校身份綁定自然人憑證",
  },
  {
    path: "/school-management",
    allowedRoles: ["epa", "tutor"],
    label: "學校管理",
    description: "僅限政府部門和輔導人員管理學校",
  },
  {
    path: "/certification/review",
    allowedRoles: ["epa", "tutor"],
    label: "認證審核",
    description: "僅限縣市政府和輔導人員審核",
  },
  {
    path: "/area-management",
    allowedRoles: ["epa"],
    label: "區域管理",
    description: "縣市政府區域管理功能",
  },
  {
    path: "/guidance",
    allowedRoles: ["tutor"],
    label: "輔導管理",
    description: "輔導人員專用功能",
  },
  {
    path: "/reports",
    allowedRoles: ["epa", "tutor"],
    label: "統計報表",
    description: "查看各種統計報表",
  },
];

// 根據使用者角色過濾可訪問的路由
export const getAccessibleRoutes = (
  userRole: "school" | "epa" | "tutor"
): RouteConfig[] => {
  return ROLE_ROUTES.filter((route) => route.allowedRoles.includes(userRole));
};

// 檢查使用者是否能訪問特定路由
export const canAccessRoute = (
  userRole: "school" | "epa" | "tutor",
  routePath: string
): boolean => {
  const route = ROLE_ROUTES.find((r) => r.path === routePath);
  return route ? route.allowedRoles.includes(userRole) : false;
};

// 導航權限檢查 Hook
export const useRoutePermissions = () => {
  const { authState } = useAuth();

  const checkPermission = (
    allowedRoles: ("school" | "epa" | "tutor")[]
  ): boolean => {
    if (!authState.user) return false;
    return allowedRoles.includes(
      authState.user.role as "school" | "epa" | "tutor"
    );
  };

  const getAccessibleRoutes = (): RouteConfig[] => {
    if (!authState.user) return [];
    return ROLE_ROUTES.filter((route) =>
      route.allowedRoles.includes(
        authState.user.role as "school" | "epa" | "tutor"
      )
    );
  };

  return {
    checkPermission,
    getAccessibleRoutes,
    userRole: authState.user?.role as "school" | "epa" | "tutor" | undefined,
  };
};
