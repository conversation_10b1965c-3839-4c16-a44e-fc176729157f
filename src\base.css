/* 集中主題用 CSS 變數，修改此處即可調整網站配色 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 142 45% 35%; /* 主色 (深綠) */
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%; /* 輔色 (淡灰) */
  --secondary-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --radius: 0.5rem;
  /* ...依需求可新增或調整其它變數 */
}

/* 深色主題，class="dark" 時覆蓋 */
.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 120 60% 18%; /* 主色 (深綠) 深色版 */
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  /* ... */
}
