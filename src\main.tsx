import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import "./assets/css/App.css";

// 配置API服務
import { configureAPI } from "@/services/certificationAPI";
// 外部連結調試（僅開發環境）
import { debugExternalLinks } from "@/config/externalLinks";

console.log("Main.tsx loading...");

try {
  configureAPI();
  console.log("API configured successfully");

  // 在開發環境中顯示外部連結配置
  debugExternalLinks();
} catch (error) {
  console.error("Failed to configure API:", error);
}

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Root element not found");
}

console.log("Creating React root...");
const root = createRoot(rootElement);

console.log("Rendering App...");
root.render(React.createElement(App));
