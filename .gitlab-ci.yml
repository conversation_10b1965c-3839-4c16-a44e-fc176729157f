# EcoCampus CI/CD Pipeline Configuration - Build and Deploy Only
# 精簡版本：專注於建構和部署功能

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "main")

variables:
  FRONTEND_PATH: "C:\\Website\\ecocampus-apply"
  API_PATH: "C:\\Website\\ecocampus-apply-api"
  BACKUP_PATH: "C:\\Website\\ecocampus-apply-backup"
  SERVICE_NAME: "EcoCampusAPI"

stages:
  - build
  - deploy
  - verify





# 建構前端 - dev 分支 (測試環境)
build_frontend_dev:
  stage: build
  tags:
    - windows
    - shell
  variables:
    NODE_ENV: testing
    VITE_APP_ENV: testing
  script:
    - 'echo "=== BUILDING FRONTEND FOR TESTING ENVIRONMENT ==="'
    - 'echo "Installing dependencies..."'
    - 'npm install'
    - 'echo "Building frontend for testing environment..."'
    - 'npx vite build --mode testing'
    - 'echo "Checking build output..."'
    - 'if (Test-Path "dist") { echo "Build successful"; Get-ChildItem "dist" | Select-Object -First 3 } else { echo "Build failed"; exit 1 }'
    - 'echo "Testing environment build completed successfully"'
  artifacts:
    paths:
      - dist/
      - api/
      - package.json
      - package-lock.json
      - env.*.example
    expire_in: 1 hour
  only:
    - dev

# 建構前端 - main 分支 (生產環境)
build_frontend_prod:
  stage: build
  tags:
    - windows
    - shell
  variables:
    NODE_ENV: production
    VITE_APP_ENV: production
  script:
    - 'echo "=== BUILDING FRONTEND FOR PRODUCTION ENVIRONMENT ==="'
    - 'echo "Installing dependencies..."'
    - 'npm install'
    - 'echo "Building frontend for production environment..."'
    - 'npx vite build --mode production'
    - 'echo "Checking build output..."'
    - 'if (Test-Path "dist") { echo "Build successful"; Get-ChildItem "dist" | Select-Object -First 3 } else { echo "Build failed"; exit 1 }'
    - 'echo "Production environment build completed successfully"'
  artifacts:
    paths:
      - dist/
      - api/
      - package.json
      - package-lock.json
      - env.*.example
    expire_in: 1 hour
  only:
    - main

# 部署前端 - dev 分支 (測試環境)
deploy_frontend_dev:
  stage: deploy
  tags:
    - windows
    - shell
  resource_group: ecocampus-deployment
  script:
    - 'echo "Deploying frontend for testing environment..."'
    - 'if (Test-Path $env:BACKUP_PATH) { Remove-Item -Path $env:BACKUP_PATH -Recurse -Force }'
    - 'New-Item -ItemType Directory -Path $env:BACKUP_PATH -Force'
    - 'if (Test-Path $env:FRONTEND_PATH) { New-Item -ItemType Directory -Path "$env:BACKUP_PATH\frontend" -Force }'
    - 'if (Test-Path $env:FRONTEND_PATH) { Copy-Item -Path "$env:FRONTEND_PATH\*" -Destination "$env:BACKUP_PATH\frontend" -Recurse -Force -ErrorAction SilentlyContinue }'
    - 'if (Test-Path $env:FRONTEND_PATH) { Remove-Item -Path $env:FRONTEND_PATH -Recurse -Force }'
    - 'New-Item -ItemType Directory -Path $env:FRONTEND_PATH -Force'
    - 'Copy-Item -Path "dist\*" -Destination $env:FRONTEND_PATH -Recurse -Force'
    - 'if (-not (Test-Path "$env:FRONTEND_PATH\index.html")) { exit 1 }'
    - 'echo "Testing environment frontend deployed"'
  dependencies:
    - build_frontend_dev
  only:
    - dev

# 部署前端 - main 分支 (生產環境)
deploy_frontend_prod:
  stage: deploy
  tags:
    - windows
    - shell
  resource_group: ecocampus-deployment
  script:
    - 'echo "Deploying frontend for production environment..."'
    - 'if (Test-Path $env:BACKUP_PATH) { Remove-Item -Path $env:BACKUP_PATH -Recurse -Force }'
    - 'New-Item -ItemType Directory -Path $env:BACKUP_PATH -Force'
    - 'if (Test-Path $env:FRONTEND_PATH) { New-Item -ItemType Directory -Path "$env:BACKUP_PATH\frontend" -Force }'
    - 'if (Test-Path $env:FRONTEND_PATH) { Copy-Item -Path "$env:FRONTEND_PATH\*" -Destination "$env:BACKUP_PATH\frontend" -Recurse -Force -ErrorAction SilentlyContinue }'
    - 'if (Test-Path $env:FRONTEND_PATH) { Remove-Item -Path $env:FRONTEND_PATH -Recurse -Force }'
    - 'New-Item -ItemType Directory -Path $env:FRONTEND_PATH -Force'
    - 'Copy-Item -Path "dist\*" -Destination $env:FRONTEND_PATH -Recurse -Force'
    - 'if (-not (Test-Path "$env:FRONTEND_PATH\index.html")) { exit 1 }'
    - 'echo "Production environment frontend deployed"'
  dependencies:
    - build_frontend_prod
  only:
    - main

# 部署API - dev 分支 (測試環境)
deploy_api_dev:
  stage: deploy
  tags:
    - windows
    - shell
  resource_group: ecocampus-deployment
  script:
    - 'echo "Deploying API for testing environment..."'
    - 'if (Test-Path $env:API_PATH) { New-Item -ItemType Directory -Path "$env:BACKUP_PATH\api" -Force }'
    - 'echo "Stopping existing services..."'
    - 'try { Get-Service -Name $env:SERVICE_NAME -ErrorAction Stop | Stop-Service -Force -NoWait } catch { Write-Host "Service not found or already stopped" }'
    - 'try { Get-Process -Name "node" -ErrorAction Stop | Stop-Process -Force } catch { Write-Host "No node processes found" }'
    - 'try { if (Get-Command pm2 -ErrorAction SilentlyContinue) { pm2 kill } } catch { Write-Host "PM2 not available" }'
    - 'Start-Sleep -Seconds 10'
    - 'if (-not (Test-Path $env:API_PATH)) { New-Item -ItemType Directory -Path $env:API_PATH -Force }'
    - 'if (-not (Test-Path "$env:API_PATH\api")) { New-Item -ItemType Directory -Path "$env:API_PATH\api" -Force }'
    - 'if (-not (Test-Path "api")) { echo "API source not found"; exit 1 }'
    - 'Copy-Item -Path "api\*" -Destination "$env:API_PATH\api\" -Recurse -Force'
    - 'Copy-Item -Path "package.json" -Destination $env:API_PATH -Force'
    - 'Copy-Item -Path "package-lock.json" -Destination $env:API_PATH -Force -ErrorAction SilentlyContinue'
    - 'if (Test-Path "env.testing-deployment.example") { Copy-Item -Path "env.testing-deployment.example" -Destination "$env:API_PATH\.env" -Force; Write-Host "Using testing deployment config" }'
    - 'if (-not (Test-Path "$env:API_PATH\api\server.ts")) { echo "server.ts not found"; exit 1 }'
    - 'Set-Location $env:API_PATH'
    - 'npm install --production'
    - 'echo "Testing environment API deployed successfully"'
  dependencies:
    - build_frontend_dev
  only:
    - dev

# 部署API - main 分支 (生產環境)
deploy_api_prod:
  stage: deploy
  tags:
    - windows
    - shell
  resource_group: ecocampus-deployment
  script:
    - 'echo "Deploying API for production environment..."'
    - 'if (Test-Path $env:API_PATH) { New-Item -ItemType Directory -Path "$env:BACKUP_PATH\api" -Force }'
    - 'echo "Stopping existing services..."'
    - 'try { Get-Service -Name $env:SERVICE_NAME -ErrorAction Stop | Stop-Service -Force -NoWait } catch { Write-Host "Service not found or already stopped" }'
    - 'try { Get-Process -Name "node" -ErrorAction Stop | Stop-Process -Force } catch { Write-Host "No node processes found" }'
    - 'try { if (Get-Command pm2 -ErrorAction SilentlyContinue) { pm2 kill } } catch { Write-Host "PM2 not available" }'
    - 'Start-Sleep -Seconds 10'
    - 'if (-not (Test-Path $env:API_PATH)) { New-Item -ItemType Directory -Path $env:API_PATH -Force }'
    - 'if (-not (Test-Path "$env:API_PATH\api")) { New-Item -ItemType Directory -Path "$env:API_PATH\api" -Force }'
    - 'if (-not (Test-Path "api")) { echo "API source not found"; exit 1 }'
    - 'Copy-Item -Path "api\*" -Destination "$env:API_PATH\api\" -Recurse -Force'
    - 'Copy-Item -Path "package.json" -Destination $env:API_PATH -Force'
    - 'Copy-Item -Path "package-lock.json" -Destination $env:API_PATH -Force -ErrorAction SilentlyContinue'
    - 'if (Test-Path "env.production.example") { Copy-Item -Path "env.production.example" -Destination "$env:API_PATH\.env" -Force; Write-Host "Using production deployment config" }'
    - 'if (-not (Test-Path "$env:API_PATH\api\server.ts")) { echo "server.ts not found"; exit 1 }'
    - 'Set-Location $env:API_PATH'
    - 'npm install --production'
    - 'echo "Production environment API deployed successfully"'
  dependencies:
    - build_frontend_prod
  only:
    - main

# 啟動API服務 - dev 分支 (測試環境)
start_api_dev:
  stage: deploy
  tags:
    - windows
    - shell
  resource_group: ecocampus-deployment
  script:
    - 'echo "Starting API service for testing environment..."'
    - 'Set-Location $env:API_PATH'
    - '$env:HOMEPATH = "C:\Users\<USER>\Users\luffe"'
    - '$env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")'
    - '$npmGlobalPath = npm config get prefix; $env:PATH = "$npmGlobalPath;$env:PATH"'
    - 'npm install -g pm2 pm2-windows-service'
    - 'if (-not (Get-Command pm2 -ErrorAction SilentlyContinue)) { echo "PM2 installation failed"; exit 1 }'
    - 'try { pm2 kill } catch { Write-Host "No existing PM2 processes" }'
    - 'Start-Sleep -Seconds 5'
    - 'if (-not (Test-Path "$env:API_PATH\api\server.ts")) { echo "server.ts not found"; exit 1 }'
    - '$env:NODE_ENV = "testing"'
    - 'pm2 start "$env:API_PATH\api\server.ts" --name ecocampus-api --interpreter "node" --interpreter-args "--import tsx/esm" --time'
    - 'echo "Waiting for API to initialize..."'
    - 'Start-Sleep -Seconds 20'
    - 'pm2 list'
    - '$pm2Status = pm2 jlist | ConvertFrom-Json -AsHashtable'
    - '$apiProcess = $pm2Status | Where-Object { $_.name -eq "ecocampus-api" }'
    - 'if (-not $apiProcess -or $apiProcess.pm2_env.status -ne "online") { pm2 logs ecocampus-api --lines 50; exit 1 }'
    - 'echo "Checking port 3001..."'
    - 'for ($i = 1; $i -le 10; $i++) { $portCheck = netstat -an | Select-String ":3001.*LISTENING"; if ($portCheck) { echo "Port 3001 is listening on attempt $i"; break } else { echo "Port check $i/10 failed, waiting..."; Start-Sleep -Seconds 5 } }'
    - 'pm2 save'
    - 'try { pm2-service-install -n $env:SERVICE_NAME } catch { Write-Host "Service installation failed, continuing..." }'
    - 'echo "Testing environment API service started successfully"'
  dependencies:
    - deploy_api_dev
  only:
    - dev

# 啟動API服務 - main 分支 (生產環境)
start_api_prod:
  stage: deploy
  tags:
    - windows
    - shell
  resource_group: ecocampus-deployment
  script:
    - 'echo "Starting API service for production environment..."'
    - 'Set-Location $env:API_PATH'
    - '$env:HOMEPATH = "C:\Users\<USER>\Users\luffe"'
    - '$env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")'
    - '$npmGlobalPath = npm config get prefix; $env:PATH = "$npmGlobalPath;$env:PATH"'
    - 'npm install -g pm2 pm2-windows-service'
    - 'if (-not (Get-Command pm2 -ErrorAction SilentlyContinue)) { echo "PM2 installation failed"; exit 1 }'
    - 'try { pm2 kill } catch { Write-Host "No existing PM2 processes" }'
    - 'Start-Sleep -Seconds 5'
    - 'if (-not (Test-Path "$env:API_PATH\api\server.ts")) { echo "server.ts not found"; exit 1 }'
    - '$env:NODE_ENV = "production"'
    - 'pm2 start "$env:API_PATH\api\server.ts" --name ecocampus-api --interpreter "node" --interpreter-args "--import tsx/esm" --time'
    - 'echo "Waiting for API to initialize..."'
    - 'Start-Sleep -Seconds 20'
    - 'pm2 list'
    - '$pm2Status = pm2 jlist | ConvertFrom-Json -AsHashtable'
    - '$apiProcess = $pm2Status | Where-Object { $_.name -eq "ecocampus-api" }'
    - 'if (-not $apiProcess -or $apiProcess.pm2_env.status -ne "online") { pm2 logs ecocampus-api --lines 50; exit 1 }'
    - 'echo "Checking port 3001..."'
    - 'for ($i = 1; $i -le 10; $i++) { $portCheck = netstat -an | Select-String ":3001.*LISTENING"; if ($portCheck) { echo "Port 3001 is listening on attempt $i"; break } else { echo "Port check $i/10 failed, waiting..."; Start-Sleep -Seconds 5 } }'
    - 'pm2 save'
    - 'try { pm2-service-install -n $env:SERVICE_NAME } catch { Write-Host "Service installation failed, continuing..." }'
    - 'echo "Production environment API service started successfully"'
  dependencies:
    - deploy_api_prod
  only:
    - main

# 驗證部署 - dev 分支 (測試環境) - 簡化版本
verify_deployment_dev:
  stage: verify
  tags:
    - windows
    - shell
  script:
    - 'echo "=== TESTING ENVIRONMENT DEPLOYMENT VERIFICATION (SIMPLIFIED) ==="'
    - 'echo "Waiting for basic initialization..."'
    - 'Start-Sleep -Seconds 30'
    - 'echo "Checking frontend deployment..."'
    - 'if (-not (Test-Path "$env:FRONTEND_PATH\index.html")) { echo "Frontend verification failed"; exit 1 }'
    - 'echo "✅ Frontend deployment verified"'
    - 'echo "Checking API deployment..."'
    - 'if (-not (Test-Path "$env:API_PATH\api\server.ts")) { echo "API files verification failed"; exit 1 }'
    - 'echo "✅ API files verified"'
    - 'echo "Basic port check..."'
    - 'netstat -an | Select-String ":3001" | Select-Object -First 3'
    - 'echo "=== BASIC DEPLOYMENT VERIFICATION COMPLETED ==="'
  dependencies:
    - deploy_frontend_dev
    - start_api_dev
  only:
    - dev

# 驗證部署 - main 分支 (生產環境)
verify_deployment_prod:
  stage: verify
  tags:
    - windows
    - shell
  script:
    - 'echo "=== PRODUCTION ENVIRONMENT DEPLOYMENT VERIFICATION START ==="'
    - 'echo "Waiting for services to fully initialize..."'
    - 'Start-Sleep -Seconds 60'
    - 'if (-not (Test-Path "$env:FRONTEND_PATH\index.html")) { echo "Frontend verification failed"; exit 1 }'
    - 'echo "Production environment frontend deployment OK"'
    - 'echo "Checking API port with multiple attempts..."'
    - 'for ($i = 1; $i -le 20; $i++) { $portCheck = netstat -an | Select-String ":3001.*LISTENING"; if ($portCheck) { echo "API port 3001 is listening (attempt $i)"; break } else { echo "Port check attempt $i/20 failed"; if ($i -eq 20) { echo "API port 3001 not listening after 20 attempts"; exit 1 }; Start-Sleep -Seconds 3 } }'
    - 'echo "API port 3001 is confirmed listening"'
    - 'echo "Testing API health endpoint..."'
    - 'for ($i = 1; $i -le 10; $i++) { try { $testResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/health" -TimeoutSec 10; if ($testResponse -and $testResponse.status -eq "ok") { Write-Host "API health check passed on attempt $i"; $healthPassed = $true; break } } catch { Write-Host "API health test $i/10 failed: $($_.Exception.Message)" }; Start-Sleep -Seconds 10 }'
    - 'if (-not $healthPassed) { echo "API health check failed after 10 attempts"; exit 1 }'
    - 'echo "=== PRODUCTION ENVIRONMENT DEPLOYMENT VERIFICATION COMPLETED SUCCESSFULLY ==="'
  dependencies:
    - deploy_frontend_prod
    - start_api_prod
  only:
    - main

# 成功通知 - dev 分支 (測試環境)
notify_success_dev:
  stage: verify
  tags:
    - windows
    - shell
  script:
    - 'echo "=== TESTING ENVIRONMENT DEPLOYMENT SUCCESS ==="'
    - 'echo "Frontend URL: http://ecocampus-v2-apply.sumire.com.tw"'
    - 'echo "API URL: http://ecocampus-v2-apply-api.sumire.com.tw"'
    - 'echo "Environment: Testing (NODE_ENV=testing)"'
    - 'echo "Deployment completed at $(Get-Date)"'
    - 'echo "All testing environment services are running and verified!"'
  dependencies:
    - verify_deployment_dev
  only:
    - dev

# 成功通知 - main 分支 (生產環境)
notify_success_prod:
  stage: verify
  tags:
    - windows
    - shell
  script:
    - 'echo "=== PRODUCTION ENVIRONMENT DEPLOYMENT SUCCESS ==="'
    - 'echo "Frontend URL: https://ecocampus.gov.tw"'
    - 'echo "API URL: https://api.ecocampus.gov.tw"'
    - 'echo "Environment: Production (NODE_ENV=production)"'
    - 'echo "Deployment completed at $(Get-Date)"'
    - 'echo "All production environment services are running and verified!"'
  dependencies:
    - verify_deployment_prod
  only:
    - main 