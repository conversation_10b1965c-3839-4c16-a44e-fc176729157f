import { getApiBaseUrl } from "./environment";

// 🆕 前端上傳配置
export interface UploadConfig {
  // 校徽上傳配置
  schoolLogo: {
    maxSize: number; // bytes
    allowedTypes: string[];
    dimensions: {
      width: number;
      height: number;
    };
    endpoint: string;
  };

  // 一般檔案上傳配置
  general: {
    maxSize: number;
    maxFiles: number;
    allowedTypes: string[];
    endpoint: string;
  };

  // 📂 路徑配置（可透過環境變數覆蓋）
  paths: {
    basePath: string;
    schoolLogoPath: string;
    generalUploadPath: string;
  };
}

// 預設配置（動態生成端點）
const getDefaultUploadConfig = (): UploadConfig => ({
  schoolLogo: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ["image/jpeg", "image/jpg", "image/png"],
    dimensions: {
      width: 150,
      height: 150,
    },
    endpoint: `${getApiBaseUrl()}/file/upload-school-logo`,
  },

  general: {
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    allowedTypes: [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "video/mp4",
      "video/avi",
      "video/mov",
    ],
    endpoint: `${getApiBaseUrl()}/file/upload`,
  },

  paths: {
    basePath: "/uploads",
    schoolLogoPath: "/uploads/school-logos",
    generalUploadPath: "/uploads", // 🔧 統一使用 /uploads 路徑
  },
});

// 🌍 環境變數支援
const getEnvironmentConfig = (): Partial<UploadConfig> => {
  // 檢查是否有環境變數配置（例如從 .env 讀取）
  const envConfig: Partial<UploadConfig> = {};

  // 在開發環境可以透過 localStorage 或其他方式覆蓋配置
  if (typeof window !== "undefined") {
    const storedConfig = localStorage.getItem("uploadConfig");
    if (storedConfig) {
      try {
        const parsed = JSON.parse(storedConfig);
        Object.assign(envConfig, parsed);
      } catch (error) {
        console.warn("解析儲存的上傳配置失敗:", error);
      }
    }
  }

  return envConfig;
};

// 合併配置
export const getUploadConfig = (): UploadConfig => {
  const defaultConfig = getDefaultUploadConfig();
  const envConfig = getEnvironmentConfig();

  return {
    ...defaultConfig,
    ...envConfig,
    // 深度合併巢狀物件
    schoolLogo: {
      ...defaultConfig.schoolLogo,
      ...envConfig.schoolLogo,
    },
    general: {
      ...defaultConfig.general,
      ...envConfig.general,
    },
    paths: {
      ...defaultConfig.paths,
      ...envConfig.paths,
    },
  };
};

// 🔧 便利函數：格式化檔案大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 🔧 便利函數：檢查檔案類型
export const isValidFileType = (
  file: File,
  allowedTypes: string[]
): boolean => {
  return allowedTypes.includes(file.type);
};

// 🔧 便利函數：檢查檔案大小
export const isValidFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize;
};

// 🔧 便利函數：完整檔案驗證
export const validateFile = (
  file: File,
  config: { maxSize: number; allowedTypes: string[] }
): { valid: boolean; error?: string } => {
  if (!isValidFileType(file, config.allowedTypes)) {
    return {
      valid: false,
      error: `不支援的檔案類型。允許的類型: ${config.allowedTypes
        .map((type) => type.split("/")[1].toUpperCase())
        .join(", ")}`,
    };
  }

  if (!isValidFileSize(file, config.maxSize)) {
    return {
      valid: false,
      error: `檔案大小超過限制。最大允許: ${formatFileSize(config.maxSize)}`,
    };
  }

  return { valid: true };
};

export default getUploadConfig;
