import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import { LocationService } from "../services/location-service.js";
import { APILogger } from "../utils/logger.js";
import {
  LocationCitiesParams,
  LocationAreasParams,
  LocationHierarchyParams,
  LocationCitiesQueryParams,
  LocationAreasQueryParams,
  LocationHierarchyQueryParams,
  LocationCitiesResponse,
  LocationAreasResponse,
  LocationHierarchyResponse,
} from "../models/location.js";
import { LOCATION_ERROR_MESSAGES, LOCATION_SUCCESS_MESSAGES } from "../constants/location.js";

const router = express.Router();

// 獲取所有縣市列表
router.get(
  "/cities",
  authenticateToken,
  async (req: express.Request, res: express.Response<LocationCitiesResponse>) => {
    try {
      const cities = await LocationService.getCities();

      res.json({
        success: true,
        data: cities,
      });
    } catch (error: unknown) {
      APILogger.logError("Location", "獲取縣市列表", error, 500);

      res.status(500).json({
        success: false,
        message: LOCATION_ERROR_MESSAGES.GET_CITIES_FAILED,
        error: error instanceof Error ? error.message : LOCATION_ERROR_MESSAGES.UNKNOWN_ERROR,
      });
    }
  }
);

// 獲取指定縣市的區域列表
router.get(
  "/areas/:cityId",
  authenticateToken,
  async (req: express.Request, res: express.Response<LocationAreasResponse>) => {
    try {
      const { cityId } = req.params;
      const areas = await LocationService.getAreasByCity(cityId);

      res.json({
        success: true,
        data: areas,
      });
    } catch (error: unknown) {
      if (error instanceof Error && error.message === LOCATION_ERROR_MESSAGES.INVALID_CITY_ID) {
        APILogger.logError("Location", "獲取區域列表", error.message, 400);
        return res.status(400).json({
          success: false,
          message: LOCATION_ERROR_MESSAGES.INVALID_CITY_ID,
        });
      }

      APILogger.logError("Location", "獲取區域列表", error, 500);
      res.status(500).json({
        success: false,
        message: LOCATION_ERROR_MESSAGES.GET_AREAS_FAILED,
        error: error instanceof Error ? error.message : LOCATION_ERROR_MESSAGES.UNKNOWN_ERROR,
      });
    }
  }
);

// 獲取完整的地區層級資料（縣市 + 區域）
router.get(
  "/hierarchy",
  authenticateToken,
  async (req: express.Request, res: express.Response<LocationHierarchyResponse>) => {
    try {
      APILogger.logRequest(req, "Location", "獲取完整地區層級資料");

      const { data, message } = await LocationService.getLocationHierarchy();

      res.json({
        success: true,
        data,
      });
    } catch (error: unknown) {
      APILogger.logError("Location", "獲取地區層級資料", error, 500);

      res.status(500).json({
        success: false,
        message: LOCATION_ERROR_MESSAGES.GET_HIERARCHY_FAILED,
        error: error instanceof Error ? error.message : LOCATION_ERROR_MESSAGES.UNKNOWN_ERROR,
      });
    }
  }
);

export default router;
