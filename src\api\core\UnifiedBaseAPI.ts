import { authService } from "../../services/authService";
import { getApiBaseUrl } from "../../config/environment";
import ApiErrorHandler, { ApiError } from "../ApiErrorHandler";

// API 響應介面
export interface ApiResponse<T = unknown> {
  success: boolean;
  data: T | null;
  message?: string;
  errors?: Record<string, string>;
  code?: string;
  timestamp?: string;
}

// API 錯誤響應介面
export interface ApiErrorResponse {
  message?: string;
  code?: string;
  details?: {
    userRole?: string;
    allowedRoles?: string[];
    action?: string;
  };
}

// API 配置介面
export interface ApiConfig {
  baseUrl?: string;
  timeout?: number;
  debugMode?: boolean;
  headers?: Record<string, string>;
}

// API 請求選項介面
export interface ApiRequestOptions extends RequestInit {
  params?: Record<string, string | number>;
  timeout?: number;
}

/**
 * 統一的 API 基底類別
 * 整合了原本的 BaseAPI、BaseService 和 apiInterceptor 功能
 * 提供完整的錯誤處理、Token 管理、超時控制等功能
 */
export abstract class UnifiedBaseAPI {
  protected readonly baseUrl: string;
  protected readonly timeout: number;
  protected readonly debugMode: boolean;
  protected readonly defaultHeaders: Record<string, string>;
  private isRedirecting = false;

  constructor(config?: ApiConfig) {
    this.baseUrl = config?.baseUrl || getApiBaseUrl();
    this.timeout = config?.timeout || 30000;
    this.debugMode = config?.debugMode || false;
    this.defaultHeaders = {
      "Content-Type": "application/json",
      ...config?.headers,
    };
  }

  /**
   * 核心 API 請求方法
   * 整合了完整的錯誤處理、認證、超時控制
   */
  protected async request<T>(endpoint: string, options: ApiRequestOptions = {}): Promise<ApiResponse<T>> {
    const url = this.buildUrl(endpoint, options.params);

    // 調試模式日誌
    if (this.debugMode) {
      console.log(`🌐 [${this.constructor.name}] API 請求:`, {
        endpoint,
        url,
        method: options.method || "GET",
        params: options.params,
      });
    }

    // 設置超時控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || this.timeout);

    try {
      // 獲取認證 headers
      const authHeaders = authService.getAuthHeaders();

      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...authHeaders,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // 處理響應
      return await this.handleResponse<T>(response, url);
    } catch (error) {
      clearTimeout(timeoutId);
      console.error(`❌ [${this.constructor.name}] 請求失敗 ${endpoint}:`, error);
      
      // 如果是網路錯誤，包裝為標準 ApiError
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw ApiErrorHandler.fromNetworkError(error as Error);
      }
      
      // 如果是取消錯誤（超時），包裝為超時錯誤
      if (error instanceof Error && error.name === 'AbortError') {
        throw ApiErrorHandler.createError('TIMEOUT_ERROR', '請求超時，請稍後再試');
      }
      
      throw error;
    }
  }

  /**
   * 統一處理 API 響應
   * 整合了所有錯誤處理邏輯
   */
  private async handleResponse<T>(response: Response, originalUrl: string): Promise<ApiResponse<T>> {
    try {
      // 檢查是否為成功響應
      if (response.ok) {
        const data = await response.json();

        if (this.debugMode) {
          console.log(`✅ [${this.constructor.name}] 響應成功:`, {
            url: originalUrl,
            status: response.status,
            dataSize: JSON.stringify(data).length,
          });
        }

        return data;
      }

      // 處理錯誤響應
      const errorData: ApiErrorResponse = await response.json().catch(() => ({
        message: `HTTP ${response.status}: ${response.statusText}`,
        code: "HTTP_ERROR",
      }));

      console.error(`❌ [${this.constructor.name}] 錯誤響應:`, errorData);

      // 檢查是否為 Token 過期相關錯誤
      if (this.isTokenExpiredError(response.status, errorData)) {
        await this.handleTokenExpired(originalUrl, errorData);
        throw new Error("TOKEN_EXPIRED");
      }

      // 檢查是否為角色權限錯誤
      if (response.status === 403) {
        await this.handlePermissionDenied(originalUrl, errorData);
        throw new Error("PERMISSION_DENIED");
      }

      // 建立標準化錯誤並拋出
      const apiError = ApiErrorHandler.fromHttpResponse(response.status, errorData);
      throw apiError;
    } catch (error) {
      if (error instanceof Error && (error.message === "TOKEN_EXPIRED" || error.message === "PERMISSION_DENIED")) {
        throw error;
      }

      console.error(`💥 [${this.constructor.name}] 響應處理失敗:`, error);
      throw new Error("API 響應處理失敗");
    }
  }

  /**
   * 檢查是否為 Token 過期錯誤
   * 整合了 apiInterceptor 的檢查邏輯
   */
  private isTokenExpiredError(status: number, errorData: ApiErrorResponse): boolean {
    // 檢查 HTTP 狀態碼
    if (status === 401) {
      return true;
    }

    // 檢查錯誤碼
    const TOKEN_EXPIRED_CODES = [
      "TOKEN_EXPIRED",
      "TOKEN_INVALID",
      "TOKEN_REVOKED", 
      "UNAUTHORIZED",
      "AUTH_FAILED",
      "TOKEN_NOT_FOUND",
      "VALIDATION_FAILED",
      "TOKEN_MISSING",
      "AUTHENTICATION_ERROR",
      "NO_TOKEN",
    ];

    if (errorData.code && TOKEN_EXPIRED_CODES.includes(errorData.code)) {
      return true;
    }

    // 檢查錯誤訊息
    const message = (errorData.message || "").toLowerCase();
    const expiredKeywords = [
      "token expired",
      "token invalid",
      "unauthorized",
      "token過期",
      "認證失敗",
      "token not found in any validation source",
      "token not found",
      "validation source",
      "no valid token",
      "token missing",
      "authentication failed",
    ];

    return expiredKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * 處理 Token 過期
   * 整合了重定向防重複邏輯
   */
  private async handleTokenExpired(url: string, errorData: ApiErrorResponse): Promise<void> {
    if (this.isRedirecting) {
      return;
    }

    console.warn(`⏰ [${this.constructor.name}] Token 已過期或無效: ${url}`, errorData);

    try {
      this.isRedirecting = true;

      // 觸發 authService 的 Token 過期回調
      authService.onTokenExpired?.();

      // 執行登出操作
      await authService.logout();

      // 顯示過期提示
      this.showExpiredMessage(errorData.message || "Token 已過期，請重新登入");

      // 延遲重定向到登入頁面
      setTimeout(() => {
        console.log("🔄 [API] 重定向到登入頁面");
        window.location.href = "/login?reason=token_expired";
      }, 2000);
    } catch (error) {
      console.error("💥 [API] 處理 Token 過期失敗:", error);
    }
  }

  /**
   * 處理權限被拒絕
   */
  private async handlePermissionDenied(url: string, errorData: ApiErrorResponse): Promise<void> {
    if (this.isRedirecting) {
      return;
    }

    console.warn(`🚫 [${this.constructor.name}] 權限被拒絕: ${url}`, errorData);

    try {
      this.isRedirecting = true;

      // 檢查是否為角色權限問題
      if (errorData.details?.action === "account_logout") {
        // 觸發角色權限被拒絕回調
        authService.onRolePermissionDenied?.(
          errorData.details.userRole || "Unknown", 
          errorData.details.allowedRoles || []
        );
        return;
      }

      // 其他權限問題
      this.showPermissionDeniedMessage(errorData.message || "您沒有權限執行此操作");

      // 延遲重定向
      setTimeout(() => {
        console.log("🔄 [API] 重定向到登入頁面");
        window.location.href = "/login?reason=permission_denied";
      }, 2000);
    } catch (error) {
      console.error("💥 [API] 處理權限拒絕失敗:", error);
    }
  }

  /**
   * 顯示 Token 過期訊息
   */
  private showExpiredMessage(message: string): void {
    if (typeof window === "undefined") return;

    try {
      const alertDiv = document.createElement("div");
      alertDiv.id = "token-expired-alert";
      alertDiv.className = "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg";
      alertDiv.innerHTML = `
        <div class="flex items-center gap-2">
          <span>⏰</span>
          <span>${message}</span>
        </div>
      `;

      const existing = document.getElementById("token-expired-alert");
      if (existing) {
        existing.remove();
      }

      document.body.appendChild(alertDiv);

      setTimeout(() => {
        if (alertDiv.parentNode) {
          alertDiv.remove();
        }
      }, 1500);
    } catch (error) {
      console.error("💥 [API] 顯示過期訊息失敗:", error);
    }
  }

  /**
   * 顯示權限被拒絕訊息
   */
  private showPermissionDeniedMessage(message: string): void {
    if (typeof window === "undefined") return;

    try {
      const alertDiv = document.createElement("div");
      alertDiv.id = "permission-denied-alert";
      alertDiv.className = "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-orange-500 text-white px-6 py-3 rounded-lg shadow-lg";
      alertDiv.innerHTML = `
        <div class="flex items-center gap-2">
          <span>🚫</span>
          <span>${message}</span>
        </div>
      `;

      const existing = document.getElementById("permission-denied-alert");
      if (existing) {
        existing.remove();
      }

      document.body.appendChild(alertDiv);

      setTimeout(() => {
        if (alertDiv.parentNode) {
          alertDiv.remove();
        }
      }, 1500);
    } catch (error) {
      console.error("💥 [API] 顯示權限拒絕訊息失敗:", error);
    }
  }

  /**
   * 構建完整 URL
   */
  private buildUrl(endpoint: string, params?: Record<string, string | number>): string {
    let url = `${this.baseUrl}${endpoint}`;

    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value));
      });
      url += `?${searchParams.toString()}`;
    }

    return url;
  }

  /**
   * GET 請求便捷方法
   */
  protected async get<T>(
    endpoint: string, 
    paramsOrHeaders?: Record<string, string | number>, 
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    // 支援兩種調用方式:
    // 1. get(endpoint, params) - 舊版相容模式
    // 2. get(endpoint, headers) - 當第二個參數是 headers 時
    let params: Record<string, string | number> | undefined;
    let requestHeaders: Record<string, string> | undefined;
    
    if (paramsOrHeaders) {
      // 判斷第二個參數是 params 還是 headers
      const isHeaders = Object.values(paramsOrHeaders).every(value => typeof value === 'string') && 
                       (Object.keys(paramsOrHeaders).some(key => key.includes('token') || key.startsWith('x-')) || 
                        headers !== undefined);
      
      if (isHeaders && headers === undefined) {
        requestHeaders = paramsOrHeaders as Record<string, string>;
      } else {
        params = paramsOrHeaders;
        requestHeaders = headers;
      }
    }

    return this.request<T>(endpoint, {
      method: "GET",
      params,
      headers: requestHeaders,
    });
  }

  /**
   * POST 請求便捷方法
   */
  protected async post<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    const options: ApiRequestOptions = {
      method: "POST",
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, options);
  }

  /**
   * PUT 請求便捷方法
   */
  protected async put<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    const options: ApiRequestOptions = {
      method: "PUT",
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, options);
  }

  /**
   * DELETE 請求便捷方法
   */
  protected async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }

  /**
   * 文件上傳方法
   * 整合了原本的上傳邏輯
   */
  protected async uploadFile<T>(
    endpoint: string, 
    file: File, 
    additionalData?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append("file", file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    // 獲取認證 headers (不包含 Content-Type，讓瀏覽器自動設置)
    const authHeaders = authService.getAuthHeaders();

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: "POST",
      headers: {
        ...authHeaders,
        // 不設置 Content-Type，讓瀏覽器自動設置 multipart/form-data
      },
      body: formData,
    });

    return this.handleResponse<T>(response, endpoint);
  }

  /**
   * 健康檢查方法
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.request<{ status: string }>("/health");
      return response.success && response.data?.status === "ok";
    } catch {
      return false;
    }
  }

  /**
   * 重置重定向狀態（用於測試或手動重置）
   */
  public resetRedirectingState(): void {
    this.isRedirecting = false;
    console.log("🔄 [API] 已重置重定向狀態");
  }

  /**
   * 獲取當前狀態
   */
  public getStatus(): { isRedirecting: boolean } {
    return {
      isRedirecting: this.isRedirecting,
    };
  }
}