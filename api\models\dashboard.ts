// ========== 儀表板相關資料模型 ==========

export interface CityStatistics {
  cityId: number;
  cityName: string;
  bronzeCount: number;
  silverCount: number;
  greenFlagCount: number;
  totalSchools: number;
}

export interface LatestCertification {
  schoolName: string;
  certificationLevel: string;
  passDate: string;
  cityName: string;
}

export interface SchoolCertificationStatus {
  certificationId: string;
  level: number;
  levelName: string;
  status: string;
  reviewStatus: number;
  applyDate: string;
  reviewDate?: string;
  passDate?: string;
}

export interface SchoolArticle {
  articleId: string;
  title: string;
  summary?: string;
  status: string;
  publishDate?: string;
  createDate: string;
}

export interface SchoolPassedCertification {
  certificationId: string;
  level: number;
  levelName: string;
  passDate: string;
}

export interface SchoolInfo {
  schoolId: string;
  schoolName: string;
  accountId: number;
}

export interface UserCityInfo {
  countyId: number;
  cityName: string;
}

// 資料庫查詢結果介面
export interface CityQueryResult {
  cityId: number;
  cityName: string;
}

export interface StatisticsQueryResult {
  totalSchools: number;
  bronzeCount: number;
  silverCount: number;
  greenFlagCount: number;
}

export interface CertificationQueryResult {
  CertificationId: string;
  Level: number;
  ReviewStatus: number;
  applyDate: string;
  ReviewDate?: string;
  passDate?: string;
  levelName: string;
}

export interface LatestCertificationQueryResult {
  schoolName: string;
  certificationLevel: string;
  passDate: string;
  cityName: string;
}

// API 回應介面
export interface TestResponse {
  success: boolean;
  data: CityStatistics;
  message: string;
}

export interface CityStatisticsResponse {
  success: boolean;
  data: CityStatistics;
  message: string;
}

export interface LatestCertificationsResponse {
  success: boolean;
  data: LatestCertification[];
  message: string;
}

export interface SchoolCurrentCertificationResponse {
  success: boolean;
  data: SchoolCertificationStatus | null;
  message: string;
}

export interface SchoolArticlesResponse {
  success: boolean;
  data: SchoolArticle[];
  message: string;
}

export interface SchoolPassedCertificationsResponse {
  success: boolean;
  data: SchoolPassedCertification[];
  message: string;
}

// 查詢參數類型
export interface CityStatisticsParams {
  cityId: string;
}

export interface LatestCertificationsParams {
  cityId: string;
}

export interface LatestCertificationsQueryParams {
  limit?: string;
}

export interface SchoolCertificationQueryParams {}

export interface SchoolArticlesQueryParams {
  limit?: string;
}

export interface SchoolPassedCertificationsQueryParams {}
