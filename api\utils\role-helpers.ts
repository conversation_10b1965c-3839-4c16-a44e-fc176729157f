import { MemberProfile } from "../config/database-mssql.js";

// 系統支援的角色類型
export const ROLE_TYPES = {
  SCHOOL: "School",
  GOVERNMENT: "Government",
  TUTOR: "Tutor",
  ADMIN: "Admin",
} as const;

// 前端角色映射
export const FRONTEND_ROLES = {
  SCHOOL: "school",
  EPA: "epa",
  TUTOR: "tutor",
  ADMIN: "admin",
} as const;

// 允許的角色類型（與認證中間件保持一致）
export const ALLOWED_ROLE_TYPES = [ROLE_TYPES.SCHOOL, ROLE_TYPES.GOVERNMENT, ROLE_TYPES.TUTOR];

// 角色層級定義（數字越高權限越大）
export const ROLE_HIERARCHY = {
  [ROLE_TYPES.SCHOOL]: 1,
  [ROLE_TYPES.TUTOR]: 2,
  [ROLE_TYPES.GOVERNMENT]: 3,
  [ROLE_TYPES.ADMIN]: 4,
} as const;

// 角色顯示名稱映射
export const ROLE_DISPLAY_NAMES = {
  [ROLE_TYPES.SCHOOL]: "學校夥伴",
  [ROLE_TYPES.GOVERNMENT]: "環保署人員",
  [ROLE_TYPES.TUTOR]: "輔導員",
  [ROLE_TYPES.ADMIN]: "系統管理員",
} as const;

/**
 * 根據資料庫布林欄位判斷角色類型
 */
export function determineRoleTypeFromBooleanFields(isSchoolPartner?: boolean, isEpaUser?: boolean, isGuidanceTeam?: boolean): string {
  if (isSchoolPartner) return ROLE_TYPES.SCHOOL;
  if (isEpaUser) return ROLE_TYPES.GOVERNMENT;
  if (isGuidanceTeam) return ROLE_TYPES.TUTOR;
  return ROLE_TYPES.SCHOOL; // 預設值
}

/**
 * 根據資料庫整數欄位判斷角色類型
 */
export function determineRoleTypeFromIntegerFields(IsSchoolPartner?: number, IsEpaUser?: number, IsGuidanceTeam?: number): string {
  // 按優先順序判斷角色
  if (IsSchoolPartner === 1) return ROLE_TYPES.SCHOOL;
  if (IsEpaUser === 1) return ROLE_TYPES.GOVERNMENT;
  if (IsGuidanceTeam === 1) return ROLE_TYPES.TUTOR;
  return ROLE_TYPES.SCHOOL; // 預設值
}

/**
 * 角色類型映射到前端格式
 */
export function mapRoleTypeToFrontendRole(roleType: string): string {
  switch (roleType) {
    case ROLE_TYPES.SCHOOL:
      return FRONTEND_ROLES.SCHOOL;
    case ROLE_TYPES.GOVERNMENT:
      return FRONTEND_ROLES.EPA;
    case ROLE_TYPES.TUTOR:
      return FRONTEND_ROLES.TUTOR;
    case ROLE_TYPES.ADMIN:
      return FRONTEND_ROLES.ADMIN;
    default:
      return FRONTEND_ROLES.SCHOOL;
  }
}

/**
 * 前端角色映射到角色類型
 */
export function mapFrontendRoleToRoleType(frontendRole: string): string {
  switch (frontendRole.toLowerCase()) {
    case FRONTEND_ROLES.SCHOOL:
      return ROLE_TYPES.SCHOOL;
    case FRONTEND_ROLES.EPA:
      return ROLE_TYPES.GOVERNMENT;
    case FRONTEND_ROLES.TUTOR:
      return ROLE_TYPES.TUTOR;
    case FRONTEND_ROLES.ADMIN:
      return ROLE_TYPES.ADMIN;
    default:
      return ROLE_TYPES.SCHOOL;
  }
}

/**
 * 轉換舊版角色格式到新格式
 */
export function mapOldRoleToNew(oldRole: string): string {
  switch (oldRole.toLowerCase()) {
    case "school":
      return ROLE_TYPES.SCHOOL;
    case "epa":
    case "government":
      return ROLE_TYPES.GOVERNMENT;
    case "tutor":
      return ROLE_TYPES.TUTOR;
    case "admin":
      return ROLE_TYPES.ADMIN;
    default:
      return ROLE_TYPES.SCHOOL;
  }
}

/**
 * 檢查角色是否被允許存取系統
 */
export function isRoleAllowed(roleType: string): boolean {
  return ALLOWED_ROLE_TYPES.includes(roleType as typeof ROLE_TYPES[keyof typeof ROLE_TYPES]);
}

/**
 * 檢查用戶是否有指定角色
 */
export function hasRole(user: MemberProfile, requiredRole: string | string[]): boolean {
  const requiredRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
  return requiredRoles.includes(user.roleType);
}

/**
 * 檢查用戶角色層級是否足夠
 */
export function hasMinimumRoleLevel(user: MemberProfile, minimumRole: string): boolean {
  const userLevel = ROLE_HIERARCHY[user.roleType as keyof typeof ROLE_HIERARCHY] || 0;
  const requiredLevel = ROLE_HIERARCHY[minimumRole as keyof typeof ROLE_HIERARCHY] || 0;
  return userLevel >= requiredLevel;
}

/**
 * 獲取角色顯示名稱
 */
export function getRoleDisplayName(roleType: string): string {
  return ROLE_DISPLAY_NAMES[roleType as keyof typeof ROLE_DISPLAY_NAMES] || roleType;
}

/**
 * 檢查用戶是否為管理員
 */
export function isAdmin(user: MemberProfile): boolean {
  return user.roleType === ROLE_TYPES.ADMIN || user.permissions.includes("admin") || user.permissionGroups.includes("admin");
}

/**
 * 檢查用戶是否為學校身份
 */
export function isSchool(user: MemberProfile): boolean {
  return user.roleType === ROLE_TYPES.SCHOOL;
}

/**
 * 檢查用戶是否為政府身份
 */
export function isGovernment(user: MemberProfile): boolean {
  return user.roleType === ROLE_TYPES.GOVERNMENT;
}

/**
 * 檢查用戶是否為輔導員身份
 */
export function isTutor(user: MemberProfile): boolean {
  return user.roleType === ROLE_TYPES.TUTOR;
}

/**
 * 獲取角色特定的權限列表
 */
export function getRolePermissions(roleType: string): string[] {
  switch (roleType) {
    case ROLE_TYPES.SCHOOL:
      return ["read", "write", "certification_apply", "profile_edit", "contact_manage", "statistics_edit"];
    case ROLE_TYPES.GOVERNMENT:
      return ["read", "write", "certification_review", "application_approve", "comment_manage", "report_view"];
    case ROLE_TYPES.TUTOR:
      return ["read", "write", "guidance_provide", "school_support", "progress_track"];
    case ROLE_TYPES.ADMIN:
      return ["admin", "user_manage", "system_config", "data_export", "log_view"];
    default:
      return ["read"];
  }
}

/**
 * 檢查角色是否有特定權限
 */
export function roleHasPermission(roleType: string, permission: string): boolean {
  const rolePermissions = getRolePermissions(roleType);
  return rolePermissions.includes(permission) || (roleType === ROLE_TYPES.ADMIN && permission !== "admin"); // 管理員有所有權限
}

/**
 * 獲取可存取指定資源的角色列表
 */
export function getRolesForResource(resource: string): string[] {
  switch (resource) {
    case "profile":
      return [ROLE_TYPES.SCHOOL, ROLE_TYPES.GOVERNMENT, ROLE_TYPES.TUTOR];
    case "certification":
      return [ROLE_TYPES.SCHOOL, ROLE_TYPES.GOVERNMENT, ROLE_TYPES.TUTOR];
    case "review":
      return [ROLE_TYPES.GOVERNMENT, ROLE_TYPES.TUTOR];
    case "admin":
      return [ROLE_TYPES.ADMIN];
    default:
      return ALLOWED_ROLE_TYPES;
  }
}

/**
 * 創建角色檢查函數
 */
export function createRoleChecker(allowedRoles: string | string[]) {
  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

  return (user: MemberProfile): boolean => {
    return hasRole(user, roles);
  };
}

/**
 * 創建權限檢查函數
 */
export function createPermissionChecker(requiredPermission: string) {
  return (user: MemberProfile): boolean => {
    return (
      user.permissions.includes(requiredPermission) ||
      user.permissionGroups.some((group) => group.toLowerCase().includes(requiredPermission.toLowerCase())) ||
      roleHasPermission(user.roleType, requiredPermission)
    );
  };
}
