// ========== 用戶相關資料模型 ==========

export interface UserProfile {
  id: string;
  account: string;
  nickName: string;
  email?: string;
  phone?: string;
  avatar?: string;
  roleType: string;
  isActive: boolean;
  createdTime: Date;
  updatedTime: Date;
  remark?: string;
  permissions: string[];
  permissionGroups: string[];
  school?: SchoolInfo;
  certifications: CertificationInfo[];
}

export interface SchoolInfo {
  id: number;
  name: string;
  englishName?: string;
  code?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
}

export interface CertificationInfo {
  id: string;
  certificationType: string;
  level: number;
  status: string;
  applyDate: Date;
  reviewDate?: Date;
  passDate?: Date;
  expiredDate?: Date;
  certificateNumber?: string;
}

export interface UserProfileUpdateRequest {
  // 基本帳號資料
  nickName?: string;
  email?: string;
  phone?: string;
  avatar?: string;

  // 學校資料 (如果是學校身份)
  school?: {
    name?: string;
    englishName?: string;
    address?: string;
    phone?: string;
    email?: string;
    website?: string;
    contactPerson?: string;
    contactPhone?: string;
    contactEmail?: string;
  };
}

export interface PermissionInfo {
  code: string;
  name: string;
  description?: string;
}

export interface PermissionGroupInfo {
  code: string;
  name: string;
  description?: string;
}

export interface UserPermissions {
  permissions: PermissionInfo[];
  permissionGroups: PermissionGroupInfo[];
}

// 資料庫查詢結果介面
export interface AccountQueryResult {
  Id: string;
  Account: string;
  NickName: string;
  Email?: string;
  Phone?: string;
  Avatar?: string;
  RoleType: string;
  IsActive: boolean;
  CreatedTime: Date;
  UpdatedTime: Date;
  Remark?: string;
  CmsUserId?: number;
}

export interface SchoolQueryResult {
  Id: number;
  Name: string;
  EnglishName?: string;
  Code?: string;
  Address?: string;
  Phone?: string;
  Email?: string;
  Website?: string;
  ContactPerson?: string;
  ContactPhone?: string;
  ContactEmail?: string;
  IsActive: boolean;
  CreatedTime: Date;
  UpdatedTime: Date;
}

export interface CertificationQueryResult {
  Id: number;
  Level?: number;
  ReviewStatus?: number;
  CreatedTime?: Date;
  ReviewDate?: Date;
  ApprovedDate?: Date;
  ExpiredDate?: Date;
  CertificateNumber?: string;
}

export interface PermissionQueryResult {
  Code: string;
  Name: string;
  Description?: string;
}

export interface PermissionGroupQueryResult {
  Code: string;
  Name: string;
  Description?: string;
}

// 更新欄位類型
export interface AccountUpdateFields {
  NickName?: string;
  Email?: string;
  Phone?: string;
  Avatar?: string;
}

export interface SchoolUpdateFields {
  Name?: string;
  EnglishName?: string;
  Address?: string;
  Phone?: string;
  Email?: string;
  Website?: string;
  ContactPerson?: string;
  ContactPhone?: string;
  ContactEmail?: string;
}

// API 回應介面
export interface UserProfileResponse {
  success: boolean;
  data: UserProfile;
  message?: string;
}

export interface UserProfileUpdateResponse {
  success: boolean;
  data: UserProfile;
  message: string;
}

export interface UserCertificationsResponse {
  success: boolean;
  data: CertificationQueryResult[];
}

export interface UserSchoolResponse {
  success: boolean;
  data: SchoolQueryResult;
}

export interface UserPermissionsResponse {
  success: boolean;
  data: UserPermissions;
}

// 查詢參數類型
export interface UserProfileParams {
  userId: string;
}

export interface UserProfileQueryParams {}

export interface UserCertificationsQueryParams {}

export interface UserSchoolQueryParams {}

export interface UserPermissionsQueryParams {}
