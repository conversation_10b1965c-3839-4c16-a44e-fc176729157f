// 校園投稿服務 - 業務邏輯層
import { campusSubmissionAPI } from "@/api";
import {
  CampusSubmission,
  CampusSubmissionCreateData,
  CampusSubmissionCreateResponse,
  CampusSubmissionDeleteResponse,
  CampusSubmissionDetail,
  CampusSubmissionDetailResponse,
  CampusSubmissionStatusResponse,
  CampusSubmissionFileUploadResponse,
} from "@/api/campusSubmissionAPI";
import { ApiPaginationWithSchoolInfoResponse } from "@/api/BaseAPI";

class CampusSubmissionService {
  /**
   * 獲取校園投稿列表
   * @param options 查詢選項
   * @returns 投稿列表和分頁資訊
   */
  async getSubmissions(options: { page?: number; limit?: number } = {}): Promise<ApiPaginationWithSchoolInfoResponse<CampusSubmission>> {
    try {
      const { page = 1, limit = 50 } = options;
      const response = await campusSubmissionAPI.getSubmissions({ page, limit });
      return response;
    } catch (error) {
      console.error("獲取校園投稿列表失敗:", error);
      throw error;
    }
  }

  /**
   * 獲取單一校園投稿詳細資料
   * @param submissionId 投稿 ID
   * @returns 投稿詳細資料
   */
  async getSubmissionDetail(submissionId: string): Promise<CampusSubmissionDetailResponse> {
    try {
      const response = await campusSubmissionAPI.getSubmissionDetail(submissionId);
      return (
        response.data || {
          success: false,
          data: {} as CampusSubmissionDetail,
          message: response.message || "獲取投稿詳情失敗",
        }
      );
    } catch (error) {
      console.error("獲取校園投稿詳情失敗:", error);
      throw error;
    }
  }

  /**
   * 建立校園投稿申請
   * @param submissionData 投稿資料
   * @returns 建立結果
   */
  async createSubmission(submissionData: CampusSubmissionCreateData): Promise<CampusSubmissionCreateResponse> {
    try {
      const response = await campusSubmissionAPI.createSubmission(submissionData);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "建立投稿失敗",
          };
    } catch (error) {
      console.error("建立校園投稿失敗:", error);
      throw error;
    }
  }

  /**
   * 刪除校園投稿（軟刪除）
   * @param submissionId 投稿 ID
   * @returns 刪除結果
   */
  async deleteSubmission(submissionId: string): Promise<CampusSubmissionDeleteResponse> {
    try {
      const response = await campusSubmissionAPI.deleteSubmission(submissionId);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "刪除投稿失敗",
          };
    } catch (error) {
      console.error("刪除校園投稿失敗:", error);
      throw error;
    }
  }

  /**
   * 申請修改投稿
   * @param submissionId 投稿 ID
   * @returns 申請結果
   */
  async requestModify(submissionId: string): Promise<CampusSubmissionStatusResponse> {
    try {
      const response = await campusSubmissionAPI.requestModify(submissionId);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "申請修改失敗",
          };
    } catch (error) {
      console.error("申請修改投稿失敗:", error);
      throw error;
    }
  }

  /**
   * 上傳校園投稿照片
   * @param submissionId 投稿 ID
   * @param files 照片檔案陣列
   * @returns 上傳結果
   */
  async uploadPhotos(submissionId: string, files: File[]): Promise<CampusSubmissionFileUploadResponse> {
    try {
      const response = await campusSubmissionAPI.uploadPhotos(submissionId, files);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "照片上傳失敗",
            data: [],
          };
    } catch (error) {
      console.error("上傳照片失敗:", error);
      throw error;
    }
  }

  /**
   * 上傳校園投稿附件
   * @param submissionId 投稿 ID
   * @param files 附件檔案陣列
   * @returns 上傳結果
   */
  async uploadAttachments(submissionId: string, files: File[]): Promise<CampusSubmissionFileUploadResponse> {
    try {
      const response = await campusSubmissionAPI.uploadAttachments(submissionId, files);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "附件上傳失敗",
            data: [],
          };
    } catch (error) {
      console.error("上傳附件失敗:", error);
      throw error;
    }
  }

  /**
   * 刪除校園投稿照片
   * @param submissionId 投稿 ID
   * @param filename 檔案名
   * @returns 刪除結果
   */
  async deletePhoto(submissionId: string, filename: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await campusSubmissionAPI.deletePhoto(submissionId, filename);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "刪除照片失敗",
          };
    } catch (error) {
      console.error("刪除照片失敗:", error);
      throw error;
    }
  }

  /**
   * 刪除校園投稿附件
   * @param submissionId 投稿 ID
   * @param filename 檔案名
   * @returns 刪除結果
   */
  async deleteAttachment(submissionId: string, filename: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await campusSubmissionAPI.deleteAttachment(submissionId, filename);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "刪除附件失敗",
          };
    } catch (error) {
      console.error("刪除附件失敗:", error);
      throw error;
    }
  }

  /**
   * 撤回投稿送審狀態（用於編輯）
   * @param submissionId 投稿 ID
   * @returns 撤回結果
   */
  async withdrawSubmission(submissionId: string): Promise<CampusSubmissionStatusResponse> {
    try {
      const response = await campusSubmissionAPI.withdrawSubmission(submissionId);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "撤回投稿失敗",
          };
    } catch (error) {
      console.error("撤回投稿失敗:", error);
      throw error;
    }
  }

  /**
   * 更新投稿內容
   * @param submissionId 投稿 ID
   * @param submissionData 更新資料
   * @returns 更新結果
   */
  async updateSubmission(submissionId: string, submissionData: CampusSubmissionCreateData): Promise<CampusSubmissionCreateResponse> {
    try {
      console.log("🔄 [Update] 開始更新投稿:", {
        submissionId,
        zhTitle: submissionData.zh?.title,
        enTitle: submissionData.en?.title,
        photosCount: submissionData.photos?.length || 0,
        attachmentsCount: submissionData.attachments?.length || 0,
        linksCount: submissionData.links?.length || 0,
      });

      const response = await campusSubmissionAPI.updateSubmission(submissionId, submissionData);

      console.log("✅ [Update] 更新成功:", response);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "更新投稿失敗",
          };
    } catch (error: unknown) {
      console.error("❌ [Update] 更新失敗:", {
        submissionId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * 格式化狀態文字
   * @param status 狀態數字
   * @returns 狀態文字
   */
  getStatusText(status: number): string {
    switch (status) {
      case -1:
        return "未送審";
      case 0:
        return "審核中";
      case 1:
        return "已發布";
      case 2:
        return "已刪除";
      case 3:
        return "已退件";
      default:
        return "未知狀態";
    }
  }

  /**
   * 獲取狀態顏色類名
   * @param status 狀態數字
   * @returns CSS 類名
   */
  getStatusColorClass(status: number): string {
    switch (status) {
      case -1:
        return "text-blue-600"; // 未送審
      case 0:
        return "text-yellow-600"; // 審核中
      case 1:
        return "text-green-600"; // 已發布
      case 2:
        return "text-gray-400"; // 已刪除
      case 3:
        return "text-red-600"; // 已退件
      default:
        return "text-gray-500";
    }
  }

  /**
   * 檢查是否可以操作（查看/編輯）
   * @param status 狀態數字
   * @returns 是否可操作
   */
  canOperate(status: number): boolean {
    // 已刪除的投稿不能再操作
    return status !== 2;
  }

  /**
   * 檢查是否可以刪除
   * @param status 狀態數字
   * @returns 是否可刪除
   */
  canDelete(status: number): boolean {
    // 只有未送審(-1)和已退件(3)的投稿可以刪除
    // 審核中(0)和已發布(1)的投稿不可刪除
    return status === -1 || status === 3;
  }

  /**
   * 格式化日期
   * @param dateString 日期字串
   * @returns 格式化後的日期
   */
  formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("zh-TW", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
    } catch {
      return dateString;
    }
  }

  /**
   * 截斷文字
   * @param text 原始文字
   * @param maxLength 最大長度
   * @returns 截斷後的文字
   */
  truncateText(text: string, maxLength: number = 100): string {
    if (!text) return "";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  }

  /**
   * 送審投稿（將未送審狀態改為審核中）
   * @param submissionId 投稿 ID
   * @returns 送審結果
   */
  async submitForReview(submissionId: string): Promise<CampusSubmissionStatusResponse> {
    try {
      const response = await campusSubmissionAPI.submitForReview(submissionId);
      return response.success
        ? response.data
        : {
            success: false,
            message: response.message || "送審投稿失敗",
          };
    } catch (error) {
      console.error("送審投稿失敗:", error);
      throw error;
    }
  }
}

// 創建服務實例
export const campusSubmissionService = new CampusSubmissionService();

// 導出預設服務
export default campusSubmissionService;
