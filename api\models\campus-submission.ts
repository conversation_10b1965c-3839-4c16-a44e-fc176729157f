// ========== 校園投稿相關資料模型 ==========

export interface CampusSubmission {
  submissionId: string;
  title: string;
  description: string;
  status: number;
  statusText: string;
  submissionDate: string;
  createdTime: string;
  updatedTime?: string;
  badgeType: number;
  featuredStatus: number;
}

export interface CampusSubmissionAttachment {
  attachmentId: string;
  fileEntryId?: string;
  contentTypeCode: string;
  title?: string;
  altUrl?: string;
}

export interface CampusSubmissionDetail extends CampusSubmission {
  zhTitle: string;
  zhContent: string;
  enTitle: string;
  enContent: string;
  attachments?: CampusSubmissionAttachment[];
}

export interface CreateCampusSubmissionRequest {
  zhTitle: string;
  zhContent: string;
  enTitle?: string;
  enContent?: string;
  attachments?: CampusSubmissionAttachment[];
}

export interface UpdateCampusSubmissionRequest extends CreateCampusSubmissionRequest {
  submissionId: string;
}

export interface SchoolInfo {
  schoolId: number;
  schoolName: string;
  AccountId: number;
}

export interface SubmissionQueryResult {
  CampusSubmissionId: string;
  title: string;
  description?: string;
  SubmissionStatus: number;
  ReviewStatus?: number;
  CampusSubmissionReviewId?: string;
  ReviewComment?: string;
  SubmissionDate: string;
  CreatedTime: string;
  UpdatedTime?: string;
  BadgeType: number;
  FeaturedStatus: number;
}

export interface SubmissionDetailQueryResult {
  CampusSubmissionId: string;
  ZhTitle: string;
  ZhContent: string;
  EnTitle?: string;
  EnContent?: string;
  SubmissionStatus: number;
  ReviewStatus?: number;
  ReviewComment?: string;
  SubmissionDate: string;
  CreatedTime: string;
  UpdatedTime?: string;
  BadgeType: number;
  FeaturedStatus: number;
}

export interface AttachmentQueryResult {
  AttachmentId: string;
  FileEntryId?: string;
  ContentTypeCode: string;
  Title?: string;
  AltUrl?: string;
}

// API 回應格式
export interface CampusSubmissionListResponse {
  success: boolean;
  data: CampusSubmission[];
  schoolInfo: {
    schoolId: number;
    schoolName: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
}

export interface CampusSubmissionDetailResponse {
  success: boolean;
  data: CampusSubmissionDetail;
  message?: string;
}

export interface CampusSubmissionCreateResponse {
  success: boolean;
  data: {
    submissionId: string;
    message: string;
  };
  message?: string;
}

export interface CampusSubmissionUpdateResponse {
  success: boolean;
  data: {
    submissionId: string;
    message: string;
  };
  message?: string;
}

export interface CampusSubmissionDeleteResponse {
  success: boolean;
  message: string;
}

export interface CampusSubmissionSubmitResponse {
  success: boolean;
  data: {
    submissionId: string;
    reviewId: string;
    message: string;
  };
  message?: string;
}

export interface CampusSubmissionWithdrawResponse {
  success: boolean;
  data: {
    submissionId: string;
    reviewId: string;
    message: string;
  };
  message?: string;
}

// 查詢參數類型
export interface CampusSubmissionListQueryParams {
  limit?: string;
  page?: string;
}

export interface CampusSubmissionDetailParams {
  submissionId: string;
}
