// ========== 位置相關常數 ==========

// 語言代碼
export const LOCALE_CODES = {
  ZH_TW: "zh-TW",
  EN_US: "en-US",
} as const;

// 錯誤訊息
export const LOCATION_ERROR_MESSAGES = {
  INVALID_CITY_ID: "無效的縣市ID",
  GET_CITIES_FAILED: "獲取縣市列表失敗",
  GET_AREAS_FAILED: "獲取區域列表失敗",
  GET_HIERARCHY_FAILED: "獲取地區層級資料失敗",
  UNKNOWN_ERROR: "未知錯誤",
} as const;

// 成功訊息
export const LOCATION_SUCCESS_MESSAGES = {
  CITIES_RETRIEVED: "縣市列表獲取成功",
  AREAS_RETRIEVED: "區域列表獲取成功",
  HIERARCHY_RETRIEVED: "地區層級資料獲取成功",
} as const;

// SQL 查詢模板
export const SQL_QUERIES = {
  GET_CITIES: `
    SELECT 
      c.CountyId,
      ct.Name
    FROM Counties c
    INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId
    WHERE ct.LocaleCode = @localeCode
    ORDER BY c.CountyId
  `,
  GET_AREAS_BY_CITY: `
    SELECT 
      d.DistrictId,
      dt.Name,
      d.CountyId
    FROM Districts d
    INNER JOIN DistrictTranslations dt ON d.DistrictId = dt.DistrictId
    WHERE d.CountyId = @cityId AND dt.LocaleCode = @localeCode
    ORDER BY d.DistrictId
  `,
  GET_HIERARCHY: `
    SELECT 
      c.CountyId,
      ct.Name as CountyName,
      d.DistrictId,
      dt.Name as DistrictName
    FROM Counties c
    INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId
    LEFT JOIN Districts d ON c.CountyId = d.CountyId
    LEFT JOIN DistrictTranslations dt ON d.DistrictId = dt.DistrictId AND dt.LocaleCode = @localeCode
    WHERE ct.LocaleCode = @localeCode
    ORDER BY c.CountyId, d.DistrictId
  `,
} as const;

// 工具函數：驗證縣市ID
export const isValidCityId = (cityId: string): boolean => {
  const parsedId = parseInt(cityId);
  return !isNaN(parsedId) && parsedId > 0;
};

// 工具函數：格式化縣市資料
export const formatCityData = (cityRow: any): any => {
  return {
    id: cityRow.CountyId,
    name: cityRow.Name,
  };
};

// 工具函數：格式化區域資料
export const formatAreaData = (areaRow: any): any => {
  return {
    id: areaRow.DistrictId,
    name: areaRow.Name,
    cityId: areaRow.CountyId,
  };
};

// 工具函數：組織層級資料
export const organizeHierarchyData = (hierarchyRows: any[]): any[] => {
  const cities: Record<number, any> = {};
  let totalAreas = 0;

  hierarchyRows.forEach((row) => {
    if (!cities[row.CountyId]) {
      cities[row.CountyId] = {
        id: row.CountyId,
        name: row.CountyName,
        areas: [],
      };
    }

    if (row.DistrictId && row.DistrictName) {
      cities[row.CountyId].areas.push({
        id: row.DistrictId,
        name: row.DistrictName,
        cityId: row.CountyId,
      });
      totalAreas++;
    }
  });

  return Object.values(cities);
};

// 工具函數：生成層級資料統計
export const generateHierarchyStats = (hierarchyData: any[]): { totalCities: number; totalAreas: number } => {
  let totalAreas = 0;

  hierarchyData.forEach((city) => {
    totalAreas += city.areas.length;
  });

  return {
    totalCities: hierarchyData.length,
    totalAreas,
  };
};

// 工具函數：生成成功訊息
export const generateSuccessMessage = (type: string, stats?: { totalCities?: number; totalAreas?: number }): string => {
  switch (type) {
    case "hierarchy":
      if (stats) {
        return `成功獲取 ${stats.totalCities} 個縣市和 ${stats.totalAreas} 個區域的層級資料`;
      }
      return LOCATION_SUCCESS_MESSAGES.HIERARCHY_RETRIEVED;
    case "cities":
      return LOCATION_SUCCESS_MESSAGES.CITIES_RETRIEVED;
    case "areas":
      return LOCATION_SUCCESS_MESSAGES.AREAS_RETRIEVED;
    default:
      return "操作成功";
  }
};

// 工具函數：解析縣市ID
export const parseCityId = (cityIdStr: string): number => {
  const cityId = parseInt(cityIdStr);
  if (isNaN(cityId) || cityId <= 0) {
    throw new Error(LOCATION_ERROR_MESSAGES.INVALID_CITY_ID);
  }
  return cityId;
};

// 預設語言代碼
export const DEFAULT_LOCALE = LOCALE_CODES.ZH_TW;
