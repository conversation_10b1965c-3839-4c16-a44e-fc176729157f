import React from "react";
import { Button } from "@/components/ui/button";

const BindPinPage = () => {
  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-start py-12">
      <section
        className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10"
        aria-label="自然人憑證綁定"
        style={{ minHeight: "500px" }}
      >
        <h1
          className="font-size-3xl font-bold text-primary mb-6 text-center"
          tabIndex={0}
        >
          自然人憑證綁定
        </h1>
        <div className="mb-6 text-gray-600 text-center">
          使用自然人憑證進行登入，請插入卡片進行綁定的動作，綁定後即可在登入頁面使用卡片進行登入。
        </div>
        <form className="flex flex-col items-center  gap-4">
          {/* <div className="w-full"> */}
          <label htmlFor="pin" className="block font-semibold mb-1">
            Pin Code
          </label>
          <input
            type="password"
            id="pin"
            placeholder="請輸入Pin Code"
            className="border border-gray-300 rounded px-4 py-2 w-1/2  font-size-lg focus:ring-2 focus:ring-green-600"
          />
          {/* </div> */}
          <Button variant="primary" type="submit" className="mt-2 px-8">
            綁定
          </Button>
        </form>
      </section>
    </main>
  );
};
export default BindPinPage;
