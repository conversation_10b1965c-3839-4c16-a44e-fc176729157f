// 環境變數配置
// 這個檔案負責從 import.meta.env 讀取環境變數並提供預設值

interface EnvConfig {
  // API 配置
  API_BASE_URL: string;
  FRONTEND_URL: string;

  // 官方網站配置
  OFFICIAL_WEBSITE_URL: string;
  OFFICIAL_LOGIN_URL: string;

  // 測試 Token
  TEST_TOKEN_SCHOOL: string;
  TEST_TOKEN_EPA: string;
  TEST_TOKEN_TUTOR: string;

  // 功能開關
  ENABLE_DEBUG_MODE: boolean;
  SHOW_TEST_ACCOUNTS: boolean;
  ENABLE_TOKEN_EXPIRY: boolean;
  SHOW_ENVIRONMENT_SELECTOR: boolean;

  // 外部連結
  EXTERNAL_LINK_BASE: string;
}

// 從環境變數讀取配置
export const env: EnvConfig = {
  // API 配置
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || "http://localhost:3001/api",
  FRONTEND_URL: import.meta.env.VITE_FRONTEND_URL || "http://localhost:8080",

  // 官方網站配置
  OFFICIAL_WEBSITE_URL: import.meta.env.VITE_OFFICIAL_WEBSITE_URL || "https://ecocampus.moenv.gov.tw",
  OFFICIAL_LOGIN_URL: import.meta.env.VITE_OFFICIAL_LOGIN_URL || "https://ecocampus.moenv.gov.tw/login",

  // 測試 Token
  TEST_TOKEN_SCHOOL: import.meta.env.VITE_TEST_TOKEN_SCHOOL || "",
  TEST_TOKEN_EPA: import.meta.env.VITE_TEST_TOKEN_EPA || "",
  TEST_TOKEN_TUTOR: import.meta.env.VITE_TEST_TOKEN_TUTOR || "",

  // 功能開關
  ENABLE_DEBUG_MODE: import.meta.env.VITE_ENABLE_DEBUG_MODE === "true",
  SHOW_TEST_ACCOUNTS: import.meta.env.VITE_SHOW_TEST_ACCOUNTS === "true",
  ENABLE_TOKEN_EXPIRY: import.meta.env.VITE_ENABLE_TOKEN_EXPIRY !== "false",
  SHOW_ENVIRONMENT_SELECTOR: import.meta.env.VITE_SHOW_ENVIRONMENT_SELECTOR === "true",

  // 外部連結
  EXTERNAL_LINK_BASE: import.meta.env.VITE_EXTERNAL_LINK_BASE || "https://ecocampus.sumire.com.tw",
};

// 根據環境變數決定當前環境名稱
export const getEnvironmentName = (): string => {
  const mode = import.meta.env.MODE;

  // 根據 API URL 判斷環境
  if (env.API_BASE_URL.includes("localhost")) {
    return "development";
  } else if (env.API_BASE_URL.includes("sumire.com.tw")) {
    return "testing";
  } else if (env.API_BASE_URL.includes("ecocampus.gov.tw")) {
    return "production";
  }

  return mode || "development";
};

// 環境判斷輔助函數
export const isDevelopment = () => getEnvironmentName() === "development";
export const isTesting = () => getEnvironmentName() === "testing";
export const isProduction = () => getEnvironmentName() === "production";

export default env;
