import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";

const MemberData = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { authState, isLoading } = useAuth();
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    // 避免重複重定向
    if (hasRedirected || isLoading) return;

    const urlParams = new URLSearchParams(location.search);
    const token = urlParams.get("token") || urlParams.get("userToken");

    // 如果 URL 中有 token，重定向到登入頁面並保留 token
    if (token) {
      setHasRedirected(true);
      navigate(`/login?token=${encodeURIComponent(token)}`, { replace: true });
      return;
    }

    // 如果已登入，重定向到 dashboard
    if (authState.isAuthenticated) {
      setHasRedirected(true);
      navigate("/dashboard", { replace: true });
      return;
    }

    // 如果未登入且沒有 token，重定向到登入頁面
    if (!authState.isAuthenticated && !isLoading) {
      setHasRedirected(true);
      navigate("/login", { replace: true });
    }
  }, [authState.isAuthenticated, isLoading, location.search, navigate, hasRedirected]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <p>重定向中...</p>
        {isLoading && <p className="font-size-sm text-gray-500 mt-2">正在檢查登入狀態...</p>}
      </div>
    </div>
  );
};

export default MemberData;
