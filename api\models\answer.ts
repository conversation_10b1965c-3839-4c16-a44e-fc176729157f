// ========== 答案相關資料模型 ==========

export interface AnswerSaveRequest {
  certificationId: string;
  questionId: number;
  answerData: any;
  templateId?: number;
}

export interface AnswerInfo {
  answerId: number;
  certificationId: string;
  questionId: number;
  questionTitle: string;
  questionTemplate: number;
  stepNumber: number;
  parentQuestionId: number | null;
  answerData: any;
  answerStatus: number;
  submittedDate: Date | null;
  reviewedDate: Date | null;
  createdTime: Date;
  updatedTime: Date | null;
}

export interface AnswerSaveResult {
  answerId: number;
  questionId: number;
  templateId: number;
  message: string;
}

export interface CertificationAnswersData {
  certificationId: string;
  answers: AnswerInfo[];
  total: number;
}

// 資料庫查詢結果介面
export interface AnswerRecord {
  CertificationAnswerId: number;
  CertificationId: string;
  QuestionId: number;
  AnswerText: string;
  AnswerStatus: number;
  SubmittedDate: Date | null;
  ReviewedDate: Date | null;
  CreatedTime: Date;
  UpdatedTime: Date | null;
  QuestionTitle: string;
  QuestionTemplate: number;
  StepNumber: number;
  ParentQuestionId: number | null;
}

export interface UserSchoolQueryResult {
  SchoolId: number;
}

export interface CertificationCheckQueryResult {
  SchoolId: number;
  ReviewStatus?: number;
}

export interface QuestionCheckQueryResult {
  QuestionId: number;
  QuestionTemplate: number;
}

export interface ExistingAnswerQueryResult {
  CertificationAnswerId: number;
}

export interface AnswerCheckQueryResult {
  CertificationAnswerId: number;
  CertificationId: string;
  SchoolId: number;
}

// API 回應介面
export interface AnswerSaveResponse {
  success: boolean;
  data: AnswerSaveResult;
  message?: string;
}

export interface CertificationAnswersResponse {
  success: boolean;
  data: CertificationAnswersData;
}

export interface SingleAnswerResponse {
  success: boolean;
  data: AnswerInfo | null;
  message?: string;
}

export interface AnswerDeleteResponse {
  success: boolean;
  message: string;
}

// 查詢參數類型
export interface AnswerSaveParams {}

export interface CertificationAnswersParams {
  certificationId: string;
}

export interface SingleAnswerParams {
  questionId: string;
  certificationId: string;
}

export interface AnswerDeleteParams {
  answerId: string;
}

export interface AnswerSaveQueryParams {}

export interface CertificationAnswersQueryParams {}

export interface SingleAnswerQueryParams {}

export interface AnswerDeleteQueryParams {}
