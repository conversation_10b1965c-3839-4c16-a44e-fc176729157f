import { UnifiedBaseAPI, ApiResponse } from "./core/UnifiedBaseAPI";

// 縣市統計資料介面
export interface CityStatistics {
  cityId: number;
  cityName: string;
  bronzeCount: number;
  silverCount: number;
  greenFlagCount: number;
  totalSchools: number;
}

// 最新認證資料介面
export interface LatestCertification {
  schoolName: string;
  certificationLevel: string;
  passDate: string;
  cityName: string;
}

// 學校認證狀態介面
export interface SchoolCertificationStatus {
  certificationId: string;
  level: number;
  levelName: string;
  status: string;
  reviewStatus: number;
  applyDate: string;
  reviewDate?: string;
  passDate?: string;
}

// 學校投稿文章介面
export interface SchoolArticle {
  articleId: string;
  title: string;
  summary?: string;
  status: string;
  publishDate?: string;
  createDate: string;
}

// 學校已通過認證介面
export interface SchoolPassedCertification {
  certificationId: string;
  level: number;
  levelName: string;
  passDate: string;
  certificateNumber?: string;
}

// 儀表板 API 類別
export class DashboardAPI extends UnifiedBaseAPI {
  constructor() {
    super();
  }

  // 獲取用戶所屬縣市的統計資料
  async getMyCityStatistics(): Promise<ApiResponse<CityStatistics>> {
    return this.get<CityStatistics>("/dashboard/my-city-statistics");
  }

  // 獲取用戶所屬縣市的最新認證
  async getMyLatestCertifications(limit: number = 6): Promise<ApiResponse<LatestCertification[]>> {
    return this.get<LatestCertification[]>("/dashboard/my-latest-certifications", { limit });
  }

  // 學校專用 API 方法

  // 獲取學校當前申請中的認證
  async getSchoolCurrentCertification(): Promise<ApiResponse<SchoolCertificationStatus>> {
    return this.get<SchoolCertificationStatus>("/dashboard/school/current-certification");
  }

  // 獲取學校最新投稿文章
  async getSchoolLatestArticles(limit: number = 6): Promise<ApiResponse<SchoolArticle[]>> {
    return this.get<SchoolArticle[]>("/dashboard/school/latest-articles", { limit });
  }

  // 獲取學校已通過的認證
  async getSchoolPassedCertifications(): Promise<ApiResponse<SchoolPassedCertification[]>> {
    return this.get<SchoolPassedCertification[]>("/dashboard/school/passed-certifications");
  }
}

// 創建全域儀表板 API 實例
export const dashboardAPI = new DashboardAPI();
