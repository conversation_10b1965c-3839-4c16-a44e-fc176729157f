import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "./ui/dialog";
import { But<PERSON> } from "./ui/button";
import { cn } from "../lib/utils";

const medals = [
  {
    name: "綠旗",
    icon: "/img/medal-luxury-green.png",
  },
  {
    name: "銀牌",
    icon: "/img/medal-luxury-silver.png",
  },
  {
    name: "銅牌",
    icon: "/img/medal-luxury-bronze.png",
  },
];

export default function CertificateApplyDialog({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  const [selected, setSelected] = useState<string | null>(null);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md" aria-label="申請認證">
        <DialogHeader>
          <DialogTitle className="font-size-xl">申請認證</DialogTitle>
        </DialogHeader>
        <div className="flex justify-center gap-6 py-6">
          {medals.map((m) => (
            <button
              key={m.name}
              onClick={() => setSelected(m.name)}
              className={cn(
                "flex flex-col items-center gap-2 p-3 rounded-xl border cursor-pointer focus:outline-none",
                selected === m.name
                  ? "border-green-600 bg-green-50 ring-2 ring-green-300"
                  : "border-gray-300 hover:bg-gray-100"
              )}
              aria-pressed={selected === m.name}
            >
              <img
                src={m.icon}
                alt={m.name}
                className="w-16 h-16 object-contain"
              />
              <span className="font-size-base font-medium">{m.name}</span>
            </button>
          ))}
        </div>
        <DialogFooter className="mt-4 flex justify-end gap-4">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              if (selected) {
                // TODO: 送出申請邏輯
                console.log("申請：", selected);
                onClose();
              }
            }}
            disabled={!selected}
          >
            確認
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
