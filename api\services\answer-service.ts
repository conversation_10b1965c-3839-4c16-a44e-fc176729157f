// ========== 答案服務 - 業務邏輯和資料庫操作 ==========

import { executeQuery, executeQuerySingle, executeInsert, executeUpdate } from "../config/database-mssql.js";
import {
  AnswerSaveRequest,
  AnswerInfo,
  AnswerSaveResult,
  CertificationAnswersData,
  AnswerRecord,
  UserSchoolQueryResult,
  CertificationCheckQueryResult,
  QuestionCheckQueryResult,
  ExistingAnswerQueryResult,
  AnswerCheckQueryResult,
} from "../models/answer.js";
import {
  ANSWER_STATUS,
  REVIEW_STATUS,
  QUESTION_STATUS,
  CERTIFICATION_STATUS,
  ANSWER_ERROR_MESSAGES,
  ANSWER_SUCCESS_MESSAGES,
  SQL_QUERIES,
  validateSaveParameters,
  canModifyCertification,
  hasPermissionForCertification,
  formatAnswerRecord,
  getOperationMessage,
} from "../constants/answer.js";

export class AnswerService {
  // 保存認證答案
  static async saveAnswer(request: AnswerSaveRequest, userId: string): Promise<AnswerSaveResult> {
    try {
      // 驗證必要參數
      const validation = validateSaveParameters({ ...request, userId });
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      const { certificationId, questionId, answerData, templateId } = request;

      // 檢查用戶權限
      const userSchool = await this.getUserSchool(userId);
      if (!userSchool) {
        throw new Error(ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL);
      }

      // 檢查認證權限和狀態
      const certificationInfo = await this.getCertificationInfo(certificationId);
      if (!certificationInfo) {
        throw new Error(ANSWER_ERROR_MESSAGES.NO_PERMISSION_MODIFY);
      }

      if (!hasPermissionForCertification(userSchool.SchoolId, certificationInfo.SchoolId)) {
        throw new Error(ANSWER_ERROR_MESSAGES.NO_PERMISSION_MODIFY);
      }

      if (!canModifyCertification(certificationInfo.ReviewStatus || 0)) {
        throw new Error(ANSWER_ERROR_MESSAGES.CERTIFICATION_UNDER_REVIEW);
      }

      // 驗證問題是否存在
      const questionInfo = await this.getQuestionInfo(questionId);
      if (!questionInfo) {
        throw new Error(ANSWER_ERROR_MESSAGES.QUESTION_NOT_FOUND);
      }

      // 準備答案文本
      const answerText = JSON.stringify(answerData);

      // 檢查是否已存在答案
      const existingAnswer = await this.getExistingAnswer(certificationId, questionId);

      let savedAnswerId: number;
      let isUpdate = false;

      if (existingAnswer) {
        // 更新現有答案
        await this.updateAnswer(existingAnswer.CertificationAnswerId, answerText, userId);
        savedAnswerId = existingAnswer.CertificationAnswerId;
        isUpdate = true;

        console.log(`[Answers API] 更新答案 ID: ${savedAnswerId}, 問題 ID: ${questionId}, 模板: ${templateId || questionInfo.QuestionTemplate}`);
      } else {
        // 插入新答案
        savedAnswerId = await this.insertAnswer(certificationId, questionId, answerText, userId);

        console.log(`[Answers API] 新增答案 ID: ${savedAnswerId}, 問題 ID: ${questionId}, 模板: ${templateId || questionInfo.QuestionTemplate}`);
      }

      return {
        answerId: savedAnswerId,
        questionId: questionId,
        templateId: templateId || questionInfo.QuestionTemplate,
        message: getOperationMessage(isUpdate),
      };
    } catch (error) {
      console.error("❌ [AnswerService] 保存答案失敗:", error);
      throw error;
    }
  }

  // 獲取認證的所有答案
  static async getCertificationAnswers(certificationId: string, userId: string): Promise<CertificationAnswersData> {
    try {
      // 檢查用戶權限
      const userSchool = await this.getUserSchool(userId);
      if (!userSchool) {
        throw new Error(ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL);
      }

      // 檢查認證權限
      const certificationInfo = await this.getCertificationSchool(certificationId);
      if (!certificationInfo) {
        throw new Error(ANSWER_ERROR_MESSAGES.NO_PERMISSION_VIEW);
      }

      if (!hasPermissionForCertification(userSchool.SchoolId, certificationInfo.SchoolId)) {
        throw new Error(ANSWER_ERROR_MESSAGES.NO_PERMISSION_VIEW);
      }

      // 查詢所有答案
      const answers = await executeQuery<AnswerRecord>(SQL_QUERIES.GET_CERTIFICATION_ANSWERS, {
        certificationId,
        activeStatus: QUESTION_STATUS.ACTIVE,
      });

      // 格式化答案數據
      const parsedAnswers = answers.map(formatAnswerRecord);

      console.log(`[Answers API] 查詢認證 ${certificationId} 的答案，共 ${parsedAnswers.length} 筆`);

      return {
        certificationId: certificationId,
        answers: parsedAnswers,
        total: parsedAnswers.length,
      };
    } catch (error) {
      console.error("❌ [AnswerService] 獲取認證答案失敗:", error);
      throw error;
    }
  }

  // 獲取單個問題的答案
  static async getSingleAnswer(questionId: number, certificationId: string, userId: string): Promise<AnswerInfo | null> {
    try {
      // 檢查用戶權限
      const userSchool = await this.getUserSchool(userId);
      if (!userSchool) {
        throw new Error(ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL);
      }

      // 檢查認證權限
      const certificationInfo = await this.getCertificationSchool(certificationId);
      if (!certificationInfo) {
        throw new Error(ANSWER_ERROR_MESSAGES.NO_PERMISSION_VIEW);
      }

      if (!hasPermissionForCertification(userSchool.SchoolId, certificationInfo.SchoolId)) {
        throw new Error(ANSWER_ERROR_MESSAGES.NO_PERMISSION_VIEW);
      }

      // 查詢特定問題的答案
      const answer = await executeQuerySingle<AnswerRecord>(SQL_QUERIES.GET_SINGLE_ANSWER, {
        certificationId,
        questionId,
        activeStatus: QUESTION_STATUS.ACTIVE,
      });

      if (!answer) {
        return null;
      }

      const result = formatAnswerRecord(answer);

      console.log(`[Answers API] 查詢問題 ${questionId} 在認證 ${certificationId} 的答案`);

      return result;
    } catch (error) {
      console.error("❌ [AnswerService] 獲取單個答案失敗:", error);
      throw error;
    }
  }

  // 刪除答案
  static async deleteAnswer(answerId: number, userId: string): Promise<void> {
    try {
      // 檢查用戶權限
      const userSchool = await this.getUserSchool(userId);
      if (!userSchool) {
        throw new Error(ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL);
      }

      // 驗證答案是否存在且用戶有權限
      const answerInfo = await this.getAnswerForDelete(answerId);
      if (!answerInfo) {
        throw new Error(ANSWER_ERROR_MESSAGES.ANSWER_NOT_FOUND);
      }

      if (!hasPermissionForCertification(userSchool.SchoolId, answerInfo.SchoolId)) {
        throw new Error(ANSWER_ERROR_MESSAGES.NO_PERMISSION_DELETE);
      }

      // 軟刪除：更新狀態而不是真正刪除
      const affected = await executeUpdate(SQL_QUERIES.DELETE_ANSWER, {
        answerId,
        userId,
        deletedStatus: ANSWER_STATUS.NOT_FILLED,
      });

      if (affected === 0) {
        throw new Error(ANSWER_ERROR_MESSAGES.ANSWER_ALREADY_DELETED);
      }

      console.log(`[Answers API] 刪除答案 ID: ${answerId}`);
    } catch (error) {
      console.error("❌ [AnswerService] 刪除答案失敗:", error);
      throw error;
    }
  }

  // ========== 私有輔助方法 ==========

  // 獲取用戶學校資訊
  private static async getUserSchool(userId: string): Promise<UserSchoolQueryResult | null> {
    return await executeQuerySingle<UserSchoolQueryResult>(SQL_QUERIES.GET_USER_SCHOOL, { userId });
  }

  // 獲取認證資訊（包含審核狀態）
  private static async getCertificationInfo(certificationId: string): Promise<CertificationCheckQueryResult | null> {
    return await executeQuerySingle<CertificationCheckQueryResult>(SQL_QUERIES.GET_CERTIFICATION_INFO, { certificationId });
  }

  // 獲取認證學校資訊
  private static async getCertificationSchool(certificationId: string): Promise<CertificationCheckQueryResult | null> {
    return await executeQuerySingle<CertificationCheckQueryResult>(SQL_QUERIES.GET_CERTIFICATION_SCHOOL, { certificationId });
  }

  // 獲取問題資訊
  private static async getQuestionInfo(questionId: number): Promise<QuestionCheckQueryResult | null> {
    return await executeQuerySingle<QuestionCheckQueryResult>(SQL_QUERIES.GET_QUESTION_INFO, {
      questionId,
      activeStatus: QUESTION_STATUS.ACTIVE,
    });
  }

  // 獲取現有答案
  private static async getExistingAnswer(certificationId: string, questionId: number): Promise<ExistingAnswerQueryResult | null> {
    return await executeQuerySingle<ExistingAnswerQueryResult>(SQL_QUERIES.GET_EXISTING_ANSWER, {
      certificationId,
      questionId,
    });
  }

  // 更新答案
  private static async updateAnswer(answerId: number, answerText: string, userId: string): Promise<void> {
    await executeUpdate(SQL_QUERIES.UPDATE_ANSWER, {
      answerText,
      userId,
      answerId,
      answerStatus: ANSWER_STATUS.FILLED,
    });
  }

  // 插入新答案
  private static async insertAnswer(certificationId: string, questionId: number, answerText: string, userId: string): Promise<number> {
    return await executeInsert(SQL_QUERIES.INSERT_ANSWER, {
      certificationId,
      questionId,
      answerText,
      userId,
      answerStatus: ANSWER_STATUS.FILLED,
    });
  }

  // 獲取答案資訊（用於刪除）
  private static async getAnswerForDelete(answerId: number): Promise<AnswerCheckQueryResult | null> {
    return await executeQuerySingle<AnswerCheckQueryResult>(SQL_QUERIES.GET_ANSWER_FOR_DELETE, { answerId });
  }
}
