// ========== 檔案處理相關資料模型 ==========

export interface UploadedFile {
  originalName: string;
  filename: string;
  fileUrl: string;
  size: number;
  mimetype: string;
  uploadDate: string;
}

export interface FileInfo {
  filename: string;
  size: number;
  createdAt: Date;
  modifiedAt: Date;
  fileUrl: string;
}

export interface SupportedFileTypes {
  images: string[];
  documents: string[];
  videos: string[];
}

export interface SchoolLogoInfo {
  logoUrl: string | null;
  fileName: string | null;
  fileId?: string;
  fileSize?: number;
  fileType?: string;
}

export interface UploadDiagnostics {
  timestamp: string;
  environment: string;
  config: {
    basePath: string;
    schoolLogoPath: string;
    generalUploadPath: string;
  };
  directories: Record<string, DirectoryStatus>;
  permissions: Record<string, PermissionStatus>;
  system: {
    cwd: string;
    platform: string;
    nodeVersion: string;
  };
}

export interface DirectoryStatus {
  exists: boolean;
  path: string;
  error?: string;
}

export interface PermissionStatus {
  readable: boolean;
  writable: boolean;
  writeError?: string;
}

export interface FileEntry {
  id: string;
  type: string;
  path: string;
  originalFileName: string;
  originalExtension: string;
  fileName: string;
  extension: string;
  createdTime?: Date;
  updatedTime?: Date;
}

export interface SchoolInfo {
  id: number;
}

export interface SchoolContent {
  schoolId: number;
  localeCode: string;
  logoFileId: string;
  name?: string;
  createdUserId?: number;
  updatedUserId?: number;
}

// 上傳請求介面
export interface FileUploadRequest {
  file_type?: string;
  certification_sid?: string;
  question_sid?: string;
}

export interface SchoolLogoUploadRequest {}

// 資料庫查詢結果介面
export interface FileEntryQueryResult {
  Id: string;
  Type: string;
  Path: string;
  OriginalFileName: string;
  OriginalExtension: string;
  FileName: string;
  Extension: string;
  CreatedTime?: Date;
  UpdatedTime?: Date;
}

export interface SchoolQueryResult {
  Id: number;
}

export interface SchoolLogoQueryResult {
  logoUrl: string;
  FileName: string;
}

// API 回應介面
export interface FileUploadResponse {
  success: boolean;
  message: string;
  data: UploadedFile | UploadedFile[];
}

export interface SingleFileUploadResponse {
  success: boolean;
  message: string;
  data: {
    id: string;
    url: string;
    fileName: string;
    fileType: string;
    fileSize: number;
  };
}

export interface FileDeleteResponse {
  success: boolean;
  message: string;
}

export interface FileInfoResponse {
  success: boolean;
  data?: FileInfo;
  message?: string;
}

export interface SupportedTypesResponse {
  success: boolean;
  data: SupportedFileTypes;
}

export interface SchoolLogoUploadResponse {
  success: boolean;
  message: string;
  data: SchoolLogoInfo;
}

export interface SchoolLogoGetResponse {
  success: boolean;
  data: SchoolLogoInfo;
}

export interface UploadDiagnosticsResponse {
  success: boolean;
  message: string;
  data: UploadDiagnostics;
}

// 查詢參數類型
export interface FileDeleteParams {
  filename: string;
}

export interface FileInfoParams {
  filename: string;
}

export interface FileDownloadParams {
  filename: string;
}

export interface SchoolLogoGetParams {
  accountId: string;
}

export interface FileUploadQueryParams {}

export interface SupportedTypesQueryParams {}

export interface UploadDiagnosticsQueryParams {}
