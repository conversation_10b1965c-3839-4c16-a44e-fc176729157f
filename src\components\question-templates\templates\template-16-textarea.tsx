import React from "react";
import { Textarea } from "@/components/ui/textarea";
import type { TextAreaData, TemplateProps } from "../types";

// 模板16 - 文字區域輸入
export const TextAreaTemplate: React.FC<TemplateProps<TextAreaData>> = ({ data, onChange, disabled = false }) => {
  const handleChange = (value: string) => {
    onChange({ textarea: value });
  };

  return (
    <div className="space-y-4">
      <Textarea
        value={data.textarea || ""}
        onChange={(e) => handleChange(e.target.value)}
        placeholder="請輸入內容"
        rows={6}
        disabled={disabled}
        className="min-h-[150px]"
      />
      <div className="font-size-sm text-gray-500 text-right">{data.textarea?.length || 0} 字</div>
    </div>
  );
};
