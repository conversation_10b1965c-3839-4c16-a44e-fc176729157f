/**
 * 共用介面模組入口點
 * 統一匯出所有介面定義，提供一致性的匯入介面
 * 
 * @description 此檔案為 shared/interfaces 目錄的主要入口，
 * 讓其他模組可以從單一位置匯入所需的介面
 * @version 1.0.0
 */

// === 認證服務相關介面 ===
export {
  MemberProfile,
  TokenValidationResult,
  RolePermissionResult,
  AuthContext,
  LogoutResult,
  IAuthService,
  IAuthMiddleware
} from './IAuthService';

// === Token 來源管理相關介面 ===
export {
  TokenStorageOptions,
  TokenInfo,
  ITokenSource,
  IEnhancedTokenSource,
  IFrontendTokenSource,
  IBackendTokenSource,
  ITokenSourceFactory,
  ITokenCache
} from './ITokenSource';

// === 用戶服務相關介面 ===
export {
  UserQueryOptions,
  UserQueryResult,
  UserListOptions,
  UserListResult,
  UserUpdateData,
  UserUpdateResult,
  IUserService,
  IUserCacheService,
  IUserRepository
} from './IUserService';

/**
 * 快速存取介面的便利匯出
 */
export const Interfaces = {
  // 認證相關
  Auth: {
    IAuthService: {} as IAuthService,
    IAuthMiddleware: {} as IAuthMiddleware,
    MemberProfile: {} as MemberProfile,
    TokenValidationResult: {} as TokenValidationResult,
    AuthContext: {} as AuthContext
  },
  
  // Token 管理相關
  Token: {
    ITokenSource: {} as ITokenSource,
    IFrontendTokenSource: {} as IFrontendTokenSource,
    IBackendTokenSource: {} as IBackendTokenSource,
    ITokenSourceFactory: {} as ITokenSourceFactory,
    TokenInfo: {} as TokenInfo
  },

  // 用戶服務相關
  User: {
    IUserService: {} as IUserService,
    IUserCacheService: {} as IUserCacheService,
    IUserRepository: {} as IUserRepository,
    UserQueryOptions: {} as UserQueryOptions,
    UserQueryResult: {} as UserQueryResult
  }
} as const;