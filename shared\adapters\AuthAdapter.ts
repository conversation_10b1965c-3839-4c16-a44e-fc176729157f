/**
 * 認證適配器 - 漸進式遷移到統一認證架構
 * 
 * @description 實現適配器模式，讓舊程式碼能夠逐步遷移到新的統一認證架構，
 * 同時保持向後相容性和提供漸進式升級路徑
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

import { TokenManager } from '../services/TokenManager';
import { TokenValidator } from '../services/TokenValidator';
import { AuthErrorCode, ErrorMessages, getErrorMessage, createErrorResponse } from '../constants/error-codes';
import type { TokenValidationResult, MemberProfile } from '../interfaces/IAuthService';
import type { Request, Response, NextFunction } from 'express';

/**
 * 功能開關介面
 * 用於控制適配器的行為和功能開關
 */
export interface FeatureFlags {
  /** 是否啟用新的 Token 驗證邏輯 */
  enableNewTokenValidation: boolean;
  /** 是否啟用新的 Token 管理 */
  enableNewTokenManager: boolean;
  /** 是否記錄遷移日誌 */
  enableMigrationLogging: boolean;
  /** 是否進行效能比較 */
  enablePerformanceComparison: boolean;
  /** 發生錯誤時是否回滾到舊邏輯 */
  enableFallbackOnError: boolean;
}

/**
 * 預設功能開關配置
 */
const DEFAULT_FEATURE_FLAGS: FeatureFlags = {
  enableNewTokenValidation: true,
  enableNewTokenManager: true,
  enableMigrationLogging: process.env.NODE_ENV === 'development',
  enablePerformanceComparison: process.env.NODE_ENV === 'development',
  enableFallbackOnError: true
};

/**
 * 適配器結果介面
 */
export interface AdapterResult {
  success: boolean;
  data?: any;
  error?: string;
  source: 'new' | 'legacy' | 'hybrid';
  performanceMs?: number;
}

/**
 * 認證適配器核心類別
 * 
 * 提供前端和後端的適配器功能，支援漸進式遷移和功能開關
 */
export class AuthAdapter {
  private static featureFlags: FeatureFlags = DEFAULT_FEATURE_FLAGS;
  private static migrationStats = {
    totalRequests: 0,
    newArchitectureRequests: 0,
    legacyRequests: 0,
    errorCount: 0,
    fallbackCount: 0
  };

  /**
   * 設定功能開關
   * 
   * @param flags 功能開關設定
   * 
   * @example
   * ```typescript
   * // 啟用所有新功能
   * AuthAdapter.setFeatureFlags({
   *   enableNewTokenValidation: true,
   *   enableNewTokenManager: true,
   *   enableMigrationLogging: true,
   *   enablePerformanceComparison: false,
   *   enableFallbackOnError: true
   * });
   * ```
   */
  static setFeatureFlags(flags: Partial<FeatureFlags>): void {
    this.featureFlags = { ...this.featureFlags, ...flags };
    this.log(`功能開關已更新:`, this.featureFlags);
  }

  /**
   * 取得當前功能開關狀態
   */
  static getFeatureFlags(): FeatureFlags {
    return { ...this.featureFlags };
  }

  /**
   * 取得遷移統計資料
   */
  static getMigrationStats() {
    return { ...this.migrationStats };
  }

  /**
   * 重設遷移統計資料
   */
  static resetMigrationStats(): void {
    this.migrationStats = {
      totalRequests: 0,
      newArchitectureRequests: 0,
      legacyRequests: 0,
      errorCount: 0,
      fallbackCount: 0
    };
  }

  /**
   * 前端適配器 - 包裝現有的 authService
   * 
   * @param authService 現有的前端 authService 實例
   * @returns 包裝後的 authService
   * 
   * @example
   * ```typescript
   * import { authService } from '@/services/authService';
   * import { AuthAdapter } from '@/shared/adapters/AuthAdapter';
   * 
   * // 包裝現有服務
   * const adaptedAuthService = AuthAdapter.adaptForFrontend(authService);
   * 
   * // 使用包裝後的服務（API 保持不變）
   * const result = await adaptedAuthService.validateToken('user-token-123');
   * ```
   */
  static adaptForFrontend(authService: any): any {
    this.log('開始適配前端 authService');

    // 保存原始方法
    const originalValidateToken = authService.validateToken?.bind(authService);
    const originalGetToken = authService.getToken?.bind(authService);
    const originalSetToken = authService.setToken?.bind(authService);
    const originalSetUser = authService.setUser?.bind(authService);
    const originalClearAuth = authService.clearAuth?.bind(authService);

    return {
      ...authService,

      /**
       * 適配後的 Token 獲取方法
       * 優先使用新的 TokenManager，失敗時回滾到舊方法
       */
      getToken(): string | null {
        this.migrationStats.totalRequests++;

        try {
          if (this.featureFlags.enableNewTokenManager) {
            const startTime = performance.now();
            const newResult = TokenManager.getToken();
            const endTime = performance.now();

            if (this.featureFlags.enablePerformanceComparison && originalGetToken) {
              const legacyStartTime = performance.now();
              const legacyResult = originalGetToken();
              const legacyEndTime = performance.now();

              this.log('Token 獲取效能比較:', {
                new: { result: newResult, time: endTime - startTime },
                legacy: { result: legacyResult, time: legacyEndTime - legacyStartTime }
              });
            }

            this.migrationStats.newArchitectureRequests++;
            this.log('使用新的 TokenManager 獲取 Token:', newResult ? '成功' : '未找到');
            return newResult;
          }
        } catch (error) {
          this.migrationStats.errorCount++;
          this.log('新 TokenManager 獲取失敗，回滾到舊方法:', error);
          
          if (this.featureFlags.enableFallbackOnError && originalGetToken) {
            this.migrationStats.fallbackCount++;
            return originalGetToken();
          }
        }

        // 回滾到舊方法
        if (originalGetToken) {
          this.migrationStats.legacyRequests++;
          return originalGetToken();
        }

        return null;
      },

      /**
       * 適配後的 Token 設定方法
       * 優先使用新的 TokenManager，同時更新舊存儲
       */
      setToken(token: string): void {
        try {
          if (this.featureFlags.enableNewTokenManager) {
            TokenManager.setToken(token, { environment: 'frontend' });
            this.log('使用新的 TokenManager 設定 Token');
          }
          
          // 同時設定到舊存儲以保持相容性
          if (originalSetToken) {
            originalSetToken(token);
          }
        } catch (error) {
          this.log('Token 設定過程中發生錯誤:', error);
          
          // 回滾到僅使用舊方法
          if (originalSetToken) {
            originalSetToken(token);
          }
        }
      },

      /**
       * 適配後的 Token 驗證方法
       * 使用新的 TokenValidator，失敗時回滾到舊驗證邏輯
       */
      async validateToken(token: string): Promise<any> {
        this.migrationStats.totalRequests++;
        const startTime = performance.now();

        try {
          if (this.featureFlags.enableNewTokenValidation) {
            // 使用新的 TokenValidator
            const validationResult = await TokenValidator.validate(token);
            const endTime = performance.now();

            if (validationResult.valid && validationResult.user) {
              // 轉換新格式到前端預期格式
              const frontendUser = this.convertToFrontendUser(validationResult.user);
              
              // 更新前端狀態
              if (originalSetToken) originalSetToken(token);
              if (originalSetUser) originalSetUser(frontendUser);

              this.migrationStats.newArchitectureRequests++;
              this.log('新 TokenValidator 驗證成功:', frontendUser.name);

              return {
                success: true,
                valid: true,
                user: frontendUser,
                message: validationResult.message || '驗證成功',
                source: 'new-architecture',
                performanceMs: endTime - startTime
              };
            } else {
              // 新驗證失敗，但可能是正當的失敗（如 Token 過期）
              this.log('新 TokenValidator 驗證失敗:', validationResult.message);
              return {
                success: false,
                valid: false,
                message: validationResult.message,
                errorCode: validationResult.errorCode,
                source: 'new-architecture',
                performanceMs: endTime - startTime
              };
            }
          }
        } catch (error) {
          this.migrationStats.errorCount++;
          this.log('新 TokenValidator 執行錯誤，嘗試回滾:', error);

          if (this.featureFlags.enableFallbackOnError && originalValidateToken) {
            this.migrationStats.fallbackCount++;
            const legacyResult = await originalValidateToken(token);
            this.log('回滾到舊驗證邏輯，結果:', legacyResult.success ? '成功' : '失敗');
            return { ...legacyResult, source: 'legacy-fallback' };
          }
        }

        // 如果新驗證被停用或失敗，使用舊邏輯
        if (originalValidateToken) {
          this.migrationStats.legacyRequests++;
          const legacyResult = await originalValidateToken(token);
          return { ...legacyResult, source: 'legacy' };
        }

        // 最後的回退
        return {
          success: false,
          valid: false,
          message: '無可用的驗證方法',
          source: 'error'
        };
      },

      /**
       * 適配後的清除認證方法
       * 同時清除新舊兩種存儲
       */
      clearAuth(): void {
        try {
          if (this.featureFlags.enableNewTokenManager) {
            TokenManager.removeToken();
            this.log('使用新的 TokenManager 清除 Token');
          }
          
          if (originalClearAuth) {
            originalClearAuth();
          }
        } catch (error) {
          this.log('清除認證過程中發生錯誤:', error);
          
          // 確保舊方法至少被呼叫
          if (originalClearAuth) {
            originalClearAuth();
          }
        }
      },

      // 新增的適配器專用方法
      /**
       * 檢查適配器狀態
       */
      getAdapterStatus() {
        return {
          featureFlags: this.featureFlags,
          migrationStats: this.migrationStats,
          hasOriginalMethods: {
            validateToken: !!originalValidateToken,
            getToken: !!originalGetToken,
            setToken: !!originalSetToken,
            clearAuth: !!originalClearAuth
          }
        };
      },

      /**
       * 強制使用新架構進行驗證
       */
      async forceNewValidation(token: string): Promise<TokenValidationResult> {
        this.log('強制使用新架構進行驗證');
        return await TokenValidator.validate(token);
      }
    };
  }

  /**
   * 後端適配器 - 包裝現有的中介層邏輯
   * 
   * @param options 適配選項
   * @returns Express 中介層函數
   * 
   * @example
   * ```typescript
   * import { AuthAdapter } from './shared/adapters/AuthAdapter';
   * 
   * // 替換現有的認證中介層
   * const authMiddleware = AuthAdapter.adaptForBackend();
   * 
   * // 在 Express 路由中使用
   * app.use('/api/protected', authMiddleware, (req, res) => {
   *   // req.user 和 req.userToken 已經設定
   *   res.json({ user: req.user });
   * });
   * ```
   */
  static adaptForBackend(options: {
    enableLegacySupport?: boolean;
    customErrorHandler?: (error: any, req: Request, res: Response) => void;
  } = {}): (req: Request, res: Response, next: NextFunction) => Promise<void> {
    
    const { enableLegacySupport = true, customErrorHandler } = options;

    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      this.migrationStats.totalRequests++;
      const startTime = performance.now();

      try {
        // 使用新的 TokenManager 獲取 token
        let token: string | null = null;
        
        if (this.featureFlags.enableNewTokenManager) {
          token = TokenManager.getToken(req);
          this.log('使用新 TokenManager 從請求獲取 Token:', token ? '成功' : '未找到');
        } else if (enableLegacySupport) {
          // 回滾到舊的獲取邏輯
          token = this.legacyGetTokenFromRequest(req);
          this.log('使用舊邏輯從請求獲取 Token:', token ? '成功' : '未找到');
        }

        if (!token) {
          const errorResponse = createErrorResponse(AuthErrorCode.TOKEN_NOT_FOUND);
          return res.status(401).json(errorResponse);
        }

        // 使用新的 TokenValidator 驗證
        let validationResult: TokenValidationResult;
        
        if (this.featureFlags.enableNewTokenValidation) {
          validationResult = await TokenValidator.validate(token);
          this.migrationStats.newArchitectureRequests++;
        } else {
          // 使用舊驗證邏輯（需要從現有中介層載入）
          validationResult = await this.legacyValidateToken(token);
          this.migrationStats.legacyRequests++;
        }

        const endTime = performance.now();

        if (validationResult.valid && validationResult.user) {
          // 設定 Express request 物件
          req.user = this.convertToExpressUser(validationResult.user);
          req.userToken = token;

          this.log(`後端驗證成功: ${validationResult.user.name} (${validationResult.user.roleType})`, {
            source: validationResult.tokenSource || 'new',
            performanceMs: endTime - startTime
          });

          next();
        } else {
          // 驗證失敗
          this.log('後端驗證失敗:', validationResult.message);
          
          const statusCode = this.getHttpStatusFromErrorCode(validationResult.errorCode);
          const errorResponse = createErrorResponse(
            validationResult.errorCode || AuthErrorCode.TOKEN_INVALID
          );
          
          res.status(statusCode).json(errorResponse);
        }

      } catch (error) {
        this.migrationStats.errorCount++;
        this.log('後端適配器發生錯誤:', error);

        if (customErrorHandler) {
          customErrorHandler(error, req, res);
        } else {
          const errorResponse = createErrorResponse(AuthErrorCode.TOKEN_INVALID);
          res.status(500).json(errorResponse);
        }
      }
    };
  },

  /**
   * 建立混合模式中介層
   * 同時支援新舊兩種驗證方式，用於 A/B 測試或漸進式遷移
   */
  static createHybridMiddleware(options: {
    newArchitectureRatio?: number; // 0-1 之間，表示使用新架構的比例
    enableComparison?: boolean; // 是否同時執行兩種驗證並比較結果
  } = {}): (req: Request, res: Response, next: NextFunction) => Promise<void> {
    
    const { newArchitectureRatio = 0.5, enableComparison = false } = options;

    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      const shouldUseNewArchitecture = Math.random() < newArchitectureRatio;
      const startTime = performance.now();

      try {
        let token = TokenManager.getToken(req);
        if (!token) {
          return res.status(401).json(createErrorResponse(AuthErrorCode.TOKEN_NOT_FOUND));
        }

        if (enableComparison) {
          // 同時執行新舊驗證並比較結果
          const [newResult, legacyResult] = await Promise.allSettled([
            TokenValidator.validate(token),
            this.legacyValidateToken(token)
          ]);

          this.log('混合模式比較結果:', {
            new: newResult.status === 'fulfilled' ? newResult.value : newResult.reason,
            legacy: legacyResult.status === 'fulfilled' ? legacyResult.value : legacyResult.reason
          });

          // 優先使用新結果
          if (newResult.status === 'fulfilled' && newResult.value.valid) {
            req.user = this.convertToExpressUser(newResult.value.user!);
            req.userToken = token;
            return next();
          } else if (legacyResult.status === 'fulfilled' && legacyResult.value.valid) {
            req.user = this.convertToExpressUser(legacyResult.value.user!);
            req.userToken = token;
            return next();
          }
        } else {
          // 按比例選擇使用新舊架構
          const result = shouldUseNewArchitecture 
            ? await TokenValidator.validate(token)
            : await this.legacyValidateToken(token);

          if (result.valid && result.user) {
            req.user = this.convertToExpressUser(result.user);
            req.userToken = token;
            return next();
          }
        }

        // 驗證失敗
        res.status(401).json(createErrorResponse(AuthErrorCode.TOKEN_INVALID));

      } catch (error) {
        this.log('混合模式中介層發生錯誤:', error);
        res.status(500).json(createErrorResponse(AuthErrorCode.TOKEN_INVALID));
      }
    };
  },

  /**
   * 遷移輔助工具 - 批次遷移檢查
   * 
   * @param tokens Token 陣列
   * @returns 遷移檢查報告
   */
  static async performMigrationCheck(tokens: string[]): Promise<{
    totalTokens: number;
    validInNew: number;
    validInLegacy: number;
    onlyValidInNew: number;
    onlyValidInLegacy: number;
    invalidInBoth: number;
    discrepancies: Array<{
      token: string;
      newResult: any;
      legacyResult: any;
    }>;
  }> {
    this.log('開始執行遷移檢查，檢查', tokens.length, '個 Token');

    const report = {
      totalTokens: tokens.length,
      validInNew: 0,
      validInLegacy: 0,
      onlyValidInNew: 0,
      onlyValidInLegacy: 0,
      invalidInBoth: 0,
      discrepancies: [] as any[]
    };

    for (const token of tokens) {
      try {
        const [newResult, legacyResult] = await Promise.allSettled([
          TokenValidator.validate(token),
          this.legacyValidateToken(token)
        ]);

        const newValid = newResult.status === 'fulfilled' && newResult.value.valid;
        const legacyValid = legacyResult.status === 'fulfilled' && legacyResult.value.valid;

        if (newValid) report.validInNew++;
        if (legacyValid) report.validInLegacy++;

        if (newValid && !legacyValid) {
          report.onlyValidInNew++;
        } else if (!newValid && legacyValid) {
          report.onlyValidInLegacy++;
        } else if (!newValid && !legacyValid) {
          report.invalidInBoth++;
        }

        // 檢查結果差異
        if (newValid !== legacyValid) {
          report.discrepancies.push({
            token: token.substring(0, 8) + '...',
            newResult: newResult.status === 'fulfilled' ? newResult.value : newResult.reason,
            legacyResult: legacyResult.status === 'fulfilled' ? legacyResult.value : legacyResult.reason
          });
        }

      } catch (error) {
        this.log('遷移檢查單個 Token 時發生錯誤:', error);
        report.invalidInBoth++;
      }
    }

    this.log('遷移檢查完成:', report);
    return report;
  },

  // === 私有輔助方法 ===

  /**
   * 轉換新架構的用戶物件為前端預期格式
   */
  private static convertToFrontendUser(user: MemberProfile): any {
    return {
      id: user.id || user.accountId,
      name: user.nickName || user.name || 'Unknown User',
      email: user.email || '',
      role: this.convertRoleTypeToLegacy(user.roleType),
      roleType: user.roleType,
      permissions: user.permissions || [],
      account: user.account,
      phone: user.phone,
      organization: user.school?.name || '',
      address: user.school?.address || '',
      status: user.isActive ? 'active' : 'inactive',
      isActive: user.isActive
    };
  },

  /**
   * 轉換新架構的用戶物件為 Express Request.user 格式
   */
  private static convertToExpressUser(user: MemberProfile): any {
    return {
      id: user.id || user.accountId,
      account: user.account,
      accountId: user.id || user.accountId,
      accountSid: user.id || user.accountId,
      nickName: user.nickName || user.name,
      email: user.email,
      roleType: user.roleType,
      permissions: user.permissions || [],
      permissionGroups: user.permissionGroups || [],
      isActive: user.isActive ?? true,
      school: user.school
    };
  },

  /**
   * 轉換角色類型到舊格式
   */
  private static convertRoleTypeToLegacy(roleType: string): string {
    const mapping: Record<string, string> = {
      'School': 'school',
      'Government': 'epa',
      'Tutor': 'tutor'
    };
    return mapping[roleType] || roleType.toLowerCase();
  },

  /**
   * 舊版 Token 獲取邏輯（回滾用）
   */
  private static legacyGetTokenFromRequest(req: Request): string | null {
    let token = req.headers['x-user-token'] as string;
    
    if (!token) {
      const authHeader = req.headers['authorization'] as string;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }
    
    return token || null;
  },

  /**
   * 舊版 Token 驗證邏輯（回滾用）
   */
  private static async legacyValidateToken(token: string): Promise<TokenValidationResult> {
    try {
      // 這裡應該調用現有的舊驗證邏輯
      // 由於舊邏輯分散在不同檔案中，這裡提供一個基本實現
      const { validateToken: legacyValidate } = await import('../../api/utils/auth-helpers');
      const result = await legacyValidate(token);
      
      return {
        valid: result.valid,
        user: result.user,
        message: result.message || 'Legacy validation'
      };
    } catch (error) {
      return {
        valid: false,
        message: 'Legacy validation failed: ' + (error as Error).message
      };
    }
  },

  /**
   * 根據錯誤碼取得對應的 HTTP 狀態碼
   */
  private static getHttpStatusFromErrorCode(errorCode?: AuthErrorCode): number {
    if (!errorCode) return 401;
    
    const statusMapping: Record<AuthErrorCode, number> = {
      [AuthErrorCode.TOKEN_EXPIRED]: 401,
      [AuthErrorCode.TOKEN_INVALID]: 401,
      [AuthErrorCode.TOKEN_NOT_FOUND]: 401,
      [AuthErrorCode.ROLE_NOT_ALLOWED]: 403,
      [AuthErrorCode.ACCOUNT_DISABLED]: 403,
      [AuthErrorCode.UNAUTHORIZED]: 401
    };
    
    return statusMapping[errorCode] || 401;
  },

  /**
   * 統一日誌記錄
   */
  private static log(message: string, ...args: any[]): void {
    if (this.featureFlags.enableMigrationLogging) {
      console.log(`🔄 [AuthAdapter] ${message}`, ...args);
    }
  }
}

/**
 * 便捷的適配器建立函數
 * 
 * @example
 * ```typescript
 * // 快速建立前端適配器
 * const adaptedService = createFrontendAdapter(authService);
 * 
 * // 快速建立後端適配器
 * const authMiddleware = createBackendAdapter();
 * ```
 */
export const createFrontendAdapter = (authService: any) => {
  return AuthAdapter.adaptForFrontend(authService);
};

export const createBackendAdapter = (options?: Parameters<typeof AuthAdapter.adaptForBackend>[0]) => {
  return AuthAdapter.adaptForBackend(options);
};

export const createHybridMiddleware = (options?: Parameters<typeof AuthAdapter.createHybridMiddleware>[0]) => {
  return AuthAdapter.createHybridMiddleware(options);
};

/**
 * 遷移監控工具
 */
export class MigrationMonitor {
  private static intervals: NodeJS.Timeout[] = [];

  /**
   * 開始監控遷移狀態
   */
  static startMonitoring(intervalMs: number = 60000): void {
    const interval = setInterval(() => {
      const stats = AuthAdapter.getMigrationStats();
      const flags = AuthAdapter.getFeatureFlags();
      
      console.log('🔍 [Migration Monitor] 遷移狀態監控:', {
        timestamp: new Date().toISOString(),
        stats,
        flags,
        newArchitecturePercentage: stats.totalRequests > 0 
          ? ((stats.newArchitectureRequests / stats.totalRequests) * 100).toFixed(2) + '%'
          : '0%',
        errorRate: stats.totalRequests > 0 
          ? ((stats.errorCount / stats.totalRequests) * 100).toFixed(2) + '%'
          : '0%'
      });
    }, intervalMs);

    this.intervals.push(interval);
  }

  /**
   * 停止監控
   */
  static stopMonitoring(): void {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
  }

  /**
   * 產生遷移報告
   */
  static generateReport(): {
    summary: string;
    recommendations: string[];
    stats: any;
    flags: any;
  } {
    const stats = AuthAdapter.getMigrationStats();
    const flags = AuthAdapter.getFeatureFlags();
    
    const totalRequests = stats.totalRequests;
    const newPercentage = totalRequests > 0 ? (stats.newArchitectureRequests / totalRequests) * 100 : 0;
    const errorRate = totalRequests > 0 ? (stats.errorCount / totalRequests) * 100 : 0;
    const fallbackRate = totalRequests > 0 ? (stats.fallbackCount / totalRequests) * 100 : 0;

    const recommendations: string[] = [];

    if (newPercentage < 50) {
      recommendations.push('考慮增加新架構的使用比例');
    }
    if (errorRate > 5) {
      recommendations.push('錯誤率偏高，建議檢查新架構的穩定性');
    }
    if (fallbackRate > 10) {
      recommendations.push('回滾率偏高，建議優化新架構的可靠性');
    }
    if (flags.enableFallbackOnError && errorRate < 1) {
      recommendations.push('錯誤率很低，可考慮關閉自動回滾功能');
    }

    return {
      summary: `總請求數: ${totalRequests}, 新架構使用率: ${newPercentage.toFixed(1)}%, 錯誤率: ${errorRate.toFixed(2)}%`,
      recommendations,
      stats,
      flags
    };
  }
}

// 預設匯出
export default AuthAdapter;

/**
 * 使用範例：
 * 
 * @example
 * ```typescript
 * // === 前端使用範例 ===
 * import { authService } from '@/services/authService';
 * import { AuthAdapter } from '@/shared/adapters/AuthAdapter';
 * 
 * // 1. 設定功能開關
 * AuthAdapter.setFeatureFlags({
 *   enableNewTokenValidation: true,
 *   enableMigrationLogging: true
 * });
 * 
 * // 2. 適配現有服務
 * const adaptedAuthService = AuthAdapter.adaptForFrontend(authService);
 * 
 * // 3. 使用適配後的服務（API 完全相同）
 * const loginResult = await adaptedAuthService.validateToken('user-token');
 * if (loginResult.success) {
 *   console.log('登入成功:', loginResult.user.name);
 * }
 * 
 * // 4. 檢查適配器狀態
 * const status = adaptedAuthService.getAdapterStatus();
 * console.log('適配器狀態:', status);
 * 
 * 
 * // === 後端使用範例 ===
 * import express from 'express';
 * import { AuthAdapter } from './shared/adapters/AuthAdapter';
 * 
 * const app = express();
 * 
 * // 1. 使用適配器中介層
 * const authMiddleware = AuthAdapter.adaptForBackend({
 *   enableLegacySupport: true,
 *   customErrorHandler: (error, req, res) => {
 *     console.error('認證錯誤:', error);
 *     res.status(500).json({ error: 'Authentication failed' });
 *   }
 * });
 * 
 * // 2. 應用到路由
 * app.use('/api/protected', authMiddleware, (req, res) => {
 *   res.json({
 *     message: '認證成功',
 *     user: req.user,
 *     token: req.userToken
 *   });
 * });
 * 
 * 
 * // === 混合模式使用範例 ===
 * import { AuthAdapter, MigrationMonitor } from './shared/adapters/AuthAdapter';
 * 
 * // 1. 建立混合模式中介層（70% 使用新架構）
 * const hybridMiddleware = AuthAdapter.createHybridMiddleware({
 *   newArchitectureRatio: 0.7,
 *   enableComparison: true
 * });
 * 
 * // 2. 開始監控遷移狀態
 * MigrationMonitor.startMonitoring(30000); // 每 30 秒檢查一次
 * 
 * // 3. 定期產生報告
 * setInterval(() => {
 *   const report = MigrationMonitor.generateReport();
 *   console.log('遷移報告:', report);
 * }, 300000); // 每 5 分鐘一次
 * 
 * 
 * // === 遷移檢查範例 ===
 * import { AuthAdapter } from './shared/adapters/AuthAdapter';
 * 
 * async function performMigrationValidation() {
 *   const testTokens = [
 *     'valid_test_token_123456789',
 *     'eco_campus_user_778_token',
 *     // ... 更多測試 Token
 *   ];
 * 
 *   const report = await AuthAdapter.performMigrationCheck(testTokens);
 *   console.log('遷移檢查報告:', report);
 * 
 *   if (report.discrepancies.length > 0) {
 *     console.warn('發現驗證差異，需要進一步調查:', report.discrepancies);
 *   }
 * }
 * 
 * performMigrationValidation();
 * ```
 */