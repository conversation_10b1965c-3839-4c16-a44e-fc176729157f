// ========== 個人資料服務 - 業務邏輯和資料庫查詢 ==========

import { executeQuerySingle, executeUpdate, executeQuery } from "../config/database-mssql.js";
import {
  FullProfile,
  MemberQueryResult,
  ContactQueryResult,
  StatisticsQueryResult,
  UpdateMemberProfileRequest,
  UpdateSchoolBasicRequest,
  UpdatePrincipalRequest,
  UpdateContactsRequest,
  UpdateStatisticsRequest,
  MemberProfile,
  SchoolInfo,
  PrincipalInfo,
  ContactInfo,
  SchoolStatistics,
} from "../models/profile.js";
import {
  determineMemberRole,
  isSchoolMember,
  LOCALE_CODE,
  DATA_STATUS,
  PROFILE_ERROR_MESSAGES,
  PROFILE_SUCCESS_MESSAGES,
  validateEmail,
  validatePhone,
  validateContactsCount,
  validateStatisticsValue,
} from "../constants/profile.js";
import { APILogger } from "../utils/logger.js";

export class ProfileService {
  // 根據 Token 獲取完整個人資料
  static async getFullProfileByToken(token: string): Promise<FullProfile | null> {
    try {
      // 查詢會員基本資料
      const memberQuery = `
        SELECT 
          a.AccountId,
          a.Username as account,
          a.email,
          a.Telephone as tel,
          a.phone,
          a.address,
          a.CountyId as account_county_id,
          a.IsSchoolPartner as is_school_partner,
          a.IsEpaUser as is_epa_user,
          a.IsGuidanceTeam as is_guidance_team,
          mp_zh.MemberName as member_cname_zh,
          mp_zh.MemberEmail as member_email,
          mp_zh.MemberTelephone as member_tel,
          mp_zh.MemberPhone as member_phone,
          mp_zh.MemberAddress as member_address,
          mp_zh.MemberUrl as member_url,
          mp_zh.JobName as job_cname,
          mp_zh.PlaceName as place_cname,
          mp_zh.MemberRole as member_role,
          mp_zh.MemberIntroduction as member_Introduction,
          mp_zh.MemberExchange as member_exchange,
          mp_en.MemberName as member_cname_en,
          ct_zh.Name as county_name_zh,
          ct_en.Name as county_name_en,
          sct_zh.Name as school_county_name_zh,
          sct_en.Name as school_county_name_en,
          s.Id as school_id,
          s.SchoolCode as school_code,
          s.Phone as school_phone,
          s.MobilePhone as school_mobile,
          s.Email as school_email,
          s.CountyId as school_county_id,
          s.DistrictId as school_district_id,
          sc_zh.Name as school_name,
          sc_zh.Address as school_address,
          sc_zh.DepartmentName as school_department,
          sc_zh.JobTitle as school_job_title,
          sc_zh.Introduction as school_introduction,
          sc_zh.WebsiteUrl as school_website,
          sc_en.Name as school_name_en,
          sc_en.Address as school_address_en,
          sp.PrincipalName as principal_cname,
          sp.PrincipalPhone as principal_tel,
          sp.PrincipalMobile as principal_phone,
          sp.PrincipalEmail as principal_email
        FROM Accounts a
        INNER JOIN UserToken ut ON a.AccountId = ut.AccountSid
        LEFT JOIN MemberProfiles mp_zh ON a.AccountId = mp_zh.AccountId AND mp_zh.LocaleCode = @zhLocale
        LEFT JOIN MemberProfiles mp_en ON a.AccountId = mp_en.AccountId AND mp_en.LocaleCode = @enLocale
        LEFT JOIN CountyTranslations ct_zh ON a.CountyId = ct_zh.CountyId AND ct_zh.LocaleCode = @zhLocale
        LEFT JOIN CountyTranslations ct_en ON a.CountyId = ct_en.CountyId AND ct_en.LocaleCode = @enLocale
        LEFT JOIN Schools s ON a.SchoolId = s.Id
        LEFT JOIN CountyTranslations sct_zh ON s.CountyId = sct_zh.CountyId AND sct_zh.LocaleCode = @zhLocale
        LEFT JOIN CountyTranslations sct_en ON s.CountyId = sct_en.CountyId AND sct_en.LocaleCode = @enLocale
        LEFT JOIN SchoolContents sc_zh ON s.Id = sc_zh.SchoolId AND sc_zh.LocaleCode = @zhLocale
        LEFT JOIN SchoolContents sc_en ON s.Id = sc_en.SchoolId AND sc_en.LocaleCode = @enLocale
        LEFT JOIN SchoolPrincipals sp ON s.Id = sp.SchoolId AND sp.Status = @activeStatus
        WHERE ut.Token = CAST(@token AS uniqueidentifier)
          AND ut.Status = @activeStatus
          AND a.Status = @activeStatus
          AND (ut.ExpireDate IS NULL OR ut.ExpireDate > GETDATE())
      `;

      const member = await executeQuerySingle<MemberQueryResult>(memberQuery, {
        token,
        zhLocale: LOCALE_CODE.ZH_TW,
        enLocale: LOCALE_CODE.EN,
        activeStatus: DATA_STATUS.ACTIVE,
      });

      if (!member) {
        return null;
      }

      // 構建基本資料
      const profile = this.buildMemberProfile(member);
      const roleType = determineMemberRole(member.is_school_partner, member.is_epa_user, member.is_guidance_team);

      // 如果是學校身份，獲取額外的學校相關資料
      if (isSchoolMember(roleType) && member.school_id) {
        const [contacts, statistics] = await Promise.all([this.getSchoolContacts(member.school_id), this.getSchoolStatistics(member.school_id)]);

        profile.school = this.buildSchoolInfo(member);
        profile.principal = this.buildPrincipalInfo(member);
        profile.contacts = contacts;
        profile.statistics = statistics;
      }

      return profile;
    } catch (error) {
      console.error("獲取個人資料失敗:", error);
      throw new Error(PROFILE_ERROR_MESSAGES.MEMBER_NOT_FOUND);
    }
  }

  // 更新會員基本資料
  static async updateMemberProfile(token: string, updateData: UpdateMemberProfileRequest): Promise<{ success: boolean; message: string }> {
    try {
      // 獲取帳號資訊
      const accountInfo = await this.getAccountInfoByToken(token);
      if (!accountInfo) {
        return { success: false, message: PROFILE_ERROR_MESSAGES.MEMBER_NOT_FOUND };
      }

      // 構建更新欄位
      const updateFields: string[] = [];
      const updateValues: Record<string, any> = { AccountId: accountInfo.AccountId };

      const fieldMapping = {
        jobTitle: "JobName",
        memberName: "MemberName",
        memberNameEn: "MemberName", // 英文版本需要分別處理
        officePhone: "MemberTelephone",
        mobilePhone: "MemberPhone",
        email: "MemberEmail",
      };

      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined && fieldMapping[key as keyof typeof fieldMapping]) {
          const dbField = fieldMapping[key as keyof typeof fieldMapping];
          updateFields.push(`${dbField} = @${key}`);
          updateValues[key] = value;
        }
      });

      if (updateFields.length > 0) {
        // 更新中文資料
        const updateQuery = `
          UPDATE MemberProfiles 
          SET ${updateFields.join(", ")}
          WHERE AccountId = @AccountId AND LocaleCode = @locale
        `;

        await executeUpdate(updateQuery, {
          ...updateValues,
          locale: LOCALE_CODE.ZH_TW,
        });

        // 如果有英文姓名，也更新英文版本
        if (updateData.memberNameEn) {
          await executeUpdate(updateQuery.replace("MemberName", "MemberName"), {
            AccountId: accountInfo.AccountId,
            memberNameEn: updateData.memberNameEn,
            locale: LOCALE_CODE.EN,
          });
        }
      }

      return { success: true, message: PROFILE_SUCCESS_MESSAGES.PROFILE_UPDATED };
    } catch (error) {
      console.error("更新會員資料失敗:", error);
      return { success: false, message: PROFILE_ERROR_MESSAGES.UPDATE_FAILED };
    }
  }

  // 更新學校基本資料
  static async updateSchoolBasicInfo(token: string, updateData: UpdateSchoolBasicRequest): Promise<{ success: boolean; message: string }> {
    try {
      const accountInfo = await this.getAccountInfoByToken(token);
      if (!accountInfo || !accountInfo.schoolId) {
        return { success: false, message: PROFILE_ERROR_MESSAGES.SCHOOL_NOT_FOUND };
      }

      // 更新 Schools 表
      const schoolFields: string[] = [];
      const schoolValues: Record<string, any> = { schoolId: accountInfo.schoolId };

      if (updateData.countyId !== undefined) {
        schoolFields.push("CountyId = @countyId");
        schoolValues.countyId = updateData.countyId;
      }

      if (updateData.districtId !== undefined) {
        schoolFields.push("DistrictId = @districtId");
        schoolValues.districtId = updateData.districtId;
      }

      if (updateData.phone !== undefined) {
        schoolFields.push("Phone = @phone");
        schoolValues.phone = updateData.phone;
      }

      if (updateData.mobile !== undefined) {
        schoolFields.push("MobilePhone = @mobile");
        schoolValues.mobile = updateData.mobile;
      }

      if (updateData.email !== undefined) {
        schoolFields.push("Email = @email");
        schoolValues.email = updateData.email;
      }

      if (schoolFields.length > 0) {
        const schoolUpdateQuery = `
          UPDATE Schools 
          SET ${schoolFields.join(", ")}
          WHERE Id = @schoolId
        `;
        await executeUpdate(schoolUpdateQuery, schoolValues);
      }

      // 更新 SchoolContents 表（多語言）
      const contentUpdates = [
        { locale: LOCALE_CODE.ZH_TW, name: updateData.schoolName, address: updateData.address, website: updateData.website },
        { locale: LOCALE_CODE.EN, name: updateData.schoolNameEn, address: updateData.addressEn, website: updateData.website },
      ];

      for (const contentUpdate of contentUpdates) {
        const contentFields: string[] = [];
        const contentValues: Record<string, any> = { schoolId: accountInfo.schoolId, locale: contentUpdate.locale };

        if (contentUpdate.name !== undefined) {
          contentFields.push("Name = @name");
          contentValues.name = contentUpdate.name;
        }

        if (contentUpdate.address !== undefined) {
          contentFields.push("Address = @address");
          contentValues.address = contentUpdate.address;
        }

        if (contentUpdate.website !== undefined) {
          contentFields.push("WebsiteUrl = @website");
          contentValues.website = contentUpdate.website;
        }

        if (contentFields.length > 0) {
          const contentUpdateQuery = `
            UPDATE SchoolContents 
            SET ${contentFields.join(", ")}
            WHERE SchoolId = @schoolId AND LocaleCode = @locale
          `;
          await executeUpdate(contentUpdateQuery, contentValues);
        }
      }

      return { success: true, message: PROFILE_SUCCESS_MESSAGES.SCHOOL_BASIC_UPDATED };
    } catch (error) {
      console.error("更新學校基本資料失敗:", error);
      return { success: false, message: PROFILE_ERROR_MESSAGES.UPDATE_FAILED };
    }
  }

  // 更新校長資料
  static async updatePrincipalInfo(token: string, updateData: UpdatePrincipalRequest): Promise<{ success: boolean; message: string }> {
    try {
      const accountInfo = await this.getAccountInfoByToken(token);
      if (!accountInfo || !accountInfo.schoolId) {
        return { success: false, message: PROFILE_ERROR_MESSAGES.SCHOOL_NOT_FOUND };
      }

      const updateFields: string[] = [];
      const updateValues: Record<string, any> = { schoolId: accountInfo.schoolId };

      const fieldMapping = {
        principalName: "PrincipalName",
        principalPhone: "PrincipalPhone",
        principalMobile: "PrincipalMobile",
        principalEmail: "PrincipalEmail",
      };

      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined && fieldMapping[key as keyof typeof fieldMapping]) {
          const dbField = fieldMapping[key as keyof typeof fieldMapping];
          updateFields.push(`${dbField} = @${key}`);
          updateValues[key] = value;
        }
      });

      if (updateFields.length > 0) {
        const updateQuery = `
          UPDATE SchoolPrincipals 
          SET ${updateFields.join(", ")}
          WHERE SchoolId = @schoolId AND Status = @activeStatus
        `;

        await executeUpdate(updateQuery, {
          ...updateValues,
          activeStatus: DATA_STATUS.ACTIVE,
        });
      }

      return { success: true, message: PROFILE_SUCCESS_MESSAGES.PRINCIPAL_UPDATED };
    } catch (error) {
      console.error("更新校長資料失敗:", error);
      return { success: false, message: PROFILE_ERROR_MESSAGES.UPDATE_FAILED };
    }
  }

  // 更新聯絡人資料
  static async updateContacts(token: string, updateData: UpdateContactsRequest): Promise<{ success: boolean; message: string }> {
    try {
      const accountInfo = await this.getAccountInfoByToken(token);
      if (!accountInfo || !accountInfo.schoolId) {
        return { success: false, message: PROFILE_ERROR_MESSAGES.SCHOOL_NOT_FOUND };
      }

      if (!validateContactsCount(updateData.contacts.length)) {
        return { success: false, message: PROFILE_ERROR_MESSAGES.CONTACT_LIMIT_EXCEEDED };
      }

      // TODO: 實現聯絡人更新邏輯
      // 這裡需要實現複雜的聯絡人同步邏輯，包括新增、更新和刪除

      return { success: true, message: PROFILE_SUCCESS_MESSAGES.CONTACTS_UPDATED };
    } catch (error) {
      console.error("更新聯絡人失敗:", error);
      return { success: false, message: PROFILE_ERROR_MESSAGES.UPDATE_FAILED };
    }
  }

  // 更新學校統計資料
  static async updateStatistics(token: string, updateData: UpdateStatisticsRequest): Promise<{ success: boolean; message: string }> {
    try {
      const accountInfo = await this.getAccountInfoByToken(token);
      if (!accountInfo || !accountInfo.schoolId) {
        return { success: false, message: PROFILE_ERROR_MESSAGES.SCHOOL_NOT_FOUND };
      }

      // 驗證統計資料
      const invalidFields = Object.entries(updateData).filter(([_, value]) => value !== undefined && !validateStatisticsValue(value as number));

      if (invalidFields.length > 0) {
        return { success: false, message: PROFILE_ERROR_MESSAGES.STATISTICS_INVALID };
      }

      const updateFields: string[] = [];
      const updateValues: Record<string, any> = { schoolId: accountInfo.schoolId };

      const fieldMapping = {
        staffTotal: "StaffTotal",
        elementary1: "Elementary1",
        elementary2: "Elementary2",
        elementary3: "Elementary3",
        elementary4: "Elementary4",
        elementary5: "Elementary5",
        elementary6: "Elementary6",
        middle7: "Middle7",
        middle8: "Middle8",
        middle9: "Middle9",
        high10: "High10",
        high11: "High11",
        high12: "High12",
      };

      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined && fieldMapping[key as keyof typeof fieldMapping]) {
          const dbField = fieldMapping[key as keyof typeof fieldMapping];
          updateFields.push(`${dbField} = @${key}`);
          updateValues[key] = value;
        }
      });

      if (updateFields.length > 0) {
        updateFields.push("WriteDate = GETDATE()");

        const updateQuery = `
          UPDATE SchoolStatistics 
          SET ${updateFields.join(", ")}
          WHERE SchoolId = @schoolId AND Status = @activeStatus
        `;

        await executeUpdate(updateQuery, {
          ...updateValues,
          activeStatus: DATA_STATUS.ACTIVE,
        });
      }

      return { success: true, message: PROFILE_SUCCESS_MESSAGES.STATISTICS_UPDATED };
    } catch (error) {
      console.error("更新統計資料失敗:", error);
      return { success: false, message: PROFILE_ERROR_MESSAGES.UPDATE_FAILED };
    }
  }

  // ========== 私有輔助方法 ==========

  // 獲取帳號基本資訊
  private static async getAccountInfoByToken(token: string): Promise<{ AccountId: number; schoolId?: number } | null> {
    const query = `
      SELECT a.AccountId, a.SchoolId
      FROM Accounts a
      INNER JOIN UserToken ut ON a.AccountId = ut.AccountSid
      WHERE ut.Token = CAST(@token AS uniqueidentifier)
        AND ut.Status = @activeStatus
        AND a.Status = @activeStatus
    `;

    return await executeQuerySingle<{ AccountId: number; SchoolId?: number }>(query, {
      token,
      activeStatus: DATA_STATUS.ACTIVE,
    }).then((result) => (result ? { AccountId: result.AccountId, schoolId: result.SchoolId } : null));
  }

  // 獲取學校聯絡人
  private static async getSchoolContacts(schoolId: number): Promise<ContactInfo[]> {
    const contactQuery = `
      SELECT 
        ContactName as contact_cname,
        JobTitle as contact_job_title,
        ContactPhone as contact_tel,
        ContactMobile as contact_phone,
        ContactEmail as contact_email,
        Id as contact_sid
      FROM SchoolContacts 
      WHERE SchoolId = @schoolId AND Status = @activeStatus
      ORDER BY SortOrder
    `;

    const contacts = await executeQuery<ContactQueryResult>(contactQuery, {
      schoolId,
      activeStatus: DATA_STATUS.ACTIVE,
    });

    return contacts.map((contact) => ({
      contactId: contact.contact_sid,
      contactName: contact.contact_cname,
      jobTitle: contact.contact_job_title,
      contactTelephone: contact.contact_tel,
      contactPhone: contact.contact_phone,
      contactEmail: contact.contact_email,
    }));
  }

  // 獲取學校統計資料
  private static async getSchoolStatistics(schoolId: number): Promise<SchoolStatistics | null> {
    const statisticsQuery = `
      SELECT 
        StaffTotal as staff_total,
        Elementary1 as elementary1,
        Elementary2 as elementary2,
        Elementary3 as elementary3,
        Elementary4 as elementary4,
        Elementary5 as elementary5,
        Elementary6 as elementary6,
        Middle7 as middle7,
        Middle8 as middle8,
        Middle9 as middle9,
        High10 as hight10,
        High11 as hight11,
        High12 as hight12,
        WriteDate as write_date,
        Id as school_statistics_sid
      FROM SchoolStatistics 
      WHERE SchoolId = @schoolId AND Status = @activeStatus
    `;

    const statistics = await executeQuerySingle<StatisticsQueryResult>(statisticsQuery, {
      schoolId,
      activeStatus: DATA_STATUS.ACTIVE,
    });

    return statistics
      ? {
          schoolStatisticsId: statistics.school_statistics_sid,
          staffTotal: statistics.staff_total,
          elementary1: statistics.elementary1,
          elementary2: statistics.elementary2,
          elementary3: statistics.elementary3,
          elementary4: statistics.elementary4,
          elementary5: statistics.elementary5,
          elementary6: statistics.elementary6,
          middle7: statistics.middle7,
          middle8: statistics.middle8,
          middle9: statistics.middle9,
          high10: statistics.hight10,
          high11: statistics.hight11,
          high12: statistics.hight12,
          writeDate: statistics.write_date,
        }
      : null;
  }

  // 構建會員資料
  private static buildMemberProfile(member: MemberQueryResult): FullProfile {
    return {
      accountId: member.AccountId,
      account: member.account,
      email: member.email,
      telephone: member.tel,
      phone: member.phone,
      address: member.address,
      countyId: member.account_county_id,
      isSchoolPartner: member.is_school_partner,
      isEpaUser: member.is_epa_user,
      isGuidanceTeam: member.is_guidance_team,
      memberName: member.member_cname_zh,
      memberEmail: member.member_email,
      memberTelephone: member.member_tel,
      memberPhone: member.member_phone,
      memberAddress: member.member_address,
      memberUrl: member.member_url,
      jobTitle: member.job_cname,
      placeName: member.place_cname,
      memberRole: member.member_role,
      memberIntroduction: member.member_Introduction,
      memberExchange: member.member_exchange,
      memberNameEn: member.member_cname_en,
      countyNameZh: member.county_name_zh,
      countyNameEn: member.county_name_en,
    };
  }

  // 構建學校資料
  private static buildSchoolInfo(member: MemberQueryResult): SchoolInfo {
    return {
      schoolId: member.school_id,
      schoolCode: member.school_code,
      schoolPhone: member.school_phone,
      schoolMobile: member.school_mobile,
      schoolEmail: member.school_email,
      schoolCountyId: member.school_county_id,
      schoolDistrictId: member.school_district_id,
      schoolName: member.school_name,
      schoolAddress: member.school_address,
      schoolDepartment: member.school_department,
      schoolJobTitle: member.school_job_title,
      schoolIntroduction: member.school_introduction,
      schoolWebsite: member.school_website,
      schoolNameEn: member.school_name_en,
      schoolAddressEn: member.school_address_en,
      schoolCountyNameZh: member.school_county_name_zh,
      schoolCountyNameEn: member.school_county_name_en,
      logoPath: member.school_logo_path,
    };
  }

  // 構建校長資料
  private static buildPrincipalInfo(member: MemberQueryResult): PrincipalInfo {
    return {
      principalName: member.principal_cname,
      principalTelephone: member.principal_tel,
      principalPhone: member.principal_phone,
      principalEmail: member.principal_email,
    };
  }
}
