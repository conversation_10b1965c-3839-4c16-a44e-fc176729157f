# 🌍 React 環境配置最佳實踐指南

## 📋 **標準環境配置結構**

### 1. **檔案命名規範**

```
.env                    # ✅ 預設值 (提交到版本控制)
.env.local              # ❌ 本地覆蓋 (不提交)
.env.development        # ✅ 開發環境
.env.development.local  # ❌ 本地開發覆蓋 (不提交)
.env.test              # ✅ 測試環境
.env.test.local        # ❌ 本地測試覆蓋 (不提交)
.env.production        # ✅ 生產環境
.env.production.local  # ❌ 本地生產覆蓋 (不提交)
```

### 2. **載入優先順序**

Vite/Create React App 按以下順序載入環境變數：

```bash
# 開發模式 (npm run dev)
1. .env.development.local  # 最高優先級
2. .env.local
3. .env.development
4. .env                    # 最低優先級

# 生產模式 (npm run build)
1. .env.production.local   # 最高優先級
2. .env.local
3. .env.production
4. .env                    # 最低優先級

# 測試模式 (npm test)
1. .env.test.local         # 最高優先級
2. .env.test
3. .env.local
4. .env                    # 最低優先級
```

## 🛠️ **推薦的配置架構**

### 📁 **專案結構**

```
your-react-app/
├── .env                      # 通用預設值
├── .env.development          # 開發環境配置
├── .env.test                # 測試環境配置  
├── .env.production          # 生產環境配置
├── .env.local               # 本地覆蓋 (git ignore)
├── .gitignore              # 排除 .env.local
├── package.json
└── src/
    └── config/
        ├── environment.ts   # 環境管理器
        ├── env.ts          # 環境變數讀取
        └── constants.ts    # 常數定義
```

### 📝 **.env 檔案範例**

#### **`.env` (通用預設值)**
```bash
# =====================================
# 通用預設配置
# =====================================

# 應用基本配置
VITE_APP_NAME=EcoCampus
VITE_APP_VERSION=1.0.0

# API 配置 (預設值)
VITE_API_BASE_URL=http://localhost:3001/api
VITE_FRONTEND_URL=http://localhost:8080

# 功能開關 (安全預設值)
VITE_ENABLE_DEBUG_MODE=false
VITE_SHOW_TEST_ACCOUNTS=false
VITE_ENABLE_TOKEN_EXPIRY=true
VITE_SHOW_ENVIRONMENT_SELECTOR=false

# 外部連結 (生產環境預設)
VITE_OFFICIAL_WEBSITE_URL=https://ecocampus.moenv.gov.tw
VITE_OFFICIAL_LOGIN_URL=https://ecocampus.moenv.gov.tw/login
```

#### **`.env.development` (開發環境)**
```bash
# =====================================
# 開發環境配置
# =====================================

# API 配置
VITE_API_BASE_URL=http://localhost:3001/api
VITE_FRONTEND_URL=http://localhost:8081

# 功能開關 (開發環境全開)
VITE_ENABLE_DEBUG_MODE=true
VITE_SHOW_TEST_ACCOUNTS=true
VITE_ENABLE_TOKEN_EXPIRY=true
VITE_SHOW_ENVIRONMENT_SELECTOR=true

# 測試 Token (僅開發)
VITE_TEST_TOKEN_SCHOOL=dev-school-token
VITE_TEST_TOKEN_EPA=dev-epa-token
VITE_TEST_TOKEN_TUTOR=dev-tutor-token

# 外部連結 (開發環境)
VITE_OFFICIAL_WEBSITE_URL=https://dev.ecocampus.local
VITE_CERTIFICATION_APPLICATION_GUIDE=https://dev.ecocampus.local/guide
```

#### **`.env.test` (測試環境)**
```bash
# =====================================
# 測試環境配置
# =====================================

# API 配置
VITE_API_BASE_URL=https://api-test.ecocampus.local/api
VITE_FRONTEND_URL=https://test.ecocampus.local

# 功能開關 (測試環境)
VITE_ENABLE_DEBUG_MODE=true
VITE_SHOW_TEST_ACCOUNTS=true
VITE_ENABLE_TOKEN_EXPIRY=true
VITE_SHOW_ENVIRONMENT_SELECTOR=true

# 測試 Token
VITE_TEST_TOKEN_SCHOOL=test-school-token
VITE_TEST_TOKEN_EPA=test-epa-token

# 外部連結 (測試環境)
VITE_OFFICIAL_WEBSITE_URL=https://test.ecocampus.local
```

#### **`.env.production` (生產環境)**
```bash
# =====================================
# 生產環境配置
# =====================================

# API 配置
VITE_API_BASE_URL=https://api.ecocampus.moenv.gov.tw/api
VITE_FRONTEND_URL=https://ecocampus.moenv.gov.tw

# 功能開關 (生產環境安全設定)
VITE_ENABLE_DEBUG_MODE=false
VITE_SHOW_TEST_ACCOUNTS=false
VITE_ENABLE_TOKEN_EXPIRY=true
VITE_SHOW_ENVIRONMENT_SELECTOR=false

# 外部連結 (正式環境)
VITE_OFFICIAL_WEBSITE_URL=https://ecocampus.moenv.gov.tw
VITE_CERTIFICATION_APPLICATION_GUIDE=https://ecocampus.moenv.gov.tw/guide
```

## 💻 **TypeScript 配置管理**

### **src/config/env.ts**
```typescript
// 環境變數型別定義
interface EnvConfig {
  // 基本配置
  APP_NAME: string;
  APP_VERSION: string;
  
  // API 配置
  API_BASE_URL: string;
  FRONTEND_URL: string;
  
  // 功能開關
  ENABLE_DEBUG_MODE: boolean;
  SHOW_TEST_ACCOUNTS: boolean;
  ENABLE_TOKEN_EXPIRY: boolean;
  SHOW_ENVIRONMENT_SELECTOR: boolean;
  
  // 外部連結
  OFFICIAL_WEBSITE_URL: string;
  OFFICIAL_LOGIN_URL: string;
}

// 環境變數載入與驗證
export const env: EnvConfig = {
  // 基本配置
  APP_NAME: import.meta.env.VITE_APP_NAME || 'EcoCampus',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  
  // API 配置
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  FRONTEND_URL: import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080',
  
  // 功能開關
  ENABLE_DEBUG_MODE: import.meta.env.VITE_ENABLE_DEBUG_MODE === 'true',
  SHOW_TEST_ACCOUNTS: import.meta.env.VITE_SHOW_TEST_ACCOUNTS === 'true',
  ENABLE_TOKEN_EXPIRY: import.meta.env.VITE_ENABLE_TOKEN_EXPIRY !== 'false',
  SHOW_ENVIRONMENT_SELECTOR: import.meta.env.VITE_SHOW_ENVIRONMENT_SELECTOR === 'true',
  
  // 外部連結
  OFFICIAL_WEBSITE_URL: import.meta.env.VITE_OFFICIAL_WEBSITE_URL || 'https://ecocampus.moenv.gov.tw',
  OFFICIAL_LOGIN_URL: import.meta.env.VITE_OFFICIAL_LOGIN_URL || 'https://ecocampus.moenv.gov.tw/login',
};

// 環境判斷函數
export const getEnvironment = (): string => {
  return import.meta.env.MODE || 'development';
};

export const isDevelopment = () => getEnvironment() === 'development';
export const isTest = () => getEnvironment() === 'test';
export const isProduction = () => getEnvironment() === 'production';

// 環境變數驗證
export const validateEnv = (): void => {
  const requiredVars = [
    'VITE_API_BASE_URL',
    'VITE_FRONTEND_URL',
    'VITE_OFFICIAL_WEBSITE_URL'
  ];
  
  const missing = requiredVars.filter(varName => !import.meta.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ 缺少必要的環境變數:', missing);
    throw new Error(\`Missing required environment variables: \${missing.join(', ')}\`);
  }
  
  console.log('✅ 環境變數驗證通過');
};

export default env;
```

### **src/config/environment.ts**
```typescript
// 環境配置接口
export interface EnvironmentConfig {
  name: string;
  displayName: string;
  apiBaseUrl: string;
  frontendUrl: string;
  features: {
    debugMode: boolean;
    showTestAccounts: boolean;
    enableTokenExpiry: boolean;
    showEnvironmentSelector: boolean;
  };
}

// 環境配置定義
export const environments: Record<string, EnvironmentConfig> = {
  development: {
    name: 'development',
    displayName: '開發環境',
    apiBaseUrl: env.API_BASE_URL,
    frontendUrl: env.FRONTEND_URL,
    features: {
      debugMode: env.ENABLE_DEBUG_MODE,
      showTestAccounts: env.SHOW_TEST_ACCOUNTS,
      enableTokenExpiry: env.ENABLE_TOKEN_EXPIRY,
      showEnvironmentSelector: env.SHOW_ENVIRONMENT_SELECTOR,
    },
  },
  test: {
    name: 'test',
    displayName: '測試環境',
    apiBaseUrl: env.API_BASE_URL,
    frontendUrl: env.FRONTEND_URL,
    features: {
      debugMode: true,
      showTestAccounts: true,
      enableTokenExpiry: true,
      showEnvironmentSelector: false,
    },
  },
  production: {
    name: 'production',
    displayName: '生產環境',
    apiBaseUrl: env.API_BASE_URL,
    frontendUrl: env.FRONTEND_URL,
    features: {
      debugMode: false,
      showTestAccounts: false,
      enableTokenExpiry: true,
      showEnvironmentSelector: false,
    },
  },
};

// 環境管理器
class EnvironmentManager {
  private currentEnv: EnvironmentConfig;
  
  constructor() {
    const envName = getEnvironment();
    this.currentEnv = environments[envName] || environments.development;
  }
  
  getCurrentEnvironment(): EnvironmentConfig {
    return this.currentEnv;
  }
  
  getApiBaseUrl(): string {
    return this.currentEnv.apiBaseUrl;
  }
  
  isFeatureEnabled(feature: keyof EnvironmentConfig['features']): boolean {
    return this.currentEnv.features[feature];
  }
}

export const environmentManager = new EnvironmentManager();
export const getCurrentEnvironment = () => environmentManager.getCurrentEnvironment();
export const getApiBaseUrl = () => environmentManager.getApiBaseUrl();
export const isFeatureEnabled = (feature: keyof EnvironmentConfig['features']) => 
  environmentManager.isFeatureEnabled(feature);
```

## 🔧 **package.json 腳本配置**

```json
{
  "scripts": {
    "dev": "vite --mode development",
    "dev:test": "vite --mode test",
    "build": "vite build --mode production",
    "build:test": "vite build --mode test",
    "build:dev": "vite build --mode development",
    "preview": "vite preview",
    "preview:test": "vite preview --mode test"
  }
}
```

## 🔒 **.gitignore 設定**

```gitignore
# 環境變數
.env.local
.env.development.local
.env.test.local
.env.production.local

# 但保留範例檔案
!.env.example
!.env.development.example
!.env.test.example
!.env.production.example
```

## 📱 **跨平台相容性確保**

### **1. 路徑分隔符**
```bash
# ✅ 使用相對路徑，避免絕對路徑
VITE_UPLOAD_PATH=./uploads
VITE_ASSETS_PATH=./public/assets

# ❌ 避免使用 Windows 特定路徑
VITE_UPLOAD_PATH=C:\uploads
```

### **2. 換行符處理**
```bash
# .gitattributes 檔案
*.env text eol=lf
*.env.* text eol=lf
```

### **3. 環境變數值**
```bash
# ✅ 使用引號包圍複雜值
VITE_COMPLEX_VALUE="值包含 空格和特殊字符"

# ✅ 布林值使用字串
VITE_ENABLE_FEATURE=true
VITE_DISABLE_FEATURE=false

# ✅ 數字值使用字串
VITE_PORT=8080
VITE_TIMEOUT=30000
```

## 🚀 **部署時的環境配置**

### **Docker 部署**
```dockerfile
# Dockerfile
FROM node:18-alpine

# 複製環境檔案
COPY .env.production .env

# 建置應用
RUN npm run build

# 設定環境變數覆蓋
ENV VITE_API_BASE_URL=https://api.example.com/api
```

### **CI/CD 配置**
```yaml
# GitHub Actions 範例
- name: Build for production
  run: npm run build
  env:
    VITE_API_BASE_URL: ${{ secrets.API_BASE_URL }}
    VITE_FRONTEND_URL: ${{ secrets.FRONTEND_URL }}
```

## ✅ **最佳實踐檢查清單**

- [ ] 使用 `VITE_` 前綴命名前端環境變數
- [ ] 設定合理的預設值在 `.env` 檔案中
- [ ] 敏感資訊不要提交到版本控制
- [ ] 為每個環境創建專門的配置檔案
- [ ] 實作環境變數驗證邏輯
- [ ] 使用 TypeScript 型別檢查環境變數
- [ ] 確保跨平台路徑相容性
- [ ] 在應用啟動時驗證必要變數
- [ ] 文檔記錄所有環境變數用途
- [ ] 定期審查和清理未使用的變數

## 🛡️ **安全性考量**

1. **永遠不要在前端環境變數中存放敏感資訊**
2. **使用不同的 API 金鑰/密碼給不同環境**
3. **定期輪替測試和開發環境的認證資訊**
4. **在生產環境中停用除錯和測試功能**
5. **使用環境變數驗證確保必要配置存在**

這個配置方案確保了：
- ✅ **跨平台相容性** (Windows/macOS/Linux)
- ✅ **環境隔離** (開發/測試/生產)
- ✅ **安全性** (敏感資訊保護)
- ✅ **可維護性** (清晰的結構和命名)
- ✅ **易於部署** (標準化的配置方式)
