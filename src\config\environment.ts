// 環境配置檔案
export interface EnvironmentConfig {
  name: string;
  displayName: string;
  apiBaseUrl: string;
  frontendUrl: string;
  officialWebsite: {
    loginUrl: string;
    homeUrl: string;
    description: string;
  };
  testTokens: {
    school: string;
    epa: string;
    tutor: string;
  };
  features: {
    debugMode: boolean;
    showTestAccounts: boolean;
    enableTokenExpiry: boolean;
    showEnvironmentSelector: boolean;
  };
  database: {
    type: "local" | "remote" | "cloud";
    description: string;
  };
}
// 0801

const ENV = import.meta.env.MODE;

const basePath = ENV === "development" ? "/" : "/apply";

export const getFrontendBasePath = () => basePath;

// 可用環境配置
export const environments: Record<string, EnvironmentConfig> = {
  // 本地開發環境 (Local Development)
  development: {
    name: "development",
    displayName: "本地開發環境",
    apiBaseUrl: "http://localhost:3001/api",
    frontendUrl: "http://localhost:8080",
    officialWebsite: {
      loginUrl: "https://ecocampus.moenv.gov.tw/login",
      homeUrl: "https://ecocampus.moenv.gov.tw",
      description: "環保署生態學校官方網站",
    },
    testTokens: {
      // 長期有效的開發測試 Token
      school: "A0B0D18E-CFA0-4BEF-96EE-F157407E85A7",
      epa: "850D03CB-927A-45E6-AFDE-173C45F93E99",
      tutor: "D1433FC9-85C6-4054-A97D-7D12B7AFD5BB",
    },
    features: {
      debugMode: true,
      showTestAccounts: true,
      enableTokenExpiry: true,
      showEnvironmentSelector: true,
    },
    database: {
      type: "local",
      description: "VM-MSSQL-2022 本地資料庫",
    },
  },

  // 測試環境 (Testing Environment)
  testing: {
    name: "testing",
    displayName: "測試環境",
    apiBaseUrl: "http://ecocampus-v2-apply-api.sumire.com.tw/api",
    frontendUrl: "http://ecocampus-v2-apply.sumire.com.tw",
    officialWebsite: {
      loginUrl: "http://ecocampus-v2.sumire.com.tw/login",
      homeUrl: "http://ecocampus-v2.sumire.com.tw",
      description: "環保署生態學校測試網站",
    },
    testTokens: {
      // 測試環境專用 Token
      school: "TEST-SCHOOL-53249B8E-39D4-4FFE-950A-FEBA2733140B",
      epa: "TEST-EPA-850D03CB-927A-45E6-AFDE-173C45F93E99",
      tutor: "TEST-TUTOR-D1433FC9-85C6-4054-A97D-7D12B7AFD5BB",
    },
    features: {
      debugMode: true,
      showTestAccounts: true,
      enableTokenExpiry: true,
      showEnvironmentSelector: true,
    },
    database: {
      type: "remote",
      description: "VM-PRJ-ECOS-V2 測試資料庫",
    },
  },

  // 正式生產環境 (Production Environment)
  production: {
    name: "production",
    displayName: "正式環境",
    apiBaseUrl: "https://ecocampus.moenv.gov.tw/apply-api",
    frontendUrl: "https://ecocampus.gov.tw",
    officialWebsite: {
      loginUrl: "https://ecocampus.moenv.gov.tw/login",
      homeUrl: "https://ecocampus.moenv.gov.tw",
      description: "環保署生態學校官方網站",
    },
    testTokens: {
      // 正式環境不提供測試 Token
      school: "",
      epa: "",
      tutor: "",
    },
    features: {
      debugMode: false,
      showTestAccounts: false,
      enableTokenExpiry: true,
      showEnvironmentSelector: false,
    },
    database: {
      type: "cloud",
      description: "正式環境雲端資料庫",
    },
  },
};

// 環境管理類
class EnvironmentManager {
  private readonly ENV_KEY = "ecocampus_environment";
  private readonly ENV_HISTORY_KEY = "ecocampus_environment_history";
  private currentEnvironment: EnvironmentConfig;

  constructor() {
    // 初始化當前環境
    this.currentEnvironment = this.loadEnvironment();
    this.recordEnvironmentUsage();
  }

  // 載入環境配置
  private loadEnvironment(): EnvironmentConfig {
    try {
      // 1. 從 localStorage 讀取用戶選擇的環境
      const savedEnv = localStorage.getItem(this.ENV_KEY);
      if (savedEnv && environments[savedEnv]) {
        console.log(`🌍 [Environment] 載入已儲存的環境: ${environments[savedEnv].displayName} (${savedEnv})`);
        return environments[savedEnv];
      }

      // 2. 從環境變數判斷
      const nodeEnv = import.meta.env.MODE || "development";
      if (environments[nodeEnv]) {
        console.log(`🌍 [Environment] 使用環境變數環境: ${environments[nodeEnv].displayName} (${nodeEnv})`);
        return environments[nodeEnv];
      }

      // 3. 從 URL 參數判斷
      const urlParams = new URLSearchParams(window.location.search);
      const envParam = urlParams.get("env");
      if (envParam && environments[envParam]) {
        console.log(`🌍 [Environment] 使用 URL 參數環境: ${environments[envParam].displayName} (${envParam})`);
        this.switchEnvironment(envParam); // 保存選擇
        return environments[envParam];
      }

      // 4. 預設使用本地開發環境
      console.log("🌍 [Environment] 使用預設本地開發環境");
      return environments.development;
    } catch (error) {
      console.error("🌍 [Environment] 載入環境配置失敗:", error);
      return environments.development;
    }
  }

  // 記錄環境使用歷史
  private recordEnvironmentUsage(): void {
    try {
      const historyStr = localStorage.getItem(this.ENV_HISTORY_KEY) || "[]";
      const history: Array<{ env: string; timestamp: number }> = JSON.parse(historyStr);

      history.push({
        env: this.currentEnvironment.name,
        timestamp: Date.now(),
      });

      // 只保留最近 20 筆記錄
      const recentHistory = history.slice(-20);
      localStorage.setItem(this.ENV_HISTORY_KEY, JSON.stringify(recentHistory));
    } catch (error) {
      console.warn("🌍 [Environment] 記錄使用歷史失敗:", error);
    }
  }

  // 獲取當前環境配置
  getCurrentEnvironment(): EnvironmentConfig {
    return this.currentEnvironment;
  }

  // 切換環境
  switchEnvironment(envName: string): boolean {
    if (!environments[envName]) {
      console.error(`🌍 [Environment] 無效的環境名稱: ${envName}`);
      return false;
    }

    try {
      const oldEnv = this.currentEnvironment.displayName;
      this.currentEnvironment = environments[envName];
      localStorage.setItem(this.ENV_KEY, envName);
      this.recordEnvironmentUsage();

      console.log(`🌍 [Environment] 已切換環境: ${oldEnv} → ${this.currentEnvironment.displayName}`);

      // 如果是切換到不同環境，提示用戶重新載入
      if (oldEnv !== this.currentEnvironment.displayName) {
        console.log("🔄 [Environment] 環境已變更，建議重新載入頁面以確保配置生效");
      }

      return true;
    } catch (error) {
      console.error("🌍 [Environment] 切換環境失敗:", error);
      return false;
    }
  }

  // 獲取可用環境列表
  getAvailableEnvironments(): EnvironmentConfig[] {
    return Object.values(environments);
  }

  // 獲取環境使用歷史
  getEnvironmentHistory(): Array<{
    env: string;
    displayName: string;
    timestamp: number;
  }> {
    try {
      const historyStr = localStorage.getItem(this.ENV_HISTORY_KEY) || "[]";
      const history: Array<{ env: string; timestamp: number }> = JSON.parse(historyStr);

      return history
        .map((item) => ({
          ...item,
          displayName: environments[item.env]?.displayName || item.env,
        }))
        .reverse(); // 最新的在前面
    } catch (error) {
      console.warn("🌍 [Environment] 讀取使用歷史失敗:", error);
      return [];
    }
  }

  // 重置為預設環境
  reset(): void {
    try {
      localStorage.removeItem(this.ENV_KEY);
      this.currentEnvironment = environments.development;
      console.log("🌍 [Environment] 已重置為本地開發環境");
    } catch (error) {
      console.error("🌍 [Environment] 重置環境失敗:", error);
    }
  }

  // 獲取快速登入連結
  getQuickLoginLinks(): { role: string; name: string; url: string }[] {
    const baseUrl = this.currentEnvironment.frontendUrl;
    const tokens = this.currentEnvironment.testTokens;

    if (!this.currentEnvironment.features.showTestAccounts) {
      return [];
    }

    return [
      {
        role: "school",
        name: "學校身份",
        url: `${baseUrl}/login?token=${tokens.school}`,
      },
      {
        role: "epa",
        name: "環保署身份",
        url: `${baseUrl}/login?token=${tokens.epa}`,
      },
      {
        role: "tutor",
        name: "輔導員身份",
        url: `${baseUrl}/login?token=${tokens.tutor}`,
      },
    ].filter((link) => link.url.includes("?token=") && !link.url.endsWith("?token="));
  }

  // 檢查是否為本地開發環境
  isDevelopment(): boolean {
    return this.currentEnvironment.name === "development";
  }

  // 檢查是否為測試環境
  isTesting(): boolean {
    return this.currentEnvironment.name === "testing";
  }

  // 檢查是否為生產環境
  isProduction(): boolean {
    return this.currentEnvironment.name === "production";
  }

  // 獲取 API 基礎 URL
  getApiBaseUrl(): string {
    return this.currentEnvironment.apiBaseUrl;
  }

  // 獲取官方網站登入 URL
  getOfficialLoginUrl(): string {
    return this.currentEnvironment.officialWebsite.loginUrl;
  }

  // 獲取官方網站首頁 URL
  getOfficialHomeUrl(): string {
    return this.currentEnvironment.officialWebsite.homeUrl;
  }

  // 獲取官方網站配置
  getOfficialWebsiteConfig(): {
    loginUrl: string;
    homeUrl: string;
    description: string;
  } {
    return this.currentEnvironment.officialWebsite;
  }

  // 檢查功能是否啟用
  isFeatureEnabled(feature: keyof EnvironmentConfig["features"]): boolean {
    return this.currentEnvironment.features[feature];
  }

  // 獲取環境資訊摘要
  getEnvironmentSummary(): {
    name: string;
    displayName: string;
    apiUrl: string;
    frontendUrl: string;
    officialWebsite: {
      loginUrl: string;
      homeUrl: string;
      description: string;
    };
    database: string;
    features: string[];
  } {
    const activeFeatures = Object.entries(this.currentEnvironment.features)
      .filter(([_, enabled]) => enabled)
      .map(([feature, _]) => feature);

    return {
      name: this.currentEnvironment.name,
      displayName: this.currentEnvironment.displayName,
      apiUrl: this.currentEnvironment.apiBaseUrl,
      frontendUrl: this.currentEnvironment.frontendUrl,
      officialWebsite: this.currentEnvironment.officialWebsite,
      database: this.currentEnvironment.database.description,
      features: activeFeatures,
    };
  }
}

// 創建全域環境管理實例
export const environmentManager = new EnvironmentManager();

// 導出便捷函數
export const getCurrentEnvironment = () => environmentManager.getCurrentEnvironment();
export const switchEnvironment = (envName: string) => environmentManager.switchEnvironment(envName);
export const getApiBaseUrl = () => environmentManager.getApiBaseUrl();
export const getOfficialLoginUrl = () => environmentManager.getOfficialLoginUrl();
export const getOfficialHomeUrl = () => environmentManager.getOfficialHomeUrl();
export const getOfficialWebsiteConfig = () => environmentManager.getOfficialWebsiteConfig();
export const isFeatureEnabled = (feature: keyof EnvironmentConfig["features"]) => environmentManager.isFeatureEnabled(feature);
export const isDevelopment = () => environmentManager.isDevelopment();
export const isTesting = () => environmentManager.isTesting();
export const isProduction = () => environmentManager.isProduction();
export const getEnvironmentSummary = () => environmentManager.getEnvironmentSummary();

export default environmentManager;
