import { BaseAP<PERSON>, ApiResponse } from "./BaseAPI";

// 認證申請資料介面
export interface CertificationData {
  sid?: number;
  member_sid: number;
  level: number; // 1=銅牌, 2=銀牌, 3=綠旗, 4=綠旗R1, 5=綠旗R2, 6=綠旗R3
  Level?: number; // 兼容性字段，與 level 相同
  review: "審核中" | "通過" | "退件" | "補件" | "尚未審核";
  ReviewStatus?: number; // 0=審核中, 1=已通過, 2=已退件, 3=待補件, 4=尚未審核
  reviewdate?: string;
  passdate?: string;
  returndate?: string;
  additionaldate?: string;
  ApplyDate?: string; // 申請日期
  certificate_sid?: number;
  is_del: number;
  add_type: "front" | "backend";
  createdate: number;
  updatedate: number;
  CreatedTime?: string; // 創建時間（ISO 格式）
  UpdatedTime?: string; // 更新時間（ISO 格式）
}

// 認證列表項目類型
export interface CertificationListItem {
  id: string;
  certificationType: string;
  level: number;
  status: string;
  statusInfo: {
    label: string;
    icon: string;
    description: string;
    color: string;
    bgColor: string;
  };
  typeInfo: {
    name: string;
    fullName: string;
    level: number;
    isRenewal: boolean;
    icon: string;
  };
  applicantName: string;
  applyDate: string;
  reviewDate?: string;
  passDate?: string;
  expiredDate?: string;
  certificateNumber?: string;
  isEditable: boolean;
  isDeletable: boolean;
}

// 認證可用性類型
export interface CertificationAvailability {
  id: string;
  name: string;
  level: number;
  available: boolean;
  reason: string;
  frontendId: string;
}

// 問題資料介面
export interface QuestionData {
  sid: number;
  parent_sid: number;
  title: string;
  step: number; // 1-9
  sequence: number;
  is_use: number;
  is_renew: number; // -1=初次, 0=通用, 1=更新
  question_tpl: number;
  lan: string;
  createdate: number;
  updatedate: number;
}

// 答案記錄介面
export interface AnswerRecord {
  sid?: number;
  certification_answer_sid: number;
  answer_json: string; // JSON格式的答案內容
  opinion?: string; // 審核意見
  status: "未完成" | "已完成" | "待補件" | "退件" | "已填寫";
  sequence: number;
  createdate: number;
  updatedate: number;
}

// 認證步驟介面
export interface CertificationStep {
  step: number;
  title: string;
  description: string;
  questionCount: number;
  isCompleted: boolean;
  questions?: QuestionData[];
}

// 表單問題配置介面
export interface FormQuestionConfig {
  questions: QuestionData[];
  steps: CertificationStep[];
}

// 進度資料介面
export interface ProgressData {
  completedSteps: number;
  totalSteps: number;
  completedQuestions: number;
  totalQuestions: number;
  progressPercentage: number;
}

// 認證 API 類別
export class CertificationAPI extends BaseAPI {
  constructor() {
    super();
  }

  // 認證管理
  async getCertification(certificationId: number): Promise<ApiResponse<CertificationData>> {
    return this.get<CertificationData>(`/certification/${certificationId}`);
  }

  async createCertification(data: Partial<CertificationData>): Promise<ApiResponse<CertificationData>> {
    return this.post<CertificationData>("/certification", data);
  }

  async updateCertification(certificationId: number, data: Partial<CertificationData>): Promise<ApiResponse<CertificationData>> {
    return this.put<CertificationData>(`/certification/${certificationId}`, data);
  }

  async submitForReview(certificationId: number): Promise<ApiResponse<void>> {
    return this.post<void>(`/certification/${certificationId}/submit`);
  }

  // 認證步驟和表單
  async getCertificationSteps(): Promise<ApiResponse<CertificationStep[]>> {
    return this.get<CertificationStep[]>("/question/certification-steps");
  }

  async getFormQuestions(): Promise<ApiResponse<FormQuestionConfig>> {
    return this.get<FormQuestionConfig>("/question/form-questions");
  }

  // 問題管理
  async getQuestionsByStep(step: number, certificationLevel: number): Promise<ApiResponse<QuestionData[]>> {
    return this.get<QuestionData[]>(`/question`, { step, level: certificationLevel });
  }

  async getQuestionConfig(step: number, questionIndex: number, questionId: number): Promise<ApiResponse<QuestionData>> {
    return this.get<QuestionData>(`/question/config`, { step, question_index: questionIndex, question_sid: questionId });
  }

  // 答案管理
  async getAnswer(certificationId: number, questionId: number): Promise<ApiResponse<AnswerRecord>> {
    return this.get<AnswerRecord>(`/answer/certification`, { certification_sid: certificationId, question_sid: questionId });
  }

  async getAnswers(certificationId: number): Promise<ApiResponse<AnswerRecord[]>> {
    return this.get<AnswerRecord[]>(`/answer/certification/${certificationId}`);
  }

  async saveAnswer(certificationId: number, questionId: number, answerData: Record<string, unknown>): Promise<ApiResponse<AnswerRecord>> {
    return this.post<AnswerRecord>("/answer/save", {
      certification_sid: certificationId,
      question_sid: questionId,
      answer_json: JSON.stringify(answerData),
      status: "已填寫",
    });
  }

  // 保存認證問題答案
  async saveCertificationAnswer(data: { certificationId: number; questionId: number; answerData: Record<string, unknown>; templateId?: number }): Promise<
    ApiResponse<{
      answerId: number;
      questionId: number;
      templateId: number;
      message: string;
    }>
  > {
    return this.post<{
      answerId: number;
      questionId: number;
      templateId: number;
      message: string;
    }>("/answer/save", data);
  }

  async updateAnswerStatus(certificationId: number, questionId: number, status: AnswerRecord["status"]): Promise<ApiResponse<void>> {
    return this.put<void>("/answers/status", {
      certification_sid: certificationId,
      question_sid: questionId,
      status,
    });
  }

  // 認證檔案上傳
  async uploadCertificationFile(
    file: File,
    fileType: string,
    certificationId: number,
    questionId: number
  ): Promise<ApiResponse<{ fileUrl: string; fileName: string }>> {
    const additionalData = {
      file_type: fileType,
      certification_sid: certificationId.toString(),
      question_sid: questionId.toString(),
    };

    return this.uploadFile<{ fileUrl: string; fileName: string }>("/files/upload", file, additionalData);
  }

  async deleteFile(fileId: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`/files/${fileId}`);
  }

  // 通用檔案上傳（單一檔案）
  async uploadSingleFile(file: File): Promise<
    ApiResponse<{
      id: string;
      url: string;
      fileName: string;
      fileType: string;
      fileSize: number;
    }>
  > {
    return this.uploadFile<{
      id: string;
      url: string;
      fileName: string;
      fileType: string;
      fileSize: number;
    }>("/file/upload-single", file);
  }

  // 自然人憑證綁定
  async bindNaturalPersonCertificate(data: {
    certificateInfo: {
      subject: string;
      issuer: string;
      serialNumber: string;
      notBefore: string;
      notAfter: string;
      fingerprint: string;
    };
    accountId?: string;
  }): Promise<ApiResponse<{ message: string; bindingId: string }>> {
    return this.post<{ message: string; bindingId: string }>("/certificate/bind", data);
  }

  // 驗證相關
  async validateAnswer(
    certificationId: number,
    questionId: number,
    answerData: Record<string, unknown>
  ): Promise<ApiResponse<{ isValid: boolean; errors: Record<string, string> }>> {
    return this.post<{ isValid: boolean; errors: Record<string, string> }>("/answers/validate", {
      certification_sid: certificationId,
      question_sid: questionId,
      answer_data: answerData,
    });
  }

  // 特殊動態表單處理
  async getDynamicFormOptions(questionId: number, certificationId: number): Promise<ApiResponse<unknown>> {
    return this.get<unknown>(`/question/dynamic-options/${questionId}`, { certification_sid: certificationId });
  }

  // 進度追蹤
  async getCertificationProgress(certificationId: number): Promise<ApiResponse<ProgressData>> {
    return this.get<ProgressData>(`/certification/${certificationId}/progress`);
  }

  // 審核歷程
  async getReviewHistory(certificationId: number): Promise<ApiResponse<AnswerRecord[]>> {
    return this.get<AnswerRecord[]>(`/answer/review-history/${certificationId}`);
  }

  // 證書生成
  async generateCertificate(certificationId: number): Promise<ApiResponse<{ certificateUrl: string }>> {
    return this.post<{ certificateUrl: string }>(`/certification/${certificationId}/certificate`);
  }

  // 認證列表管理
  async getCertificationList(): Promise<
    ApiResponse<{
      all: CertificationListItem[];
      drafts: CertificationListItem[];
      pending: CertificationListItem[];
      passed: CertificationListItem[];
      statistics: {
        total: number;
        drafts: number;
        pending: number;
        passed: number;
        inReview: number;
        returned: number;
      };
    }>
  > {
    return this.get<{
      all: CertificationListItem[];
      drafts: CertificationListItem[];
      pending: CertificationListItem[];
      passed: CertificationListItem[];
      statistics: {
        total: number;
        drafts: number;
        pending: number;
        passed: number;
        inReview: number;
        returned: number;
      };
    }>("/certification/list");
  }

  // 檢查認證可用性
  async getCertificationAvailability(): Promise<
    ApiResponse<{
      availability: CertificationAvailability[];
    }>
  > {
    return this.get<{
      availability: CertificationAvailability[];
    }>("/certification/availability");
  }

  // 創建認證申請
  async createCertificationApplication(data: { certificationType: string; level: number }): Promise<
    ApiResponse<{
      CertificationId: number;
      message: string;
    }>
  > {
    return this.post<{
      CertificationId: number;
      message: string;
    }>("/certification/create", data);
  }

  // 刪除認證申請
  async deleteCertification(certificationId: string): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.delete<{
      message: string;
    }>(`/certification/${certificationId}`);
  }

  // 提交認證送審
  async submitCertificationForReview(certificationId: string): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.post<{
      message: string;
    }>(`/certification/${certificationId}/submit`, {
      certificationId: certificationId,
    });
  }

  // 管理員權限驗證
  async verifyAdminPermission(token: string): Promise<
    ApiResponse<{
      isAdmin: boolean;
      userRole: string;
    }>
  > {
    return this.post<{
      isAdmin: boolean;
      userRole: string;
    }>("/auth/verify-admin", { token });
  }

  // 管理員標記答案狀態
  async markAnswerStatus(data: { certificationId: number; questionId: string; status: string; action: string }): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.post<{
      message: string;
    }>("/admin/mark-answer-status", data);
  }

  // 保存評審意見
  async saveReviewComment(data: { certificationId: number; stepId: string; comment: string }): Promise<
    ApiResponse<{
      message: string;
    }>
  > {
    return this.post<{
      message: string;
    }>("/admin/save-review-comment", data);
  }

  // 獲取評審意見
  async getReviewComments(certificationId: string): Promise<
    ApiResponse<
      Array<{
        CertificationStepRecordId: number;
        StepNumber: number;
        Comment: string;
        CreatedTime: string;
        AdminUsername: string;
      }>
    >
  > {
    return this.get<
      Array<{
        CertificationStepRecordId: number;
        StepNumber: number;
        Comment: string;
        CreatedTime: string;
        AdminUsername: string;
      }>
    >(`/admin/review-comments/${certificationId}`);
  }

  // 模板答案儲存
  async saveTemplateAnswer(
    certificationId: number,
    questionId: number,
    templateId: number,
    answerData: Record<string, unknown>,
    questionTitle?: string
  ): Promise<
    ApiResponse<{
      certification_sid: number;
      question_sid: number;
      template_id: number;
      question_title: string;
      answer_json: string;
      raw_answer_data: Record<string, unknown>;
      validation_result: { isValid: boolean; errors: string[] };
      timestamp: string;
      status: string;
    }>
  > {
    return this.post<{
      certification_sid: number;
      question_sid: number;
      template_id: number;
      question_title: string;
      answer_json: string;
      raw_answer_data: Record<string, unknown>;
      validation_result: { isValid: boolean; errors: string[] };
      timestamp: string;
      status: string;
    }>("/template-answers/save", {
      certification_sid: certificationId,
      question_sid: questionId,
      template_id: templateId,
      answer_data: answerData,
      question_title: questionTitle,
    });
  }

  // 驗證模板答案格式
  async validateTemplateAnswer(templateId: number, answerData: Record<string, unknown>): Promise<ApiResponse<{ isValid: boolean; errors: string[] }>> {
    return this.post<{ isValid: boolean; errors: string[] }>("/template-answers/validate", {
      template_id: templateId,
      answer_data: answerData,
    });
  }
}

// 創建全域認證 API 實例
export const certificationAPI = new CertificationAPI();
