import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import { CertificateService } from "../services/certificate-service.js";
import {
  CertificateValidateParams,
  CertificateBindParams,
  CertificateUnbindParams,
  CertificateBindingsParams,
  CertificateLoginParams,
  CertificateTestParams,
  CertificateValidateQueryParams,
  CertificateBindQueryParams,
  CertificateUnbindQueryParams,
  CertificateBindingsQueryParams,
  CertificateLoginQueryParams,
  CertificateTestQueryParams,
  CertificateValidateResponse,
  CertificateBindResponse,
  CertificateUnbindResponse,
  CertificateBindingsResponse,
  CertificateLoginResponse,
  CertificateTestResponse,
  CertificateValidateRequest,
  CertificateBindRequest,
  CertificateLoginRequest,
} from "../models/certificate.js";
import { CERTIFICATE_ERROR_MESSAGES, CERTIFICATE_SUCCESS_MESSAGES } from "../constants/certificate.js";

const router = express.Router();

// 驗證憑證有效性
router.post(
  "/validate",
  async (
    req: express.Request<CertificateValidateParams, CertificateValidateResponse, CertificateValidateRequest, CertificateValidateQueryParams>,
    res: express.Response<CertificateValidateResponse>
  ) => {
    try {
      const result = await CertificateService.validateCertificate(req.body);

      res.json({
        success: true,
        message: CERTIFICATE_SUCCESS_MESSAGES.VALIDATION_SUCCESS,
        data: result,
      });
    } catch (error: unknown) {
      console.error("憑證驗證錯誤:", error);

      if (error instanceof Error) {
        if ([CERTIFICATE_ERROR_MESSAGES.MISSING_CERTIFICATE_DATA, CERTIFICATE_ERROR_MESSAGES.INVALID_CERTIFICATE].includes(error.message)) {
          return res.status(400).json({
            success: false,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: CERTIFICATE_ERROR_MESSAGES.VALIDATION_ERROR,
      });
    }
  }
);

// 綁定憑證到帳號
router.post(
  "/bind",
  authenticateToken,
  async (
    req: express.Request<CertificateBindParams, CertificateBindResponse, CertificateBindRequest, CertificateBindQueryParams>,
    res: express.Response<CertificateBindResponse>
  ) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "用戶未認證",
        });
      }

      const result = await CertificateService.bindCertificate(req.body, userId);

      res.json({
        success: true,
        message: CERTIFICATE_SUCCESS_MESSAGES.BIND_SUCCESS,
        data: result,
      });
    } catch (error: unknown) {
      console.error("憑證綁定錯誤:", error);

      if (error instanceof Error) {
        if ([CERTIFICATE_ERROR_MESSAGES.MISSING_BIND_INFO].includes(error.message)) {
          return res.status(400).json({
            success: false,
            message: error.message,
          });
        }

        if ([CERTIFICATE_ERROR_MESSAGES.PERMISSION_DENIED_BIND].includes(error.message)) {
          return res.status(403).json({
            success: false,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: CERTIFICATE_ERROR_MESSAGES.BIND_ERROR,
      });
    }
  }
);

// 解除憑證綁定
router.delete(
  "/unbind/:accountId",
  authenticateToken,
  async (
    req: express.Request<CertificateUnbindParams, CertificateUnbindResponse, {}, CertificateUnbindQueryParams>,
    res: express.Response<CertificateUnbindResponse>
  ) => {
    try {
      const { accountId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "用戶未認證",
        });
      }

      await CertificateService.unbindCertificate(accountId, userId);

      res.json({
        success: true,
        message: CERTIFICATE_SUCCESS_MESSAGES.UNBIND_SUCCESS,
      });
    } catch (error: unknown) {
      console.error("解除憑證綁定錯誤:", error);

      if (error instanceof Error) {
        if ([CERTIFICATE_ERROR_MESSAGES.NO_CERTIFICATE_BOUND].includes(error.message)) {
          return res.status(400).json({
            success: false,
            message: error.message,
          });
        }

        if ([CERTIFICATE_ERROR_MESSAGES.PERMISSION_DENIED_UNBIND].includes(error.message)) {
          return res.status(403).json({
            success: false,
            message: error.message,
          });
        }

        if ([CERTIFICATE_ERROR_MESSAGES.ACCOUNT_NOT_FOUND].includes(error.message)) {
          return res.status(404).json({
            success: false,
            message: error.message,
          });
        }

        if ([CERTIFICATE_ERROR_MESSAGES.UNBIND_FAILED].includes(error.message)) {
          return res.status(500).json({
            success: false,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: CERTIFICATE_ERROR_MESSAGES.UNBIND_ERROR,
      });
    }
  }
);

// 查詢帳號的憑證綁定狀態
router.get(
  "/bindings",
  authenticateToken,
  async (
    req: express.Request<CertificateBindingsParams, CertificateBindingsResponse, {}, CertificateBindingsQueryParams>,
    res: express.Response<CertificateBindingsResponse>
  ) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "用戶未認證",
        });
      }

      const result = await CertificateService.getCertificateBindings(userId);

      res.json({
        success: true,
        data: result,
      });
    } catch (error: unknown) {
      console.error("查詢憑證綁定錯誤:", error);

      if (error instanceof Error && error.message === CERTIFICATE_ERROR_MESSAGES.ACCOUNT_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: CERTIFICATE_ERROR_MESSAGES.QUERY_ERROR,
      });
    }
  }
);

// 憑證登入
router.post(
  "/login",
  async (
    req: express.Request<CertificateLoginParams, CertificateLoginResponse, CertificateLoginRequest, CertificateLoginQueryParams>,
    res: express.Response<CertificateLoginResponse>
  ) => {
    try {
      const ipAddress = req.ip || "unknown";
      const userAgent = req.get("User-Agent") || "unknown";

      const result = await CertificateService.loginWithCertificate(req.body, ipAddress, userAgent);

      res.json({
        success: true,
        message: CERTIFICATE_SUCCESS_MESSAGES.LOGIN_SUCCESS,
        data: result,
      });
    } catch (error: unknown) {
      console.error("憑證登入錯誤:", error);

      if (error instanceof Error) {
        if ([CERTIFICATE_ERROR_MESSAGES.MISSING_LOGIN_CREDENTIALS].includes(error.message)) {
          return res.status(400).json({
            success: false,
            message: error.message,
          });
        }

        if ([CERTIFICATE_ERROR_MESSAGES.INVALID_PASSWORD].includes(error.message)) {
          return res.status(401).json({
            success: false,
            message: error.message,
          });
        }

        if ([CERTIFICATE_ERROR_MESSAGES.NO_BOUND_ACCOUNT].includes(error.message)) {
          return res.status(404).json({
            success: false,
            message: error.message,
          });
        }
      }

      res.status(500).json({
        success: false,
        message: CERTIFICATE_ERROR_MESSAGES.LOGIN_ERROR,
      });
    }
  }
);

// 測試端點
router.get(
  "/test",
  authenticateToken,
  async (
    req: express.Request<CertificateTestParams, CertificateTestResponse, {}, CertificateTestQueryParams>,
    res: express.Response<CertificateTestResponse>
  ) => {
    try {
      const user = req.user;

      if (!user) {
        return res.status(401).json({
          success: false,
          message: "用戶未認證",
        });
      }

      const result = await CertificateService.testCertificateAPI(user);

      res.json({
        success: true,
        message: CERTIFICATE_SUCCESS_MESSAGES.TEST_SUCCESS,
        data: result,
      });
    } catch (error: unknown) {
      console.error("憑證測試錯誤:", error);

      res.status(500).json({
        success: false,
        message: CERTIFICATE_ERROR_MESSAGES.TEST_FAILED,
      });
    }
  }
);

export default router;
