import { Router, Request, Response } from "express";
import { authenticateToken } from "../middleware/auth.js";
import { executeQuery<PERSON>ingle, executeUpdate, executeQuery } from "../config/database-mssql.js";
import { APILogger } from "../utils/logger";

const router = Router();

// 共用介面定義
interface MemberQueryResult {
  AccountId: number;
  account: string;
  email: string;
  tel: string;
  phone: string;
  address: string;
  account_county_id: number;
  is_school_partner: number;
  is_epa_user: number;
  is_guidance_team: number;
  member_cname_zh: string;
  member_email: string;
  member_tel: string;
  member_phone: string;
  member_address: string;
  member_url: string;
  job_cname: string;
  place_cname: string;
  member_role: string;
  member_Introduction: string;
  member_exchange: string;
  member_cname_en: string;
  county_name_zh: string;
  county_name_en: string;
  school_county_name_zh: string;
  school_county_name_en: string;
  school_id: number;
  school_code: string;
  school_phone: string;
  school_mobile: string;
  school_email: string;
  school_county_id: number;
  school_district_id: number;
  school_name: string;
  school_address: string;
  school_department: string;
  school_job_title: string;
  school_introduction: string;
  school_website: string;
  school_name_en: string;
  school_address_en: string;
  principal_cname: string;
  principal_tel: string;
  principal_phone: string;
  principal_email: string;
  school_logo_path?: string;
}

interface ContactResult {
  contact_cname: string;
  contact_job_title: string;
  contact_tel: string;
  contact_phone: string;
  contact_email: string;
  contact_sid: number;
}

interface StatisticsResult {
  staff_total: number;
  elementary1: number;
  elementary2: number;
  elementary3: number;
  elementary4: number;
  elementary5: number;
  elementary6: number;
  middle7: number;
  middle8: number;
  middle9: number;
  hight10: number;
  hight11: number;
  hight12: number;
  write_date: Date;
  school_statistics_sid: number;
}

// 簡化版基本資料維護 API - 獲取會員資料
router.get("/", authenticateToken, async (req: Request, res: Response) => {
  try {
    APILogger.logRequest(req, "Profile", "獲取會員資料");

    // 認證中間件已經驗證了 Token
    if (!req.user) {
      APILogger.logError("Profile", "獲取會員資料", "使用者未認證", 401);
      return res.status(401).json({
        success: false,
        message: "使用者未認證",
      });
    }

    // 獲取 Token
    let userToken = req.userToken;
    if (!userToken) {
      const authHeader = req.headers["authorization"] as string;
      if (authHeader && authHeader.startsWith("Bearer ")) {
        userToken = authHeader.substring(7);
      } else {
        userToken = req.headers["x-user-token"] as string;
      }
    }

    if (!userToken) {
      return res.status(401).json({
        success: false,
        message: "缺少 Token",
      });
    }
    console.log("userToken", userToken);
    // 查詢 Accounts 和 MemberProfiles 資料（多語言支援 + 負責縣市 + 學校完整資料）
    const memberQuery = `
      SELECT 
        a.AccountId,
        a.Username as account,
        a.email,
        a.Telephone as tel,
        a.phone,
        a.address,
        a.CountyId as account_county_id,
        a.IsSchoolPartner as is_school_partner,
        a.IsEpaUser as is_epa_user,
        a.IsGuidanceTeam as is_guidance_team,
        mp_zh.MemberName as member_cname_zh,
        mp_zh.MemberEmail as member_email,
        mp_zh.MemberTelephone as member_tel,
        mp_zh.MemberPhone as member_phone,
        mp_zh.MemberAddress as member_address,
        mp_zh.MemberUrl as member_url,
        mp_zh.JobName as job_cname,
        mp_zh.PlaceName as place_cname,
        mp_zh.MemberRole as member_role,
        mp_zh.MemberIntroduction as member_Introduction,
        mp_zh.MemberExchange as member_exchange,
        mp_en.MemberName as member_cname_en,
        ct_zh.Name as county_name_zh,
        ct_en.Name as county_name_en,
        -- 學校縣市名稱
        sct_zh.Name as school_county_name_zh,
        sct_en.Name as school_county_name_en,
        -- 學校相關資料
        s.Id as school_id,
        s.SchoolCode as school_code,
        s.Phone as school_phone,
        s.MobilePhone as school_mobile,
        s.Email as school_email,
        s.CountyId as school_county_id,
        s.DistrictId as school_district_id,
        sc_zh.Name as school_name,
        sc_zh.Address as school_address,
        sc_zh.DepartmentName as school_department,
        sc_zh.JobTitle as school_job_title,
        sc_zh.Introduction as school_introduction,
        sc_zh.WebsiteUrl as school_website,
        sc_en.Name as school_name_en,
        sc_en.Address as school_address_en,
        -- 校長資料
        sp.PrincipalName as principal_cname,
        sp.PrincipalPhone as principal_tel,
        sp.PrincipalMobile as principal_phone,
        sp.PrincipalEmail as principal_email
      FROM Accounts a
      INNER JOIN UserToken ut ON a.AccountId = ut.AccountSid
      LEFT JOIN MemberProfiles mp_zh ON a.AccountId = mp_zh.AccountId AND mp_zh.LocaleCode = 'zh-TW'
      LEFT JOIN MemberProfiles mp_en ON a.AccountId = mp_en.AccountId AND mp_en.LocaleCode = 'en'
      LEFT JOIN CountyTranslations ct_zh ON a.CountyId = ct_zh.CountyId AND ct_zh.LocaleCode = 'zh-TW'
      LEFT JOIN CountyTranslations ct_en ON a.CountyId = ct_en.CountyId AND ct_en.LocaleCode = 'en'
      LEFT JOIN Schools s ON a.SchoolId = s.Id
      LEFT JOIN CountyTranslations sct_zh ON s.CountyId = sct_zh.CountyId AND sct_zh.LocaleCode = 'zh-TW'
      LEFT JOIN CountyTranslations sct_en ON s.CountyId = sct_en.CountyId AND sct_en.LocaleCode = 'en'
      LEFT JOIN SchoolContents sc_zh ON s.Id = sc_zh.SchoolId AND sc_zh.LocaleCode = 'zh-TW'
      LEFT JOIN SchoolContents sc_en ON s.Id = sc_en.SchoolId AND sc_en.LocaleCode = 'en'
      LEFT JOIN SchoolPrincipals sp ON s.Id = sp.SchoolId AND sp.Status = 1
      WHERE ut.Token = CAST(@token AS uniqueidentifier)
        AND ut.Status = 1 
        AND a.Status = 1
        AND (ut.ExpireDate IS NULL OR ut.ExpireDate > GETDATE())
    `;

    const member = await executeQuerySingle<MemberQueryResult>(memberQuery, { token: userToken });

    if (!member) {
      return res.status(404).json({
        success: false,
        message: "找不到會員資料",
      });
    }

    // 判斷身份類型
    let roleType = "school";
    if (member.is_epa_user === 1) {
      roleType = "epa";
    } else if (member.is_guidance_team === 1) {
      roleType = "tutor";
    }

    // 獲取學校聯絡人資料（如果是學校身份）

    let contacts: ContactResult[] = [];
    let statistics: StatisticsResult | null = null;

    if (roleType === "school" && member.school_id) {
      // 查詢聯絡人
      const contactQuery = `
        SELECT 
          ContactName as contact_cname,
          JobTitle as contact_job_title,
          ContactPhone as contact_tel,
          ContactMobile as contact_phone,
          ContactEmail as contact_email,
          Id as contact_sid
        FROM SchoolContacts 
        WHERE SchoolId = @schoolId AND Status = 1
        ORDER BY SortOrder
      `;

      const contactResult = await executeQuery<ContactResult>(contactQuery, { schoolId: member.school_id });
      contacts = contactResult || [];

      // 查詢統計資料
      const statisticsQuery = `
        SELECT 
          StaffTotal as staff_total,
          Elementary1 as elementary1,
          Elementary2 as elementary2,
          Elementary3 as elementary3,
          Elementary4 as elementary4,
          Elementary5 as elementary5,
          Elementary6 as elementary6,
          Middle7 as middle7,
          Middle8 as middle8,
          Middle9 as middle9,
          High10 as hight10,
          High11 as hight11,
          High12 as hight12,
          WriteDate as write_date,
          Id as school_statistics_sid
        FROM SchoolStatistics 
        WHERE SchoolId = @schoolId AND Status = 1
      `;

      const statisticsResult = await executeQuerySingle<StatisticsResult>(statisticsQuery, { schoolId: member.school_id });
      statistics = statisticsResult || null;
    }

    // 組合會員資料（多語言支援 + 負責縣市 + 學校完整資料）
    const memberProfile = {
      sid: member.AccountId,
      account: member.account,
      roleType: roleType,
      member_role: roleType as "school" | "epa" | "tutor", // 新增：符合前端期望的欄位名稱
      member_cname: member.member_cname_zh || member.school_name || "",
      member_cname_en: member.member_cname_en || member.school_name_en || "",
      member_email: member.member_email || member.email || "",
      member_tel: member.member_tel || member.tel || "",
      member_phone: member.member_phone || member.phone || "",
      member_address: member.member_address || member.school_address || member.address || "",
      member_url: member.member_url || member.school_website || "",
      job_cname: member.job_cname || "",
      place_cname: member.place_cname || member.school_name || "",
      place_cname_en: member.school_name_en || "", // 🆕 學校英文名稱
      member_Introduction: member.member_Introduction || member.school_introduction || "",
      member_exchange: member.member_exchange || "0",
      county_id: member.account_county_id,
      county_name: member.school_county_name_zh || member.county_name_zh, // 優先使用學校縣市名稱
      county_name_en: member.county_name_en || "",
      // 🆕 地址分解資訊
      city_name: member.school_county_name_zh || member.county_name_zh || "",
      area_name: "", // TODO: 需要區域名稱查詢
      detail_address: member.member_address || member.school_address || member.address || "",
      // 🆕 校徽資訊
      school_logo: member.school_logo_path || null,
      is_school_partner: member.is_school_partner,
      is_epa_user: member.is_epa_user,
      is_guidance_team: member.is_guidance_team,
      city_sid: member.school_county_id || member.account_county_id, // 優先使用學校的縣市資料
      area_sid: member.school_district_id || null, // 學校的區域資料
      register_review: "已通過" as const, // 新增：與前端 MemberProfile 介面一致
      member_passdate: "", // 新增：與前端 MemberProfile 介面一致
      isuse: 1, // 新增：與前端 MemberProfile 介面一致
      createdate: Math.floor(Date.now() / 1000), // 新增：與前端 MemberProfile 介面一致
      updatedate: Math.floor(Date.now() / 1000), // 新增：與前端 MemberProfile 介面一致

      // 學校身份專用的完整資料
      ...(roleType === "school" && {
        member_record: {
          member_role: "school" as const,
          member_sid: String(member.AccountId),
          sid: String(member.school_id || ""),
          school_principal_sid: String(member.school_id || ""),

          // 基本學校資料
          member_cname: member.school_name || member.member_cname_zh || "",
          member_cname_en: member.school_name_en || member.member_cname_en || "",
          member_address: member.school_address || member.member_address || member.address || "",
          member_tel: member.school_phone || member.member_tel || member.tel || "",
          member_email: member.school_email || member.member_email || member.email || "",
          member_url: member.school_website || member.member_url || "",
          member_Introduction: member.school_introduction || member.member_Introduction || "",
          member_exchange: member.member_exchange || "0",
          file_token_member_pohto: "",
          // 🆕 校徽資訊
          school_logo: member.school_logo_path || null,

          // 校長資訊
          principal_cname: member.principal_cname || "",
          principal_tel: member.principal_tel || "",
          principal_phone: member.principal_phone || "",
          principal_email: member.principal_email || "",

          // 聯絡人資訊
          contact_num: String(contacts.length),
          contact: contacts,

          // 學校統計資料 - 🆕 支援 statistics 子物件
          school_statistics_sid: String(statistics?.school_statistics_sid || ""),
          write_date: statistics?.write_date ? new Date(statistics.write_date).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
          staff_total: String(statistics?.staff_total || "0"),
          elementary1: String(statistics?.elementary1 || "0"),
          elementary2: String(statistics?.elementary2 || "0"),
          elementary3: String(statistics?.elementary3 || "0"),
          elementary4: String(statistics?.elementary4 || "0"),
          elementary5: String(statistics?.elementary5 || "0"),
          elementary6: String(statistics?.elementary6 || "0"),
          middle7: String(statistics?.middle7 || "0"),
          middle8: String(statistics?.middle8 || "0"),
          middle9: String(statistics?.middle9 || "0"),
          hight10: String(statistics?.hight10 || "0"),
          hight11: String(statistics?.hight11 || "0"),
          hight12: String(statistics?.hight12 || "0"),

          // 🆕 新增 statistics 子物件，供前端使用
          statistics: {
            staff_total: String(statistics?.staff_total || "0"),
            elementary1: String(statistics?.elementary1 || "0"),
            elementary2: String(statistics?.elementary2 || "0"),
            elementary3: String(statistics?.elementary3 || "0"),
            elementary4: String(statistics?.elementary4 || "0"),
            elementary5: String(statistics?.elementary5 || "0"),
            elementary6: String(statistics?.elementary6 || "0"),
            middle7: String(statistics?.middle7 || "0"),
            middle8: String(statistics?.middle8 || "0"),
            middle9: String(statistics?.middle9 || "0"),
            hight10: String(statistics?.hight10 || "0"),
            hight11: String(statistics?.hight11 || "0"),
            hight12: String(statistics?.hight12 || "0"),
            write_date: statistics?.write_date ? new Date(statistics.write_date).toISOString().split("T")[0] : new Date().toISOString().split("T")[0],
          },
        },
      }),
    };

    APILogger.logSuccess(
      "Profile",
      "獲取會員資料",
      {
        roleType: memberProfile.roleType,
        hasSchoolData: !!memberProfile.member_record,
        contactCount: memberProfile.member_record?.contact?.length || 0,
      },
      "成功獲取會員資料"
    );

    res.json({
      success: true,
      data: memberProfile,
    });
  } catch (error) {
    APILogger.logError("Profile", "獲取會員資料", error, 500);
    res.status(500).json({
      success: false,
      message: "獲取會員資料失敗",
      error: (error as Error).message,
    });
  }
});

// 簡化版基本資料維護 API - 更新會員資料
router.put("/", authenticateToken, async (req: Request, res: Response) => {
  try {
    APILogger.logRequest(req, "Profile", "更新會員資料");

    if (!req.user) {
      APILogger.logError("Profile", "更新會員資料", "使用者未認證", 401);
      return res.status(401).json({
        success: false,
        message: "使用者未認證",
      });
    }

    const updateData = req.body;

    // 獲取 Token
    let userToken = req.userToken;
    if (!userToken) {
      const authHeader = req.headers["authorization"] as string;
      if (authHeader && authHeader.startsWith("Bearer ")) {
        userToken = authHeader.substring(7);
      } else {
        userToken = req.headers["x-user-token"] as string;
      }
    }

    if (!userToken) {
      return res.status(401).json({
        success: false,
        message: "缺少 Token",
      });
    }

    // 查詢現有資料
    const memberQuery = `
      SELECT 
        a.AccountId,
        a.IsSchoolPartner as is_school_partner,
        a.IsEpaUser as is_epa_user,
        a.IsGuidanceTeam as is_guidance_team,
        mp.MemberRole as member_role
      FROM Accounts a
      INNER JOIN UserToken ut ON a.AccountId = ut.AccountSid
      LEFT JOIN MemberProfiles mp ON a.AccountId = mp.AccountId AND mp.LocaleCode = 'zh-TW'
      WHERE ut.Token = CAST(@token AS uniqueidentifier)
        AND ut.Status = 1 
        AND a.Status = 1
    `;

    interface UpdateMemberResult {
      AccountId: number;
      is_school_partner: number;
      is_epa_user: number;
      is_guidance_team: number;
      member_role: string;
    }

    const member = await executeQuerySingle<UpdateMemberResult>(memberQuery, { token: userToken });

    if (!member) {
      return res.status(404).json({
        success: false,
        message: "找不到會員資料",
      });
    }

    // 判斷身份並定義允許更新的欄位
    let roleType = "school";
    if (member.is_epa_user === 1) {
      roleType = "epa";
    } else if (member.is_guidance_team === 1) {
      roleType = "tutor";
    }

    console.log(`🔐 [Auth] 用戶身份: ${roleType}, 更新資料:`, updateData);

    // 更新基本會員資料
    const memberUpdateFields: string[] = [];
    const memberUpdateValues: Record<string, string | number> = {};

    if (roleType === "school") {
      // 學校身份可更新的基本欄位映射
      const schoolFieldMapping = {
        member_cname: "MemberName",
        member_email: "MemberEmail",
        member_tel: "MemberTelephone",
        member_address: "MemberAddress",
        member_url: "MemberUrl",
        member_Introduction: "MemberIntroduction",
        member_exchange: "MemberExchange",
      };

      // 構建動態更新語句
      Object.keys(schoolFieldMapping).forEach((field) => {
        if (updateData[field] !== undefined) {
          const dbField = schoolFieldMapping[field as keyof typeof schoolFieldMapping];
          memberUpdateFields.push(`${dbField} = @${field}`);
          memberUpdateValues[field] = updateData[field];
        }
      });

      // 更新 MemberProfile 基本資料
      if (memberUpdateFields.length > 0) {
        memberUpdateValues.AccountId = member.AccountId;
        const memberUpdateQuery = `
          UPDATE MemberProfiles 
          SET ${memberUpdateFields.join(", ")}
          WHERE AccountId = @AccountId AND LocaleCode = 'zh-TW'
        `;
        await executeUpdate(memberUpdateQuery, memberUpdateValues);
      }

      // 處理地址更新邏輯（縣市、區域、詳細地址）
      if (updateData.city_sid !== undefined || updateData.area_sid !== undefined || updateData.member_address !== undefined) {
        // 首先獲取學校ID（使用正確的關聯查詢）
        const schoolQuery = `
          SELECT s.Id 
          FROM Schools s
          INNER JOIN Accounts a ON s.Id = a.SchoolId
          WHERE a.AccountId = @AccountId AND s.Status = 1
        `;
        const schoolResult = (await executeQuerySingle(schoolQuery, { AccountId: member.AccountId })) as { Id: string } | null;

        if (schoolResult && schoolResult.Id) {
          const schoolId = schoolResult.Id;
          console.log(`🌍 [Address] 找到學校ID: ${schoolId}`);

          // 1. 更新 Schools 表的縣市和區域資訊
          const schoolUpdateFields: string[] = [];
          const schoolUpdateValues: Record<string, string | number> = { schoolId };

          if (updateData.city_sid !== undefined) {
            const cityId = parseInt(updateData.city_sid);
            if (!isNaN(cityId) && cityId > 0) {
              schoolUpdateFields.push("CountyId = @city_sid");
              schoolUpdateValues.city_sid = cityId;
              console.log(`🌍 [Address] 縣市更新: ${cityId}`);
            }
          }

          if (updateData.area_sid !== undefined) {
            const areaId = parseInt(updateData.area_sid);
            if (!isNaN(areaId) && areaId > 0) {
              schoolUpdateFields.push("DistrictId = @area_sid");
              schoolUpdateValues.area_sid = areaId;
              console.log(`🌍 [Address] 區域更新: ${areaId}`);
            }
          }

          // 執行 Schools 表更新
          if (schoolUpdateFields.length > 0) {
            const schoolUpdateQuery = `
              UPDATE Schools 
              SET ${schoolUpdateFields.join(", ")}
              WHERE Id = @schoolId
            `;

            try {
              await executeQuery(schoolUpdateQuery, schoolUpdateValues);
              console.log(`✅ [Address] Schools 表更新成功`);
            } catch (error) {
              console.error("🌍 [Address] Schools 表更新失敗:", error);
            }
          }

          // 2. 更新 SchoolContents 表的詳細地址（多語言）
          if (updateData.member_address !== undefined) {
            const addressUpdateQueries = [
              {
                locale: "zh-TW",
                query: `
                  UPDATE SchoolContents 
                  SET Address = @address
                  WHERE SchoolId = @schoolId AND LocaleCode = 'zh-TW'
                `,
              },
              {
                locale: "en",
                query: `
                  UPDATE SchoolContents 
                  SET Address = @address
                  WHERE SchoolId = @schoolId AND LocaleCode = 'en'
                `,
              },
            ];

            for (const addressUpdate of addressUpdateQueries) {
              try {
                await executeQuery(addressUpdate.query, {
                  address: updateData.member_address,
                  schoolId,
                });
                console.log(`✅ [Address] SchoolContents 地址更新成功 (${addressUpdate.locale})`);
              } catch (error) {
                console.error(`❌ [Address] SchoolContents 地址更新失敗 (${addressUpdate.locale}):`, error);
              }
            }
          }
        } else {
          console.log("⚠️ [Address] 找不到對應的學校記錄");
        }
      }

      // 🆕 學校身份：先處理使用者個人基本資料
      const personalFields = [
        "job_cname",
        "member_cname",
        "member_cname_en",
        "member_tel",
        "member_phone",
        "member_email",
        "place_cname",
        "member_url",
        "member_Introduction",
        "member_exchange",
        "detail_address", // 🆕 支援詳細地址
      ];

      const zhTwFields: string[] = [];
      const zhTwValues: Record<string, string | number> = {};
      const enFields: string[] = [];
      const enValues: Record<string, string | number> = {};

      // 處理個人資料欄位
      personalFields.forEach((field) => {
        if (updateData[field] !== undefined) {
          if (field === "member_cname_en") {
            // 英文姓名只更新英文記錄
            enFields.push(`MemberName = @${field}`);
            enValues[field] = updateData[field];
          } else {
            // 其他欄位更新中文記錄
            const dbField = {
              job_cname: "JobName",
              member_cname: "MemberName",
              member_tel: "MemberTelephone",
              member_phone: "MemberPhone",
              member_email: "MemberEmail",
              place_cname: "PlaceName",
              member_url: "MemberUrl",
              member_Introduction: "MemberIntroduction",
              member_exchange: "MemberExchange",
              detail_address: "MemberAddress", // 🆕 詳細地址映射到個人地址
            }[field];

            if (dbField) {
              zhTwFields.push(`${dbField} = @${field}`);
              zhTwValues[field] = updateData[field];
            }
          }
        }
      });

      // 更新中文記錄
      if (zhTwFields.length > 0) {
        zhTwValues.AccountId = member.AccountId;
        const zhTwUpdateQuery = `
          UPDATE MemberProfiles 
          SET ${zhTwFields.join(", ")} 
          WHERE AccountId = @AccountId AND LocaleCode = 'zh-TW'
        `;
        console.log(`🔧 [School User] 更新使用者個人資料 (中文):`, zhTwUpdateQuery);
        await executeUpdate(zhTwUpdateQuery, zhTwValues);
        console.log(`✅ [School User] 使用者個人資料 (中文) 更新完成`);
      }

      // 處理英文記錄
      if (enFields.length > 0) {
        // 檢查英文記錄是否存在
        const enRecordQuery = `
          SELECT MemberProfileId FROM MemberProfiles 
          WHERE AccountId = @AccountId AND LocaleCode = 'en'
        `;
        const enRecord = await executeQuerySingle<{ MemberProfileId: number }>(enRecordQuery, { AccountId: member.AccountId });

        if (enRecord) {
          // 更新現有英文記錄
          enValues.AccountId = member.AccountId;
          const enUpdateQuery = `
            UPDATE MemberProfiles 
            SET ${enFields.join(", ")} 
            WHERE AccountId = @AccountId AND LocaleCode = 'en'
          `;
          console.log(`🔧 [School User] 更新使用者個人資料 (英文):`, enUpdateQuery);
          await executeUpdate(enUpdateQuery, enValues);
          console.log(`✅ [School User] 使用者個人資料 (英文) 更新完成`);
        } else {
          // 創建新的英文記錄
          console.log(`➕ [School User] 創建新的使用者英文記錄`);
          interface ZhMemberRecordForInsert {
            MemberEmail: string;
            MemberTelephone: string;
            MemberPhone: string;
            JobName: string;
            PlaceName: string;
            MemberRole: string;
            isuse: number;
          }

          const zhRecord = await executeQuerySingle<ZhMemberRecordForInsert>(
            `SELECT * FROM MemberProfiles WHERE AccountId = @AccountId AND LocaleCode = 'zh-TW'`,
            {
              AccountId: member.AccountId,
            }
          );

          if (zhRecord) {
            const insertEnQuery = `
              INSERT INTO MemberProfiles (
                AccountId, LocaleCode, MemberName, MemberEmail, MemberTelephone, 
                MemberPhone, JobName, PlaceName, MemberRole, isuse, CreatedTime, UpdatedTime
              ) VALUES (
                @AccountId, 'en', @member_cname_en, @MemberEmail, @MemberTelephone,
                @MemberPhone, @JobName, @PlaceName, @MemberRole, @isuse, GETDATE(), GETDATE()
              )
            `;

            const insertParams = {
              AccountId: member.AccountId,
              member_cname_en: updateData.member_cname_en,
              MemberEmail: zhRecord.MemberEmail || "",
              MemberTelephone: zhRecord.MemberTelephone || "",
              MemberPhone: zhRecord.MemberPhone || "",
              JobName: zhRecord.JobName || "",
              PlaceName: zhRecord.PlaceName || "",
              MemberRole: zhRecord.MemberRole || "",
              isuse: zhRecord.isuse || 1,
            };

            await executeUpdate(insertEnQuery, insertParams);
            console.log(`✅ [School User] 使用者英文記錄創建完成`);
          }
        }
      }

      // 🆕 處理學校基本資料更新（中英文名稱）
      if (updateData.place_cname || updateData.place_cname_en) {
        console.log(`🏫 [School] 學校基本資料更新: 中文名稱=${updateData.place_cname}, 英文名稱=${updateData.place_cname_en}`);

        // 查詢學校 ID（使用正確的關聯查詢）
        const schoolQuery = `
          SELECT s.Id 
          FROM Schools s
          INNER JOIN Accounts a ON s.Id = a.SchoolId
          WHERE a.AccountId = @AccountId AND s.Status = 1
        `;
        const schoolResult = await executeQuerySingle<{ Id: string }>(schoolQuery, { AccountId: member.AccountId });

        if (schoolResult) {
          const schoolId = schoolResult.Id;

          // 更新中文學校名稱
          if (updateData.place_cname) {
            const updateSchoolZhQuery = `
              UPDATE SchoolContents 
              SET Name = @place_cname, UpdatedTime = GETDATE()
              WHERE SchoolId = @schoolId AND LocaleCode = 'zh-TW'
            `;
            await executeUpdate(updateSchoolZhQuery, {
              schoolId: schoolId,
              place_cname: updateData.place_cname,
            });
            console.log(`✅ [School] 學校中文名稱更新完成: ${updateData.place_cname}`);
          }

          // 更新英文學校名稱
          if (updateData.place_cname_en) {
            // 檢查英文記錄是否存在
            const enSchoolQuery = `SELECT COUNT(*) as recordCount FROM SchoolContents WHERE SchoolId = @schoolId AND LocaleCode = 'en'`;
            const enSchoolRecord = await executeQuerySingle<{ recordCount: number }>(enSchoolQuery, { schoolId: schoolId });

            if (enSchoolRecord && enSchoolRecord.recordCount > 0) {
              // 更新現有英文記錄
              const updateSchoolEnQuery = `
                UPDATE SchoolContents 
                SET Name = @place_cname_en, UpdatedTime = GETDATE()
                WHERE SchoolId = @schoolId AND LocaleCode = 'en'
              `;
              await executeUpdate(updateSchoolEnQuery, {
                schoolId: schoolId,
                place_cname_en: updateData.place_cname_en,
              });
              console.log(`✅ [School] 學校英文名稱更新完成: ${updateData.place_cname_en}`);
            } else {
              // 創建新的英文記錄
              console.log(`➕ [School] 創建新的學校英文記錄`);
              const insertSchoolEnQuery = `
                INSERT INTO SchoolContents (SchoolId, LocaleCode, Name, CreatedTime, UpdatedTime)
                VALUES (@schoolId, 'en', @place_cname_en, GETDATE(), GETDATE())
              `;
              await executeUpdate(insertSchoolEnQuery, {
                schoolId: schoolId,
                place_cname_en: updateData.place_cname_en,
              });
              console.log(`✅ [School] 學校英文記錄創建完成: ${updateData.place_cname_en}`);
            }
          }
        }
      }

      // 處理學校特有的資料更新
      const schoolRecordData = updateData.member_record_data;
      if (schoolRecordData) {
        console.log(`📊 [Data] 學校詳細資料更新:`, schoolRecordData);

        // 查詢學校 ID（使用正確的關聯查詢）
        const schoolQuery = `
          SELECT s.Id 
          FROM Schools s
          INNER JOIN Accounts a ON s.Id = a.SchoolId
          WHERE a.AccountId = @AccountId AND s.Status = 1
        `;
        const schoolResult = await executeQuerySingle<{ Id: string }>(schoolQuery, { AccountId: member.AccountId });

        if (schoolResult) {
          const schoolId = schoolResult.Id;

          // 更新校長資訊
          if (schoolRecordData.principal_cname || schoolRecordData.principal_tel || schoolRecordData.principal_phone || schoolRecordData.principal_email) {
            const principalInsertOrUpdateQuery = `
              DECLARE @existingCount INT;
              SELECT @existingCount = COUNT(*) FROM SchoolPrincipals WHERE SchoolId = @schoolId AND Status = 1;

              IF @existingCount = 0 
              BEGIN
                INSERT INTO SchoolPrincipals (SchoolId, PrincipalName, PrincipalPhone, PrincipalMobile, PrincipalEmail, Status, CreatedTime, UpdatedTime)
                VALUES (@schoolId, @principal_cname, @principal_tel, @principal_phone, @principal_email, 1, GETDATE(), GETDATE());
              END
              ELSE
              BEGIN
                UPDATE SchoolPrincipals
                SET PrincipalName = @principal_cname,
                    PrincipalPhone = @principal_tel,
                    PrincipalMobile = @principal_phone,
                    PrincipalEmail = @principal_email,
                    UpdatedTime = GETDATE()
                WHERE SchoolId = @schoolId AND Status = 1;
              END
            `;

            await executeUpdate(principalInsertOrUpdateQuery, {
              schoolId: schoolId,
              principal_cname: schoolRecordData.principal_cname || "",
              principal_tel: schoolRecordData.principal_tel || "",
              principal_phone: schoolRecordData.principal_phone || "",
              principal_email: schoolRecordData.principal_email || "",
            });
          }

          // 🆕 更新聯絡人資料（包含刪除、更新、新增）
          if (schoolRecordData.contact && Array.isArray(schoolRecordData.contact)) {
            console.log(`📞 [Contact] 開始更新 ${schoolRecordData.contact.length} 個聯絡人資料`);

            // 1. 查詢資料庫中現有的聯絡人
            const existingContactsQuery = `
              SELECT Id, ContactName 
              FROM SchoolContacts 
              WHERE SchoolId = @schoolId AND Status = 1
            `;

            interface ExistingContact {
              Id: number;
              ContactName: string;
            }

            const existingContacts = await executeQuery<ExistingContact>(existingContactsQuery, { schoolId: schoolId });

            // 2. 收集前端發送的聯絡人 ID（字串格式，因為前端發送的是字串）
            const frontendContactIds = schoolRecordData.contact
              .filter((contact) => contact.contact_sid && contact.contact_sid !== "")
              .map((contact) => String(contact.contact_sid)); // 使用字串格式

            console.log(`📞 [Contact] 現有聯絡人 ${existingContacts.length} 個，前端聯絡人 ${frontendContactIds.length} 個`);
            console.log(`📞 [Contact] 前端聯絡人 ID 列表: [${frontendContactIds.join(", ")}]`);

            // 輸出現有聯絡人詳細資訊
            if (existingContacts && existingContacts.length > 0) {
              console.log(`📞 [Contact] 現有聯絡人詳細資訊:`);
              existingContacts.forEach((contact) => {
                console.log(`  - ${contact.ContactName} (ID: ${contact.Id}, 類型: ${typeof contact.Id})`);
              });
            }

            // 3. 刪除不在前端列表中的聯絡人
            if (existingContacts && existingContacts.length > 0) {
              for (const existingContact of existingContacts) {
                const existingContactIdStr = String(existingContact.Id);
                const shouldKeep = frontendContactIds.includes(existingContactIdStr);

                console.log(`📞 [Contact] 檢查聯絡人: ${existingContact.ContactName} (ID: ${existingContactIdStr})`);
                console.log(`📞 [Contact] 是否保留: ${shouldKeep} (在前端列表中: ${frontendContactIds.includes(existingContactIdStr)})`);

                if (!shouldKeep) {
                  console.log(`🗑️ [Contact] 刪除聯絡人: ${existingContact.ContactName} (ID: ${existingContact.Id})`);

                  const deleteContactQuery = `
                    UPDATE SchoolContacts 
                    SET Status = 0, UpdatedTime = GETDATE()
                    WHERE Id = @contact_id AND SchoolId = @schoolId
                  `;

                  await executeUpdate(deleteContactQuery, {
                    contact_id: existingContact.Id,
                    schoolId: schoolId,
                  });

                  console.log(`✅ [Contact] 聯絡人已刪除: ${existingContact.ContactName}`);
                } else {
                  console.log(`✅ [Contact] 聯絡人保留: ${existingContact.ContactName}`);
                }
              }
            }

            // 4. 更新或新增聯絡人資料
            for (let i = 0; i < schoolRecordData.contact.length; i++) {
              const contact = schoolRecordData.contact[i];
              const contactSid = contact.contact_sid;

              console.log(`📞 [Contact] 處理聯絡人 ${i + 1}: ${contact.contact_cname} (ID: ${contactSid})`);

              if (contactSid && contactSid !== "") {
                // 更新現有聯絡人
                const contactUpdateQuery = `
                  UPDATE SchoolContacts 
                  SET ContactName = @contact_cname,
                      JobTitle = @contact_job_title,
                      ContactPhone = @contact_tel,
                      ContactMobile = @contact_phone,
                      ContactEmail = @contact_email,
                      SortOrder = @sort_order,
                      UpdatedTime = GETDATE()
                  WHERE Id = @contact_sid AND SchoolId = @schoolId AND Status = 1
                `;

                await executeUpdate(contactUpdateQuery, {
                  schoolId: schoolId,
                  contact_sid: parseInt(contactSid),
                  contact_cname: contact.contact_cname || "",
                  contact_job_title: contact.contact_job_title || "",
                  contact_tel: contact.contact_tel || "",
                  contact_phone: contact.contact_phone || "",
                  contact_email: contact.contact_email || "",
                  sort_order: i + 1,
                });

                console.log(`✅ [Contact] 聯絡人 ${contact.contact_cname} 更新成功`);
              } else {
                // 新增聯絡人 (如果沒有 contact_sid 或為空字串)
                console.log(`➕ [Contact] 新增聯絡人: ${contact.contact_cname}`);
                const contactInsertQuery = `
                  INSERT INTO SchoolContacts (SchoolId, ContactName, JobTitle, ContactPhone, ContactMobile, ContactEmail, SortOrder, Status, CreatedTime, UpdatedTime)
                  VALUES (@schoolId, @contact_cname, @contact_job_title, @contact_tel, @contact_phone, @contact_email, @sort_order, 1, GETDATE(), GETDATE())
                `;

                await executeUpdate(contactInsertQuery, {
                  schoolId: schoolId,
                  contact_cname: contact.contact_cname || "",
                  contact_job_title: contact.contact_job_title || "",
                  contact_tel: contact.contact_tel || "",
                  contact_phone: contact.contact_phone || "",
                  contact_email: contact.contact_email || "",
                  sort_order: i + 1,
                });

                console.log(`✅ [Contact] 新聯絡人 ${contact.contact_cname} 新增成功`);
              }
            }

            console.log(`📞 [Contact] 聯絡人資料同步完成`);
          }

          // 更新學校統計資料
          if (schoolRecordData.statistics) {
            const stats = schoolRecordData.statistics;
            console.log(`📊 [Statistics] 統計資料更新:`, stats);

            // 🆕 處理高中欄位映射（兼容舊的 hight1-3 和新的 hight10-12）
            const getHighGradeValue = (gradeNum: number): number => {
              // 優先使用正確的欄位名稱 hight10-12
              const correctField = `hight${gradeNum}`;
              if (stats[correctField] !== undefined) {
                return parseInt(stats[correctField]) || 0;
              }

              // 兼容舊的錯誤欄位名稱 hight1-3
              const oldFieldMap = { 10: "hight1", 11: "hight2", 12: "hight3" };
              const oldField = oldFieldMap[gradeNum as keyof typeof oldFieldMap];
              if (oldField && stats[oldField] !== undefined) {
                console.log(`📊 [Statistics] 使用兼容欄位 ${oldField} -> ${correctField}: ${stats[oldField]}`);
                return parseInt(stats[oldField]) || 0;
              }

              return 0;
            };

            const statisticsInsertOrUpdateQuery = `
              DECLARE @existingCount INT;
              SELECT @existingCount = COUNT(*) FROM SchoolStatistics WHERE SchoolId = @schoolId AND Status = 1;

              IF @existingCount = 0 
              BEGIN
                INSERT INTO SchoolStatistics (SchoolId, StaffTotal, Elementary1, Elementary2, Elementary3, Elementary4, Elementary5, Elementary6, Middle7, Middle8, Middle9, High10, High11, High12, WriteDate, Status, CreatedTime, UpdatedTime)
                VALUES (@schoolId, @staff_total, @elementary1, @elementary2, @elementary3, @elementary4, @elementary5, @elementary6, @middle7, @middle8, @middle9, @hight10, @hight11, @hight12, @write_date, 1, GETDATE(), GETDATE());
              END
              ELSE
              BEGIN
              UPDATE SchoolStatistics 
              SET StaffTotal = @staff_total,
                  Elementary1 = @elementary1,
                  Elementary2 = @elementary2,
                  Elementary3 = @elementary3,
                  Elementary4 = @elementary4,
                  Elementary5 = @elementary5,
                  Elementary6 = @elementary6,
                  Middle7 = @middle7,
                  Middle8 = @middle8,
                  Middle9 = @middle9,
                  High10 = @hight10,
                  High11 = @hight11,
                  High12 = @hight12,
                  WriteDate = @write_date,
                  UpdatedTime = GETDATE()
                WHERE SchoolId = @schoolId AND Status = 1;
              END
            `;

            const updateParams = {
              schoolId: schoolId,
              staff_total: parseInt(stats.staff_total) || 0,
              elementary1: parseInt(stats.elementary1) || 0,
              elementary2: parseInt(stats.elementary2) || 0,
              elementary3: parseInt(stats.elementary3) || 0,
              elementary4: parseInt(stats.elementary4) || 0,
              elementary5: parseInt(stats.elementary5) || 0,
              elementary6: parseInt(stats.elementary6) || 0,
              middle7: parseInt(stats.middle7) || 0,
              middle8: parseInt(stats.middle8) || 0,
              middle9: parseInt(stats.middle9) || 0,
              hight10: getHighGradeValue(10),
              hight11: getHighGradeValue(11),
              hight12: getHighGradeValue(12),
              write_date: stats.write_date || new Date().toISOString().split("T")[0],
            };

            console.log(`📊 [Statistics] 更新參數:`, updateParams);
            await executeUpdate(statisticsInsertOrUpdateQuery, updateParams);
            console.log(`📊 [Statistics] 統計資料更新完成`);
          }
        }
      }
    } else {
      // EPA/Tutor 身份的更新邏輯 - 🔧 修正欄位映射與多語系處理
      const epaTutorFieldMapping = {
        job_cname: "JobName",
        member_cname: "MemberName",
        member_cname_en: "MemberName", // 🆕 英文姓名映射
        member_tel: "MemberTelephone",
        member_phone: "MemberPhone",
        member_email: "MemberEmail",
      };

      // 🆕 分別處理中文和英文記錄
      const zhTwFields: string[] = [];
      const zhTwValues: Record<string, string | number> = {};
      const enFields: string[] = [];
      const enValues: Record<string, string | number> = {};

      // 分類處理更新欄位
      Object.keys(epaTutorFieldMapping).forEach((field) => {
        if (updateData[field] !== undefined) {
          const dbField = epaTutorFieldMapping[field as keyof typeof epaTutorFieldMapping];

          if (field === "member_cname_en") {
            // 英文姓名只更新英文記錄
            enFields.push(`${dbField} = @${field}`);
            enValues[field] = updateData[field];
          } else {
            // 其他欄位更新中文記錄
            zhTwFields.push(`${dbField} = @${field}`);
            zhTwValues[field] = updateData[field];
          }
        }
      });

      // 🆕 更新中文記錄
      if (zhTwFields.length > 0) {
        zhTwValues.AccountId = member.AccountId;
        const zhTwUpdateQuery = `
          UPDATE MemberProfiles 
          SET ${zhTwFields.join(", ")} 
          WHERE AccountId = @AccountId AND LocaleCode = 'zh-TW'
        `;
        console.log(`🔧 [EPA/Tutor] 更新中文 MemberProfiles:`, zhTwUpdateQuery);
        console.log(`🔧 [EPA/Tutor] 中文更新參數:`, zhTwValues);
        await executeUpdate(zhTwUpdateQuery, zhTwValues);
        console.log(`✅ [EPA/Tutor] 中文 MemberProfiles 更新完成`);
      }

      // 🆕 處理英文記錄
      if (enFields.length > 0) {
        // 檢查英文記錄是否存在
        const enRecordQuery = `
          SELECT MemberProfileId FROM MemberProfiles 
          WHERE AccountId = @AccountId AND LocaleCode = 'en'
        `;
        const enRecord = await executeQuerySingle<{ MemberProfileId: number }>(enRecordQuery, { AccountId: member.AccountId });

        if (enRecord) {
          // 更新現有英文記錄
          enValues.AccountId = member.AccountId;
          const enUpdateQuery = `
            UPDATE MemberProfiles 
            SET ${enFields.join(", ")} 
            WHERE AccountId = @AccountId AND LocaleCode = 'en'
          `;
          console.log(`🔧 [EPA/Tutor] 更新英文 MemberProfiles:`, enUpdateQuery);
          console.log(`🔧 [EPA/Tutor] 英文更新參數:`, enValues);
          await executeUpdate(enUpdateQuery, enValues);
          console.log(`✅ [EPA/Tutor] 英文 MemberProfiles 更新完成`);
        } else {
          // 創建新的英文記錄
          console.log(`➕ [EPA/Tutor] 創建新的英文記錄`);

          // 獲取中文記錄作為基礎
          interface ZhMemberRecordForEpaTutor {
            MemberEmail: string;
            MemberTelephone: string;
            MemberPhone: string;
            JobName: string;
            PlaceName: string;
            MemberRole: string;
            isuse: number;
          }

          const zhRecord = await executeQuerySingle<ZhMemberRecordForEpaTutor>(
            `
            SELECT * FROM MemberProfiles 
            WHERE AccountId = @AccountId AND LocaleCode = 'zh-TW'
          `,
            { AccountId: member.AccountId }
          );

          if (zhRecord) {
            const insertEnQuery = `
              INSERT INTO MemberProfiles (
                AccountId, LocaleCode, MemberName, MemberEmail, MemberTelephone, 
                MemberPhone, JobName, PlaceName, MemberRole, isuse, CreatedTime, UpdatedTime
              ) VALUES (
                @AccountId, 'en', @member_cname_en, @MemberEmail, @MemberTelephone,
                @MemberPhone, @JobName, @PlaceName, @MemberRole, @isuse, GETDATE(), GETDATE()
              )
            `;

            const insertParams = {
              AccountId: member.AccountId,
              member_cname_en: updateData.member_cname_en,
              MemberEmail: zhRecord.MemberEmail || "",
              MemberTelephone: zhRecord.MemberTelephone || "",
              MemberPhone: zhRecord.MemberPhone || "",
              JobName: zhRecord.JobName || "",
              PlaceName: zhRecord.PlaceName || "",
              MemberRole: zhRecord.MemberRole || "",
              isuse: zhRecord.isuse || 1,
            };

            console.log(`🔧 [EPA/Tutor] 新增英文記錄參數:`, insertParams);
            await executeUpdate(insertEnQuery, insertParams);
            console.log(`✅ [EPA/Tutor] 英文記錄創建完成`);
          }
        }
      }
    }

    // 重新查詢更新後的資料（完整資料包含學校專屬資訊）
    const updatedMemberQuery = `
      SELECT 
        a.AccountId,
        a.Username as account,
        a.email,
        a.Telephone as tel,
        a.phone,
        a.address,
        a.CountyId as account_county_id,
        a.IsSchoolPartner as is_school_partner,
        a.IsEpaUser as is_epa_user,
        a.IsGuidanceTeam as is_guidance_team,
        mp_zh.MemberName as member_cname_zh,
        mp_zh.MemberEmail as member_email,
        mp_zh.MemberTelephone as member_tel,
        mp_zh.MemberPhone as member_phone,
        mp_zh.MemberAddress as member_address,
        mp_zh.MemberUrl as member_url,
        mp_zh.JobName as job_cname,
        mp_zh.PlaceName as place_cname,
        mp_zh.MemberRole as member_role,
        mp_zh.MemberIntroduction as member_Introduction,
        mp_zh.MemberExchange as member_exchange,
        mp_en.MemberName as member_cname_en,
        ct_zh.Name as county_name_zh,
        ct_en.Name as county_name_en,
        -- 學校縣市名稱
        sct_zh.Name as school_county_name_zh,
        sct_en.Name as school_county_name_en,
        -- 學校相關資料
        s.Id as school_id,
        s.SchoolCode as school_code,
        s.Phone as school_phone,
        s.MobilePhone as school_mobile,
        s.Email as school_email,
        s.CountyId as school_county_id,
        s.DistrictId as school_district_id,
        sc_zh.Name as school_name,
        sc_zh.Address as school_address,
        sc_zh.DepartmentName as school_department,
        sc_zh.JobTitle as school_job_title,
        sc_zh.Introduction as school_introduction,
        sc_zh.WebsiteUrl as school_website,
        sc_en.Name as school_name_en,
        sc_en.Address as school_address_en,
        -- 校長資料
        sp.PrincipalName as principal_cname,
        sp.PrincipalPhone as principal_tel,
        sp.PrincipalMobile as principal_phone,
        sp.PrincipalEmail as principal_email
      FROM Accounts a
      INNER JOIN UserToken ut ON a.AccountId = ut.AccountSid
      LEFT JOIN MemberProfiles mp_zh ON a.AccountId = mp_zh.AccountId AND mp_zh.LocaleCode = 'zh-TW'
      LEFT JOIN MemberProfiles mp_en ON a.AccountId = mp_en.AccountId AND mp_en.LocaleCode = 'en'
      LEFT JOIN CountyTranslations ct_zh ON a.CountyId = ct_zh.CountyId AND ct_zh.LocaleCode = 'zh-TW'
      LEFT JOIN CountyTranslations ct_en ON a.CountyId = ct_en.CountyId AND ct_en.LocaleCode = 'en'
      LEFT JOIN Schools s ON a.SchoolId = s.Id
      LEFT JOIN CountyTranslations sct_zh ON s.CountyId = sct_zh.CountyId AND sct_zh.LocaleCode = 'zh-TW'
      LEFT JOIN CountyTranslations sct_en ON s.CountyId = sct_en.CountyId AND sct_en.LocaleCode = 'en'
      LEFT JOIN SchoolContents sc_zh ON s.Id = sc_zh.SchoolId AND sc_zh.LocaleCode = 'zh-TW'
      LEFT JOIN SchoolContents sc_en ON s.Id = sc_en.SchoolId AND sc_en.LocaleCode = 'en'
      LEFT JOIN SchoolPrincipals sp ON s.Id = sp.SchoolId AND sp.Status = 1
      WHERE ut.Token = CAST(@token AS uniqueidentifier)
        AND ut.Status = 1 
        AND a.Status = 1
        AND (ut.ExpireDate IS NULL OR ut.ExpireDate > GETDATE())
    `;

    const updatedMember = await executeQuerySingle<MemberQueryResult>(updatedMemberQuery, { token: userToken });

    if (!updatedMember) {
      return res.status(404).json({
        success: false,
        message: "無法獲取更新後的資料",
      });
    }

    // 重新查詢學校聯絡人和統計資料（如果是學校身份）
    let updatedContacts: ContactResult[] = [];
    let updatedStatistics: StatisticsResult | null = null;

    if (roleType === "school" && updatedMember.school_id) {
      // 查詢聯絡人
      const contactQuery = `
        SELECT 
          ContactName as contact_cname,
          JobTitle as contact_job_title,
          ContactPhone as contact_tel,
          ContactMobile as contact_phone,
          ContactEmail as contact_email,
          Id as contact_sid
        FROM SchoolContacts 
        WHERE SchoolId = @schoolId AND Status = 1
        ORDER BY SortOrder
      `;

      const contactResult = await executeQuery<ContactResult>(contactQuery, { schoolId: updatedMember.school_id });
      updatedContacts = Array.isArray(contactResult) ? contactResult : [];

      // 查詢統計資料
      const statisticsQuery = `
        SELECT 
          StaffTotal as staff_total,
          Elementary1 as elementary1,
          Elementary2 as elementary2,
          Elementary3 as elementary3,
          Elementary4 as elementary4,
          Elementary5 as elementary5,
          Elementary6 as elementary6,
          Middle7 as middle7,
          Middle8 as middle8,
          Middle9 as middle9,
          High10 as hight10,
          High11 as hight11,
          High12 as hight12,
          WriteDate as write_date,
          Id as school_statistics_sid
        FROM SchoolStatistics 
        WHERE SchoolId = @schoolId AND Status = 1
      `;

      const statisticsResult = await executeQuerySingle<StatisticsResult>(statisticsQuery, { schoolId: updatedMember.school_id });
      updatedStatistics = statisticsResult || null;
    }

    // 組合更新後的會員資料（完整包含學校專屬資料）
    const updatedMemberProfile = {
      sid: updatedMember.AccountId,
      account: updatedMember.account,
      roleType: roleType,
      member_role: roleType as "school" | "epa" | "tutor", // 新增：符合前端期望的欄位名稱
      // 🔧 修正：使用者個人資料只使用 MemberProfiles 的資料，不 fallback 到學校資料
      member_cname: updatedMember.member_cname_zh || "",
      member_cname_en: updatedMember.member_cname_en || "",
      member_email: updatedMember.member_email || "",
      member_tel: updatedMember.member_tel || "",
      member_phone: updatedMember.member_phone || "",
      member_address: updatedMember.member_address || "",
      member_url: updatedMember.member_url || "",
      job_cname: updatedMember.job_cname || "",
      place_cname: updatedMember.place_cname || updatedMember.school_name || "",
      place_cname_en: updatedMember.school_name_en || "", // 🆕 學校英文名稱
      member_Introduction: updatedMember.member_Introduction || "",
      // 🆕 地址分解資訊（在更新響應中也要包含）
      city_name: updatedMember.school_county_name_zh || updatedMember.county_name_zh || "",
      area_name: "", // TODO: 需要區域名稱查詢
      detail_address: updatedMember.member_address || updatedMember.school_address || updatedMember.address || "",
      // 🆕 校徽資訊（確保更新後響應包含校徽）
      school_logo: updatedMember.school_logo_path || null,
      member_exchange: updatedMember.member_exchange || "0",
      county_id: updatedMember.account_county_id,
      county_name: updatedMember.school_county_name_zh || updatedMember.county_name_zh, // 優先使用學校縣市名稱
      county_name_en: updatedMember.county_name_en || "",
      is_school_partner: updatedMember.is_school_partner,
      is_epa_user: updatedMember.is_epa_user,
      is_guidance_team: updatedMember.is_guidance_team,
      city_sid: updatedMember.school_county_id || updatedMember.account_county_id, // 優先使用學校的縣市資料
      area_sid: updatedMember.school_district_id || null, // 學校的區域資料
      register_review: "已通過" as const, // 新增：與前端 MemberProfile 介面一致
      member_passdate: "", // 新增：與前端 MemberProfile 介面一致
      isuse: 1, // 新增：與前端 MemberProfile 介面一致
      createdate: Math.floor(Date.now() / 1000), // 新增：與前端 MemberProfile 介面一致
      updatedate: Math.floor(Date.now() / 1000), // 新增：與前端 MemberProfile 介面一致

      // 學校身份專用的完整資料
      ...(roleType === "school" && {
        member_record: {
          member_role: "school" as const,
          member_sid: String(updatedMember.AccountId),
          sid: String(updatedMember.school_id || ""),
          school_principal_sid: String(updatedMember.school_id || ""),

          // 基本學校資料
          member_cname: updatedMember.school_name || updatedMember.member_cname_zh || "",
          member_cname_en: updatedMember.school_name_en || updatedMember.member_cname_en || "",
          member_address: updatedMember.school_address || updatedMember.member_address || updatedMember.address || "",
          member_tel: updatedMember.school_phone || updatedMember.member_tel || updatedMember.tel || "",
          member_email: updatedMember.school_email || updatedMember.member_email || updatedMember.email || "",
          member_url: updatedMember.school_website || updatedMember.member_url || "",
          member_Introduction: updatedMember.school_introduction || updatedMember.member_Introduction || "",
          member_exchange: updatedMember.member_exchange || "0",
          file_token_member_pohto: "",

          // 校長資訊
          principal_cname: updatedMember.principal_cname || "",
          principal_tel: updatedMember.principal_tel || "",
          principal_phone: updatedMember.principal_phone || "",
          principal_email: updatedMember.principal_email || "",

          // 聯絡人資訊
          contact_num: String(updatedContacts.length),
          contact: updatedContacts,

          // 學校統計資料 - 🆕 正確組合為 statistics 子物件
          school_statistics_sid: String(updatedStatistics?.school_statistics_sid || ""),
          write_date: updatedStatistics?.write_date
            ? new Date(updatedStatistics.write_date).toISOString().split("T")[0]
            : new Date().toISOString().split("T")[0],
          staff_total: String(updatedStatistics?.staff_total || "0"),
          elementary1: String(updatedStatistics?.elementary1 || "0"),
          elementary2: String(updatedStatistics?.elementary2 || "0"),
          elementary3: String(updatedStatistics?.elementary3 || "0"),
          elementary4: String(updatedStatistics?.elementary4 || "0"),
          elementary5: String(updatedStatistics?.elementary5 || "0"),
          elementary6: String(updatedStatistics?.elementary6 || "0"),
          middle7: String(updatedStatistics?.middle7 || "0"),
          middle8: String(updatedStatistics?.middle8 || "0"),
          middle9: String(updatedStatistics?.middle9 || "0"),
          hight10: String(updatedStatistics?.hight10 || "0"),
          hight11: String(updatedStatistics?.hight11 || "0"),
          hight12: String(updatedStatistics?.hight12 || "0"),

          // 🆕 新增 statistics 子物件，供前端使用
          statistics: {
            staff_total: String(updatedStatistics?.staff_total || "0"),
            elementary1: String(updatedStatistics?.elementary1 || "0"),
            elementary2: String(updatedStatistics?.elementary2 || "0"),
            elementary3: String(updatedStatistics?.elementary3 || "0"),
            elementary4: String(updatedStatistics?.elementary4 || "0"),
            elementary5: String(updatedStatistics?.elementary5 || "0"),
            elementary6: String(updatedStatistics?.elementary6 || "0"),
            middle7: String(updatedStatistics?.middle7 || "0"),
            middle8: String(updatedStatistics?.middle8 || "0"),
            middle9: String(updatedStatistics?.middle9 || "0"),
            hight10: String(updatedStatistics?.hight10 || "0"),
            hight11: String(updatedStatistics?.hight11 || "0"),
            hight12: String(updatedStatistics?.hight12 || "0"),
            write_date: updatedStatistics?.write_date
              ? new Date(updatedStatistics.write_date).toISOString().split("T")[0]
              : new Date().toISOString().split("T")[0],
          },
        },
      }),
    };

    APILogger.logSuccess(
      "Profile",
      "更新會員資料",
      {
        roleType: updatedMemberProfile.roleType,
        updatedFields: Object.keys(updateData).length,
        hasSchoolData: !!updatedMemberProfile.member_record,
        contactCount: updatedMemberProfile.member_record?.contact?.length || 0,
      },
      "會員資料更新成功"
    );

    res.json({
      success: true,
      message: "會員資料更新成功",
      data: updatedMemberProfile,
    });
  } catch (error) {
    APILogger.logError("Profile", "更新會員資料", error, 500);
    res.status(500).json({
      success: false,
      message: "更新會員資料失敗",
      error: (error as Error).message,
    });
  }
});

export default router;
