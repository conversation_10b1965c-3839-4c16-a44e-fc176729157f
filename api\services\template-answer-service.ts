// ========== 模板答案服務 - 業務邏輯和資料操作 ==========

import {
  TemplateAnswerSaveRequest,
  TemplateAnswerValidateRequest,
  TemplateAnswerValidationResult,
  TemplateAnswerSaveResult,
} from "../models/template-answer.js";
import {
  REQUIRED_PARAMETERS,
  TEMPLATE_ANSWER_ERROR_MESSAGES,
  validateTemplateAnswer,
  standardizeAnswerFormat,
  checkRequiredParameters,
  formatSaveResult,
  isSupportedTemplate,
} from "../constants/template-answer.js";

export class TemplateAnswerService {
  // 保存模板答案
  static async saveTemplateAnswer(request: TemplateAnswerSaveRequest): Promise<TemplateAnswerSaveResult> {
    try {
      const { certification_sid, question_sid, template_id, answer_data } = request;

      // 驗證必要參數
      const missingParams = checkRequiredParameters(request, REQUIRED_PARAMETERS.SAVE);
      if (missingParams.length > 0) {
        throw new Error(`${TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS}: ${missingParams.join(", ")}`);
      }

      // 檢查模板ID是否支援
      if (!isSupportedTemplate(template_id)) {
        throw new Error(`${TEMPLATE_ANSWER_ERROR_MESSAGES.UNKNOWN_TEMPLATE}: ${template_id}`);
      }

      // 根據模板ID驗證答案格式
      const validationResult = this.validateTemplateAnswer(template_id, answer_data);

      if (!validationResult.isValid) {
        const error = new Error(TEMPLATE_ANSWER_ERROR_MESSAGES.VALIDATION_FAILED);
        (error as any).validationErrors = validationResult.errors;
        throw error;
      }

      // 生成標準化的答案 JSON
      const standardizedAnswer = this.standardizeAnswerFormat(template_id, answer_data);

      // 格式化返回結果
      return formatSaveResult(request, standardizedAnswer, validationResult);
    } catch (error) {
      console.error("❌ [TemplateAnswerService] 保存模板答案失敗:", error);
      throw error;
    }
  }

  // 驗證模板答案格式
  static async validateTemplateAnswerFormat(request: TemplateAnswerValidateRequest): Promise<TemplateAnswerValidationResult> {
    try {
      const { template_id, answer_data } = request;

      // 驗證必要參數
      const missingParams = checkRequiredParameters(request, REQUIRED_PARAMETERS.VALIDATE);
      if (missingParams.length > 0) {
        throw new Error(`${TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS}: ${missingParams.join(", ")}`);
      }

      // 檢查模板ID是否支援
      if (!isSupportedTemplate(template_id)) {
        throw new Error(`${TEMPLATE_ANSWER_ERROR_MESSAGES.UNKNOWN_TEMPLATE}: ${template_id}`);
      }

      // 執行驗證
      return this.validateTemplateAnswer(template_id, answer_data);
    } catch (error) {
      console.error("❌ [TemplateAnswerService] 驗證模板答案失敗:", error);
      throw error;
    }
  }

  // ========== 私有輔助方法 ==========

  // 驗證模板答案
  private static validateTemplateAnswer(templateId: number, answerData: Record<string, unknown>): TemplateAnswerValidationResult {
    return validateTemplateAnswer(templateId, answerData);
  }

  // 標準化答案格式
  private static standardizeAnswerFormat(templateId: number, answerData: Record<string, unknown>): Record<string, unknown> {
    return standardizeAnswerFormat(templateId, answerData);
  }
}
