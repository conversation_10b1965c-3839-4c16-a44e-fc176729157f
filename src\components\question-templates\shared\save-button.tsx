import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Save, Loader2 } from "lucide-react";

// 保存按鈕組件（提供給問題層級使用）
interface SaveButtonProps<T> {
  data: T;
  templateId: number;
  certificationId?: number;
  questionId?: number;
  questionTitle?: string;
  disabled?: boolean;
  onSave?: (data: T) => Promise<void>;
}

export const SaveButton = <T,>({ data, templateId, certificationId, questionId, questionTitle, disabled = false, onSave }: SaveButtonProps<T>) => {
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: "success" | "error"; text: string; details?: string } | null>(null);

  const handleSave = async () => {
    // 修正驗證邏輯：檢查 null/undefined 而非所有 falsy 值，因為 ID 可能為 0
    if (certificationId == null || questionId == null) {
      setSaveMessage({
        type: "error",
        text: "缺少必要的認證ID或問題ID",
        details: `認證ID: ${certificationId}, 問題ID: ${questionId}`,
      });
      return;
    }

    setIsSaving(true);
    setSaveMessage(null);

    try {
      if (onSave) {
        // 使用自定義保存函數
        await onSave(data);
        setSaveMessage({
          type: "success",
          text: "答案保存成功",
          details: `✅ 已透過自定義保存函數處理`,
        });
      } else {
        // 使用 certificationAPI 服務保存答案
        const { certificationAPI } = await import("../../../services/certificationAPI");
        const { formatAnswerForSave } = await import("./format-answer");

        // 格式化答案數據
        const formattedData = formatAnswerForSave(templateId, data);

        // 調用 certificationAPI 服務保存答案
        const response = await certificationAPI.saveCertificationAnswer({
          certificationId: certificationId, // 對應到 CertificationAnswers.CertificationId
          questionId: questionId, // 對應到 CertificationAnswers.QuestionId
          answerData: formattedData as Record<string, unknown>, // 對應到 CertificationAnswers.AnswerText (JSON)
          templateId: templateId,
        });

        if (response.success) {
          setSaveMessage({
            type: "success",
            text: "答案保存成功",
            details: `✅ ${response.data?.message}\n📝 答案ID: ${response.data?.answerId}\n🆔 認證ID: ${certificationId} → CertificationAnswers.CertificationId\n❓ 問題ID: ${questionId} → CertificationAnswers.QuestionId`,
          });
        } else {
          setSaveMessage({
            type: "error",
            text: response.message || "保存失敗",
            details: `❌ 保存到 CertificationAnswers 表失敗\n認證ID: ${certificationId}\n問題ID: ${questionId}`,
          });
        }
      }
    } catch (error) {
      setSaveMessage({
        type: "error",
        text: error instanceof Error ? error.message : "保存失敗",
        details: `❌ 網路錯誤或伺服器錯誤\n認證ID: ${certificationId}\n問題ID: ${questionId}\n錯誤: ${error instanceof Error ? error.message : "未知錯誤"}`,
      });
    } finally {
      setIsSaving(false);
      // 7秒後清除消息
      setTimeout(() => setSaveMessage(null), 7000);
    }
  };

  return (
    <div className="flex flex-col gap-2">
      <Button onClick={handleSave} disabled={disabled || isSaving} className="w-full" variant="default">
        {isSaving ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Save className="w-4 h-4 mr-2" />}
        {isSaving ? "保存中..." : "保存答案"}
      </Button>

      {saveMessage && (
        <Alert className={saveMessage.type === "success" ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
          <AlertDescription className={saveMessage.type === "success" ? "text-green-800" : "text-red-800"}>
            <div className="mb-2">{saveMessage.text}</div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
