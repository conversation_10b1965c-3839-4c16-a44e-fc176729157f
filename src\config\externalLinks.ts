// 外部連結設定檔
// 統一管理所有外部連結，方便未來調整
// 連結可透過環境變數設定，以便在不同環境中靈活調整

export interface ExternalLinks {
  // 認證相關連結
  certification: {
    applicationGuide: string; // 認證申請說明
    standardsInfo: string; // 認證標準資訊
    processFlow: string; // 認證流程圖
  };

  // 輔導相關連結
  guidance: {
    applicationForm: string; // 輔導申請表單
    contactInfo: string; // 聯絡資訊
  };

  // 官方網站連結
  official: {
    mainWebsite: string; // 主要官網
    newsPage: string; // 最新消息
    resources: string; // 資源頁面
  };

  // 社群媒體連結
  social: {
    facebook: string; // Facebook 粉絲專頁
    line: string; // LINE 官方帳號
  };

  // 資源圖片連結
  assets: {
    medalImages: {
      bronze: string; // 銅牌圖示
      silver: string; // 銀牌圖示
      green: string; // 綠旗圖示
    };
  };
}

// 預設連結（作為環境變數的備用值）
const DEFAULT_LINKS = {
  certification: {
    applicationGuide: "https://ecocampus.sumire.com.tw/front/page_certifaction_intro",
    standardsInfo: "https://ecocampus.sumire.com.tw/front/page_certifaction_intro#standards",
    processFlow: "https://ecocampus.sumire.com.tw/front/page_certifaction_intro#process",
  },
  guidance: {
    applicationForm: "https://reurl.cc/ZGmkXA",
    contactInfo: "https://ecocampus.sumire.com.tw/front/contact",
  },
  official: {
    mainWebsite: "https://ecocampus.sumire.com.tw",
    newsPage: "https://ecocampus.sumire.com.tw/front/news",
    resources: "https://ecocampus.sumire.com.tw/front/resources",
  },
  social: {
    facebook: "https://www.facebook.com/taiwanecoschools",
    line: "https://line.me/R/ti/p/@ecoschool",
  },
  assets: {
    medalImages: {
      bronze: "https://ecocampus.sumire.com.tw/assets/images/medal-copper.png",
      silver: "https://ecocampus.sumire.com.tw/assets/images/medal-silver.png",
      green: "https://ecocampus.sumire.com.tw/assets/images/medal-green.png",
    },
  },
};

// 從環境變數讀取連結，如果沒有設定則使用預設值
export const EXTERNAL_LINKS: ExternalLinks = {
  certification: {
    applicationGuide: import.meta.env.VITE_CERTIFICATION_APPLICATION_GUIDE || DEFAULT_LINKS.certification.applicationGuide,
    standardsInfo: import.meta.env.VITE_CERTIFICATION_STANDARDS_INFO || DEFAULT_LINKS.certification.standardsInfo,
    processFlow: import.meta.env.VITE_CERTIFICATION_PROCESS_FLOW || DEFAULT_LINKS.certification.processFlow,
  },

  guidance: {
    applicationForm: import.meta.env.VITE_GUIDANCE_APPLICATION_FORM || DEFAULT_LINKS.guidance.applicationForm,
    contactInfo: import.meta.env.VITE_GUIDANCE_CONTACT_INFO || DEFAULT_LINKS.guidance.contactInfo,
  },

  official: {
    mainWebsite: import.meta.env.VITE_OFFICIAL_MAIN_WEBSITE || DEFAULT_LINKS.official.mainWebsite,
    newsPage: import.meta.env.VITE_OFFICIAL_NEWS_PAGE || DEFAULT_LINKS.official.newsPage,
    resources: import.meta.env.VITE_OFFICIAL_RESOURCES || DEFAULT_LINKS.official.resources,
  },

  social: {
    facebook: import.meta.env.VITE_SOCIAL_FACEBOOK || DEFAULT_LINKS.social.facebook,
    line: import.meta.env.VITE_SOCIAL_LINE || DEFAULT_LINKS.social.line,
  },

  assets: {
    medalImages: {
      bronze: import.meta.env.VITE_ASSET_MEDAL_BRONZE || DEFAULT_LINKS.assets.medalImages.bronze,
      silver: import.meta.env.VITE_ASSET_MEDAL_SILVER || DEFAULT_LINKS.assets.medalImages.silver,
      green: import.meta.env.VITE_ASSET_MEDAL_GREEN || DEFAULT_LINKS.assets.medalImages.green,
    },
  },
};

// 工具函數：在新分頁開啟外部連結
export const openExternalLink = (url: string, windowName?: string): void => {
  window.open(url, windowName || "_blank", "noopener,noreferrer");
};

// 工具函數：取得特定類別的連結
export const getCertificationLinks = () => EXTERNAL_LINKS.certification;
export const getGuidanceLinks = () => EXTERNAL_LINKS.guidance;
export const getOfficialLinks = () => EXTERNAL_LINKS.official;
export const getSocialLinks = () => EXTERNAL_LINKS.social;
export const getAssetLinks = () => EXTERNAL_LINKS.assets;

// 調試工具：檢查是否使用了環境變數（僅開發環境）
export const debugExternalLinks = () => {
  if (import.meta.env.DEV) {
    console.group("🔗 External Links Configuration");
    console.log("認證申請說明:", EXTERNAL_LINKS.certification.applicationGuide);
    console.log("輔導申請表單:", EXTERNAL_LINKS.guidance.applicationForm);
    console.log("銅牌圖示:", EXTERNAL_LINKS.assets.medalImages.bronze);

    // 檢查哪些連結來自環境變數
    const envVars = [
      { name: "VITE_CERTIFICATION_APPLICATION_GUIDE", value: import.meta.env.VITE_CERTIFICATION_APPLICATION_GUIDE },
      { name: "VITE_GUIDANCE_APPLICATION_FORM", value: import.meta.env.VITE_GUIDANCE_APPLICATION_FORM },
      { name: "VITE_ASSET_MEDAL_BRONZE", value: import.meta.env.VITE_ASSET_MEDAL_BRONZE },
    ];

    console.log("環境變數狀態:");
    envVars.forEach(({ name, value }) => {
      console.log(`  ${name}: ${value ? "✅ 已設定" : "❌ 使用預設值"}`);
    });
    console.groupEnd();
  }
};

/**
 * 使用說明：
 *
 * 1. 複製 env.example 為 .env
 * 2. 修改 .env 中的外部連結變數
 * 3. 重新啟動開發服務器
 *
 * 環境變數命名規則：
 * - 必須以 VITE_ 開頭（Vite 要求）
 * - 使用大寫字母和底線
 * - 按類別分組：CERTIFICATION_、GUIDANCE_、OFFICIAL_、SOCIAL_、ASSET_
 *
 * 範例：
 * VITE_CERTIFICATION_APPLICATION_GUIDE=https://your-custom-domain.com/guide
 * VITE_GUIDANCE_APPLICATION_FORM=https://your-form-url.com
 */
