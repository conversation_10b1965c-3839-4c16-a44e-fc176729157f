import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate, useParams } from "react-router-dom";
import { ExternalLink, RefreshCw } from "lucide-react";
import { getApiBaseUrl } from "@/config/environment";
import { useAuthService } from "@/services/authService";
import { buildFrontendUrl } from "@/utils/pathUtils";
import { certificationAPI } from "@/services/certificationAPI";

// Application status types+
//  認證申請 有iframe的頁面
type ApplicationStatus = "no-status" | "modify" | "accepted" | "returned" | "verifying";

interface StatusInfo {
  label: string;
  icon: string;
  description: string;
}

interface ApplicationData {
  id: number;
  certificationType: string;
  status: ApplicationStatus;
  submissionDate: string | null;
  lastModified: Date;
}

const statusMapping: Record<ApplicationStatus, StatusInfo> = {
  "no-status": {
    label: "未送審",
    icon: "/assets/images/license-icon-no-status.svg",
    description: "尚未提交審核",
  },
  modify: {
    label: "待補件",
    icon: "/assets/images/license-icon-modify.svg",
    description: "需要補充相關文件",
  },
  accepted: {
    label: "審核通過",
    icon: "/assets/images/license-icon-accepted.svg",
    description: "申請已通過審核",
  },
  returned: {
    label: "已退件",
    icon: "/assets/images/license-icon-returned.svg",
    description: "申請已被退回",
  },
  verifying: {
    label: "審核中",
    icon: "/assets/images/license-icon-verifying.svg",
    description: "正在進行審核",
  },
};

const certificationTypeNames: Record<string, string> = {
  bronze: "銅牌",
  silver: "銀牌",
  green_flag: "綠旗",
  "green-flag-r1": "綠旗R1",
  "green-flag-r2": "綠旗R2",
  "green-flag-r3": "綠旗R3",
};

// Main Component
const CertificationApplicationPage = () => {
  const navigate = useNavigate();
  const { certificationId } = useParams<{ certificationId: string }>();
  const authService = useAuthService();

  // State
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    id: parseInt(certificationId || "0"),
    certificationType: "bronze", // 將從 API 載入
    status: "no-status",
    submissionDate: null,
    lastModified: new Date(),
  });

  // 載入申請資料
  useEffect(() => {
    const loadApplicationData = async () => {
      if (!certificationId) {
        setError("缺少認證 ID");
        setIsLoading(false);
        return;
      }

      console.log("🔍 [認證申請頁面] 載入認證資料:", certificationId);
      setIsLoading(true);
      setError(null);

      try {
        // 檢查認證狀態
        if (!authService.isAuthenticated()) {
          throw new Error("用戶未登入，請先登入");
        }

        // 使用 certificationAPI 服務
        const result = await certificationAPI.getCertification(parseInt(certificationId));

        if (!result.success) {
          throw new Error(result.message || "載入認證資料失敗");
        }

        const certData = result.data;

        // 根據 ReviewStatus 判斷狀態
        let status: ApplicationStatus = "no-status";
        if (certData.ReviewStatus === 0) {
          status = "verifying"; // 審核中
        } else if (certData.ReviewStatus === 1) {
          status = "accepted"; // 已通過
        } else if (certData.ReviewStatus === 2) {
          status = "returned"; // 已退件
        } else if (certData.ReviewStatus === 3) {
          status = "modify"; // 待補件
        }

        // 根據 Level 判斷認證類型
        let certificationType = "bronze";
        if (certData.Level === 2) certificationType = "bronze";
        else if (certData.Level === 3) certificationType = "silver";
        else if (certData.Level === 4) certificationType = "green_flag";
        else if (certData.Level === 5) certificationType = "green-flag-r1";
        else if (certData.Level === 6) certificationType = "green-flag-r2";
        else if (certData.Level === 7) certificationType = "green-flag-r3";

        setApplicationData({
          id: parseInt(certificationId),
          certificationType,
          status,
          submissionDate: certData.ApplyDate || null,
          lastModified: new Date(certData.UpdatedTime || certData.CreatedTime),
        });

        console.log("✅ [認證申請頁面] 認證資料載入成功:", {
          status,
          certificationType,
        });
        setIsLoading(false);
      } catch (err) {
        console.error("❌ [認證申請頁面] 載入認證資料失敗:", err);
        setError(err instanceof Error ? err.message : "載入失敗");
        setIsLoading(false);
      }
    };

    loadApplicationData();
  }, [certificationId]);

  // Event handlers
  const handleGoBack = () => {
    navigate("/certificate");
  };

  const handleRefreshIframe = () => {
    const iframe = document.getElementById("certification-iframe") as HTMLIFrameElement;
    if (iframe) {
      const currentSrc = iframe.src;
      iframe.src = "about:blank";
      setTimeout(() => {
        iframe.src = currentSrc;
      }, 100);
    }
  };

  const handleOpenInNewWindow = () => {
    const iframeUrl = constructIframeUrl();
    window.open(iframeUrl, "_blank", "width=1200,height=800");
  };

  const constructIframeUrl = () => {
    const params = new URLSearchParams({
      id: applicationData.id.toString(),
      certType: applicationData.certificationType,
    });

    // 當狀態為審核中或審核通過時，設定為唯讀模式
    if (applicationData.status === "verifying" || applicationData.status === "accepted") {
      params.set("readonly", "true");
    }

    return buildFrontendUrl(`certification-application-iframe?${params.toString()}`);
  };

  // Loading screen
  if (isLoading) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="font-size-lg font-medium mb-2">載入中...</h3>
            <p className="text-gray-600">正在載入認證申請資料</p>
          </CardContent>
        </Card>
      </main>
    );
  }

  // Error screen
  if (error) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <div className="text-red-500 text-xl mb-4">⚠️</div>
            <h3 className="font-size-lg font-medium mb-2 text-red-700">載入失敗</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={() => navigate("/certificate")}>
                返回認證列表
              </Button>
              <Button variant="primary" onClick={() => window.location.reload()}>
                重新載入
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    );
  }

  // 確保資料載入完成後才顯示主界面
  if (applicationData.id === 0) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="font-size-lg font-medium mb-2">準備中...</h3>
            <p className="text-gray-600">正在準備申請表單</p>
          </CardContent>
        </Card>
      </main>
    );
  }

  // Main interface
  const iframeUrl = constructIframeUrl();

  return (
    <main className="min-h-[calc(100vh-60px)] bg-gray-50">
      <div className="container mx-auto px-6 py-6">
        {/* Simple Header Controls */}
        <div className="flex items-center justify-between mb-4">
          <Button variant="outline" onClick={handleGoBack}>
            ← 回到認證申請
          </Button>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleRefreshIframe}>
              <RefreshCw className="w-4 h-4 mr-2" />
              重新載入
            </Button>
            <Button variant="outline" size="sm" onClick={handleOpenInNewWindow}>
              <ExternalLink className="w-4 h-4 mr-2" />
              新視窗開啟
            </Button>
          </div>
        </div>

        {/* Iframe Container */}
        <Card className="w-full">
          <CardContent className="p-0">
            <div className="relative w-full" style={{ height: "calc(100vh - 140px)" }}>
              <iframe
                id="certification-iframe"
                src={iframeUrl}
                className="w-full h-full border-0 rounded-lg"
                title="認證申請表單"
                loading="lazy"
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                onLoad={() => {
                  console.log("Iframe loaded successfully");
                  // 隱藏loading overlay
                  const loading = document.getElementById("iframe-loading");
                  if (loading) {
                    loading.style.display = "none";
                  }
                }}
                onError={(e) => {
                  console.error("Iframe failed to load:", e);
                  // 顯示錯誤訊息
                  const loading = document.getElementById("iframe-loading");
                  if (loading) {
                    loading.innerHTML = `
                      <div class="text-center">
                        <div class="text-red-600 mb-2">⚠️</div>
                        <p class="font-size-sm text-red-600">載入失敗，請重試</p>
                      </div>
                    `;
                  }
                }}
              />

              {/* Loading overlay */}
              <div className="absolute inset-0 bg-gray-50 flex items-center justify-center rounded-lg" id="iframe-loading">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="font-size-sm text-gray-600">載入申請表單中...</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  );
};

export default CertificationApplicationPage;
