/**
 * 共用服務模組入口點
 * 統一匯出所有服務實作，提供一致性的匯入介面
 * 
 * @description 此檔案為 shared/services 目錄的主要入口，
 * 讓其他模組可以從單一位置匯入所需的服務
 * @version 1.0.0
 */

// === Token 管理服務 ===
export {
  TokenManager,
  FrontendTokenSource,
  BackendTokenSource
} from './TokenManager';

// === Token 驗證服務 ===
export {
  TokenValidator
} from './TokenValidator';

// === 用戶資料服務 ===
export {
  UserDataService,
  userDataService
} from './UserDataService';

/**
 * 快速存取服務的便利匯出
 */
export const Services = {
  // Token 相關服務
  Token: {
    Manager: TokenManager,
    Validator: TokenValidator,
    FrontendSource: FrontendTokenSource,
    BackendSource: BackendTokenSource
  },
  
  // 用戶相關服務
  User: {
    DataService: UserDataService,
    Instance: userDataService
  }
} as const;

/**
 * 服務工廠方法
 * 提供統一的服務實例建立方式
 */
export class ServiceFactory {
  /**
   * 建立前端 Token 來源服務
   */
  static createFrontendTokenSource() {
    return new FrontendTokenSource();
  }

  /**
   * 建立後端 Token 來源服務
   */
  static createBackendTokenSource(headers: Record<string, string | string[]>) {
    return new BackendTokenSource(headers);
  }

  /**
   * 取得用戶資料服務實例
   */
  static getUserDataService() {
    return UserDataService.getInstance();
  }

  /**
   * 建立適應性 Token 來源（自動偵測環境）
   */
  static createAdaptiveTokenSource(context?: any) {
    if (typeof window !== 'undefined') {
      return new FrontendTokenSource();
    } else if (context?.headers) {
      return new BackendTokenSource(context.headers);
    }
    throw new Error('無法確定環境類型，請明確指定 Token 來源');
  }
}

// 預設匯出服務工廠
export default ServiceFactory;