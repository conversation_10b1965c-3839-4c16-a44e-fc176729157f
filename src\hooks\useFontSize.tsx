import React, { createContext, useContext, useState, useEffect } from "react";

type FontSize = "sm" | "base" | "lg";

// CSS 變數映射表
const FONT_SIZE_VARS: Record<FontSize, Record<string, string>> = {
  sm: {
    "--font-xs": "0.75rem", // 12px
    "--font-sm": "0.875rem", // 14px
    "--font-base": "1rem", // 16px
    "--font-lg": "1.125rem", // 18px
    "--font-xl": "1.25rem", // 20px
    "--font-2xl": "1.5rem", // 24px
    "--font-3xl": "1.875rem", // 30px
    "--font-4xl": "2.25rem", // 36px
  },
  base: {
    "--font-xs": "0.875rem", // 14px
    "--font-sm": "1rem", // 16px
    "--font-base": "1.125rem", // 18px
    "--font-lg": "1.25rem", // 20px
    "--font-xl": "1.5rem", // 24px
    "--font-2xl": "1.875rem", // 30px
    "--font-3xl": "2.25rem", // 36px
    "--font-4xl": "2.75rem", // 44px
  },
  lg: {
    "--font-xs": "1rem", // 16px
    "--font-sm": "1.125rem", // 18px
    "--font-base": "1.25rem", // 20px
    "--font-lg": "1.5rem", // 24px
    "--font-xl": "1.875rem", // 30px
    "--font-2xl": "2.25rem", // 36px
    "--font-3xl": "2.75rem", // 44px
    "--font-4xl": "3.5rem", // 56px
  },
};

const FontSizeContext = createContext<{
  fontSize: FontSize;
  setFontSize: (size: FontSize) => void;
}>({
  fontSize: "base",
  setFontSize: () => {},
});

export const FontSizeProvider = ({ children }: { children: React.ReactNode }) => {
  const [fontSize, setFontSize] = useState<FontSize>("base");

  // 更新 CSS 變數
  useEffect(() => {
    const root = document.documentElement;
    const vars = FONT_SIZE_VARS[fontSize];

    Object.entries(vars).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }, [fontSize]);

  return <FontSizeContext.Provider value={{ fontSize, setFontSize }}>{children}</FontSizeContext.Provider>;
};

export function useFontSize() {
  return useContext(FontSizeContext);
}
