import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Trash2, Plus, Upload, Link as LinkIcon, Image, FileText, AlertCircle, CheckCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { campusSubmissionsAPI } from "@/api";
import campusSubmissionService from "@/services/campusSubmissionService";
import { getApiBaseUrl } from "@/config/environment";
import { authAPI } from "@/api/authAPI";
import { certificationAPI } from "@/api";

// 介面定義
interface PhotoItem {
  id: string;
  file?: File;
  url?: string;
  serverUrl?: string; // 伺服器返回的URL
  description: string;
  uploading?: boolean;
  error?: string;
}

interface AttachmentItem {
  id: string;
  file?: File;
  url?: string;
  description: string;
  uploading?: boolean;
  error?: string;
}

interface LinkItem {
  id: string;
  title: string;
  url: string;
}

interface CampusSubmissionForm {
  zh: {
    title: string;
    content: string;
  };
  en: {
    title: string;
    content: string;
  };
  photos: PhotoItem[];
  attachments: AttachmentItem[];
  links: LinkItem[];
}

const CampusSubmissionCreatePage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const editId = searchParams.get("edit");
  const isEditMode = !!editId;

  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [activeTab, setActiveTab] = useState("zh-TW");
  const photoInputRef = useRef<HTMLInputElement>(null);
  const attachmentInputRef = useRef<HTMLInputElement>(null);

  // 表單狀態
  const [formData, setFormData] = useState<CampusSubmissionForm>({
    zh: {
      title: "",
      content: "",
    },
    en: {
      title: "",
      content: "",
    },
    photos: [],
    attachments: [],
    links: [],
  });

  // 驗證狀態
  const [errors, setErrors] = useState<{
    zh?: { title?: string; content?: string };
    en?: { title?: string; content?: string };
    photos?: string;
    attachments?: string;
    links?: string;
  }>({});

  // 確認對話框狀態
  const [confirmDialog, setConfirmDialog] = useState(false);

  // 載入現有投稿資料（編輯模式）
  const loadExistingSubmission = async (submissionId: string) => {
    try {
      setLoadingData(true);
      console.log("🔄 [Edit] 載入現有投稿資料，ID:", submissionId);

      const response = await campusSubmissionService.getSubmissionDetail(submissionId);

      if (response.success && response.data) {
        const data = response.data;
        console.log("✅ [Edit] 投稿資料載入成功:", data);

        // 填充表單資料
        setFormData({
          zh: {
            title: data.zhTitle || data.title,
            content: data.zhContent || data.description,
          },
          en: {
            title: data.enTitle || data.title,
            content: data.enContent || data.description,
          },
          photos: data.photos
            ? data.photos.map((photo) => ({
                id: photo.photoId,
                file: undefined, // 已存在的圖片不需要檔案物件
                url: photo.url,
                serverUrl: photo.url, // 已上傳的圖片URL
                description: photo.description || "",
                uploading: false,
              }))
            : [],
          attachments: data.attachments
            ? data.attachments.map((attachment) => ({
                id: attachment.attachmentId,
                file: undefined, // 已存在的附件不需要檔案物件
                description: attachment.title || "",
                uploading: false,
                url: attachment.altUrl || "",
              }))
            : [],
          links: [], // 連結資料需要從後端API獲取，目前後端未提供
        });

        toast({
          title: "資料載入成功",
          description: `已載入投稿「${data.zhTitle || data.title}」的資料`,
        });
      } else {
        throw new Error(response.message || "載入投稿資料失敗");
      }
    } catch (error) {
      console.error("❌ [Edit] 載入投稿資料失敗:", error);
      toast({
        variant: "destructive",
        title: "載入資料失敗",
        description: error instanceof Error ? error.message : "請稍後再試",
      });

      // 載入失敗時返回列表頁面
      navigate("/news");
    } finally {
      setLoadingData(false);
    }
  };

  // 載入編輯資料
  useEffect(() => {
    if (isEditMode && editId) {
      loadExistingSubmission(editId);
    }
  }, [isEditMode, editId]);

  // Quill 編輯器配置
  const quillModules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ color: [] }, { background: [] }],
      [{ align: [] }],
      ["link"],
      ["clean"],
    ],
  };

  const quillFormats = ["header", "bold", "italic", "underline", "strike", "list", "bullet", "color", "background", "align", "link"];

  // 更新表單數據
  const updateFormData = (section: keyof CampusSubmissionForm, field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  // 照片處理 - 修改為立即上傳
  const handlePhotoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    for (const file of Array.from(files)) {
      // 檢查檔案格式
      if (!["image/jpeg", "image/jpg", "image/png"].includes(file.type)) {
        toast({
          variant: "destructive",
          title: "檔案格式錯誤",
          description: "照片僅支援 JPG、PNG 格式",
        });
        continue;
      }

      // 檢查檔案大小 (2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "檔案太大",
          description: "照片大小不可超過 2MB",
        });
        continue;
      }

      const photoId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
      const photoUrl = URL.createObjectURL(file);

      // 先加入到狀態中，標記為上傳中
      setFormData((prev) => ({
        ...prev,
        photos: [
          ...prev.photos,
          {
            id: photoId,
            file: file,
            url: photoUrl,
            description: "",
            uploading: true,
          },
        ],
      }));

      try {
        // 使用校園投稿檔案上傳服務
        const result = await campusSubmissionService.uploadPhotos("temp-submission", [file]);

        if (result.success) {
          // 更新狀態：上傳成功
          const uploadedPhoto = result.data[0] as any; // 假設返回的是照片陣列
          setFormData((prev) => ({
            ...prev,
            photos: prev.photos.map((photo) =>
              photo.id === photoId
                ? {
                    ...photo,
                    uploading: false,
                    serverUrl: uploadedPhoto.fileUrl, // 伺服器返回的URL
                  }
                : photo
            ),
          }));

          toast({
            title: "圖片上傳成功",
            description: `${file.name} 已成功上傳`,
          });
        } else {
          throw new Error(result.message || "上傳失敗");
        }
      } catch (error) {
        // 上傳失敗處理
        setFormData((prev) => ({
          ...prev,
          photos: prev.photos.filter((photo) => photo.id !== photoId),
        }));

        toast({
          variant: "destructive",
          title: "圖片上傳失敗",
          description: error instanceof Error ? error.message : "未知錯誤",
        });
      }
    }

    // 清空 input
    if (photoInputRef.current) {
      photoInputRef.current.value = "";
    }
  };

  const updatePhotoDescription = (photoId: string, description: string) => {
    setFormData((prev) => ({
      ...prev,
      photos: prev.photos.map((photo) => (photo.id === photoId ? { ...photo, description } : photo)),
    }));
  };

  const removePhoto = (photoId: string) => {
    setFormData((prev) => ({
      ...prev,
      photos: prev.photos.filter((photo) => photo.id !== photoId),
    }));
  };

  // 附件處理
  const handleAttachmentUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    for (const file of Array.from(files)) {
      // 檢查檔案格式
      if (!["application/pdf", "application/vnd.oasis.opendocument.text"].includes(file.type)) {
        toast({
          variant: "destructive",
          title: "檔案格式錯誤",
          description: "附件僅支援 PDF、ODT 格式",
        });
        continue;
      }

      // 檢查檔案大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "檔案太大",
          description: "附件大小不可超過 5MB",
        });
        continue;
      }

      const attachmentId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

      // 先加入到狀態中，標記為上傳中
      setFormData((prev) => ({
        ...prev,
        attachments: [
          ...prev.attachments,
          {
            id: attachmentId,
            file: file,
            description: "",
            uploading: true,
          },
        ],
      }));

      try {
        // 使用校園投稿檔案上傳服務
        const result = await campusSubmissionService.uploadAttachments("temp-submission", [file]);

        if (result.success) {
          // 更新狀態：上傳成功
          const uploadedAttachment = result.data[0] as any; // 假設返回的是附件陣列
          setFormData((prev) => ({
            ...prev,
            attachments: prev.attachments.map((attachment) =>
              attachment.id === attachmentId
                ? {
                    ...attachment,
                    uploading: false,
                    serverUrl: uploadedAttachment.fileUrl, // 伺服器返回的URL
                  }
                : attachment
            ),
          }));

          toast({
            title: "附件上傳成功",
            description: `${file.name} 已成功上傳`,
          });
        } else {
          throw new Error(result.message || "上傳失敗");
        }
      } catch (error) {
        // 上傳失敗處理
        setFormData((prev) => ({
          ...prev,
          attachments: prev.attachments.filter((attachment) => attachment.id !== attachmentId),
        }));

        toast({
          variant: "destructive",
          title: "附件上傳失敗",
          description: error instanceof Error ? error.message : "未知錯誤",
        });
      }
    }

    // 清空 input
    if (attachmentInputRef.current) {
      attachmentInputRef.current.value = "";
    }
  };

  const updateAttachmentDescription = (attachmentId: string, description: string) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.map((attachment) => (attachment.id === attachmentId ? { ...attachment, description } : attachment)),
    }));
  };

  const removeAttachment = (attachmentId: string) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((attachment) => attachment.id !== attachmentId),
    }));
  };

  // 連結處理
  const addLink = () => {
    const linkId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    setFormData((prev) => ({
      ...prev,
      links: [
        ...prev.links,
        {
          id: linkId,
          title: "",
          url: "",
        },
      ],
    }));
  };

  const updateLink = (linkId: string, field: "title" | "url", value: string) => {
    setFormData((prev) => ({
      ...prev,
      links: prev.links.map((link) => (link.id === linkId ? { ...link, [field]: value } : link)),
    }));
  };

  const removeLink = (linkId: string) => {
    setFormData((prev) => ({
      ...prev,
      links: prev.links.filter((link) => link.id !== linkId),
    }));
  };

  // 表單驗證
  const validateForm = (): boolean => {
    const newErrors: {
      zh?: { title?: string; content?: string };
      en?: { title?: string; content?: string };
      photos?: string;
      attachments?: string;
      links?: string;
    } = {};

    // 驗證中文版本
    if (!formData.zh.title.trim()) {
      newErrors.zh = { ...newErrors.zh, title: "請輸入中文標題" };
    } else if (formData.zh.title.length > 50) {
      newErrors.zh = { ...newErrors.zh, title: "中文標題不可超過50字" };
    }

    if (!formData.zh.content.trim()) {
      newErrors.zh = { ...newErrors.zh, content: "請輸入中文內容" };
    }

    // 驗證英文版本
    if (!formData.en.title.trim()) {
      newErrors.en = { ...newErrors.en, title: "請輸入英文標題" };
    } else if (formData.en.title.length > 50) {
      newErrors.en = { ...newErrors.en, title: "英文標題不可超過50字" };
    }

    if (!formData.en.content.trim()) {
      newErrors.en = { ...newErrors.en, content: "請輸入英文內容" };
    }

    // 驗證照片描述
    const photosWithoutDescription = formData.photos.filter((photo) => !photo.description.trim());
    if (photosWithoutDescription.length > 0) {
      newErrors.photos = "所有照片都需要填寫描述";
    }

    // 驗證附件描述
    const attachmentsWithoutDescription = formData.attachments.filter((attachment) => !attachment.description.trim());
    if (attachmentsWithoutDescription.length > 0) {
      newErrors.attachments = "所有附件都需要填寫描述";
    }

    // 驗證連結
    const incompleteLinks = formData.links.filter((link) => !link.title.trim() || !link.url.trim());
    if (incompleteLinks.length > 0) {
      newErrors.links = "所有連結都需要填寫標題和網址";
    }

    // 驗證網址格式
    const invalidLinks = formData.links.filter((link) => {
      try {
        new URL(link.url);
        return false;
      } catch {
        return true;
      }
    });
    if (invalidLinks.length > 0) {
      newErrors.links = "請輸入有效的網址格式";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表單
  const handleSubmit = () => {
    console.log("🚀 [Form] 開始驗證表單");

    if (!validateForm()) {
      toast({
        variant: "destructive",
        title: "表單驗證失敗",
        description: "請檢查並修正表單中的錯誤",
      });
      return;
    }

    setConfirmDialog(true);
  };

  const confirmSubmit = async () => {
    try {
      setLoading(true);
      console.log(`🚀 [${isEditMode ? "Update" : "Submit"}] 開始${isEditMode ? "更新" : "提交"}投稿${isEditMode ? "" : "申請"}`);
      console.log("📊 [Data] 表單資料:", formData);

      // 準備提交資料
      const submissionData = {
        zh: {
          title: formData.zh.title,
          content: formData.zh.content,
        },
        en: {
          title: formData.en.title,
          content: formData.en.content,
        },
        photos: formData.photos.map((photo) => ({
          id: photo.id,
          description: photo.description,
          url: photo.serverUrl || photo.url, // 優先使用伺服器URL
          file: photo.serverUrl ? undefined : photo.file, // 如果已上傳則不傳送檔案
        })),
        attachments: formData.attachments.map((attachment) => ({
          id: attachment.id,
          description: attachment.description,
          file: attachment.file,
        })),
        links: formData.links.map((link) => ({
          id: link.id,
          title: link.title,
          url: link.url,
        })),
      };

      let response;
      if (isEditMode && editId) {
        // 編輯模式：更新投稿
        response = await campusSubmissionService.updateSubmission(editId, submissionData);
      } else {
        // 新建模式：建立投稿
        response = await campusSubmissionService.createSubmission(submissionData);
      }

      if (response.success) {
        const submissionId = isEditMode ? editId : response.data?.submissionId;

        toast({
          title: isEditMode ? "投稿更新成功" : "投稿建立成功",
          description: isEditMode
            ? `投稿「${response.data?.zhTitle}」已成功更新，現在為未送審狀態`
            : `投稿「${response.data?.zhTitle}」已成功建立，現在為未送審狀態`,
        });

        // 導向投稿詳細資訊頁面
        navigate(`/news/detail/${submissionId}`);
      } else {
        throw new Error(response.message || `投稿${isEditMode ? "更新" : "建立"}失敗`);
      }
    } catch (error) {
      console.error(`❌ [Error] 投稿${isEditMode ? "更新" : "建立"}失敗:`, error);
      toast({
        variant: "destructive",
        title: `投稿${isEditMode ? "更新" : "建立"}失敗`,
        description: error instanceof Error ? error.message : "請稍後再試或聯絡系統管理員",
      });
    } finally {
      setLoading(false);
      setConfirmDialog(false);
    }
  };

  // 載入狀態
  if (loadingData) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-6">
          <h1 className="font-size-3xl font-bold text-primary mb-2">載入投稿資料中...</h1>
          <p className="text-gray-600">請稍候，正在載入現有投稿資料</p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-6">
        <h1 className="font-size-3xl font-bold text-primary mb-2">{isEditMode ? "編輯校園投稿" : "建立校園投稿"}</h1>
        <p className="text-gray-600">
          {isEditMode
            ? "編輯完成後投稿將為未送審狀態，需在詳細資訊頁面點擊「送審投稿」進行送審。"
            : "建立完成後投稿將為未送審狀態，需在詳細資訊頁面點擊「送審投稿」進行送審。"}
        </p>
      </div>

      <div className="space-y-6">
        {/* 語言版本標籤 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* <TabsList className="grid w-full grid-cols-2 bg-muted h-18">
            <TabsTrigger value="zh-TW">中文版本</TabsTrigger>
            <TabsTrigger value="en">English Version</TabsTrigger>
          </TabsList> */}
          <TabsList className="grid w-full grid-cols-2 bg-muted h-18">
            <TabsTrigger value="zh-TW" className="relative">
              中文版本
              {(errors.zh?.title || errors.zh?.content) && (
                <span className="absolute -top-1 -right-2 text-red-500 text-xl leading-none">
                  <AlertCircle className="w-4 h-4 text-red-500 ml-1" />
                </span>
              )}
            </TabsTrigger>

            <TabsTrigger value="en" className="relative">
              English Version
              {(errors.en?.title || errors.en?.content) && (
                <span className="absolute -top-1 -right-2 text-red-500 text-xl leading-none">
                  <AlertCircle className="w-4 h-4 text-red-500 ml-1" />
                </span>
              )}
            </TabsTrigger>
          </TabsList>

          {/* 撰寫中文版本內容 */}
          <TabsContent value="zh-TW">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span>中文版本內容</span>
                  {formData.zh.title && formData.zh.content && <CheckCircle className="h-5 w-5 text-green-500" />}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="zh-title">
                    新聞標題<span className="text-red-500"> *</span>
                  </Label>
                  <Input
                    id="zh-title"
                    value={formData.zh.title}
                    onChange={(e) => updateFormData("zh", "title", e.target.value)}
                    placeholder="請輸入中文新聞標題（最多50字）"
                    maxLength={50}
                    className={errors.zh?.title ? "border-red-500" : ""}
                  />
                  <div className="flex justify-between items-center mt-1">
                    {errors.zh?.title && <span className="text-red-500 text-sm">{errors.zh.title}</span>}
                    <span className="text-gray-500 text-sm ml-auto">{formData.zh.title.length}/50</span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="zh-content">
                    投稿內容<span className="text-red-500"> *</span>
                  </Label>
                  <div className={`mt-2 ${errors.zh?.content ? "border border-red-500 rounded" : ""}`}>
                    <ReactQuill
                      theme="snow"
                      value={formData.zh.content}
                      onChange={(value) => updateFormData("zh", "content", value)}
                      modules={quillModules}
                      formats={quillFormats}
                      placeholder="請輸入中文投稿內容..."
                      className="quill-editor-large"
                    />
                  </div>
                  {errors.zh?.content && <span className="text-red-500 text-sm">{errors.zh.content}</span>}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 撰寫英文版本內容 */}
          <TabsContent value="en">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span>English Version Content</span>
                  {formData.en.title && formData.en.content && <CheckCircle className="h-5 w-5 text-green-500" />}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="en-title">
                    News Title<span className="text-red-500"> *</span>
                  </Label>
                  <Input
                    id="en-title"
                    value={formData.en.title}
                    onChange={(e) => updateFormData("en", "title", e.target.value)}
                    placeholder="Please enter English news title (max 50 characters)"
                    maxLength={50}
                    className={errors.en?.title ? "border-red-500" : ""}
                  />
                  <div className="flex justify-between items-center mt-1">
                    {errors.en?.title && <span className="text-red-500 text-sm">{errors.en.title}</span>}
                    <span className="text-gray-500 text-sm ml-auto">{formData.en.title.length}/50</span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="en-content">
                    Submission Content<span className="text-red-500"> *</span>
                  </Label>
                  <div className={`mt-2 ${errors.en?.content ? "border border-red-500 rounded" : ""}`}>
                    <ReactQuill
                      theme="snow"
                      value={formData.en.content}
                      onChange={(value) => updateFormData("en", "content", value)}
                      modules={quillModules}
                      formats={quillFormats}
                      placeholder="Please enter English submission content..."
                      style={{
                        minHeight: "100px",
                        // height: "100px",
                        // 1200px
                      }}
                      className="quill-editor-large"
                    />
                  </div>
                  {errors.en?.content && <span className="text-red-500 text-sm">{errors.en.content}</span>}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 照片上傳 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              照片上傳
              {formData.photos.length > 0 && <span className="text-sm text-gray-500">({formData.photos.length} 張照片)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <input ref={photoInputRef} type="file" accept="image/jpeg,image/jpg,image/png" multiple onChange={handlePhotoUpload} className="hidden" />
                <Button type="button" variant="outline" onClick={() => photoInputRef.current?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  選擇照片
                </Button>
                <p className="text-sm text-gray-500 mt-2">支援 JPG、PNG 格式，單張照片不超過 2MB</p>
              </div>

              {formData.photos.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {formData.photos.map((photo, index) => (
                    <div key={photo.id} className="border rounded-lg p-3 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">照片 {index + 1}</span>
                          {photo.uploading && (
                            <div className="flex items-center space-x-1 text-blue-600">
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                              <span className="text-xs">上傳中...</span>
                            </div>
                          )}
                          {photo.serverUrl && !photo.uploading && (
                            <div className="flex items-center space-x-1 text-green-600">
                              <CheckCircle className="h-3 w-3" />
                              <span className="text-xs">已上傳</span>
                            </div>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removePhoto(photo.id)}
                          className="text-red-500 hover:text-red-700 border-red-200"
                          disabled={photo.uploading}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      {photo.url && <img src={photo.url} alt={`照片 ${index + 1}`} className="w-full h-32 object-cover rounded" />}
                      <div>
                        <Label htmlFor={`photo-desc-${photo.id}`}>照片描述 *</Label>
                        <Textarea
                          id={`photo-desc-${photo.id}`}
                          value={photo.description}
                          onChange={(e) => updatePhotoDescription(photo.id, e.target.value)}
                          placeholder="請描述這張照片的內容..."
                          rows={2}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {errors.photos && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.photos}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 檔案附件 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              檔案附件
              {formData.attachments.length > 0 && <span className="text-sm text-gray-500">({formData.attachments.length} 個檔案)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <input
                  ref={attachmentInputRef}
                  type="file"
                  accept=".pdf,.odt,application/pdf,application/vnd.oasis.opendocument.text"
                  multiple
                  onChange={handleAttachmentUpload}
                  className="hidden"
                />
                <Button type="button" variant="outline" onClick={() => attachmentInputRef.current?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  選擇檔案
                </Button>
                <p className="text-sm text-gray-500 mt-2">支援 PDF、ODT 格式，單個檔案不超過 5MB</p>
              </div>

              {formData.attachments.length > 0 && (
                <div className="space-y-3">
                  {formData.attachments.map((attachment, index) => (
                    <div key={attachment.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span className="text-sm font-medium">{attachment.file?.name || `附件 ${index + 1}`}</span>
                          <span className="text-xs text-gray-500">({attachment.file ? (attachment.file.size / 1024 / 1024).toFixed(1) : "0"} MB)</span>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeAttachment(attachment.id)}
                          className="text-red-500 hover:text-red-700 border-red-200">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div>
                        <Label htmlFor={`attachment-desc-${attachment.id}`}>檔案描述 *</Label>
                        <Textarea
                          id={`attachment-desc-${attachment.id}`}
                          value={attachment.description}
                          onChange={(e) => updateAttachmentDescription(attachment.id, e.target.value)}
                          placeholder="請描述這個檔案的內容..."
                          rows={2}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {errors.attachments && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.attachments}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 相關連結 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LinkIcon className="h-5 w-5" />
              相關連結
              {formData.links.length > 0 && <span className="text-sm text-gray-500">({formData.links.length} 個連結)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button type="button" variant="outline" onClick={addLink}>
                <Plus className="h-4 w-4 mr-2" />
                新增連結
              </Button>

              {formData.links.length > 0 && (
                <div className="space-y-3">
                  {formData.links.map((link, index) => (
                    <div key={link.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-sm font-medium">連結 {index + 1}</span>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeLink(link.id)}
                          className="text-red-500 hover:text-red-700 border-red-200">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <Label htmlFor={`link-title-${link.id}`}>連結標題 *</Label>
                          <Input
                            id={`link-title-${link.id}`}
                            value={link.title}
                            onChange={(e) => updateLink(link.id, "title", e.target.value)}
                            placeholder="請輸入連結標題"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`link-url-${link.id}`}>網址 *</Label>
                          <Input
                            id={`link-url-${link.id}`}
                            value={link.url}
                            onChange={(e) => updateLink(link.id, "url", e.target.value)}
                            placeholder="https://..."
                            type="url"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {errors.links && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.links}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 操作按鈕 */}
        <div className="flex justify-between items-center pt-6 border-t">
          <Button type="button" variant="outline" onClick={() => navigate("/news")}>
            取消
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={loading}>
            {loading ? (isEditMode ? "更新中..." : "建立中...") : isEditMode ? "更新投稿" : "建立投稿"}
          </Button>
        </div>
      </div>

      {/* 確認對話框 */}
      <Dialog open={confirmDialog} onOpenChange={setConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{isEditMode ? "確認更新投稿" : "確認建立投稿"}</DialogTitle>
            <DialogDescription>
              您即將{isEditMode ? "更新校園投稿" : "建立校園投稿"}，包含：
              <ul className="mt-2 space-y-1 text-sm">
                <li>• 中英文標題和內容</li>
                {formData.photos.length > 0 && <li>• {formData.photos.length} 張照片</li>}
                {formData.attachments.length > 0 && <li>• {formData.attachments.length} 個檔案附件</li>}
                {formData.links.length > 0 && <li>• {formData.links.length} 個相關連結</li>}
              </ul>
              <p className="mt-2">
                {isEditMode
                  ? "⚠️ 更新後投稿將為未送審狀態，需在詳細資訊頁面點擊「送審投稿」。確認要更新嗎？"
                  : "📝 建立後投稿將為未送審狀態，需在詳細資訊頁面點擊「送審投稿」。確認要建立嗎？"}
              </p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setConfirmDialog(false)}>
              取消
            </Button>
            <Button type="button" onClick={confirmSubmit} disabled={loading}>
              {loading ? (isEditMode ? "更新中..." : "建立中...") : isEditMode ? "確認更新" : "確認建立"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CampusSubmissionCreatePage;
