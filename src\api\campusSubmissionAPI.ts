import { BaseAPI, ApiResponse, ApiPaginationResponse, ApiPaginationWithSchoolInfoResponse } from "./BaseAPI";

// 校園投稿介面定義
export interface CampusSubmission {
  submissionId: string;
  title: string;
  description: string;
  status: number;
  statusText: string;
  submissionDate: string;
  createdTime: string;
  updatedTime?: string;
  badgeType: number;
  featuredStatus: number;
}

export interface CampusSubmissionDetail extends CampusSubmission {
  attachments?: CampusSubmissionAttachment[];
  // 中英文內容
  zhTitle: string;
  enTitle: string;
  zhContent: string;
  enContent: string;
  // 圖片資料 (從attachments中篩選出來)
  photos?: CampusSubmissionPhoto[];
}

export interface CampusSubmissionPhoto {
  photoId: string;
  url: string;
  title?: string;
  description?: string;
}

export interface CampusSubmissionAttachment {
  attachmentId: string;
  fileEntryId?: string;
  contentTypeCode: string;
  title?: string;
  altUrl?: string;
}

export interface CampusSubmissionPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface CampusSubmissionDetailResponse {
  success: boolean;
  data: CampusSubmissionDetail;
  message?: string;
}

export interface CampusSubmissionDeleteResponse {
  success: boolean;
  message?: string;
}

export interface CampusSubmissionCreateData {
  zh: { title: string; content: string };
  en: { title: string; content: string };
  photos?: { id: string; description: string; file?: File }[];
  attachments?: { id: string; description: string; file?: File }[];
  links?: { id: string; title: string; url: string }[];
}

export interface CampusSubmissionCreateResponse {
  success: boolean;
  message?: string;
  data?: {
    submissionId: string;
    message: string;
  };
}

export interface CampusSubmissionStatusResponse {
  success: boolean;
  message?: string;
  data?: {
    submissionId: string;
    reviewId: string;
    message: string;
  };
}

// 檔案上傳響應介面
export interface CampusSubmissionFileUploadResponse {
  success: boolean;
  message: string;
  data: CampusSubmissionPhoto[] | CampusSubmissionAttachment[];
}

// 檔案上傳項目介面
export interface CampusSubmissionFileItem {
  id: string;
  originalName: string;
  filename: string;
  fileUrl: string;
  size: number;
  mimetype: string;
  description: string;
  uploadDate: string;
}

// 校園投稿 API 類別
export class CampusSubmissionAPI extends BaseAPI {
  constructor() {
    super();
  }

  /**
   * 獲取校園投稿列表
   * @param options 查詢選項
   * @returns 投稿列表和分頁資訊
   */
  async getSubmissions(options: { page?: number; limit?: number } = {}): Promise<ApiPaginationWithSchoolInfoResponse<CampusSubmission>> {
    const { page = 1, limit = 50 } = options;
    const response = await this.get<CampusSubmission[]>("/campus-submissions", { page, limit });
    return response as ApiPaginationWithSchoolInfoResponse<CampusSubmission>;
  }

  /**
   * 獲取單一校園投稿詳細資料
   * @param submissionId 投稿 ID
   * @returns 投稿詳細資料
   */
  async getSubmissionDetail(submissionId: string): Promise<ApiResponse<CampusSubmissionDetailResponse>> {
    return this.get<CampusSubmissionDetailResponse>(`/campus-submissions/${submissionId}`);
  }

  /**
   * 建立校園投稿申請
   * @param submissionData 投稿資料
   * @returns 建立結果
   */
  async createSubmission(submissionData: CampusSubmissionCreateData): Promise<ApiResponse<CampusSubmissionCreateResponse>> {
    return this.post<CampusSubmissionCreateResponse>("/campus-submissions", submissionData);
  }

  /**
   * 刪除校園投稿（軟刪除）
   * @param submissionId 投稿 ID
   * @returns 刪除結果
   */
  async deleteSubmission(submissionId: string): Promise<ApiResponse<CampusSubmissionDeleteResponse>> {
    return this.delete<CampusSubmissionDeleteResponse>(`/campus-submissions/${submissionId}`);
  }

  /**
   * 申請修改投稿
   * @param submissionId 投稿 ID
   * @returns 申請結果
   */
  async requestModify(submissionId: string): Promise<ApiResponse<CampusSubmissionStatusResponse>> {
    return this.put<CampusSubmissionStatusResponse>(`/campus-submissions/${submissionId}/request-modify`);
  }

  /**
   * 撤回投稿送審狀態（用於編輯）
   * @param submissionId 投稿 ID
   * @returns 撤回結果
   */
  async withdrawSubmission(submissionId: string): Promise<ApiResponse<CampusSubmissionStatusResponse>> {
    return this.put<CampusSubmissionStatusResponse>(`/campus-submissions/${submissionId}/withdraw`);
  }

  /**
   * 更新投稿內容
   * @param submissionId 投稿 ID
   * @param submissionData 更新資料
   * @returns 更新結果
   */
  async updateSubmission(submissionId: string, submissionData: CampusSubmissionCreateData): Promise<ApiResponse<CampusSubmissionCreateResponse>> {
    return this.put<CampusSubmissionCreateResponse>(`/campus-submissions/${submissionId}`, submissionData);
  }

  /**
   * 送審投稿（將未送審狀態改為審核中）
   * @param submissionId 投稿 ID
   * @returns 送審結果
   */
  async submitForReview(submissionId: string): Promise<ApiResponse<CampusSubmissionStatusResponse>> {
    return this.put<CampusSubmissionStatusResponse>(`/campus-submissions/${submissionId}/submit`);
  }

  /**
   * 上傳校園投稿照片
   * @param submissionId 投稿 ID
   * @param files 照片檔案陣列
   * @returns 上傳結果
   */
  async uploadPhotos(submissionId: string, files: File[]): Promise<ApiResponse<CampusSubmissionFileUploadResponse>> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("photos", file);
    });

    return this.uploadMultipleFiles<CampusSubmissionFileUploadResponse>(`/campus-submissions/${submissionId}/photos`, formData);
  }

  /**
   * 上傳校園投稿附件
   * @param submissionId 投稿 ID
   * @param files 附件檔案陣列
   * @returns 上傳結果
   */
  async uploadAttachments(submissionId: string, files: File[]): Promise<ApiResponse<CampusSubmissionFileUploadResponse>> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("attachments", file);
    });

    return this.uploadMultipleFiles<CampusSubmissionFileUploadResponse>(`/campus-submissions/${submissionId}/attachments`, formData);
  }

  /**
   * 刪除校園投稿照片
   * @param submissionId 投稿 ID
   * @param filename 檔案名
   * @returns 刪除結果
   */
  async deletePhoto(submissionId: string, filename: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return this.delete<{ success: boolean; message: string }>(`/campus-submissions/${submissionId}/photos/${filename}`);
  }

  /**
   * 刪除校園投稿附件
   * @param submissionId 投稿 ID
   * @param filename 檔案名
   * @returns 刪除結果
   */
  async deleteAttachment(submissionId: string, filename: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return this.delete<{ success: boolean; message: string }>(`/campus-submissions/${submissionId}/attachments/${filename}`);
  }
}

// 創建全域校園投稿 API 實例
export const campusSubmissionAPI = new CampusSubmissionAPI();
