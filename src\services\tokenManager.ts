/**
 * Token 管理器 - 集中化 Token 管理和臨時 Token 堆疊處理
 * 
 * 核心功能：
 * - 單例模式確保全域唯一實例
 * - Token 堆疊機制支援臨時 Token 操作
 * - 訂閱機制通知 Token 變更事件
 * - 自動清理和錯誤恢復機制
 */

import { StorageManager } from './storageManager';

// Token 訂閱者介面
export interface TokenSubscriber {
  onTokenChange(newToken: string | null): void;
  onTokenExpired?(): void;
  onTokenError?(error: Error): void;
}

// Token 狀態介面
export interface TokenState {
  currentToken: string | null;
  isTemporaryMode: boolean;
  stackDepth: number;
  lastUpdate: number;
}

// Token 驗證結果介面
export interface TokenValidationResult {
  valid: boolean;
  reason?: string;
  shouldRefresh?: boolean;
}

/**
 * TokenManager 類別 - 單例模式的 Token 管理器
 */
export class TokenManager {
  private static instance: TokenManager;
  private readonly storageManager: StorageManager;
  private readonly TOKEN_KEY = 'userToken';
  
  // Token 狀態管理
  private currentToken: string | null = null;
  private tokenStack: string[] = []; // Token 堆疊，支援巢狀臨時 Token
  private isInitialized = false;
  
  // 訂閱者管理
  private subscribers = new Set<TokenSubscriber>();
  
  // 防抖機制
  private notificationDebounceTimer: NodeJS.Timeout | null = null;
  private readonly NOTIFICATION_DEBOUNCE_MS = 100;
  
  // Token 驗證快取
  private validationCache = new Map<string, { result: boolean; timestamp: number }>();
  private readonly VALIDATION_CACHE_TTL = 5 * 60 * 1000; // 5 分鐘

  private constructor() {
    this.storageManager = StorageManager.getInstance();
    this.initialize();
  }

  /**
   * 獲取 TokenManager 單例實例
   */
  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * 初始化 Token 管理器
   * 從儲存中載入現有 Token
   */
  private initialize(): void {
    try {
      // 從 sessionStorage 載入 Token
      const storedToken = this.storageManager.get(this.TOKEN_KEY, 'token');
      if (storedToken && typeof storedToken === 'string') {
        this.currentToken = storedToken;
      }
      
      this.isInitialized = true;
      console.log('🔑 [TokenManager] 初始化完成，當前 Token:', this.currentToken ? '已載入' : '未設定');
    } catch (error) {
      console.error('❌ [TokenManager] 初始化失敗:', error);
      this.currentToken = null;
      this.isInitialized = true;
    }
  }

  /**
   * 確保管理器已初始化
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      this.initialize();
    }
  }

  /**
   * 獲取當前 Token
   * @returns 當前使用的 Token（可能是臨時 Token）
   */
  public getToken(): string | null {
    this.ensureInitialized();
    
    // 如果有臨時 Token，返回堆疊頂部的 Token
    if (this.tokenStack.length > 0) {
      return this.tokenStack[this.tokenStack.length - 1];
    }
    
    return this.currentToken;
  }

  /**
   * 設定 Token
   * @param token 新的 Token 值
   */
  public setToken(token: string | null): void {
    this.ensureInitialized();
    
    try {
      const previousToken = this.currentToken;
      
      // 設定新 Token
      this.currentToken = token;
      
      // 清除 Token 堆疊（設定新 Token 時不應該有臨時 Token）
      if (this.tokenStack.length > 0) {
        console.warn('⚠️ [TokenManager] 設定新 Token 時清除了 Token 堆疊');
        this.tokenStack = [];
      }
      
      // 儲存到 sessionStorage
      if (token) {
        this.storageManager.set(this.TOKEN_KEY, token, 'token');
      } else {
        this.storageManager.remove(this.TOKEN_KEY, 'token');
      }
      
      // 清除驗證快取
      this.clearValidationCache();
      
      // 通知訂閱者（防抖）
      this.notifySubscribersDebounced(token);
      
      console.log('🔑 [TokenManager] Token 已更新:', {
        previous: previousToken ? '***已設定***' : '未設定',
        current: token ? '***已設定***' : '未設定'
      });
    } catch (error) {
      console.error('❌ [TokenManager] 設定 Token 失敗:', error);
      this.notifyTokenError(error as Error);
    }
  }

  /**
   * 清除當前 Token
   */
  public clearToken(): void {
    this.setToken(null);
    
    // 清除所有 Token 堆疊
    this.tokenStack = [];
    
    console.log('🗑️ [TokenManager] Token 已清除');
  }

  /**
   * 推送臨時 Token 到堆疊
   * @param tempToken 臨時 Token
   */
  public pushTemporaryToken(tempToken: string): void {
    this.ensureInitialized();
    
    if (!tempToken) {
      throw new Error('臨時 Token 不能為空');
    }
    
    try {
      // 推送到堆疊
      this.tokenStack.push(tempToken);
      
      console.log('⬆️ [TokenManager] 推送臨時 Token，堆疊深度:', this.tokenStack.length);
      
      // 通知訂閱者 Token 變更
      this.notifySubscribersDebounced(tempToken);
    } catch (error) {
      console.error('❌ [TokenManager] 推送臨時 Token 失敗:', error);
      this.notifyTokenError(error as Error);
      throw error;
    }
  }

  /**
   * 從堆疊彈出臨時 Token
   * @returns 被彈出的 Token，如果堆疊為空則返回 null
   */
  public popTemporaryToken(): string | null {
    this.ensureInitialized();
    
    try {
      const poppedToken = this.tokenStack.pop() || null;
      
      console.log('⬇️ [TokenManager] 彈出臨時 Token，剩餘堆疊深度:', this.tokenStack.length);
      
      // 通知訂閱者當前 Token 變更
      const currentToken = this.getToken();
      this.notifySubscribersDebounced(currentToken);
      
      return poppedToken;
    } catch (error) {
      console.error('❌ [TokenManager] 彈出臨時 Token 失敗:', error);
      this.notifyTokenError(error as Error);
      return null;
    }
  }

  /**
   * 清除所有臨時 Token
   */
  public clearTemporaryTokens(): void {
    const stackDepth = this.tokenStack.length;
    this.tokenStack = [];
    
    if (stackDepth > 0) {
      console.log('🧹 [TokenManager] 已清除所有臨時 Token，原堆疊深度:', stackDepth);
      
      // 通知訂閱者 Token 變更
      this.notifySubscribersDebounced(this.currentToken);
    }
  }

  /**
   * 執行需要臨時 Token 的操作
   * @param tempToken 臨時 Token
   * @param callback 需要執行的操作
   * @returns Promise<T> 操作結果
   */
  public async withTemporaryToken<T>(
    tempToken: string,
    callback: () => Promise<T>
  ): Promise<T> {
    if (!tempToken) {
      throw new Error('臨時 Token 不能為空');
    }
    
    if (!callback) {
      throw new Error('回調函數不能為空');
    }
    
    // 推送臨時 Token
    this.pushTemporaryToken(tempToken);
    
    try {
      // 執行操作
      const result = await callback();
      return result;
    } catch (error) {
      console.error('❌ [TokenManager] 臨時 Token 操作失敗:', error);
      throw error;
    } finally {
      // 確保總是彈出臨時 Token
      this.popTemporaryToken();
    }
  }

  /**
   * 驗證 Token 是否有效（帶快取）
   * @param token 要驗證的 Token，如果未提供則使用當前 Token
   * @returns Promise<TokenValidationResult> 驗證結果
   */
  public async validateToken(token?: string): Promise<TokenValidationResult> {
    const targetToken = token || this.getToken();
    
    if (!targetToken) {
      return { valid: false, reason: '沒有可驗證的 Token' };
    }
    
    try {
      // 檢查快取
      const cacheKey = targetToken;
      const cached = this.validationCache.get(cacheKey);
      const now = Date.now();
      
      if (cached && (now - cached.timestamp) < this.VALIDATION_CACHE_TTL) {
        return { 
          valid: cached.result, 
          reason: cached.result ? '驗證成功（快取）' : '驗證失敗（快取）' 
        };
      }
      
      // 這裡應該調用實際的 Token 驗證 API
      // 暫時返回基本的格式驗證
      const isValidFormat = this.isValidTokenFormat(targetToken);
      
      // 更新快取
      this.validationCache.set(cacheKey, {
        result: isValidFormat,
        timestamp: now
      });
      
      return {
        valid: isValidFormat,
        reason: isValidFormat ? '格式驗證通過' : '格式驗證失敗'
      };
    } catch (error) {
      console.error('❌ [TokenManager] Token 驗證失敗:', error);
      return { 
        valid: false, 
        reason: `驗證過程出錯: ${error instanceof Error ? error.message : '未知錯誤'}` 
      };
    }
  }

  /**
   * 檢查 Token 格式是否有效
   * @param token Token 字串
   * @returns boolean 格式是否有效
   */
  private isValidTokenFormat(token: string): boolean {
    // 基本格式檢查：非空且長度合理
    if (!token || token.trim().length === 0) {
      return false;
    }
    
    // 檢查是否為 GUID 格式（EcoCampus 系統使用的格式）
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return guidRegex.test(token);
  }

  /**
   * 清除驗證快取
   */
  private clearValidationCache(): void {
    this.validationCache.clear();
  }

  /**
   * 獲取 Token 狀態
   * @returns TokenState 當前狀態
   */
  public getTokenState(): TokenState {
    this.ensureInitialized();
    
    return {
      currentToken: this.currentToken,
      isTemporaryMode: this.tokenStack.length > 0,
      stackDepth: this.tokenStack.length,
      lastUpdate: Date.now()
    };
  }

  /**
   * 訂閱 Token 變更事件
   * @param subscriber 訂閱者
   */
  public subscribe(subscriber: TokenSubscriber): void {
    if (!subscriber || typeof subscriber.onTokenChange !== 'function') {
      throw new Error('訂閱者必須實現 onTokenChange 方法');
    }
    
    this.subscribers.add(subscriber);
    console.log('👀 [TokenManager] 新增訂閱者，總數:', this.subscribers.size);
  }

  /**
   * 取消訂閱 Token 變更事件
   * @param subscriber 訂閱者
   */
  public unsubscribe(subscriber: TokenSubscriber): void {
    const removed = this.subscribers.delete(subscriber);
    
    if (removed) {
      console.log('👋 [TokenManager] 移除訂閱者，剩餘:', this.subscribers.size);
    }
  }

  /**
   * 通知所有訂閱者 Token 變更（帶防抖）
   * @param newToken 新的 Token
   */
  private notifySubscribersDebounced(newToken: string | null): void {
    // 清除之前的定時器
    if (this.notificationDebounceTimer) {
      clearTimeout(this.notificationDebounceTimer);
    }
    
    // 設置新的防抖定時器
    this.notificationDebounceTimer = setTimeout(() => {
      this.notifySubscribers(newToken);
      this.notificationDebounceTimer = null;
    }, this.NOTIFICATION_DEBOUNCE_MS);
  }

  /**
   * 立即通知所有訂閱者 Token 變更
   * @param newToken 新的 Token
   */
  private notifySubscribers(newToken: string | null): void {
    if (this.subscribers.size === 0) {
      return;
    }
    
    console.log('📢 [TokenManager] 通知訂閱者 Token 變更，訂閱者數量:', this.subscribers.size);
    
    this.subscribers.forEach(subscriber => {
      try {
        subscriber.onTokenChange(newToken);
      } catch (error) {
        console.error('❌ [TokenManager] 訂閱者通知失敗:', error);
        
        // 如果訂閱者有錯誤處理方法，調用它
        if (subscriber.onTokenError) {
          try {
            subscriber.onTokenError(error as Error);
          } catch (handlerError) {
            console.error('❌ [TokenManager] 訂閱者錯誤處理失敗:', handlerError);
          }
        }
      }
    });
  }

  /**
   * 通知訂閱者 Token 錯誤
   * @param error 錯誤物件
   */
  private notifyTokenError(error: Error): void {
    this.subscribers.forEach(subscriber => {
      if (subscriber.onTokenError) {
        try {
          subscriber.onTokenError(error);
        } catch (handlerError) {
          console.error('❌ [TokenManager] 訂閱者錯誤通知失敗:', handlerError);
        }
      }
    });
  }

  /**
   * 獲取除錯資訊
   * @returns 除錯資訊物件
   */
  public getDebugInfo(): Record<string, any> {
    return {
      isInitialized: this.isInitialized,
      currentToken: this.currentToken ? '***已設定***' : '未設定',
      tokenStackDepth: this.tokenStack.length,
      subscribersCount: this.subscribers.size,
      validationCacheSize: this.validationCache.size,
      hasDebounceTimer: this.notificationDebounceTimer !== null
    };
  }

  /**
   * 銷毀管理器（清理資源）
   */
  public destroy(): void {
    // 清除防抖定時器
    if (this.notificationDebounceTimer) {
      clearTimeout(this.notificationDebounceTimer);
      this.notificationDebounceTimer = null;
    }
    
    // 清除所有訂閱者
    this.subscribers.clear();
    
    // 清除快取
    this.validationCache.clear();
    
    // 清除 Token 狀態
    this.currentToken = null;
    this.tokenStack = [];
    
    console.log('💥 [TokenManager] 管理器已銷毀');
  }
}

// 導出單例實例（便於直接使用）
export const tokenManager = TokenManager.getInstance();

// 導出預設值
export default tokenManager;