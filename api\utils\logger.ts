import { Request, Response } from "express";

// 定義用戶類型
interface AuthUser {
  account?: string;
  id?: string;
  roleType?: string;
  role?: string;
}

// API 日誌工具類
export class APILogger {
  // 格式化時間戳
  private static getTimestamp(): string {
    return new Date().toISOString().replace("T", " ").slice(0, 19);
  }

  // 獲取用戶信息
  private static getUserInfo(req: Request): string {
    const user = (req as Request & { user?: AuthUser }).user;
    if (user) {
      return `${user.account || user.id || "Unknown"}(${user.roleType || user.role || "Unknown Role"})`;
    }
    return "Anonymous";
  }

  // 獲取請求 IP
  private static getClientIP(req: Request): string {
    return req.ip || req.connection.remoteAddress || "Unknown IP";
  }

  // 記錄API請求開始
  static logRequest(req: Request, module: string, action: string): void {
    const timestamp = this.getTimestamp();
    const userInfo = this.getUserInfo(req);
    const ip = this.getClientIP(req);
    const method = req.method;
    const path = req.originalUrl;

    console.log(`\n🚀 [${timestamp}] [${module}] ${action}`);
    console.log(`📋 [Request] ${method} ${path}`);
    console.log(`👤 [User] ${userInfo}`);
    console.log(`🌐 [IP] ${ip}`);

    // 記錄請求參數
    if (Object.keys(req.params).length > 0) {
      console.log(`📊 [Params] ${JSON.stringify(req.params)}`);
    }

    if (Object.keys(req.query).length > 0) {
      console.log(`🔍 [Query] ${JSON.stringify(req.query)}`);
    }

    if (Object.keys(req.body).length > 0) {
      // 隱藏敏感信息
      const sanitizedBody = { ...req.body };
      if (sanitizedBody.password) sanitizedBody.password = "***";
      if (sanitizedBody.oldPassword) sanitizedBody.oldPassword = "***";
      if (sanitizedBody.newPassword) sanitizedBody.newPassword = "***";
      console.log(`📝 [Body] ${JSON.stringify(sanitizedBody)}`);
    }
  }

  // 記錄API請求成功
  static logSuccess(module: string, action: string, data?: unknown, message?: string): void {
    const timestamp = this.getTimestamp();
    console.log(`✅ [${timestamp}] [${module}] ${action} - 成功`);

    if (message) {
      console.log(`💬 [Message] ${message}`);
    }

    if (data) {
      // 如果是陣列，顯示長度
      if (Array.isArray(data)) {
        console.log(`📦 [Data] 返回 ${data.length} 筆記錄`);
      } else if (typeof data === "object") {
        console.log(`📦 [Data] ${JSON.stringify(data).slice(0, 200)}${JSON.stringify(data).length > 200 ? "..." : ""}`);
      }
    }
    console.log(`─────────────────────────────────────\n`);
  }

  // 記錄API請求失敗
  static logError(module: string, action: string, error: unknown, statusCode?: number): void {
    const timestamp = this.getTimestamp();
    console.log(`❌ [${timestamp}] [${module}] ${action} - 失敗`);

    if (statusCode) {
      console.log(`🔢 [Status] ${statusCode}`);
    }

    console.log(`💥 [Error] ${error instanceof Error ? error.message : String(error)}`);

    if (error instanceof Error && error.stack) {
      console.log(`📚 [Stack] ${error.stack}`);
    }
    console.log(`─────────────────────────────────────\n`);
  }

  // 記錄數據庫查詢
  static logDatabase(operation: string, query: string, params?: Record<string, unknown>, resultCount?: number): void {
    const timestamp = this.getTimestamp();
    console.log(`🗄️ [${timestamp}] [Database] ${operation}`);
    console.log(`📝 [Query] ${query.replace(/\s+/g, " ").trim()}`);

    if (params && Object.keys(params).length > 0) {
      console.log(`🔧 [Params] ${JSON.stringify(params)}`);
    }

    if (resultCount !== undefined) {
      console.log(`📊 [Result] ${resultCount} 筆記錄`);
    }
  }

  // 記錄認證相關
  static logAuth(action: string, userInfo?: unknown, success: boolean = true): void {
    const timestamp = this.getTimestamp();
    const status = success ? "✅" : "❌";
    console.log(`${status} [${timestamp}] [Auth] ${action}`);

    if (userInfo) {
      console.log(`👤 [User] ${JSON.stringify(userInfo)}`);
    }
  }
}
