// ========== 認證服務 - 業務邏輯和資料庫查詢 ==========

import crypto from "crypto";
import { executeQuerySingle, executeQuery } from "../config/database-mssql.js";
import { UserService } from "./user-service.js";
import { LoginResult, TokenValidationResult, AccountRow, MemberProfileRow, SchoolRow, TokenRow } from "../models/auth.js";
import {
  ALLOWED_ROLE_TYPES,
  ACCOUNT_STATUS,
  TOKEN_STATUS,
  PASSWORD_CONFIG,
  AUTH_ERROR_MESSAGES,
  AUTH_SUCCESS_MESSAGES,
  determineRoleTypeFromIntegerFields,
  mapRoleTypeToFrontendRole,
  validatePasswordStrength,
  isAllowedRole,
} from "../constants/auth.js";
import { APILogger } from "../utils/logger.js";
import { UserProfile } from "../models/user.js";

export class AuthService {
  // Token 驗證
  static async validateToken(token: string): Promise<TokenValidationResult> {
    try {
      // 1. 嘗試從新資料庫驗證
      try {
        const user = await UserService.getUserByTokenWithRoleMapping(token);
        if (user) {
          // 檢查角色是否被允許
          if (!isAllowedRole(user.roleType)) {
            console.log(`角色 ${user.roleType} 不被允許`);
            return {
              valid: false,
              message: AUTH_ERROR_MESSAGES.ROLE_NOT_ALLOWED,
              user: user,
            };
          }

          // 檢查帳號是否啟用
          if (!user.isActive) {
            console.log(`帳號 ${user.account} 已被停用`);
            return {
              valid: false,
              message: AUTH_ERROR_MESSAGES.ACCOUNT_DISABLED,
            };
          }

          return {
            valid: true,
            user: user,
            message: "Token validated from database with role mapping",
          };
        }
      } catch (dbError) {
        console.log("資料庫查詢失敗，回退到測試 Token:", dbError);
      }

      // 2. 檢查舊版 Token (向後相容)
      const legacyValidation = this.validateLegacyToken(token);
      if (legacyValidation.valid) {
        return legacyValidation;
      }

      return {
        valid: false,
        message: AUTH_ERROR_MESSAGES.INVALID_TOKEN,
      };
    } catch (error) {
      console.error("Token validation failed:", error);
      return {
        valid: false,
        message: `Token validation error: ${(error as Error).message}`,
      };
    }
  }

  // 密碼登入驗證
  static async validateAccountPassword(account: string, password: string): Promise<LoginResult> {
    try {
      const accountQuery = `
        SELECT 
          a.AccountId,
          a.Username as account,
          a.password,
          a.PasswordSalt as password_salt,
          a.email,
          a.phone,
          a.Status as AccountStatus,
          a.IsSchoolPartner,
          a.IsEpaUser,
          a.IsGuidanceTeam,
          mp.MemberName as member_cname,
          mp.MemberEmail as member_email,
          mp.MemberPhone as member_phone,
          mp.MemberPhotoFileId as member_photo,
          mp.MemberIntroduction as member_Introduction
        FROM Accounts a
        LEFT JOIN MemberProfiles mp ON mp.AccountId = a.AccountId AND mp.LocaleCode = 'zh-TW'
        WHERE a.Username = @account AND a.Status = @status
      `;

      const accountResult = await executeQuerySingle<AccountRow & MemberProfileRow>(accountQuery, {
        account,
        status: ACCOUNT_STATUS.ACTIVE,
      });

      if (!accountResult) {
        console.log("帳號不存在:", account);
        return {
          valid: false,
          message: AUTH_ERROR_MESSAGES.ACCOUNT_NOT_FOUND,
        };
      }

      // 驗證密碼
      const passwordValid = await this.verifyPassword(password, accountResult.password_salt, accountResult.password);
      if (!passwordValid) {
        console.log("密碼驗證失敗:", account);
        return {
          valid: false,
          message: AUTH_ERROR_MESSAGES.INVALID_CREDENTIALS,
        };
      }

      const user = await UserService.getUserByAccountId(accountResult.AccountId.toString());

      // 檢查角色權限
      if (!isAllowedRole(user.roleType)) {
        return {
          valid: false,
          user: user,
          message: AUTH_ERROR_MESSAGES.ROLE_NOT_ALLOWED,
        };
      }

      return {
        valid: true,
        user: user,
        message: AUTH_SUCCESS_MESSAGES.PASSWORD_LOGIN_SUCCESS,
      };
    } catch (error) {
      console.error("密碼驗證失敗:", error);
      return {
        valid: false,
        message: AUTH_ERROR_MESSAGES.INTERNAL_ERROR,
      };
    }
  }

  // 更新密碼
  static async updatePassword(userId: string, newPassword: string): Promise<{ success: boolean; message: string }> {
    try {
      // 驗證密碼強度
      const passwordValidation = validatePasswordStrength(newPassword);
      if (!passwordValidation.valid) {
        return {
          success: false,
          message: passwordValidation.message || AUTH_ERROR_MESSAGES.PASSWORD_REQUIREMENTS,
        };
      }

      // 生成新的 Salt 和密碼雜湊
      const salt = crypto.randomBytes(PASSWORD_CONFIG.SALT_LENGTH).toString("hex");
      const hashedPassword = crypto.createHmac(PASSWORD_CONFIG.HMAC_ALGORITHM, salt).update(newPassword).digest("hex");

      const updateQuery = `
        UPDATE Accounts 
        SET 
          password = @hashedPassword,
          PasswordSalt = @salt,
          UpdatedTime = GETDATE()
        WHERE AccountId = @userId AND Status = @status
      `;

      await executeQuery(updateQuery, {
        userId: parseInt(userId),
        hashedPassword,
        salt,
        status: ACCOUNT_STATUS.ACTIVE,
      });

      return {
        success: true,
        message: AUTH_SUCCESS_MESSAGES.PASSWORD_CHANGED,
      };
    } catch (error) {
      console.error("更新密碼失敗:", error);
      return {
        success: false,
        message: AUTH_ERROR_MESSAGES.PASSWORD_UPDATE_FAILED,
      };
    }
  }

  // 創建登入 Token
  static async createLoginToken(userId: string, validityDays: number = 30): Promise<string> {
    try {
      return await UserService.createUserToken(userId, "Login", validityDays);
    } catch (error) {
      console.error("創建 Token 失敗:", error);
      throw new Error(AUTH_ERROR_MESSAGES.TOKEN_GENERATION_FAILED);
    }
  }

  // 撤銷 Token
  static async revokeToken(token: string): Promise<void> {
    try {
      await UserService.revokeUserToken(token);
    } catch (error) {
      console.error("撤銷 Token 失敗:", error);
      throw new Error("Failed to revoke token");
    }
  }

  // 批量重設密碼（管理員功能）
  static async resetPasswordsForAccounts(accountIds: number[], newPassword: string): Promise<Array<{ accountId: number; success: boolean; message: string }>> {
    const results: Array<{ accountId: number; success: boolean; message: string }> = [];

    for (const accountId of accountIds) {
      try {
        const result = await this.updatePassword(accountId.toString(), newPassword);
        results.push({
          accountId,
          success: result.success,
          message: result.message,
        });
      } catch (error) {
        results.push({
          accountId,
          success: false,
          message: `重設失敗: ${(error as Error).message}`,
        });
      }
    }

    return results;
  }

  // ========== 私有輔助方法 ==========

  // 驗證密碼（支援 HMAC-SHA256 和 MD5 向下相容）
  private static async verifyPassword(password: string, salt?: string, hashedPassword?: string): Promise<boolean> {
    if (!salt || !hashedPassword) return false;

    try {
      // 優先嘗試 HMAC-SHA256 驗證 (新格式)
      const hmacPassword = crypto.createHmac(PASSWORD_CONFIG.HMAC_ALGORITHM, salt).update(password).digest("hex");
      if (hmacPassword === hashedPassword) {
        console.log("HMAC-SHA256 密碼驗證成功");
        return true;
      }

      // 如果 HMAC-SHA256 失敗，嘗試 MD5 驗證 (舊格式，向下相容)
      if (hashedPassword.length === 32) {
        const md5Password = crypto
          .createHash("md5")
          .update(salt + password)
          .digest("hex");
        if (md5Password === hashedPassword) {
          console.log("MD5 密碼驗證成功（向下相容）");
          // TODO: 自動升級為 HMAC-SHA256 格式
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("密碼驗證時發生錯誤:", error);
      return false;
    }
  }

  // 驗證舊版 Token（向下相容）
  private static validateLegacyToken(token: string): TokenValidationResult {
    // 舊版固定 Token
    const LEGACY_TOKENS = {
      eco_campus_user_778_token: {
        id: "778",
        name: "使用者 778",
        email: "<EMAIL>",
        role: "school",
        permissions: ["read", "write", "certification_apply", "certification_manage"],
      },
      valid_test_token_123456789: {
        id: "778",
        name: "測試使用者 778",
        email: "<EMAIL>",
        role: "school",
        permissions: ["read", "write", "certification_apply"],
      },
    };

    const legacyUser = LEGACY_TOKENS[token as keyof typeof LEGACY_TOKENS];
    if (legacyUser) {
      const mappedRole = this.mapOldRoleToNew(legacyUser.role);
      if (!isAllowedRole(mappedRole)) {
        return {
          valid: false,
          message: `Legacy token role ${legacyUser.role} is not authorized`,
        };
      }

      const user: UserProfile = {
        id: legacyUser.id,
        account: legacyUser.email,
        nickName: legacyUser.name,
        email: legacyUser.email,
        roleType: mappedRole,
        permissions: legacyUser.permissions,
        permissionGroups: [legacyUser.role],
        isActive: true,
        createdTime: new Date(),
        updatedTime: new Date(),
        remark: "",
        school: undefined,
        certifications: [],
      };

      return {
        valid: true,
        user: user,
      };
    }

    // 通用舊版 Token 格式
    if (token.startsWith("valid_") && token.length > 10) {
      const user: UserProfile = {
        id: "778",
        account: "<EMAIL>",
        nickName: "測試使用者",
        email: "<EMAIL>",
        roleType: "School",
        permissions: ["read", "write"],
        permissionGroups: ["school"],
        isActive: true,
        createdTime: new Date(),
        updatedTime: new Date(),
        remark: "",
        school: undefined,
        certifications: [],
      };

      return {
        valid: true,
        user: user,
      };
    }

    return {
      valid: false,
      message: AUTH_ERROR_MESSAGES.INVALID_TOKEN,
    };
  }

  // 舊角色映射
  private static mapOldRoleToNew(oldRole: string): string {
    switch (oldRole.toLowerCase()) {
      case "school":
        return "School";
      case "epa":
      case "government":
        return "Government";
      case "tutor":
        return "Tutor";
      default:
        return "School";
    }
  }
}
