import { Request, Response, NextFunction } from "express";
import { APILogger } from "./logger.js";
import { FileConfigManager } from "../config/config-manager.js";

// 標準 API 響應介面
export interface StandardAPIResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  details?: Record<string, unknown>;
  timestamp?: string;
}

// 分頁響應介面
export interface PaginatedResponse<T = unknown> extends StandardAPIResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Token 驗證響應介面
export interface TokenValidationResponse extends StandardAPIResponse {
  valid: boolean;
  user?: {
    id: string;
    account: string;
    nickName?: string;
    email?: string;
    roleType: string;
    permissions: string[];
    permissionGroups: string[];
    phone?: string;
    avatar?: string;
    isActive: boolean;
    school?: {
      id: string;
      name: string;
      englishName?: string;
      code?: string;
      address?: string;
      phone?: string;
      email?: string;
      website?: string;
    };
    certifications?: Array<{
      id: string;
      certificationType: string;
      level?: number;
      status: string;
      applyDate: Date;
      certificateNumber?: string;
    }>;
  };
  token?: string;
  details?: {
    userRole?: string;
    allowedRoles?: string[];
    action?: string;
  };
}

/**
 * 發送成功響應
 */
export function sendSuccess<T>(res: Response, data?: T, message?: string, statusCode: number = 200): Response<StandardAPIResponse<T>> {
  const response: StandardAPIResponse<T> = {
    success: true,
    data,
    message: message || "操作成功",
    timestamp: new Date().toISOString(),
  };

  return res.status(statusCode).json(response);
}

/**
 * 檢查是否為開發環境
 */
export function isDevelopmentEnvironment(): boolean {
  try {
    const configManager = FileConfigManager.getInstance();
    return configManager.getEnvironment() === "development";
  } catch {
    // 如果配置管理器未初始化，退回到環境變數檢查
    return process.env.NODE_ENV === "development";
  }
}

/**
 * 發送錯誤響應
 */
export function sendError(
  res: Response,
  message: string,
  statusCode: number = 500,
  error?: string,
  details?: Record<string, unknown>
): Response<StandardAPIResponse> {
  const response: StandardAPIResponse = {
    success: false,
    message,
    error,
    details,
    timestamp: new Date().toISOString(),
  };

  return res.status(statusCode).json(response);
}

/**
 * 發送分頁響應
 */
export function sendPaginatedSuccess<T>(
  res: Response,
  data: T[],
  pagination: PaginatedResponse<T>["pagination"],
  message?: string,
  statusCode: number = 200
): Response<PaginatedResponse<T>> {
  const response: PaginatedResponse<T> = {
    success: true,
    data,
    pagination,
    message: message || "查詢成功",
    timestamp: new Date().toISOString(),
  };

  return res.status(statusCode).json(response);
}

/**
 * 發送 Token 驗證響應
 */
export function sendTokenValidationResponse(
  res: Response,
  valid: boolean,
  user?: TokenValidationResponse["user"],
  token?: string,
  message?: string,
  statusCode?: number,
  details?: TokenValidationResponse["details"]
): Response<TokenValidationResponse> {
  const response: TokenValidationResponse = {
    success: valid,
    valid,
    user,
    token,
    message: message || (valid ? "Token 驗證成功" : "Token 驗證失敗"),
    details,
    timestamp: new Date().toISOString(),
  };

  const code = statusCode || (valid ? 200 : 401);
  return res.status(code).json(response);
}

/**
 * 統一的認證失敗響應
 */
export function sendAuthenticationRequired(res: Response, message?: string): Response<StandardAPIResponse> {
  return sendError(res, message || "需要認證", 401, "authentication_required");
}

/**
 * 統一的權限不足響應
 */
export function sendPermissionDenied(res: Response, requiredPermission?: string, userRole?: string, userPermissions?: string[]): Response<StandardAPIResponse> {
  return sendError(res, "權限不足", 403, "permission_denied", {
    requiredPermission,
    userRole,
    userPermissions,
  });
}

/**
 * 統一的角色權限不足響應
 */
export function sendRoleAccessDenied(res: Response, userRole: string, allowedRoles: string[], shouldLogout: boolean = false): Response<StandardAPIResponse> {
  return sendError(res, "存取被拒絕。您的帳號角色不被允許存取此系統。", 403, "role_access_denied", {
    userRole,
    allowedRoles,
    action: shouldLogout ? "account_logout" : undefined,
  });
}

/**
 * 統一的帳號停用響應
 */
export function sendAccountDeactivated(res: Response): Response<StandardAPIResponse> {
  return sendError(res, "存取被拒絕。您的帳號已被停用。", 403, "account_deactivated", {
    action: "account_logout",
  });
}

/**
 * 統一的資料不存在響應
 */
export function sendNotFound(res: Response, resource?: string): Response<StandardAPIResponse> {
  return sendError(res, `${resource || "資料"}不存在`, 404, "not_found");
}

/**
 * 統一的驗證失敗響應
 */
export function sendValidationError(res: Response, validationErrors: Record<string, string>, message?: string): Response<StandardAPIResponse> {
  return sendError(res, message || "資料驗證失敗", 400, "validation_error", {
    validationErrors,
  });
}

/**
 * 統一的操作失敗響應（自動處理開發環境錯誤詳情）
 */
export function sendOperationError(res: Response, message: string, error?: unknown, statusCode: number = 500): Response<StandardAPIResponse> {
  const isDev = isDevelopmentEnvironment();

  return sendError(res, message, statusCode, "operation_failed", {
    // 只在開發環境包含錯誤詳情
    ...(isDev && error && { error }),
  });
}

/**
 * 統一的內部伺服器錯誤響應
 */
export function sendInternalServerError(res: Response, error?: Error, module?: string, action?: string): Response<StandardAPIResponse> {
  // 記錄詳細錯誤
  if (error && module && action) {
    APILogger.logError(module, action, error, 500);
  }

  const isDev = isDevelopmentEnvironment();

  return sendError(res, "內部伺服器錯誤", 500, "internal_server_error", {
    errorMessage: error?.message,
    module,
    action,
    // 只在開發環境包含完整錯誤堆疊
    ...(isDev && error && { stack: error.stack }),
    // 在開發環境包含完整錯誤對象
    ...(isDev && error && { fullError: error }),
  });
}

/**
 * 創建分頁信息
 */
export function createPaginationInfo(page: number, limit: number, total: number): PaginatedResponse["pagination"] {
  const totalPages = Math.ceil(total / limit);

  return {
    page,
    limit,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
}

/**
 * 處理異步路由的錯誤包裝器
 */
export function asyncRouteHandler(handler: (req: Request, res: Response, next?: NextFunction) => Promise<void>) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await handler(req, res, next);
    } catch (error) {
      APILogger.logError("AsyncRoute", "Handler", error, 500);
      sendInternalServerError(res, error as Error);
    }
  };
}

/**
 * 響應時間中間件
 */
export function responseTimeMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();

    const originalSend = res.send;
    res.send = function (data) {
      const responseTime = Date.now() - startTime;
      res.setHeader("X-Response-Time", `${responseTime}ms`);
      return originalSend.call(this, data);
    };

    next();
  };
}
