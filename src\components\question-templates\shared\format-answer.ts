// 格式化答案數據的輔助函數（提供給外部使用）
export const formatAnswerForSave = (templateId: number, answerData: unknown): unknown => {
  try {
    switch (templateId) {
      case 1:
        return { is_yes_no: (answerData as { is_yes_no?: string }).is_yes_no || "" };
      case 2: {
        const teamData = answerData as {
          student_list?: Array<{ input_1: string; input_2: string; input_3: string }>;
          teacher_list?: Array<{ input_1: string; input_2: string; input_3: string }>;
          community_member_list?: Array<{ input_1: string; input_2: string; input_3: string }>;
        };
        return {
          student_list: teamData.student_list || [],
          teacher_list: teamData.teacher_list || [],
          community_member_list: teamData.community_member_list || [],
        };
      }
      case 3: {
        const meetingData = answerData as {
          meeting_date_and_theme?: Array<{ input_1: string; input_2: string }>;
          file?: Array<{ file_url: string; file_name: string }>;
        };
        return {
          meeting_date_and_theme: meetingData.meeting_date_and_theme || [],
          file: meetingData.file || [],
        };
      }
      case 6: {
        const photoData = answerData as {
          photo_record?: Array<{ photo_url: string; photo_date: string; photo_des: string }>;
        };
        return {
          photo_record: photoData.photo_record || [],
        };
      }
      case 11: {
        const template11Data = answerData as {
          question_1?: string;
          question_2?: string;
          question_3?: string;
          question_4?: string;
        };
        return {
          question_1: template11Data.question_1 || "",
          question_2: template11Data.question_2 || "",
          question_3: template11Data.question_3 || "",
          question_4: template11Data.question_4 || "",
        };
      }
      case 13: {
        const template13Data = answerData as {
          question_1?: string;
          question_2?: string;
        };
        return {
          question_1: template13Data.question_1 || "",
          question_2: template13Data.question_2 || "",
        };
      }
      case 22: {
        const template22Data = answerData as {
          case_1?: { grade: string; course: string; activity: string };
          case_2?: { grade: string; course: string; activity: string };
          case_3?: { grade: string; course: string; activity: string };
        };
        return {
          case_1: template22Data.case_1 || { grade: "", course: "", activity: "" },
          case_2: template22Data.case_2 || { grade: "", course: "", activity: "" },
          case_3: template22Data.case_3 || { grade: "", course: "", activity: "" },
        };
      }
      default:
        return answerData;
    }
  } catch (error) {
    console.warn("格式化答案數據時出現警告:", error);
    return answerData;
  }
};
