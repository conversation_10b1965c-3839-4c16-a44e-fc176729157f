import { UserProfile } from "./user";

export interface LoginResult {
  valid: boolean;
  user?: UserProfile;
  message?: string;
}

export interface TokenValidationResult {
  valid: boolean;
  user?: UserProfile;
  message?: string;
}

export interface AccountRow {
  AccountId: number;
  Username: string;
  password: string;
  password_salt: string;
  Email?: string;
  Phone?: string;
  Status: number;
  IsSchoolPartner?: number;
  IsEpaUser?: number;
  IsGuidanceTeam?: number;
  SchoolId?: number;
}

export interface MemberProfileRow {
  MemberName?: string;
  EnglishName?: string;
  JobTitle?: string;
  ContactNumber?: string;
  Email?: string;
}

export interface SchoolRow {
  Id: number;
  Name?: string;
  EnglishName?: string;
  Code?: string;
  Address?: string;
  Phone?: string;
  Email?: string;
  Website?: string;
}

export interface TokenRow {
  UserTokenSid: number;
  Token: string;
  TokenType: string;
  ExpirationDate: Date;
  IsActive: number;
  CreatedTime: Date;
  LastAccessDate?: Date;
}

// API 請求和回應介面
export interface TokenValidationRequest {
  userToken: string;
}

export interface TokenLoginRequest {
  token: string;
}

export interface PasswordLoginRequest {
  account: string;
  password: string;
}

export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}

export interface AdminResetPasswordRequest {
  accountIds: number[];
  newPassword: string;
}

export interface CreateTokenRequest {
  accountId?: number;
  tokenType?: string;
  validityDays?: number;
}

export interface TokenValidationResponse {
  success: boolean;
  data: {
    valid: boolean;
    user?: UserProfile;
    details?: {
      userRole?: string;
      allowedRoles?: string[];
      action?: string;
    };
  };
  message?: string;
}

export interface PasswordLoginResponse {
  success: boolean;
  data: {
    user?: UserProfile;
    valid?: boolean;
    token?: string;
    details?: {
      userRole?: string;
      allowedRoles?: string[];
      action?: string;
    };
  };
  message?: string;
}

export interface ChangePasswordResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    account: string;
  };
}

export interface AdminResetPasswordResponse {
  success: boolean;
  message: string;
  results: Array<{
    accountId: number;
    success: boolean;
    message: string;
  }>;
}

export interface CreateTokenResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    tokenType: string;
    expirationDate: string;
    accountId: number;
  };
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface RoleCheckResponse {
  success: boolean;
  valid: boolean;
  user?: UserProfile;
  message?: string;
  details?: {
    userRole?: string;
    allowedRoles?: string[];
    action?: string;
  };
}

// 路由參數類型
export interface TokenValidationParams {}
export interface PasswordLoginParams {}
export interface ChangePasswordParams {}
export interface AdminResetPasswordParams {}
export interface CreateTokenParams {}
export interface LogoutParams {}
export interface RoleCheckParams {}
