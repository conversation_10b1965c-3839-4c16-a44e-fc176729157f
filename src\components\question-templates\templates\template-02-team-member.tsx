import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Users, Trash2, Plus } from "lucide-react";
import type { TeamMemberData, TemplateProps } from "../types";

// 模板2 - 團隊成員表單
export const TeamMemberTemplate: React.FC<TemplateProps<TeamMemberData>> = ({ data, onChange, disabled = false }) => {
  const safeData = {
    student_list: data.student_list || [],
    teacher_list: data.teacher_list || [],
    community_member_list: data.community_member_list || [],
  };

  const updateMemberList = (listType: keyof TeamMemberData, index: number, field: string, value: string) => {
    const currentList = safeData[listType] as Array<{ input_1: string; input_2: string; input_3: string }>;
    const updatedList = [...currentList];
    updatedList[index] = { ...updatedList[index], [field]: value };
    onChange({ ...safeData, [listType]: updatedList });
  };

  const addMember = (listType: keyof TeamMemberData) => {
    const currentList = safeData[listType] as Array<{ input_1: string; input_2: string; input_3: string }>;
    const newMember = { input_1: "", input_2: "", input_3: "" };
    onChange({ ...safeData, [listType]: [...currentList, newMember] });
  };

  const removeMember = (listType: keyof TeamMemberData, index: number) => {
    const currentList = safeData[listType] as Array<{ input_1: string; input_2: string; input_3: string }>;
    const updatedList = currentList.filter((_, i) => i !== index);
    onChange({ ...safeData, [listType]: updatedList });
  };

  const renderMemberSection = (listType: keyof TeamMemberData, title: string, fieldLabels: { input_1: string; input_2: string; input_3: string }) => {
    const memberList = safeData[listType] as Array<{ input_1: string; input_2: string; input_3: string }>;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="font-size-base flex items-center gap-2">
            <Users className="w-4 h-4" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {memberList.map((member, index) => (
            <div key={index} className="grid grid-cols-3 gap-3 p-3 border border-gray-200 rounded-lg">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-1">{fieldLabels.input_1}</label>
                <Input value={member.input_1} onChange={(e) => updateMemberList(listType, index, "input_1", e.target.value)} disabled={disabled} />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-1">{fieldLabels.input_2}</label>
                <Input value={member.input_2} onChange={(e) => updateMemberList(listType, index, "input_2", e.target.value)} disabled={disabled} />
              </div>
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <label className="block font-size-sm font-medium text-gray-700 mb-1">{fieldLabels.input_3}</label>
                  <Input value={member.input_3} onChange={(e) => updateMemberList(listType, index, "input_3", e.target.value)} disabled={disabled} />
                </div>
                {!disabled && (
                  <Button variant="outline" size="sm" onClick={() => removeMember(listType, index)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
          {!disabled && (
            <Button variant="outline" onClick={() => addMember(listType)} className="w-full border-dashed">
              <Plus className="w-4 h-4 mr-2" />
              新增{title}
            </Button>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {renderMemberSection("student_list", "學生成員", {
        input_1: "年級",
        input_2: "姓名",
        input_3: "任務分工",
      })}
      {renderMemberSection("teacher_list", "教師成員", {
        input_1: "單位/職位",
        input_2: "姓名",
        input_3: "任務分工",
      })}
      {renderMemberSection("community_member_list", "社區成員", {
        input_1: "單位/職位",
        input_2: "姓名",
        input_3: "任務分工",
      })}
    </div>
  );
};
