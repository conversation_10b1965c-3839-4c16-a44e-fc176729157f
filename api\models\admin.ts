// ========== 管理員功能相關資料模型 ==========

export interface AuthenticatedUser {
  id: string;
  account: string;
  roleType: string;
  permissions: string[];
  permissionGroups: string[];
  isActive: boolean;
}

export interface AnswerStatusMarkRequest {
  certificationId: string;
  questionId: string;
  status: string;
  action: string;
}

export interface ReviewCommentRequest {
  certificationId: string;
  stepId: string;
  comment: string;
}

export interface AnswerStatusInfo {
  answerId: number;
  questionId: number;
  previousStatus: number;
  newStatus: number;
  statusDescription: string;
  action: string;
  timestamp: string;
}

export interface AdminActionLog {
  actionLogId: number;
  certificationId: number;
  questionId: number;
  answerId: number;
  action: string;
  previousStatus: number;
  newStatus: number;
  actionTime: Date;
  notes: string;
  adminUsername: string;
  questionTitle: string;
}

export interface ReviewComment {
  certificationStepRecordId: number;
  certificationId: number;
  stepNumber: number;
  comment: string;
  createdTime: Date;
  updatedTime: Date;
  adminUsername: string;
}

export interface ReviewCommentSaveResult {
  recordId?: number;
  action: string;
  timestamp: string;
}

// 資料庫查詢結果介面
export interface CertificationQueryResult {
  CertificationId: number;
  ReviewStatus: number;
  Status: number;
}

export interface AnswerQueryResult {
  AnswerId: number;
  QuestionId: number;
  AnswerStatus: number;
}

export interface ActionLogQueryResult {
  ActionLogId: number;
  CertificationId: number;
  QuestionId: number;
  AnswerId: number;
  Action: string;
  PreviousStatus: number;
  NewStatus: number;
  ActionTime: Date;
  Notes: string;
  AdminUsername: string;
  QuestionTitle: string;
}

export interface ReviewCommentQueryResult {
  CertificationStepRecordId: number;
  CertificationId: number;
  StepNumber: number;
  Comment: string;
  CreatedTime: Date;
  UpdatedTime: Date;
  AdminUsername: string;
}

export interface StepRecordQueryResult {
  CertificationStepRecordId: number;
}

// API 回應介面
export interface AnswerStatusMarkResponse {
  success: boolean;
  message: string;
  data: AnswerStatusInfo;
}

export interface ActionLogsResponse {
  success: boolean;
  message: string;
  data: AdminActionLog[];
}

export interface ReviewCommentSaveResponse {
  success: boolean;
  message: string;
  data: ReviewCommentSaveResult;
}

export interface ReviewCommentsResponse {
  success: boolean;
  message: string;
  data: ReviewComment[];
}

// 查詢參數類型
export interface AnswerStatusMarkParams {}

export interface ActionLogsParams {
  certificationId: string;
}

export interface ReviewCommentSaveParams {}

export interface ReviewCommentsParams {
  certificationId: string;
}

export interface AnswerStatusMarkQueryParams {}

export interface ActionLogsQueryParams {}

export interface ReviewCommentSaveQueryParams {}

export interface ReviewCommentsQueryParams {}
