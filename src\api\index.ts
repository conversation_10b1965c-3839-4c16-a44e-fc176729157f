// API 統一導出檔案

import { authAPI } from "./authAPI";
import { campusSubmissionAPI } from "./campusSubmissionAPI";
import { certificationAPI } from "./certificationAPI";
import { dashboardAPI } from "./dashboardAPI";
import { userAPI } from "./userAPI";

// 基底 API 類別和介面
export { BaseAPI, type ApiResponse } from "./BaseAPI";

// 認證相關 API
export { AuthAPI, authAPI } from "./authAPI";

// 用戶相關 API
export { UserAPI, userAPI } from "./userAPI";

// 認證申請相關 API
export { CertificationAPI, certificationAPI } from "./certificationAPI";

// 校園投稿相關 API
export { CampusSubmissionAPI, campusSubmissionAPI } from "./campusSubmissionAPI";

// 儀表板相關 API
export { DashboardAPI, dashboardAPI } from "./dashboardAPI";

// 便捷的 API 實例集合
export const api = {
  auth: authAPI,
  user: userAPI,
  certification: certificationAPI,
  campusSubmission: campusSubmissionAPI,
  dashboard: dashboardAPI,
};

// 型別定義匯出
export type { 
  LoginCredentials,
  LoginResponse,
  TokenStatusResponse,
  ChangePasswordResponse
} from "./authAPI";

export type {
  MemberProfile,
  UpdateProfileData,
  UserProfileListResponse
} from "./userAPI";

export type {
  DashboardStats,
  CertificationProgress
} from "./dashboardAPI";
