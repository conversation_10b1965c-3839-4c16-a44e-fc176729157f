// ========== 模板答案相關常數 ==========

// 支援的模板ID
export const SUPPORTED_TEMPLATE_IDS = {
  YES_NO: 1, // 是非選擇題
  TEAM_MEMBER: 2, // 團隊成員表單
  MEETING_RECORD: 3, // 會議記錄
  SHARE_MEETING: 4, // 分享會議資訊
  RECRUIT_MEMBER: 5, // 招募新成員
  PHOTO_RECORD: 6, // 照片上傳
  ENVIRONMENT_PATH: 8, // 環境路徑選擇
  TEXTAREA: 16, // 文字區域
  SUMMARY: 19, // 總結申論
  RECERTIFICATION_MEETING: 21, // 會議記錄（再認證）
} as const;

// 答案狀態
export const ANSWER_STATUS = {
  DRAFT: "draft", // 草稿狀態
  SUBMITTED: "submitted", // 已提交
  VALIDATED: "validated", // 已驗證
  REJECTED: "rejected", // 已拒絕
} as const;

// 是非選擇值
export const YES_NO_VALUES = ["0", "1", ""] as const;

// 日期格式
export const DATE_FORMAT_REGEX = /^\d{4}-\d{2}-\d{2}$/;

// 錯誤訊息
export const TEMPLATE_ANSWER_ERROR_MESSAGES = {
  MISSING_PARAMETERS: "缺少必要參數",
  VALIDATION_FAILED: "答案格式驗證失敗",
  SAVE_FAILED: "模板答案儲存失敗",
  VALIDATE_FAILED: "答案驗證失敗",
  UNKNOWN_ERROR: "未知錯誤",
  VALIDATION_ERROR: "驗證過程中發生錯誤",
  UNKNOWN_TEMPLATE: "未知的模板ID",

  // 欄位特定錯誤
  MISSING_YES_NO: "缺少 is_yes_no 欄位",
  INVALID_YES_NO: "is_yes_no 必須是 '0', '1' 或空字串",
  INVALID_ARRAY: "必須是陣列",
  INVALID_STRING: "必須是字串",
  MISSING_FIELD: "缺少必要欄位",
  INVALID_DATE_FORMAT: "日期格式錯誤，應為 YYYY-MM-DD",
  MISSING_DATE_OR_THEME: "缺少日期或主題",
  INVALID_MEMBER_FORMAT: "的欄位格式錯誤",
  MISSING_PATH_OR_NAME: "缺少路徑或名稱",
  MISSING_DATE_INFO: "缺少日期資訊",
} as const;

// 成功訊息
export const TEMPLATE_ANSWER_SUCCESS_MESSAGES = {
  VALIDATION_SUCCESS: "答案格式驗證成功",
  SAVE_SUCCESS: "模板答案保存成功",
  FORMAT_VALID: "答案格式有效",
} as const;

// 必要參數
export const REQUIRED_PARAMETERS = {
  SAVE: ["certification_sid", "question_sid", "template_id", "answer_data"],
  VALIDATE: ["template_id", "answer_data"],
} as const;

// 模板特定驗證規則
export const TEMPLATE_VALIDATION_RULES = {
  [SUPPORTED_TEMPLATE_IDS.YES_NO]: {
    required_fields: ["is_yes_no"],
    valid_values: {
      is_yes_no: YES_NO_VALUES,
    },
  },
  [SUPPORTED_TEMPLATE_IDS.TEAM_MEMBER]: {
    required_lists: ["student_list", "teacher_list", "community_member_list"],
    member_fields: ["input_1", "input_2", "input_3"],
  },
  [SUPPORTED_TEMPLATE_IDS.MEETING_RECORD]: {
    required_arrays: ["meeting_date_and_theme", "file"],
    meeting_fields: ["input_1", "input_2"],
    date_validation: true,
  },
  [SUPPORTED_TEMPLATE_IDS.SHARE_MEETING]: {
    required_fields: ["is_yes_no"],
    required_objects: ["share_people", "how_share_meeting"],
    checkbox_fields: ["share_people.checkbox", "how_share_meeting.checkbox"],
  },
  [SUPPORTED_TEMPLATE_IDS.RECRUIT_MEMBER]: {
    required_fields: ["is_yes_no", "textarea"],
    string_fields: ["textarea"],
  },
  [SUPPORTED_TEMPLATE_IDS.PHOTO_RECORD]: {
    required_arrays: ["photo_record"],
  },
  [SUPPORTED_TEMPLATE_IDS.ENVIRONMENT_PATH]: {
    required_arrays: ["improve_path_list"],
    path_fields: ["path", "cname"],
    date_fields: ["date.input_1", "date.input_2"],
  },
  [SUPPORTED_TEMPLATE_IDS.TEXTAREA]: {
    required_fields: ["textarea"],
    string_fields: ["textarea"],
  },
  [SUPPORTED_TEMPLATE_IDS.SUMMARY]: {
    required_textareas: 5,
  },
  [SUPPORTED_TEMPLATE_IDS.RECERTIFICATION_MEETING]: {
    required_textareas: 7,
  },
} as const;

// 工具函數：驗證模板答案
export const validateTemplateAnswer = (templateId: number, answerData: Record<string, unknown>): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  try {
    switch (templateId) {
      case SUPPORTED_TEMPLATE_IDS.YES_NO: {
        if (!("is_yes_no" in answerData)) {
          errors.push(TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_YES_NO);
        } else if (!YES_NO_VALUES.includes(String(answerData.is_yes_no) as any)) {
          errors.push(TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_YES_NO);
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.TEAM_MEMBER: {
        const requiredLists = ["student_list", "teacher_list", "community_member_list"];
        for (const listName of requiredLists) {
          if (!Array.isArray(answerData[listName])) {
            errors.push(`${listName} ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_ARRAY}`);
          } else {
            const memberList = answerData[listName] as Array<Record<string, unknown>>;
            memberList.forEach((member, index) => {
              if (typeof member.input_1 !== "string" || typeof member.input_2 !== "string" || typeof member.input_3 !== "string") {
                errors.push(`${listName}[${index}] ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_MEMBER_FORMAT}`);
              }
            });
          }
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.MEETING_RECORD: {
        if (!Array.isArray(answerData.meeting_date_and_theme)) {
          errors.push(`meeting_date_and_theme ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_ARRAY}`);
        } else {
          const meetings = answerData.meeting_date_and_theme as Array<Record<string, unknown>>;
          meetings.forEach((meeting, index) => {
            if (!meeting.input_1 || !meeting.input_2) {
              errors.push(`meeting_date_and_theme[${index}] ${TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_DATE_OR_THEME}`);
            }
            if (meeting.input_1 && !DATE_FORMAT_REGEX.test(String(meeting.input_1))) {
              errors.push(`meeting_date_and_theme[${index}] ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_DATE_FORMAT}`);
            }
          });
        }
        if (!Array.isArray(answerData.file)) {
          errors.push(`file ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_ARRAY}`);
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.SHARE_MEETING: {
        if (!("is_yes_no" in answerData)) {
          errors.push(TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_YES_NO);
        }

        const sharePeople = answerData.share_people as Record<string, unknown> | undefined;
        if (!sharePeople || !Array.isArray(sharePeople.checkbox)) {
          errors.push(`share_people.checkbox ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_ARRAY}`);
        }

        const howShareMeeting = answerData.how_share_meeting as Record<string, unknown> | undefined;
        if (!howShareMeeting || !Array.isArray(howShareMeeting.checkbox)) {
          errors.push(`how_share_meeting.checkbox ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_ARRAY}`);
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.RECRUIT_MEMBER: {
        if (!("is_yes_no" in answerData)) {
          errors.push(TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_YES_NO);
        }
        if (typeof answerData.textarea !== "string") {
          errors.push(`textarea ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_STRING}`);
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.PHOTO_RECORD: {
        if (!Array.isArray(answerData.photo_record)) {
          errors.push(`photo_record ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_ARRAY}`);
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.ENVIRONMENT_PATH: {
        if (!Array.isArray(answerData.improve_path_list)) {
          errors.push(`improve_path_list ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_ARRAY}`);
        } else {
          const pathList = answerData.improve_path_list as Array<Record<string, unknown>>;
          pathList.forEach((path, index) => {
            if (!path.path || !path.cname) {
              errors.push(`improve_path_list[${index}] ${TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_PATH_OR_NAME}`);
            }
            const pathDate = path.date as Record<string, unknown> | undefined;
            if (!pathDate || !pathDate.input_1 || !pathDate.input_2) {
              errors.push(`improve_path_list[${index}] ${TEMPLATE_ANSWER_ERROR_MESSAGES.MISSING_DATE_INFO}`);
            }
          });
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.TEXTAREA: {
        if (typeof answerData.textarea !== "string") {
          errors.push(`textarea ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_STRING}`);
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.SUMMARY: {
        for (let i = 1; i <= 5; i++) {
          if (typeof answerData[`textarea_${i}`] !== "string") {
            errors.push(`textarea_${i} ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_STRING}`);
          }
        }
        break;
      }

      case SUPPORTED_TEMPLATE_IDS.RECERTIFICATION_MEETING: {
        for (let i = 1; i <= 7; i++) {
          if (typeof answerData[`textarea_${i}`] !== "string") {
            errors.push(`textarea_${i} ${TEMPLATE_ANSWER_ERROR_MESSAGES.INVALID_STRING}`);
          }
        }
        break;
      }

      default:
        errors.push(`${TEMPLATE_ANSWER_ERROR_MESSAGES.UNKNOWN_TEMPLATE}: ${templateId}`);
    }
  } catch (error) {
    errors.push(`${TEMPLATE_ANSWER_ERROR_MESSAGES.VALIDATION_ERROR}: ${String(error)}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// 工具函數：標準化答案格式
export const standardizeAnswerFormat = (templateId: number, answerData: Record<string, unknown>): Record<string, unknown> => {
  switch (templateId) {
    case SUPPORTED_TEMPLATE_IDS.YES_NO:
      return {
        is_yes_no: String(answerData.is_yes_no || ""),
      };

    case SUPPORTED_TEMPLATE_IDS.TEAM_MEMBER:
      return {
        student_list: answerData.student_list || [],
        teacher_list: answerData.teacher_list || [],
        community_member_list: answerData.community_member_list || [],
      };

    case SUPPORTED_TEMPLATE_IDS.MEETING_RECORD:
      return {
        meeting_date_and_theme: answerData.meeting_date_and_theme || [],
        file: answerData.file || [],
      };

    case SUPPORTED_TEMPLATE_IDS.SHARE_MEETING: {
      const sharePeople = answerData.share_people as Record<string, unknown> | undefined;
      const howShareMeeting = answerData.how_share_meeting as Record<string, unknown> | undefined;
      const text = howShareMeeting?.text as Record<string, unknown> | undefined;

      return {
        is_yes_no: String(answerData.is_yes_no || ""),
        share_people: {
          checkbox: sharePeople?.checkbox || [],
        },
        how_share_meeting: {
          checkbox: howShareMeeting?.checkbox || [],
          text: {
            text_1: String(text?.text_1 || ""),
            text_2: String(text?.text_2 || ""),
            text_3: String(text?.text_3 || ""),
          },
        },
      };
    }

    case SUPPORTED_TEMPLATE_IDS.RECRUIT_MEMBER:
      return {
        is_yes_no: String(answerData.is_yes_no || ""),
        textarea: String(answerData.textarea || ""),
      };

    case SUPPORTED_TEMPLATE_IDS.PHOTO_RECORD:
      return {
        photo_record: answerData.photo_record || [],
      };

    case SUPPORTED_TEMPLATE_IDS.ENVIRONMENT_PATH:
      return {
        improve_path_list: answerData.improve_path_list || [],
      };

    case SUPPORTED_TEMPLATE_IDS.TEXTAREA:
      return {
        textarea: String(answerData.textarea || ""),
      };

    case SUPPORTED_TEMPLATE_IDS.SUMMARY:
      return {
        textarea_1: String(answerData.textarea_1 || ""),
        textarea_2: String(answerData.textarea_2 || ""),
        textarea_3: String(answerData.textarea_3 || ""),
        textarea_4: String(answerData.textarea_4 || ""),
        textarea_5: String(answerData.textarea_5 || ""),
      };

    case SUPPORTED_TEMPLATE_IDS.RECERTIFICATION_MEETING:
      return {
        textarea_1: String(answerData.textarea_1 || ""),
        textarea_2: String(answerData.textarea_2 || ""),
        textarea_3: String(answerData.textarea_3 || ""),
        textarea_4: String(answerData.textarea_4 || ""),
        textarea_5: String(answerData.textarea_5 || ""),
        textarea_6: String(answerData.textarea_6 || ""),
        textarea_7: String(answerData.textarea_7 || ""),
      };

    default:
      return answerData;
  }
};

// 工具函數：檢查必要參數
export const checkRequiredParameters = (params: Record<string, unknown>, required: string[]): string[] => {
  const missing: string[] = [];

  for (const param of required) {
    if (!(param in params) || params[param] === undefined || params[param] === null) {
      missing.push(param);
    }
  }

  return missing;
};

// 工具函數：生成問題標題
export const generateQuestionTitle = (questionSid: string, customTitle?: string): string => {
  return customTitle || `問題${questionSid}`;
};

// 工具函數：格式化答案保存結果
export const formatSaveResult = (request: any, standardizedAnswer: Record<string, unknown>, validationResult: { isValid: boolean; errors: string[] }): any => {
  return {
    certification_sid: request.certification_sid,
    question_sid: request.question_sid,
    template_id: request.template_id,
    question_title: generateQuestionTitle(request.question_sid, request.question_title),
    answer_json: JSON.stringify(standardizedAnswer, null, 2),
    raw_answer_data: request.answer_data,
    validation_result: validationResult,
    timestamp: new Date().toISOString(),
    status: ANSWER_STATUS.DRAFT,
  };
};

// 工具函數：檢查模板ID是否支援
export const isSupportedTemplate = (templateId: number): boolean => {
  return Object.values(SUPPORTED_TEMPLATE_IDS).includes(templateId as any);
};
