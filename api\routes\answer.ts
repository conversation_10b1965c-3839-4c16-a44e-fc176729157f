import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import { AnswerService } from "../services/answer-service.js";
import { sendOperationError } from "../utils/response-helpers.js";
import {
  AnswerSaveParams,
  CertificationAnswersParams,
  SingleAnswerParams,
  AnswerDeleteParams,
  AnswerSaveQueryParams,
  CertificationAnswersQueryParams,
  SingleAnswerQueryParams,
  AnswerDeleteQueryParams,
  AnswerSaveResponse,
  CertificationAnswersResponse,
  SingleAnswerResponse,
  AnswerDeleteResponse,
  AnswerSaveRequest,
} from "../models/answer.js";
import { ANSWER_ERROR_MESSAGES, ANSWER_SUCCESS_MESSAGES, isValidId, generateDebugInfo } from "../constants/answer.js";

const router = express.Router();

// 保存認證答案
router.post(
  "/save",
  authenticateToken,
  async (req: express.Request<AnswerSaveParams, AnswerSaveResponse, AnswerSaveRequest, AnswerSaveQueryParams>, res: express.Response<AnswerSaveResponse>) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          data: {} as any,
        });
      }

      // 驗證參數
      const { certificationId, questionId, answerData } = req.body;
      if (certificationId == null || questionId == null || !answerData) {
        return res.status(400).json({
          success: false,
          data: {} as any,
          message: ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS,
        });
      }

      const result = await AnswerService.saveAnswer(req.body, userId);

      res.json({
        success: true,
        data: result,
      });
    } catch (error: unknown) {
      console.error("[Answers API] 保存答案失敗:", error);

      if (error instanceof Error) {
        if ([ANSWER_ERROR_MESSAGES.MISSING_PARAMETERS, ANSWER_ERROR_MESSAGES.CERTIFICATION_UNDER_REVIEW].includes(error.message)) {
          return res.status(400).json({
            success: false,
            data: {} as any,
            message: error.message,
          });
        }

        if ([ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL, ANSWER_ERROR_MESSAGES.NO_PERMISSION_MODIFY].includes(error.message)) {
          return res.status(403).json({
            success: false,
            data: {} as any,
            message: error.message,
          });
        }

        if (error.message === ANSWER_ERROR_MESSAGES.QUESTION_NOT_FOUND) {
          return res.status(404).json({
            success: false,
            data: {} as any,
            message: error.message,
          });
        }
      }

      return sendOperationError(res, ANSWER_ERROR_MESSAGES.SAVE_FAILED, error);
    }
  }
);

// 獲取認證答案
router.get(
  "/certification/:certificationId",
  authenticateToken,
  async (
    req: express.Request<CertificationAnswersParams, CertificationAnswersResponse, {}, CertificationAnswersQueryParams>,
    res: express.Response<CertificationAnswersResponse>
  ) => {
    try {
      const { certificationId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          data: {} as any,
        });
      }

      if (!certificationId) {
        return res.status(400).json({
          success: false,
          data: {} as any,
        });
      }

      const result = await AnswerService.getCertificationAnswers(certificationId, userId);

      res.json({
        success: true,
        data: result,
      });
    } catch (error: unknown) {
      console.error("[Answers API] 查詢答案失敗:", error);

      if (error instanceof Error) {
        if ([ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL, ANSWER_ERROR_MESSAGES.NO_PERMISSION_VIEW].includes(error.message)) {
          return res.status(403).json({
            success: false,
            data: {} as any,
          });
        }
      }

      return sendOperationError(res, ANSWER_ERROR_MESSAGES.QUERY_FAILED, error);
    }
  }
);

// 獲取單個問題的答案
router.get(
  "/question/:questionId/certification/:certificationId",
  authenticateToken,
  async (req: express.Request<SingleAnswerParams, SingleAnswerResponse, {}, SingleAnswerQueryParams>, res: express.Response<SingleAnswerResponse>) => {
    try {
      const { questionId, certificationId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          data: null,
        });
      }

      // 驗證參數
      if (!isValidId(questionId) || !isValidId(certificationId)) {
        return res.status(400).json({
          success: false,
          data: null,
          message: ANSWER_ERROR_MESSAGES.MISSING_QUESTION_OR_CERTIFICATION_ID,
        });
      }

      const result = await AnswerService.getSingleAnswer(parseInt(questionId), certificationId, userId);

      res.json({
        success: true,
        data: result,
        message: result ? undefined : ANSWER_SUCCESS_MESSAGES.NO_ANSWER_FOUND,
      });
    } catch (error: unknown) {
      console.error("[Answers API] 查詢單個答案失敗:", error);

      if (error instanceof Error) {
        if ([ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL, ANSWER_ERROR_MESSAGES.NO_PERMISSION_VIEW].includes(error.message)) {
          return res.status(403).json({
            success: false,
            data: null,
            message: error.message,
          });
        }
      }

      return sendOperationError(res, ANSWER_ERROR_MESSAGES.QUERY_FAILED, error);
    }
  }
);

// 刪除答案
router.delete(
  "/:answerId",
  authenticateToken,
  async (req: express.Request<AnswerDeleteParams, AnswerDeleteResponse, {}, AnswerDeleteQueryParams>, res: express.Response<AnswerDeleteResponse>) => {
    try {
      const { answerId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "用戶未認證",
        });
      }

      if (!answerId) {
        return res.status(400).json({
          success: false,
          message: ANSWER_ERROR_MESSAGES.MISSING_ANSWER_ID,
        });
      }

      await AnswerService.deleteAnswer(parseInt(answerId), userId);

      res.json({
        success: true,
        message: ANSWER_SUCCESS_MESSAGES.ANSWER_DELETED,
      });
    } catch (error: unknown) {
      console.error("[Answers API] 刪除答案失敗:", error);

      if (error instanceof Error) {
        if ([ANSWER_ERROR_MESSAGES.USER_NOT_ASSIGNED_SCHOOL, ANSWER_ERROR_MESSAGES.NO_PERMISSION_DELETE].includes(error.message)) {
          return res.status(403).json({
            success: false,
            message: error.message,
          });
        }

        if ([ANSWER_ERROR_MESSAGES.ANSWER_NOT_FOUND, ANSWER_ERROR_MESSAGES.ANSWER_ALREADY_DELETED].includes(error.message)) {
          return res.status(404).json({
            success: false,
            message: error.message,
          });
        }
      }

      return sendOperationError(res, ANSWER_ERROR_MESSAGES.DELETE_FAILED, error);
    }
  }
);

export default router;
