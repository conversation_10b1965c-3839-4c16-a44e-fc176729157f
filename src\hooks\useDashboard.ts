import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { dashboardAPI } from '@/api';
import { dashboardService } from '@/services/dashboardService';
import type { 
  CityStatistics, 
  LatestCertification, 
  SchoolArticle, 
  SchoolCertificationStatus, 
  SchoolPassedCertification 
} from '@/api/dashboardAPI';
import type { 
  ProcessedCityStatistics,
  ProcessedLatestCertifications,
  ProcessedSchoolCertificationStatus,
  ProcessedSchoolArticles,
  ProcessedSchoolPassedCertifications,
} from '@/services/dashboardService';

/**
 * Dashboard React Query Hook
 * 整合 API 調用、業務邏輯處理和狀態管理
 * 提供完整的儀表板資料管理功能
 */

// === Query Keys ===
export const dashboardQueryKeys = {
  all: ['dashboard'] as const,
  cityStatistics: () => [...dashboardQueryKeys.all, 'cityStatistics'] as const,
  latestCertifications: (limit?: number) => [...dashboardQueryKeys.all, 'latestCertifications', limit] as const,
  school: {
    all: () => [...dashboardQueryKeys.all, 'school'] as const,
    currentCertification: () => [...dashboardQueryKeys.school.all(), 'currentCertification'] as const,
    articles: (limit?: number) => [...dashboardQueryKeys.school.all(), 'articles', limit] as const,
    passedCertifications: () => [...dashboardQueryKeys.school.all(), 'passedCertifications'] as const,
  },
};

// === Query Options 類型 ===
type DashboardQueryOptions<T> = Omit<UseQueryOptions<T>, 'queryKey' | 'queryFn'>;

// === 縣市統計資料 Hook ===
export function useCityStatistics(options?: DashboardQueryOptions<ProcessedCityStatistics | null>) {
  return useQuery({
    queryKey: dashboardQueryKeys.cityStatistics(),
    queryFn: async () => {
      const response = await dashboardAPI.getMyCityStatistics();
      const rawData = response.success ? response.data : null;
      return dashboardService.processCityStatistics(rawData);
    },
    staleTime: 5 * 60 * 1000, // 5分鐘
    cacheTime: 10 * 60 * 1000, // 10分鐘
    retry: 2,
    refetchOnWindowFocus: false,
    ...options,
  });
}

// === 最新認證列表 Hook ===
export function useLatestCertifications(
  limit: number = 6,
  options?: DashboardQueryOptions<ProcessedLatestCertifications>
) {
  return useQuery({
    queryKey: dashboardQueryKeys.latestCertifications(limit),
    queryFn: async () => {
      const response = await dashboardAPI.getMyLatestCertifications(limit);
      const rawData = response.success ? response.data || [] : [];
      return dashboardService.processLatestCertifications(rawData);
    },
    staleTime: 3 * 60 * 1000, // 3分鐘
    cacheTime: 10 * 60 * 1000, // 10分鐘
    retry: 2,
    refetchOnWindowFocus: false,
    ...options,
  });
}

// === 學校當前認證狀態 Hook ===
export function useSchoolCurrentCertification(
  options?: DashboardQueryOptions<ProcessedSchoolCertificationStatus | null>
) {
  return useQuery({
    queryKey: dashboardQueryKeys.school.currentCertification(),
    queryFn: async () => {
      const response = await dashboardAPI.getSchoolCurrentCertification();
      const rawData = response.success ? response.data : null;
      return dashboardService.processSchoolCertificationStatus(rawData);
    },
    staleTime: 2 * 60 * 1000, // 2分鐘（認證狀態變化較頻繁）
    cacheTime: 5 * 60 * 1000, // 5分鐘
    retry: 2,
    refetchOnWindowFocus: true, // 認證狀態需要及時更新
    ...options,
  });
}

// === 學校最新投稿文章 Hook ===
export function useSchoolLatestArticles(
  limit: number = 6,
  options?: DashboardQueryOptions<ProcessedSchoolArticles>
) {
  return useQuery({
    queryKey: dashboardQueryKeys.school.articles(limit),
    queryFn: async () => {
      const response = await dashboardAPI.getSchoolLatestArticles(limit);
      const rawData = response.success ? response.data || [] : [];
      return dashboardService.processSchoolArticles(rawData);
    },
    staleTime: 3 * 60 * 1000, // 3分鐘
    cacheTime: 10 * 60 * 1000, // 10分鐘
    retry: 2,
    refetchOnWindowFocus: false,
    ...options,
  });
}

// === 學校已通過認證列表 Hook ===
export function useSchoolPassedCertifications(
  options?: DashboardQueryOptions<ProcessedSchoolPassedCertifications>
) {
  return useQuery({
    queryKey: dashboardQueryKeys.school.passedCertifications(),
    queryFn: async () => {
      const response = await dashboardAPI.getSchoolPassedCertifications();
      const rawData = response.success ? response.data || [] : [];
      return dashboardService.processSchoolPassedCertifications(rawData);
    },
    staleTime: 10 * 60 * 1000, // 10分鐘（已通過認證變化較少）
    cacheTime: 30 * 60 * 1000, // 30分鐘
    retry: 2,
    refetchOnWindowFocus: false,
    ...options,
  });
}

// === 複合 Hook：完整的儀表板資料 ===
export function useDashboard(options?: {
  cityStatistics?: DashboardQueryOptions<ProcessedCityStatistics | null>;
  latestCertifications?: { limit?: number } & DashboardQueryOptions<ProcessedLatestCertifications>;
  enableSchoolData?: boolean; // 控制是否載入學校相關資料
  school?: {
    currentCertification?: DashboardQueryOptions<ProcessedSchoolCertificationStatus | null>;
    articles?: { limit?: number } & DashboardQueryOptions<ProcessedSchoolArticles>;
    passedCertifications?: DashboardQueryOptions<ProcessedSchoolPassedCertifications>;
  };
}) {
  // 通用資料
  const cityStatistics = useCityStatistics(options?.cityStatistics);
  const latestCertifications = useLatestCertifications(
    options?.latestCertifications?.limit || 6,
    options?.latestCertifications
  );

  // 學校專用資料（條件性載入）
  const schoolCurrentCertification = useSchoolCurrentCertification({
    ...options?.school?.currentCertification,
    enabled: options?.enableSchoolData && (options?.school?.currentCertification?.enabled !== false),
  });

  const schoolLatestArticles = useSchoolLatestArticles(
    options?.school?.articles?.limit || 6,
    {
      ...options?.school?.articles,
      enabled: options?.enableSchoolData && (options?.school?.articles?.enabled !== false),
    }
  );

  const schoolPassedCertifications = useSchoolPassedCertifications({
    ...options?.school?.passedCertifications,
    enabled: options?.enableSchoolData && (options?.school?.passedCertifications?.enabled !== false),
  });

  return {
    // 通用資料
    cityStatistics: {
      data: cityStatistics.data,
      isLoading: cityStatistics.isLoading,
      error: cityStatistics.error,
      refetch: cityStatistics.refetch,
      isRefetching: cityStatistics.isRefetching,
    },
    latestCertifications: {
      data: latestCertifications.data,
      isLoading: latestCertifications.isLoading,
      error: latestCertifications.error,
      refetch: latestCertifications.refetch,
      isRefetching: latestCertifications.isRefetching,
    },

    // 學校專用資料
    school: {
      currentCertification: {
        data: schoolCurrentCertification.data,
        isLoading: schoolCurrentCertification.isLoading,
        error: schoolCurrentCertification.error,
        refetch: schoolCurrentCertification.refetch,
        isRefetching: schoolCurrentCertification.isRefetching,
      },
      articles: {
        data: schoolLatestArticles.data,
        isLoading: schoolLatestArticles.isLoading,
        error: schoolLatestArticles.error,
        refetch: schoolLatestArticles.refetch,
        isRefetching: schoolLatestArticles.isRefetching,
      },
      passedCertifications: {
        data: schoolPassedCertifications.data,
        isLoading: schoolPassedCertifications.isLoading,
        error: schoolPassedCertifications.error,
        refetch: schoolPassedCertifications.refetch,
        isRefetching: schoolPassedCertifications.isRefetching,
      },
    },

    // 整體狀態
    isLoading: cityStatistics.isLoading || latestCertifications.isLoading || 
      (options?.enableSchoolData && (
        schoolCurrentCertification.isLoading || 
        schoolLatestArticles.isLoading || 
        schoolPassedCertifications.isLoading
      )),

    hasError: !!(cityStatistics.error || latestCertifications.error || 
      schoolCurrentCertification.error || 
      schoolLatestArticles.error || 
      schoolPassedCertifications.error),

    // 重新載入所有資料
    refetchAll: async () => {
      await Promise.all([
        cityStatistics.refetch(),
        latestCertifications.refetch(),
        ...(options?.enableSchoolData ? [
          schoolCurrentCertification.refetch(),
          schoolLatestArticles.refetch(),
          schoolPassedCertifications.refetch(),
        ] : [])
      ]);
    },
  };
}

// === 便捷的型別匯出 ===
export type {
  ProcessedCityStatistics,
  ProcessedLatestCertifications,
  ProcessedSchoolCertificationStatus,
  ProcessedSchoolArticles,
  ProcessedSchoolPassedCertifications,
} from '../services/dashboardService';

// === 實用工具 Hook ===

/**
 * 簡化版的縣市統計 Hook
 * 適用於只需要基本統計資訊的場景
 */
export function useCityStatisticsSimple() {
  const { data, isLoading, error } = useCityStatistics();
  return {
    statistics: data,
    isLoading,
    hasError: !!error,
    isEmpty: data?.isEmpty || false,
    totalCertifications: data?.totalCertifications || 0,
    certificationRate: data?.certificationRateDisplay || '0.0%',
  };
}

/**
 * 學校儀表板專用 Hook
 * 整合學校所需的所有資料
 */
export function useSchoolDashboard() {
  return useDashboard({
    enableSchoolData: true,
    latestCertifications: { limit: 3 }, // 學校檢視只需要3個最新認證
    school: {
      articles: { limit: 5 }, // 顯示5篇最新文章
    },
  });
}

/**
 * 一般用戶儀表板 Hook
 * 只載入通用資料
 */
export function useGeneralDashboard() {
  return useDashboard({
    enableSchoolData: false,
    latestCertifications: { limit: 8 }, // 一般檢視顯示更多認證
  });
}