// ========== 模板答案相關資料模型 ==========

export interface TemplateAnswerSaveRequest {
  certification_sid: string;
  question_sid: string;
  template_id: number;
  answer_data: Record<string, unknown>;
  question_title?: string;
}

export interface TemplateAnswerValidateRequest {
  template_id: number;
  answer_data: Record<string, unknown>;
}

export interface TemplateAnswerValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface TemplateAnswerSaveResult {
  certification_sid: string;
  question_sid: string;
  template_id: number;
  question_title: string;
  answer_json: string;
  raw_answer_data: Record<string, unknown>;
  validation_result: TemplateAnswerValidationResult;
  timestamp: string;
  status: string;
}

// 模板特定答案格式介面
export interface YesNoAnswerData {
  is_yes_no: string;
}

export interface TeamMemberAnswerData {
  student_list: Array<{
    input_1: string;
    input_2: string;
    input_3: string;
  }>;
  teacher_list: Array<{
    input_1: string;
    input_2: string;
    input_3: string;
  }>;
  community_member_list: Array<{
    input_1: string;
    input_2: string;
    input_3: string;
  }>;
}

export interface MeetingRecordAnswerData {
  meeting_date_and_theme: Array<{
    input_1: string;
    input_2: string;
  }>;
  file: Array<{
    file_url: string;
    file_name: string;
  }>;
}

export interface ShareMeetingAnswerData {
  is_yes_no: string;
  share_people: {
    checkbox: string[];
  };
  how_share_meeting: {
    checkbox: string[];
    text: {
      text_1: string;
      text_2: string;
      text_3: string;
    };
  };
}

export interface RecruitMemberAnswerData {
  is_yes_no: string;
  textarea: string;
}

export interface PhotoRecordAnswerData {
  photo_record: Array<{
    photo_url: string;
    photo_name: string;
    description?: string;
  }>;
}

export interface EnvironmentPathAnswerData {
  improve_path_list: Array<{
    path: string;
    cname: string;
    date: {
      input_1: string;
      input_2: string;
    };
  }>;
}

export interface TextAreaAnswerData {
  textarea: string;
}

export interface SummaryAnswerData {
  textarea_1: string;
  textarea_2: string;
  textarea_3: string;
  textarea_4: string;
  textarea_5: string;
}

export interface RecertificationMeetingAnswerData {
  textarea_1: string;
  textarea_2: string;
  textarea_3: string;
  textarea_4: string;
  textarea_5: string;
  textarea_6: string;
  textarea_7: string;
}

// API 回應介面
export interface TemplateAnswerSaveResponse {
  success: boolean;
  message: string;
  data?: TemplateAnswerSaveResult;
  required?: string[];
  errors?: string[];
  error?: string;
}

export interface TemplateAnswerValidateResponse {
  success: boolean;
  message?: string;
  data?: TemplateAnswerValidationResult;
}

// 查詢參數類型
export interface TemplateAnswerSaveParams {}

export interface TemplateAnswerValidateParams {}

export interface TemplateAnswerSaveQueryParams {}

export interface TemplateAnswerValidateQueryParams {}
