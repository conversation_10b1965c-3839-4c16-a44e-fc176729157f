// ========== 問題相關常數 ==========

// 認證最大步驟數
export const MAX_CERTIFICATION_STEPS = 7;

// 排除的問題模板ID
export const EXCLUDED_QUESTION_TEMPLATES = [12, 15];

// 步驟標題映射
export const STEP_TITLES: Record<number, string> = {
  1: "步驟一、生態行動團隊",
  2: "步驟二、環境檢視",
  3: "步驟三、生態行動方案",
  4: "步驟四、監控執行情形",
  5: "步驟五、結合教育課程",
  6: "步驟六、社區參與",
  7: "步驟七、生態宣言",
};

// 綠旗再認證步驟
export const GREEN_FLAG_STEPS = [8, 9];

// 預設認證等級
export const DEFAULT_CERTIFICATION_LEVEL = 1;

// 問題狀態
export const QUESTION_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
} as const;

// 獲取步驟標題的工具函數
export const getStepTitle = (stepNumber: number): string => {
  return STEP_TITLES[stepNumber] || `步驟 ${stepNumber}`;
};

// 檢查是否為綠旗再認證步驟
export const isGreenFlagStep = (stepNumber: number): boolean => {
  return GREEN_FLAG_STEPS.includes(stepNumber);
};
