import express from "express";
import multer from "multer";
import path from "path";
import { fileURLToPath } from "url";
import { authenticateToken } from "../middleware/auth.js";
import { FileService } from "../services/file-service.js";
import { FileConfigManager } from "../config/config-manager.js";
import {
  FileDeleteParams,
  FileInfoParams,
  FileDownloadParams,
  SchoolLogoGetParams,
  FileUploadResponse,
  SingleFileUploadResponse,
  FileDeleteResponse,
  FileInfoResponse,
  SupportedTypesResponse,
  SchoolLogoUploadResponse,
  SchoolLogoGetResponse,
  UploadDiagnosticsResponse,
  FileUploadRequest,
  SchoolLogoUploadRequest,
  FileUploadQueryParams,
  SupportedTypesQueryParams,
  UploadDiagnosticsQueryParams,
} from "../models/file.js";
import {
  FILE_SIZE_LIMITS,
  FILE_COUNT_LIMITS,
  ALLOWED_MIME_TYPES,
  ALL_ALLOWED_TYPES,
  FILE_ERROR_MESSAGES,
  FILE_SUCCESS_MESSAGES,
  getMulterErrorMessage,
  MULTER_ERROR_CODES,
  generateLogoFilename,
} from "../constants/file.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

// 獲取上傳配置
const configManager = FileConfigManager.getInstance();
const uploadConfig = configManager.getUploadConfig();

// 設置 Multer 儲存配置 - 通用檔案
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      await FileService.ensureGeneralUploadDir();
      cb(null, uploadConfig.path);
    } catch (error) {
      cb(error as Error, "");
    }
  },
  filename: (req, file, cb) => {
    const timestamp = Date.now();
    const originalName = Buffer.from(file.originalname, "latin1").toString("utf8");
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const filename = `${timestamp}_${name}${ext}`;
    cb(null, filename);
  },
});

// 檔案過濾器
const fileFilter = (req: express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (ALL_ALLOWED_TYPES.includes(file.mimetype as (typeof ALL_ALLOWED_TYPES)[number])) {
    cb(null, true);
  } else {
    cb(new Error(FILE_ERROR_MESSAGES.UNSUPPORTED_FILE_TYPE));
  }
};

// 設置上傳限制 - 通用檔案
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: FILE_SIZE_LIMITS.GENERAL_FILE,
    files: FILE_COUNT_LIMITS.MULTIPLE_UPLOAD,
  },
});

// 校徽上傳配置
const storageSchoolLogo = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      const logoDir = await FileService.ensureLogoUploadDirectory();
      cb(null, logoDir);
    } catch (error) {
      cb(error as Error, "");
    }
  },
  filename: (req, file, cb) => {
    const user = req.user;
    const accountId = user?.accountSid || user?.id || "unknown";
    const filename = generateLogoFilename(file.originalname, accountId);
    console.log(`📸 [Logo] 生成檔名: ${filename}, 用戶ID: ${accountId}`);
    cb(null, filename);
  },
});

const uploadSchoolLogo = multer({
  storage: storageSchoolLogo,
  limits: {
    fileSize: FILE_SIZE_LIMITS.SCHOOL_LOGO,
  },
  fileFilter: (req, file, cb) => {
    if (ALLOWED_MIME_TYPES.SCHOOL_LOGO.includes(file.mimetype as (typeof ALLOWED_MIME_TYPES.SCHOOL_LOGO)[number])) {
      cb(null, true);
    } else {
      cb(new Error(FILE_ERROR_MESSAGES.LOGO_FORMAT_ERROR));
    }
  },
});

// 檔案上傳端點（多檔案）
router.post(
  "/upload",
  authenticateToken,
  upload.array("files", 5),
  async (req: express.Request<{}, FileUploadResponse, FileUploadRequest, FileUploadQueryParams>, res: express.Response<FileUploadResponse>) => {
    try {
      const files = req.files as Express.Multer.File[];
      const { file_type, certification_sid, question_sid } = req.body;

      const uploadedFiles = await FileService.processMultipleFileUpload(files, {
        file_type,
        certification_sid,
        question_sid,
      });

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.FILE_UPLOADED,
        data: uploadedFiles,
      });
    } catch (error: unknown) {
      console.error("檔案上傳失敗:", error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
        data: [],
      });
    }
  }
);

// 單檔上傳端點
router.post(
  "/upload-single",
  authenticateToken,
  upload.single("file"),
  async (req: express.Request<{}, SingleFileUploadResponse, FileUploadRequest, FileUploadQueryParams>, res: express.Response<SingleFileUploadResponse>) => {
    try {
      const file = req.file;
      const { file_type, certification_sid, question_sid } = req.body;

      const fileInfo = await FileService.processSingleFileUpload(file, {
        file_type,
        certification_sid,
        question_sid,
      });

      // 轉換為前端期望的格式
      const responseData = {
        id: fileInfo.filename,
        url: fileInfo.fileUrl,
        fileName: fileInfo.originalName,
        fileType: fileInfo.mimetype,
        fileSize: fileInfo.size,
      };

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.FILE_UPLOADED,
        data: responseData,
      });
    } catch (error: unknown) {
      console.error("檔案上傳失敗:", error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
        data: {
          id: "",
          url: "",
          fileName: "",
          fileType: "",
          fileSize: 0,
        },
      });
    }
  }
);

// 刪除檔案
router.delete("/:filename", async (req: express.Request<FileDeleteParams>, res: express.Response<FileDeleteResponse>) => {
  try {
    const { filename } = req.params;
    await FileService.deleteFile(filename);

    res.json({
      success: true,
      message: FILE_SUCCESS_MESSAGES.FILE_DELETED,
    });
  } catch (error: unknown) {
    console.error("刪除檔案失敗:", error);

    if (error instanceof Error) {
      if (error.message === FILE_ERROR_MESSAGES.INVALID_FILENAME) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message === FILE_ERROR_MESSAGES.FILE_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
    });
  }
});

// 獲取檔案資訊
router.get("/info/:filename", async (req: express.Request<FileInfoParams>, res: express.Response<FileInfoResponse>) => {
  try {
    const { filename } = req.params;
    const fileInfo = await FileService.getFileInfo(filename);

    res.json({
      success: true,
      data: fileInfo,
    });
  } catch (error: unknown) {
    console.error("獲取檔案資訊失敗:", error);

    if (error instanceof Error) {
      if (error.message === FILE_ERROR_MESSAGES.INVALID_FILENAME) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message === FILE_ERROR_MESSAGES.FILE_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
    });
  }
});

// 下載檔案
router.get("/download/:filename", async (req: express.Request<FileDownloadParams>, res: express.Response) => {
  try {
    const { filename } = req.params;
    const { filePath, originalName } = await FileService.getDownloadPath(filename);

    // 設置下載標頭
    res.setHeader("Content-Disposition", `attachment; filename="${encodeURIComponent(originalName)}"`);
    res.sendFile(filePath);
  } catch (error: unknown) {
    console.error("下載檔案失敗:", error);

    if (error instanceof Error) {
      if (error.message === FILE_ERROR_MESSAGES.INVALID_FILENAME) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message === FILE_ERROR_MESSAGES.FILE_NOT_FOUND) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
    });
  }
});

// 獲取支援的檔案類型
router.get(
  "/supported-types",
  (req: express.Request<{}, SupportedTypesResponse, {}, SupportedTypesQueryParams>, res: express.Response<SupportedTypesResponse>) => {
    const supportedTypes = FileService.getSupportedFileTypes();

    res.json({
      success: true,
      data: supportedTypes,
    });
  }
);

router.post(
  "/upload-school-logo",
  authenticateToken,
  uploadSchoolLogo.single("logo"),
  async (req: express.Request<{}, SchoolLogoUploadResponse, SchoolLogoUploadRequest>, res: express.Response<SchoolLogoUploadResponse>) => {
    try {
      console.log(`📸 [Logo] 收到校徽上傳請求（舊版端點）`);

      const user = req.user;
      const file = req.file;

      console.log(`📸 [Logo] 用戶認證資訊:`, {
        id: user?.id,
        accountSid: user?.accountSid,
        account: user?.account,
        roleType: user?.roleType,
      });

      const accountId = user?.accountSid || user?.id;
      if (!accountId) {
        console.error(`📸 [Logo] 無法取得用戶ID:`, user);
        return res.status(401).json({
          success: false,
          message: FILE_ERROR_MESSAGES.INVALID_USER_AUTH,
          data: { logoUrl: null, fileName: null },
        });
      }

      const logoInfo = await FileService.processSchoolLogoUpload(file, accountId);

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.SCHOOL_LOGO_UPLOADED,
        data: logoInfo,
      });
    } catch (error: unknown) {
      console.error("📸 [Logo] 校徽上傳失敗:", error);

      const errorMessage = error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED;

      if (error instanceof Error) {
        if (error.message === FILE_ERROR_MESSAGES.PLEASE_SELECT_LOGO) {
          return res.status(400).json({
            success: false,
            message: error.message,
            data: { logoUrl: null, fileName: null },
          });
        }

        if (
          [FILE_ERROR_MESSAGES.LOGO_SIZE_ERROR, FILE_ERROR_MESSAGES.LOGO_FORMAT_ERROR].includes(
            error.message as typeof FILE_ERROR_MESSAGES.LOGO_SIZE_ERROR | typeof FILE_ERROR_MESSAGES.LOGO_FORMAT_ERROR
          )
        ) {
          return res.status(400).json({
            success: false,
            message: error.message,
            data: { logoUrl: null, fileName: null },
          });
        }

        if (error.message === FILE_ERROR_MESSAGES.SCHOOL_NOT_FOUND) {
          return res.status(404).json({
            success: false,
            message: error.message,
            data: { logoUrl: null, fileName: null },
          });
        }
      }

      res.status(500).json({
        success: false,
        message: errorMessage,
        data: { logoUrl: null, fileName: null },
      });
    }
  }
);

// 取得校徽 API
router.get("/school-logo/:accountId", async (req: express.Request<SchoolLogoGetParams>, res: express.Response<SchoolLogoGetResponse>) => {
  try {
    const { accountId } = req.params;
    const logoInfo = await FileService.getSchoolLogo(accountId);

    res.json({
      success: true,
      data: logoInfo,
    });
  } catch (error: unknown) {
    console.error("取得校徽失敗:", error);
    res.status(500).json({
      success: false,
      data: { logoUrl: null, fileName: null },
    });
  }
});

// 診斷API端點
router.get(
  "/upload-diagnostics",
  authenticateToken,
  async (req: express.Request<{}, UploadDiagnosticsResponse, {}, UploadDiagnosticsQueryParams>, res: express.Response<UploadDiagnosticsResponse>) => {
    try {
      const diagnostics = await FileService.generateUploadDiagnostics();

      res.json({
        success: true,
        message: FILE_SUCCESS_MESSAGES.UPLOAD_DIAGNOSTICS_COMPLETE,
        data: diagnostics,
      });
    } catch (error: unknown) {
      console.error("上傳診斷失敗:", error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
        data: {
          timestamp: new Date().toISOString(),
          environment: "error",
          config: {
            basePath: "",
            schoolLogoPath: "",
            generalUploadPath: "",
          },
          directories: {},
          permissions: {},
          system: {
            cwd: process.cwd(),
            platform: process.platform,
            nodeVersion: process.version,
          },
        },
      });
    }
  }
);

// 檔案上傳錯誤處理中間件
router.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    const message = getMulterErrorMessage(error.code);
    return res.status(400).json({
      success: false,
      message,
    });
  }

  res.status(500).json({
    success: false,
    message: error.message || FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED,
  });
});

export default router;
