/**
 * 效能監控和指標收集系統
 * 
 * 核心功能：
 * - Token 操作效能監控
 * - API 請求效能追蹤
 * - 記憶體使用監控
 * - 使用者體驗指標收集
 * - 自動化警報和閾值監控
 */

import { errorHandler, ErrorCategory } from './errorHandler';

// 效能指標類型
export enum MetricType {
  TOKEN_OPERATION = 'TOKEN_OPERATION',
  STORAGE_OPERATION = 'STORAGE_OPERATION',
  API_REQUEST = 'API_REQUEST',
  COMPONENT_RENDER = 'COMPONENT_RENDER',
  USER_INTERACTION = 'USER_INTERACTION',
  MEMORY_USAGE = 'MEMORY_USAGE'
}

// 效能指標介面
export interface PerformanceMetric {
  id: string;
  type: MetricType;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  metadata?: Record<string, any>;
  timestamp: number;
}

// 聚合統計介面
export interface AggregateStats {
  count: number;
  totalDuration: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  successRate: number;
  p95Duration: number;
  p99Duration: number;
}

// 警報閾值配置
export interface AlertThreshold {
  metricType: MetricType;
  name: string;
  maxDuration: number;
  minSuccessRate: number;
  sampleSize: number;
  enabled: boolean;
}

/**
 * 效能監控器
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private alertThresholds: AlertThreshold[] = [];
  private isEnabled = true;
  
  // 配置常數
  private readonly MAX_METRICS = 10000;
  private readonly CLEANUP_INTERVAL = 300000; // 5 分鐘
  private readonly ALERT_CHECK_INTERVAL = 60000; // 1 分鐘
  
  private constructor() {
    this.initializeDefaultThresholds();
    this.startPeriodicTasks();
  }

  /**
   * 獲取 PerformanceMonitor 單例實例
   */
  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 初始化預設警報閾值
   */
  private initializeDefaultThresholds(): void {
    this.alertThresholds = [
      {
        metricType: MetricType.TOKEN_OPERATION,
        name: 'Token驗證',
        maxDuration: 10, // 10ms
        minSuccessRate: 0.95,
        sampleSize: 50,
        enabled: true
      },
      {
        metricType: MetricType.STORAGE_OPERATION,
        name: '儲存操作',
        maxDuration: 5, // 5ms
        minSuccessRate: 0.98,
        sampleSize: 100,
        enabled: true
      },
      {
        metricType: MetricType.API_REQUEST,
        name: 'API請求',
        maxDuration: 3000, // 3秒
        minSuccessRate: 0.90,
        sampleSize: 20,
        enabled: true
      },
      {
        metricType: MetricType.COMPONENT_RENDER,
        name: '元件渲染',
        maxDuration: 16, // 16ms (60fps)
        minSuccessRate: 0.95,
        sampleSize: 30,
        enabled: true
      }
    ];
  }

  /**
   * 啟動定期任務
   */
  private startPeriodicTasks(): void {
    // 定期清理舊指標
    setInterval(() => {
      this.cleanupOldMetrics();
    }, this.CLEANUP_INTERVAL);

    // 定期檢查警報
    setInterval(() => {
      this.checkAlerts();
    }, this.ALERT_CHECK_INTERVAL);
  }

  /**
   * 開始效能監控
   */
  public startMetric(type: MetricType, name: string, metadata?: Record<string, any>): string {
    if (!this.isEnabled) return '';

    const metricId = this.generateMetricId();
    const metric: PerformanceMetric = {
      id: metricId,
      type,
      name,
      startTime: performance.now(),
      success: true, // 預設為成功，除非明確標記為失敗
      metadata,
      timestamp: Date.now()
    };

    this.metrics.push(metric);
    return metricId;
  }

  /**
   * 結束效能監控
   */
  public endMetric(metricId: string, success = true, additionalMetadata?: Record<string, any>): void {
    if (!this.isEnabled || !metricId) return;

    const metric = this.metrics.find(m => m.id === metricId);
    if (!metric || metric.endTime) return;

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.success = success;
    
    if (additionalMetadata) {
      metric.metadata = { ...metric.metadata, ...additionalMetadata };
    }

    // 記錄除錯訊息
    if (metric.duration > this.getThreshold(metric.type)?.maxDuration || 0) {
      errorHandler.warn(
        ErrorCategory.SYSTEM,
        'PERFORMANCE_SLOW',
        `效能指標超過閾值: ${metric.name}`,
        {
          type: metric.type,
          duration: metric.duration,
          threshold: this.getThreshold(metric.type)?.maxDuration,
          metadata: metric.metadata
        }
      );
    }
  }

  /**
   * 獲取閾值配置
   */
  private getThreshold(type: MetricType): AlertThreshold | undefined {
    return this.alertThresholds.find(t => t.metricType === type && t.enabled);
  }

  /**
   * 快捷方法 - 監控 Token 操作
   */
  public async measureTokenOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const metricId = this.startMetric(MetricType.TOKEN_OPERATION, operation, metadata);
    
    try {
      const result = await fn();
      this.endMetric(metricId, true);
      return result;
    } catch (error) {
      this.endMetric(metricId, false, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 快捷方法 - 監控儲存操作
   */
  public measureStorageOperation<T>(
    operation: string,
    fn: () => T,
    metadata?: Record<string, any>
  ): T {
    const metricId = this.startMetric(MetricType.STORAGE_OPERATION, operation, metadata);
    
    try {
      const result = fn();
      this.endMetric(metricId, true);
      return result;
    } catch (error) {
      this.endMetric(metricId, false, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 快捷方法 - 監控 API 請求
   */
  public async measureApiRequest<T>(
    endpoint: string,
    method: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const metricId = this.startMetric(MetricType.API_REQUEST, `${method} ${endpoint}`, {
      endpoint,
      method,
      ...metadata
    });
    
    try {
      const result = await fn();
      this.endMetric(metricId, true);
      return result;
    } catch (error) {
      this.endMetric(metricId, false, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * 生成指標 ID
   */
  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 獲取聚合統計
   */
  public getAggregateStats(type?: MetricType, name?: string, timeWindow?: number): AggregateStats {
    let filteredMetrics = this.metrics.filter(m => m.duration !== undefined);

    if (type) {
      filteredMetrics = filteredMetrics.filter(m => m.type === type);
    }

    if (name) {
      filteredMetrics = filteredMetrics.filter(m => m.name === name);
    }

    if (timeWindow) {
      const cutoff = Date.now() - timeWindow;
      filteredMetrics = filteredMetrics.filter(m => m.timestamp > cutoff);
    }

    if (filteredMetrics.length === 0) {
      return {
        count: 0,
        totalDuration: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        successRate: 0,
        p95Duration: 0,
        p99Duration: 0
      };
    }

    const durations = filteredMetrics.map(m => m.duration!).sort((a, b) => a - b);
    const successCount = filteredMetrics.filter(m => m.success).length;

    return {
      count: filteredMetrics.length,
      totalDuration: durations.reduce((sum, d) => sum + d, 0),
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minDuration: durations[0],
      maxDuration: durations[durations.length - 1],
      successRate: successCount / filteredMetrics.length,
      p95Duration: durations[Math.floor(durations.length * 0.95)],
      p99Duration: durations[Math.floor(durations.length * 0.99)]
    };
  }

  /**
   * 檢查警報條件
   */
  private checkAlerts(): void {
    if (!this.isEnabled) return;

    for (const threshold of this.alertThresholds.filter(t => t.enabled)) {
      const stats = this.getAggregateStats(threshold.metricType, undefined, 300000); // 5分鐘窗口
      
      if (stats.count < threshold.sampleSize) continue;

      // 檢查平均響應時間
      if (stats.averageDuration > threshold.maxDuration) {
        errorHandler.warn(
          ErrorCategory.SYSTEM,
          'PERFORMANCE_THRESHOLD_EXCEEDED',
          `效能指標超出閾值: ${threshold.name}`,
          {
            threshold: threshold.maxDuration,
            actual: stats.averageDuration,
            stats
          }
        );
      }

      // 檢查成功率
      if (stats.successRate < threshold.minSuccessRate) {
        errorHandler.error(
          ErrorCategory.SYSTEM,
          'SUCCESS_RATE_LOW',
          `成功率低於閾值: ${threshold.name}`,
          {
            threshold: threshold.minSuccessRate,
            actual: stats.successRate,
            stats
          }
        );
      }
    }
  }

  /**
   * 清理舊指標
   */
  private cleanupOldMetrics(): void {
    if (this.metrics.length > this.MAX_METRICS) {
      const excessCount = this.metrics.length - this.MAX_METRICS;
      this.metrics.splice(0, excessCount);
      
      errorHandler.info(
        ErrorCategory.SYSTEM,
        'METRICS_CLEANUP',
        `清理舊效能指標: ${excessCount} 條`
      );
    }
  }

  /**
   * 獲取效能報告
   */
  public getPerformanceReport(timeWindow = 300000): Record<string, any> {
    const report: Record<string, any> = {
      timeWindow: timeWindow / 1000, // 轉換為秒
      timestamp: Date.now(),
      summary: {
        totalMetrics: this.metrics.length,
        metricsInWindow: this.metrics.filter(m => Date.now() - m.timestamp <= timeWindow).length
      },
      byType: {}
    };

    // 按類型統計
    for (const type of Object.values(MetricType)) {
      const stats = this.getAggregateStats(type as MetricType, undefined, timeWindow);
      if (stats.count > 0) {
        report.byType[type] = stats;
      }
    }

    // 最慢的操作
    const slowestMetrics = this.metrics
      .filter(m => m.duration !== undefined && Date.now() - m.timestamp <= timeWindow)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, 10)
      .map(m => ({
        type: m.type,
        name: m.name,
        duration: m.duration,
        success: m.success,
        timestamp: m.timestamp
      }));

    report.slowest = slowestMetrics;

    return report;
  }

  /**
   * 記錄自定義指標
   */
  public recordCustomMetric(type: MetricType, name: string, duration: number, success = true, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      id: this.generateMetricId(),
      type,
      name,
      startTime: performance.now() - duration,
      endTime: performance.now(),
      duration,
      success,
      metadata,
      timestamp: Date.now()
    };

    this.metrics.push(metric);
  }

  /**
   * 設定警報閾值
   */
  public setAlertThreshold(threshold: AlertThreshold): void {
    const existingIndex = this.alertThresholds.findIndex(
      t => t.metricType === threshold.metricType && t.name === threshold.name
    );

    if (existingIndex >= 0) {
      this.alertThresholds[existingIndex] = threshold;
    } else {
      this.alertThresholds.push(threshold);
    }
  }

  /**
   * 啟用/停用監控
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    
    errorHandler.info(
      ErrorCategory.SYSTEM,
      'PERFORMANCE_MONITOR_TOGGLE',
      `效能監控已${enabled ? '啟用' : '停用'}`
    );
  }

  /**
   * 清除所有指標
   */
  public clearMetrics(): void {
    const count = this.metrics.length;
    this.metrics = [];
    
    errorHandler.info(
      ErrorCategory.SYSTEM,
      'METRICS_CLEARED',
      `已清除所有效能指標: ${count} 條`
    );
  }

  /**
   * 銷毀監控器
   */
  public destroy(): void {
    this.metrics = [];
    this.alertThresholds = [];
    this.isEnabled = false;
    
    console.log('💥 [PerformanceMonitor] 效能監控器已銷毀');
  }
}

// 導出單例實例
export const performanceMonitor = PerformanceMonitor.getInstance();

// 導出預設值
export default performanceMonitor;