import React from "react";
import { Button } from "@/components/ui/button";
// main.jsx寫 輔導人員專用路由
const GuidancePage = () => (
  <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-start py-12 ">
    <section
      className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10"
      aria-label="輔導申請"
    >
      <h1
        className="font-size-3xl font-bold text-primary mb-6 text-center"
        tabIndex={0}
      >
        輔導申請
      </h1>
      <div className="text-left text-gray-500">
        <p>
          <span>
            如果您與學生團隊在執行計畫上有任何困難，我們有專業的輔導團隊將可以協助您克服難關唷！另針對生態學校到校輔導方式可分為電話諮詢與實際到校輔導兩種方式。
          </span>
        </p>

        <p>&nbsp;</p>

        <ol>
          <li>
            <span>
              <strong>電話諮詢</strong>
              ：輔導人員直接或間接獲得學校諮詢電話需求，依據受輔導之學校所提之問題，以電話聯繫之方式進行問題之瞭解。一般來說，電話諮詢可協助處理之問題包含：生態學校認證系統之操作、認證申請應準備之資料、認證流程等。
            </span>
          </li>
        </ol>

        <p>&nbsp;</p>

        <ol>
          <li>
            <span>
              <strong>到校輔導</strong>
              ：當輔導人員經電話諮詢後，研判需進行到校輔導時，應先回報環保署委辦單位，並可視學校需求及欲精進之領域，邀請1位專家學者或顧問、環保局人員或相關委辦單位等，共同到校進行輔導。
            </span>
          </li>
        </ol>

        <p>&nbsp;</p>

        <p>
          <span>除了輔導團隊之外，您也可以直接與本計畫團隊聯繫唷！</span>
        </p>

        <ul>
          <li>
            <span>陳小姐 /&nbsp;02-2775-3919 #232 / <EMAIL></span>
          </li>
          <li>
            <span>蔡小姐 /&nbsp;02-2775-3919 #350/ <EMAIL></span>
          </li>
          <li>
            <span>游小姐 /&nbsp;02-2775-3919 #293 / <EMAIL></span>
          </li>
          <li>
            <span>林先生 / 02-2775-3919 #376 / <EMAIL></span>
          </li>
        </ul>

        <p>&nbsp;</p>

        <p>
          <span>輔導申請，線上填單&nbsp;➤https://reurl.cc/ZGmkXA</span>
        </p>
      </div>

      {/* <div className="flex justify-center gap-4 mt-8">
        <Button variant="primary">我要申請</Button>
        <Button variant="outline">操作說明</Button>
      </div> */}
    </section>
  </main>
);

export default GuidancePage;
