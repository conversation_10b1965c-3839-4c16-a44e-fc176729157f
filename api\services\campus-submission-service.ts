// ========== 校園投稿服務 - 業務邏輯和資料庫查詢 ==========

import { executeQuery, executeQuerySingle } from "../config/database-mssql.js";
import {
  CampusSubmission,
  CampusSubmissionDetail,
  CampusSubmissionAttachment,
  SchoolInfo,
  SubmissionQueryResult,
  SubmissionDetailQueryResult,
  AttachmentQueryResult,
  CreateCampusSubmissionRequest,
  UpdateCampusSubmissionRequest,
} from "../models/campus-submission.js";
import {
  getFinalStatus,
  hasValidReview,
  formatDateToISOString,
  generateDescriptionSummary,
  SUBMISSION_STATUS,
  LOCALE_CODE,
  DEFAULT_PAGINATION,
} from "../constants/campus-submission.js";
import { APILogger } from "../utils/logger.js";

export class CampusSubmissionService {
  // 根據 Token 獲取學校資訊
  static async getSchoolInfoByToken(token: string): Promise<SchoolInfo | null> {
    const schoolQuery = `
      SELECT 
        s.Id as schoolId,
        sc.Name as schoolName,
        a.AccountId
      FROM UserToken ut
      INNER JOIN Accounts a ON ut.AccountSid = a.AccountId
      INNER JOIN Schools s ON a.SchoolId = s.Id
      INNER JOIN SchoolContents sc ON s.Id = sc.SchoolId AND sc.LocaleCode = @localeCode
      WHERE ut.Token = CAST(@token AS uniqueidentifier)
        AND ut.Status = 1 
        AND a.Status = 1
        AND s.Status = 1
    `;

    return await executeQuerySingle<SchoolInfo>(schoolQuery, {
      token,
      localeCode: LOCALE_CODE.ZH_TW,
    });
  }

  // 獲取校園投稿列表
  static async getCampusSubmissionList(
    schoolId: number,
    page: number = DEFAULT_PAGINATION.PAGE,
    limit: number = DEFAULT_PAGINATION.LIMIT
  ): Promise<{ submissions: CampusSubmission[]; total: number }> {
    const offset = (page - 1) * limit;

    // 查詢校園投稿列表（排除已刪除的記錄）- 優先使用最新審核狀態
    const submissionsQuery = `
      SELECT 
        cs.CampusSubmissionId,
        csc.Title as title,
        CASE 
          WHEN LEN(csc.Description) > @summaryLength THEN SUBSTRING(csc.Description, 1, @summaryLength) + '...'
          ELSE csc.Description
        END as description,
        cs.Status as SubmissionStatus,
        latest_review.ReviewStatus,
        latest_review.CampusSubmissionReviewId,
        latest_review.ReviewComment,
        cs.SubmissionDate,
        cs.CreatedTime,
        cs.UpdatedTime,
        cs.BadgeType,
        cs.FeaturedStatus
      FROM CampusSubmissions cs
      INNER JOIN CampusSubmissionContents csc ON cs.CampusSubmissionId = csc.CampusSubmissionId
      LEFT JOIN (
        SELECT 
          csr.CampusSubmissionId,
          csr.ReviewStatus,
          csr.CampusSubmissionReviewId,
          csr.ReviewComment,
          ROW_NUMBER() OVER (PARTITION BY csr.CampusSubmissionId ORDER BY csr.CreatedTime DESC) as rn
        FROM CampusSubmissionReviews csr
        WHERE csr.Status != @deletedStatus
      ) latest_review ON cs.CampusSubmissionId = latest_review.CampusSubmissionId AND latest_review.rn = 1
      WHERE cs.SchoolId = @schoolId
        AND csc.LocaleCode = @localeCode
        AND cs.Status != @deletedStatus
      ORDER BY cs.CreatedTime DESC
      OFFSET @offset ROWS
      FETCH NEXT @limit ROWS ONLY
    `;

    // 查詢總數
    const countQuery = `
      SELECT COUNT(*) as total
      FROM CampusSubmissions cs
      INNER JOIN CampusSubmissionContents csc ON cs.CampusSubmissionId = csc.CampusSubmissionId
      WHERE cs.SchoolId = @schoolId
        AND csc.LocaleCode = @localeCode
        AND cs.Status != @deletedStatus
    `;

    const [submissions, countResult] = await Promise.all([
      executeQuery<SubmissionQueryResult>(submissionsQuery, {
        schoolId,
        localeCode: LOCALE_CODE.ZH_TW,
        deletedStatus: SUBMISSION_STATUS.DELETED,
        summaryLength: 200,
        offset,
        limit,
      }),
      executeQuerySingle<{ total: number }>(countQuery, {
        schoolId,
        localeCode: LOCALE_CODE.ZH_TW,
        deletedStatus: SUBMISSION_STATUS.DELETED,
      }),
    ]);

    const formattedSubmissions = submissions.map(this.formatSubmissionFromQuery);
    const total = countResult?.total || 0;

    return { submissions: formattedSubmissions, total };
  }

  // 獲取校園投稿詳情
  static async getCampusSubmissionDetail(submissionId: string, schoolId: number): Promise<CampusSubmissionDetail | null> {
    // 查詢投稿詳細資料 - 包含最新審核狀態
    const submissionQuery = `
      SELECT 
        cs.CampusSubmissionId,
        cs.Status as SubmissionStatus,
        latest_review.ReviewStatus,
        latest_review.CampusSubmissionReviewId,
        latest_review.ReviewComment,
        cs.SubmissionDate,
        cs.CreatedTime,
        cs.UpdatedTime,
        cs.BadgeType,
        cs.FeaturedStatus,
        cs.SchoolId,
        zh.Title as ZhTitle,
        zh.Description as ZhContent,
        en.Title as EnTitle,
        en.Description as EnContent
      FROM CampusSubmissions cs
      LEFT JOIN CampusSubmissionContents zh ON cs.CampusSubmissionId = zh.CampusSubmissionId AND zh.LocaleCode = @zhLocale
      LEFT JOIN CampusSubmissionContents en ON cs.CampusSubmissionId = en.CampusSubmissionId AND en.LocaleCode = @enLocale
      LEFT JOIN (
        SELECT 
          csr.CampusSubmissionId,
          csr.ReviewStatus,
          csr.CampusSubmissionReviewId,
          csr.ReviewComment,
          ROW_NUMBER() OVER (PARTITION BY csr.CampusSubmissionId ORDER BY csr.CreatedTime DESC) as rn
        FROM CampusSubmissionReviews csr
        WHERE csr.Status != @deletedStatus
      ) latest_review ON cs.CampusSubmissionId = latest_review.CampusSubmissionId AND latest_review.rn = 1
      WHERE cs.CampusSubmissionId = @submissionId
        AND cs.SchoolId = @schoolId
        AND cs.Status != @deletedStatus
    `;

    // 查詢附件資料
    const attachmentsQuery = `
      SELECT 
        csa.CampusSubmissionAttachmentId as AttachmentId,
        csa.FileEntryId,
        csa.ContentTypeCode,
        csa.Title,
        csa.AltUrl
      FROM CampusSubmissionAttachments csa
      INNER JOIN CampusSubmissionContents csc ON csa.CampusSubmissionContentId = csc.CampusSubmissionContentId
      WHERE csc.CampusSubmissionId = @submissionId
        AND csc.LocaleCode = @localeCode
      ORDER BY csa.SortOrder
    `;

    const [submission, attachments] = await Promise.all([
      executeQuerySingle<SubmissionDetailQueryResult>(submissionQuery, {
        submissionId: parseInt(submissionId),
        schoolId,
        zhLocale: LOCALE_CODE.ZH_TW,
        enLocale: LOCALE_CODE.EN,
        deletedStatus: SUBMISSION_STATUS.DELETED,
      }),
      executeQuery<AttachmentQueryResult>(attachmentsQuery, {
        submissionId: parseInt(submissionId),
        localeCode: LOCALE_CODE.ZH_TW,
      }),
    ]);

    if (!submission) return null;

    return this.formatSubmissionDetailFromQuery(submission, attachments);
  }

  // 刪除校園投稿
  static async deleteCampusSubmission(
    submissionId: string,
    schoolId: number,
    userId: number
  ): Promise<{ canDelete: boolean; message: string; submission?: any }> {
    // 檢查投稿是否存在且屬於該學校，並獲取審核狀態
    const checkQuery = `
      SELECT 
        cs.CampusSubmissionId,
        cs.Status as SubmissionStatus,
        csc.Title,
        latest_review.ReviewStatus,
        latest_review.ReviewComment
      FROM CampusSubmissions cs
      INNER JOIN CampusSubmissionContents csc ON cs.CampusSubmissionId = csc.CampusSubmissionId
      LEFT JOIN (
        SELECT 
          csr.CampusSubmissionId,
          csr.ReviewStatus,
          csr.ReviewComment,
          ROW_NUMBER() OVER (PARTITION BY csr.CampusSubmissionId ORDER BY csr.CreatedTime DESC) as rn
        FROM CampusSubmissionReviews csr
        WHERE csr.Status != @deletedStatus
      ) latest_review ON cs.CampusSubmissionId = latest_review.CampusSubmissionId AND latest_review.rn = 1
      WHERE cs.CampusSubmissionId = @submissionId
        AND cs.SchoolId = @schoolId
        AND csc.LocaleCode = @localeCode
        AND cs.Status != @deletedStatus
    `;

    const existingSubmission = await executeQuerySingle<{
      CampusSubmissionId: string;
      SubmissionStatus: number;
      Title: string;
      ReviewStatus?: number;
      ReviewComment?: string;
    }>(checkQuery, {
      submissionId: parseInt(submissionId),
      schoolId,
      localeCode: LOCALE_CODE.ZH_TW,
      deletedStatus: SUBMISSION_STATUS.DELETED,
    });

    if (!existingSubmission) {
      return {
        canDelete: false,
        message: "找不到指定的投稿記錄或已被刪除",
      };
    }

    // 計算真正的狀態
    const hasReview = hasValidReview(existingSubmission.ReviewComment);
    const finalStatus = getFinalStatus(existingSubmission.SubmissionStatus, existingSubmission.ReviewStatus, hasReview, existingSubmission.ReviewComment);

    // 檢查投稿狀態是否允許刪除 - 只有未送審(-1)和已退件(3)可以刪除
    if (finalStatus.status === SUBMISSION_STATUS.UNDER_REVIEW) {
      return {
        canDelete: false,
        message: "審核中的投稿不可刪除",
        submission: existingSubmission,
      };
    }

    if (finalStatus.status === SUBMISSION_STATUS.PUBLISHED) {
      return {
        canDelete: false,
        message: "已發布的投稿不可刪除",
        submission: existingSubmission,
      };
    }

    // 執行軟刪除（設定 Status = 2）
    const deleteQuery = `
      UPDATE CampusSubmissions 
      SET 
        Status = @deletedStatus,
        UpdatedTime = GETDATE(),
        UpdatedUserId = @userId
      WHERE CampusSubmissionId = @submissionId
        AND SchoolId = @schoolId
    `;

    await executeQuery(deleteQuery, {
      submissionId: parseInt(submissionId),
      schoolId,
      userId,
      deletedStatus: SUBMISSION_STATUS.DELETED,
    });

    return {
      canDelete: true,
      message: "校園投稿刪除成功",
      submission: existingSubmission,
    };
  }

  // 創建校園投稿
  static async createCampusSubmission(
    data: CreateCampusSubmissionRequest,
    schoolId: number,
    userId: number
  ): Promise<{ submissionId: string; message: string }> {
    // 這裡應該實現創建邏輯
    // 由於原檔案太長，這裡先留一個框架
    throw new Error("方法尚未實現");
  }

  // 更新校園投稿
  static async updateCampusSubmission(
    data: UpdateCampusSubmissionRequest,
    schoolId: number,
    userId: number
  ): Promise<{ submissionId: string; message: string }> {
    // 這裡應該實現更新邏輯
    throw new Error("方法尚未實現");
  }

  // ========== 私有輔助方法 ==========

  // 格式化查詢結果為 CampusSubmission
  private static formatSubmissionFromQuery(submission: SubmissionQueryResult): CampusSubmission {
    const hasReview = hasValidReview(submission.ReviewComment);
    const finalStatus = getFinalStatus(submission.SubmissionStatus, submission.ReviewStatus, hasReview, submission.ReviewComment);

    return {
      submissionId: submission.CampusSubmissionId.toString(),
      title: submission.title,
      description: submission.description || "",
      status: finalStatus.status,
      statusText: finalStatus.statusText,
      submissionDate: formatDateToISOString(submission.SubmissionDate),
      createdTime: formatDateToISOString(submission.CreatedTime),
      updatedTime: submission.UpdatedTime ? formatDateToISOString(submission.UpdatedTime) : undefined,
      badgeType: submission.BadgeType,
      featuredStatus: submission.FeaturedStatus,
    };
  }

  // 格式化查詢結果為 CampusSubmissionDetail
  private static formatSubmissionDetailFromQuery(submission: SubmissionDetailQueryResult, attachments: AttachmentQueryResult[]): CampusSubmissionDetail {
    const hasReview = hasValidReview(submission.ReviewComment);
    const finalStatus = getFinalStatus(submission.SubmissionStatus, submission.ReviewStatus, hasReview, submission.ReviewComment);

    const formattedAttachments: CampusSubmissionAttachment[] = attachments.map((att) => ({
      attachmentId: att.AttachmentId,
      fileEntryId: att.FileEntryId || undefined,
      contentTypeCode: att.ContentTypeCode,
      title: att.Title || undefined,
      altUrl: att.AltUrl || undefined,
    }));

    return {
      submissionId: submission.CampusSubmissionId.toString(),
      title: submission.ZhTitle,
      description: submission.ZhContent,
      status: finalStatus.status,
      statusText: finalStatus.statusText,
      submissionDate: formatDateToISOString(submission.SubmissionDate),
      createdTime: formatDateToISOString(submission.CreatedTime),
      updatedTime: submission.UpdatedTime ? formatDateToISOString(submission.UpdatedTime) : undefined,
      badgeType: submission.BadgeType,
      featuredStatus: submission.FeaturedStatus,
      zhTitle: submission.ZhTitle,
      zhContent: submission.ZhContent,
      enTitle: submission.EnTitle || "",
      enContent: submission.EnContent || "",
      attachments: formattedAttachments,
    };
  }
}
