// ========== 校園投稿檔案服務 - 專門處理校園投稿的照片和附件上傳 ==========

import fs from "fs/promises";
import path from "path";
import { FileConfigManager } from "../config/config-manager.js";
import {
  FILE_ERROR_MESSAGES,
  isValidFileSize,
  isValidFilename,
  generateFilename,
} from "../constants/file.js";

export interface CampusSubmissionPhoto {
  id: string;
  originalName: string;
  filename: string;
  fileUrl: string;
  size: number;
  mimetype: string;
  description: string;
  uploadDate: string;
}

export interface CampusSubmissionAttachment {
  id: string;
  originalName: string;
  filename: string;
  fileUrl: string;
  size: number;
  mimetype: string;
  description: string;
  uploadDate: string;
}

export interface CampusSubmissionFileUploadResponse {
  success: boolean;
  message: string;
  data: CampusSubmissionPhoto[] | CampusSubmissionAttachment[];
}

export class CampusSubmissionFileService {
  private static configManager = FileConfigManager.getInstance();
  private static uploadConfig = this.configManager.getUploadConfig();

  // 確保校園投稿照片目錄存在
  static async ensurePhotoDirectory(): Promise<void> {
    const photoPath = this.uploadConfig.campusSubmissionPhotoPath as string;
    if (!photoPath) {
      throw new Error("校園投稿照片路徑未配置");
    }
    await this.ensureDirectory(photoPath);
  }

  // 確保校園投稿附件目錄存在
  static async ensureAttachmentDirectory(): Promise<void> {
    const attachmentPath = this.uploadConfig.campusSubmissionAttachmentPath as string;
    if (!attachmentPath) {
      throw new Error("校園投稿附件路徑未配置");
    }
    await this.ensureDirectory(attachmentPath);
  }

  // 確保目錄存在
  private static async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  // 驗證照片檔案
  private static validatePhotoFile(file: Express.Multer.File): void {
    // 檢查檔案格式
    const allowedPhotoTypes = ["image/jpeg", "image/jpg", "image/png"];
    if (!allowedPhotoTypes.includes(file.mimetype)) {
      throw new Error("照片僅支援 JPG、PNG 格式");
    }

    // 檢查檔案大小 (2MB)
    const maxPhotoSize = 2 * 1024 * 1024; // 2MB
    if (!isValidFileSize(file.size, "photo")) {
      throw new Error("照片大小不可超過 2MB");
    }
  }

  // 驗證附件檔案
  private static validateAttachmentFile(file: Express.Multer.File): void {
    // 檢查檔案格式
    const allowedAttachmentTypes = ["application/pdf", "application/vnd.oasis.opendocument.text"];
    if (!allowedAttachmentTypes.includes(file.mimetype)) {
      throw new Error("附件僅支援 PDF、ODT 格式");
    }

    // 檢查檔案大小 (5MB)
    const maxAttachmentSize = 5 * 1024 * 1024; // 5MB
    if (!isValidFileSize(file.size, "attachment")) {
      throw new Error("附件大小不可超過 5MB");
    }
  }

  // 處理校園投稿照片上傳
  static async processPhotoUpload(
    files: Express.Multer.File[],
    submissionId: string
  ): Promise<CampusSubmissionPhoto[]> {
    if (!files || files.length === 0) {
      throw new Error(FILE_ERROR_MESSAGES.NO_FILE_UPLOADED);
    }

    // 確保目錄存在
    await this.ensurePhotoDirectory();

    const photos: CampusSubmissionPhoto[] = [];
    const photoPath = this.uploadConfig.campusSubmissionPhotoPath as string;

    for (const file of files) {
      // 驗證檔案
      this.validatePhotoFile(file);

      // 生成檔案名
      const filename = generateFilename(file.originalname, submissionId);
      const fullPhotoPath = path.join(photoPath, filename);

      // 移動檔案到目標目錄
      await fs.rename(file.path, fullPhotoPath);

      // 創建照片記錄
      const photo: CampusSubmissionPhoto = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        originalName: Buffer.from(file.originalname, "latin1").toString("utf8"),
        filename: filename,
        fileUrl: `/uploads/campus-submissions/photos/${filename}`,
        size: file.size,
        mimetype: file.mimetype,
        description: "",
        uploadDate: new Date().toISOString(),
      };

      photos.push(photo);
    }

    return photos;
  }

  // 處理校園投稿附件上傳
  static async processAttachmentUpload(
    files: Express.Multer.File[],
    submissionId: string
  ): Promise<CampusSubmissionAttachment[]> {
    if (!files || files.length === 0) {
      throw new Error(FILE_ERROR_MESSAGES.NO_FILE_UPLOADED);
    }

    // 確保目錄存在
    await this.ensureAttachmentDirectory();

    const attachments: CampusSubmissionAttachment[] = [];
    const attachmentPath = this.uploadConfig.campusSubmissionAttachmentPath as string;

    for (const file of files) {
      // 驗證檔案
      this.validateAttachmentFile(file);

      // 生成檔案名
      const filename = generateFilename(file.originalname, submissionId);
      const fullAttachmentPath = path.join(attachmentPath, filename);

      // 移動檔案到目標目錄
      await fs.rename(file.path, fullAttachmentPath);

      // 創建附件記錄
      const attachment: CampusSubmissionAttachment = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        originalName: Buffer.from(file.originalname, "latin1").toString("utf8"),
        filename: filename,
        fileUrl: `/uploads/campus-submissions/attachments/${filename}`,
        size: file.size,
        mimetype: file.mimetype,
        description: "",
        uploadDate: new Date().toISOString(),
      };

      attachments.push(attachment);
    }

    return attachments;
  }

  // 刪除校園投稿照片
  static async deletePhoto(filename: string): Promise<void> {
    if (!isValidFilename(filename)) {
      throw new Error(FILE_ERROR_MESSAGES.INVALID_FILENAME);
    }

    const photoPath = this.uploadConfig.campusSubmissionPhotoPath as string;
    const fullPhotoPath = path.join(photoPath, filename);
    
    try {
      await fs.unlink(fullPhotoPath);
    } catch (error) {
      throw new Error(`刪除照片失敗: ${error instanceof Error ? error.message : "未知錯誤"}`);
    }
  }

  // 刪除校園投稿附件
  static async deleteAttachment(filename: string): Promise<void> {
    if (!isValidFilename(filename)) {
      throw new Error(FILE_ERROR_MESSAGES.INVALID_FILENAME);
    }

    const attachmentPath = this.uploadConfig.campusSubmissionAttachmentPath as string;
    const fullAttachmentPath = path.join(attachmentPath, filename);
    
    try {
      await fs.unlink(fullAttachmentPath);
    } catch (error) {
      throw new Error(`刪除附件失敗: ${error instanceof Error ? error.message : "未知錯誤"}`);
    }
  }

  // 獲取校園投稿檔案配置
  static getFileConfig() {
    return {
      photoPath: this.uploadConfig.campusSubmissionPhotoPath as string,
      attachmentPath: this.uploadConfig.campusSubmissionAttachmentPath as string,
      maxPhotoSize: 2 * 1024 * 1024, // 2MB
      maxAttachmentSize: 5 * 1024 * 1024, // 5MB
      allowedPhotoTypes: ["image/jpeg", "image/jpg", "image/png"],
      allowedAttachmentTypes: ["application/pdf", "application/vnd.oasis.opendocument.text"],
    };
  }
} 
