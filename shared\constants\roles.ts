/**
 * 系統角色常數定義
 * 統一管理所有角色相關的常數和映射關係
 * 
 * @description 整合原本分散在各處的角色定義，提供一致性的角色管理
 * @version 1.0.0
 */

/**
 * 允許的角色列表
 * 所有系統認可的角色都必須在此定義
 */
export const ALLOWED_ROLES = {
  /** 生態學校 */
  SCHOOL: 'school',
  /** 縣市政府 */
  EPA: 'epa',
  /** 輔導人員 */
  TUTOR: 'tutor'
} as const;

/**
 * 角色類型定義
 * 從 ALLOWED_ROLES 推導出的類型
 */
export type AllowedRole = typeof ALLOWED_ROLES[keyof typeof ALLOWED_ROLES];

/**
 * 角色映射表
 * 支援多種格式的角色映射，包含中英文對照、舊版相容性、TokenType 數字映射
 */
export const ROLE_MAPPING = {
  // === 英文到中文映射 ===
  [ALLOWED_ROLES.SCHOOL]: '生態學校',
  [ALLOWED_ROLES.EPA]: '縣市政府',
  [ALLOWED_ROLES.TUTOR]: '輔導人員',
  
  // === 中文到英文映射（反向映射）===
  '生態學校': ALLOWED_ROLES.SCHOOL,
  '縣市政府': ALLOWED_ROLES.EPA,
  '輔導人員': ALLOWED_ROLES.TUTOR,
  
  // === 舊版相容性映射 ===
  // 支援舊版本中使用的首字母大寫格式
  'School': ALLOWED_ROLES.SCHOOL,
  'Government': ALLOWED_ROLES.EPA,
  'Tutor': ALLOWED_ROLES.TUTOR,
  
  // === TokenType 數字映射 ===
  // 資料庫中 TokenType 欄位的數字對應到角色字串
  1: ALLOWED_ROLES.SCHOOL,
  2: ALLOWED_ROLES.EPA,
  3: ALLOWED_ROLES.TUTOR
} as const;

/**
 * 角色顯示名稱取得器
 * @param role 角色識別碼
 * @returns 角色的中文顯示名稱
 */
export const getRoleDisplayName = (role: string | number): string => {
  // 先嘗試從映射表取得英文角色
  const mappedRole = ROLE_MAPPING[role as keyof typeof ROLE_MAPPING];
  
  if (mappedRole && typeof mappedRole === 'string') {
    // 如果映射結果是英文角色，再轉為中文
    const displayName = ROLE_MAPPING[mappedRole as keyof typeof ROLE_MAPPING];
    if (displayName && typeof displayName === 'string' && displayName.includes('生態') || displayName.includes('縣市') || displayName.includes('輔導')) {
      return displayName;
    }
  }
  
  // 如果直接就是中文名稱
  if (typeof role === 'string' && (role.includes('生態') || role.includes('縣市') || role.includes('輔導'))) {
    return role;
  }
  
  return '未知角色';
};

/**
 * 角色驗證器
 * @param role 要驗證的角色
 * @returns 是否為有效角色
 */
export const isValidRole = (role: string | number): boolean => {
  // 檢查是否在 ALLOWED_ROLES 中
  if (typeof role === 'string') {
    return Object.values(ALLOWED_ROLES).includes(role as AllowedRole);
  }
  
  // 檢查是否為有效的 TokenType 數字
  if (typeof role === 'number') {
    return [1, 2, 3].includes(role);
  }
  
  return false;
};

/**
 * 角色正規化器
 * 將各種格式的角色識別碼轉換為標準的小寫英文格式
 * @param role 角色識別碼（字串或數字）
 * @returns 標準化的角色字串
 */
export const normalizeRole = (role: string | number): AllowedRole | null => {
  if (typeof role === 'number') {
    const mappedRole = ROLE_MAPPING[role];
    if (mappedRole && Object.values(ALLOWED_ROLES).includes(mappedRole as AllowedRole)) {
      return mappedRole as AllowedRole;
    }
  }
  
  if (typeof role === 'string') {
    const mappedRole = ROLE_MAPPING[role as keyof typeof ROLE_MAPPING];
    if (mappedRole && Object.values(ALLOWED_ROLES).includes(mappedRole as AllowedRole)) {
      return mappedRole as AllowedRole;
    }
    
    // 直接檢查是否為有效的角色
    const lowerRole = role.toLowerCase();
    if (Object.values(ALLOWED_ROLES).includes(lowerRole as AllowedRole)) {
      return lowerRole as AllowedRole;
    }
  }
  
  return null;
};