// ========== 位置服務 - 業務邏輯和資料庫操作 ==========

import { executeQuery } from "../config/database-mssql.js";
import { APILogger } from "../utils/logger.js";
import { LocationCity, LocationArea, LocationCityWithAreas, CityQueryResult, AreaQueryResult, HierarchyQueryResult } from "../models/location.js";
import {
  DEFAULT_LOCALE,
  LOCATION_ERROR_MESSAGES,
  SQL_QUERIES,
  isValidCityId,
  formatCityData,
  formatAreaData,
  organizeHierarchyData,
  generateHierarchyStats,
  generateSuccessMessage,
  parseCityId,
} from "../constants/location.js";

export class LocationService {
  // 獲取所有縣市列表
  static async getCities(): Promise<LocationCity[]> {
    try {
      // 查詢真實的縣市資料
      const cities = await executeQuery<CityQueryResult>(SQL_QUERIES.GET_CITIES, {
        localeCode: DEFAULT_LOCALE,
      });

      return cities.map(formatCityData);
    } catch (error) {
      console.error("❌ [LocationService] 獲取縣市列表失敗:", error);
      throw error;
    }
  }

  // 獲取指定縣市的區域列表
  static async getAreasByCity(cityIdStr: string): Promise<LocationArea[]> {
    try {
      // 驗證並解析縣市ID
      const cityId = parseCityId(cityIdStr);

      // 查詢指定縣市的區域資料
      const areas = await executeQuery<AreaQueryResult>(SQL_QUERIES.GET_AREAS_BY_CITY, {
        cityId,
        localeCode: DEFAULT_LOCALE,
      });

      return areas.map(formatAreaData);
    } catch (error) {
      console.error("❌ [LocationService] 獲取區域列表失敗:", error);
      throw error;
    }
  }

  // 獲取完整的地區層級資料（縣市 + 區域）
  static async getLocationHierarchy(): Promise<{ data: LocationCityWithAreas[]; message: string }> {
    try {
      APILogger.logDatabase("查詢地區層級資料", SQL_QUERIES.GET_HIERARCHY);

      // 查詢所有縣市及其區域
      const hierarchyData = await executeQuery<HierarchyQueryResult>(SQL_QUERIES.GET_HIERARCHY, {
        localeCode: DEFAULT_LOCALE,
      });

      // 組織層級資料
      const result = organizeHierarchyData(hierarchyData);

      // 生成統計資訊
      const stats = generateHierarchyStats(result);
      const message = generateSuccessMessage("hierarchy", stats);

      APILogger.logSuccess("Location", "獲取地區層級資料", result, message);

      return {
        data: result,
        message,
      };
    } catch (error) {
      console.error("❌ [LocationService] 獲取地區層級資料失敗:", error);
      throw error;
    }
  }
}
