// 🔧 前端上傳診斷工具
import { getApiBaseUrl } from "@/config/environment";
import { authService } from "@/services/authService";

export interface UploadDiagnostics {
  timestamp: string;
  environment: string;
  frontend: {
    apiBaseUrl: string;
    uploadEndpoints: {
      single: string;
      multiple: string;
      schoolLogo: string;
    };
    staticPaths: {
      uploads: string;
      schoolLogos: string;
    };
  };
  backend?: {
    config: any;
    directories: any;
    permissions: any;
    system: any;
  };
  tests: {
    apiConnection: boolean;
    staticFileAccess: boolean;
    uploadEndpoint: boolean;
  };
  recommendations: string[];
}

/**
 * 執行前端上傳診斷
 */
export const runUploadDiagnostics = async (): Promise<UploadDiagnostics> => {
  const diagnostics: UploadDiagnostics = {
    timestamp: new Date().toISOString(),
    environment: import.meta.env.MODE || "unknown",
    frontend: {
      apiBaseUrl: getApiBaseUrl(),
      uploadEndpoints: {
        single: `${getApiBaseUrl()}/file/upload-single`,
        multiple: `${getApiBaseUrl()}/file/upload`,
        schoolLogo: `${getApiBaseUrl()}/file/upload-school-logo`,
      },
      staticPaths: {
        uploads: "/uploads",
        schoolLogos: "/uploads/school-logos",
      },
    },
    tests: {
      apiConnection: false,
      staticFileAccess: false,
      uploadEndpoint: false,
    },
    recommendations: [],
  };

  // 測試1: API連接
  try {
    const response = await fetch(`${getApiBaseUrl()}/health`);
    diagnostics.tests.apiConnection = response.ok;

    if (!response.ok) {
      diagnostics.recommendations.push("API服務無法連接，請檢查後端服務是否正常運行");
    }
  } catch (error) {
    diagnostics.tests.apiConnection = false;
    diagnostics.recommendations.push(`API連接失敗: ${error instanceof Error ? error.message : String(error)}`);
  }

  // 測試2: 靜態檔案訪問（測試已知的校徽檔案）
  try {
    // 嘗試訪問一個測試圖片
    const testImageUrl = "/uploads/school-logos/test.jpg";
    const response = await fetch(testImageUrl, { method: "HEAD" });
    diagnostics.tests.staticFileAccess = response.ok || response.status === 404; // 404也算正常，表示路徑可訪問

    if (response.status === 403) {
      diagnostics.recommendations.push("靜態檔案路徑被禁止訪問，請檢查伺服器配置");
    } else if (response.status === 500) {
      diagnostics.recommendations.push("靜態檔案服務發生內部錯誤，請檢查伺服器日誌");
    }
  } catch (error) {
    diagnostics.tests.staticFileAccess = false;
    diagnostics.recommendations.push("靜態檔案路徑無法訪問，請檢查伺服器靜態服務配置");
  }

  // 測試3: 上傳端點可用性
  try {
    const response = await fetch(`${getApiBaseUrl()}/file/upload-diagnostics`, {
      method: "GET",
      headers: {
        ...authService.getAuthHeaders(),
      },
    });

    if (response.ok) {
      const backendDiagnostics = await response.json();
      diagnostics.backend = backendDiagnostics.data;
      diagnostics.tests.uploadEndpoint = true;

      // 分析後端診斷結果
      if (diagnostics.backend) {
        const { directories, permissions } = diagnostics.backend;

        // 檢查目錄狀態
        Object.entries(directories || {}).forEach(([name, info]: [string, any]) => {
          if (!info.exists) {
            diagnostics.recommendations.push(`${name} 目錄不存在: ${info.path}`);
          }
        });

        // 檢查權限狀態
        Object.entries(permissions || {}).forEach(([name, info]: [string, any]) => {
          if (!info.writable) {
            diagnostics.recommendations.push(`${name} 目錄沒有寫入權限: ${info.writeError || "未知錯誤"}`);
          }
        });
      }
    } else {
      diagnostics.tests.uploadEndpoint = false;
      diagnostics.recommendations.push("上傳診斷端點無法訪問，可能是認證問題或端點不存在");
    }
  } catch (error) {
    diagnostics.tests.uploadEndpoint = false;
    diagnostics.recommendations.push(`上傳端點測試失敗: ${error instanceof Error ? error.message : String(error)}`);
  }

  // 生成總體建議
  if (diagnostics.tests.apiConnection && diagnostics.tests.staticFileAccess && diagnostics.tests.uploadEndpoint) {
    if (diagnostics.recommendations.length === 0) {
      diagnostics.recommendations.push("✅ 所有測試通過，上傳功能應該正常工作");
    }
  } else {
    diagnostics.recommendations.unshift("❌ 部分測試失敗，請根據以下建議進行修復：");
  }

  return diagnostics;
};

/**
 * 格式化診斷結果為可讀的文字
 */
export const formatDiagnosticsReport = (diagnostics: UploadDiagnostics): string => {
  const lines: string[] = [];

  lines.push("# 上傳功能診斷報告");
  lines.push(`時間: ${diagnostics.timestamp}`);
  lines.push(`環境: ${diagnostics.environment}`);
  lines.push("");

  lines.push("## 前端配置");
  lines.push(`API基礎URL: ${diagnostics.frontend.apiBaseUrl}`);
  lines.push(`校徽上傳端點: ${diagnostics.frontend.uploadEndpoints.schoolLogo}`);
  lines.push(`一般上傳端點: ${diagnostics.frontend.uploadEndpoints.single}`);
  lines.push(`靜態檔案路徑: ${diagnostics.frontend.staticPaths.uploads}`);
  lines.push("");

  lines.push("## 測試結果");
  lines.push(`API連接: ${diagnostics.tests.apiConnection ? "✅ 通過" : "❌ 失敗"}`);
  lines.push(`靜態檔案訪問: ${diagnostics.tests.staticFileAccess ? "✅ 通過" : "❌ 失敗"}`);
  lines.push(`上傳端點: ${diagnostics.tests.uploadEndpoint ? "✅ 通過" : "❌ 失敗"}`);
  lines.push("");

  if (diagnostics.backend) {
    lines.push("## 後端配置");
    lines.push(`環境: ${diagnostics.backend.environment || "unknown"}`);
    lines.push(`工作目錄: ${diagnostics.backend.system?.cwd || "unknown"}`);
    lines.push(`平台: ${diagnostics.backend.system?.platform || "unknown"}`);
    lines.push("");

    lines.push("### 目錄狀態");
    Object.entries(diagnostics.backend.directories || {}).forEach(([name, info]: [string, any]) => {
      lines.push(`${name}: ${info.exists ? "✅ 存在" : "❌ 不存在"} (${info.path})`);
    });
    lines.push("");

    lines.push("### 權限狀態");
    Object.entries(diagnostics.backend.permissions || {}).forEach(([name, info]: [string, any]) => {
      const status = info.writable ? "✅ 可寫入" : "❌ 無寫入權限";
      lines.push(`${name}: ${status}`);
      if (!info.writable && info.writeError) {
        lines.push(`  錯誤: ${info.writeError}`);
      }
    });
    lines.push("");
  }

  lines.push("## 建議");
  diagnostics.recommendations.forEach((rec, index) => {
    lines.push(`${index + 1}. ${rec}`);
  });

  return lines.join("\n");
};

/**
 * 測試校徽上傳功能
 */
export const testSchoolLogoUpload = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    // 創建一個1x1像素的測試圖片
    const canvas = document.createElement("canvas");
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext("2d");
    if (ctx) {
      ctx.fillStyle = "#FF0000";
      ctx.fillRect(0, 0, 1, 1);
    }

    // 轉換為Blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, "image/png");
    });

    // 創建FormData
    const formData = new FormData();
    formData.append("logo", blob, "test-logo.png");

    // 發送上傳請求
    const headers = authService.getAuthHeaders();
    delete headers["Content-Type"]; // FormData 請求不能設定 Content-Type
    const response = await fetch(`${getApiBaseUrl()}/file/upload-school-logo`, {
      method: "POST",
      headers,
      body: formData,
    });

    const result = await response.json();

    return {
      success: response.ok,
      message: result.message || (response.ok ? "測試上傳成功" : "測試上傳失敗"),
      details: result,
    };
  } catch (error) {
    return {
      success: false,
      message: `測試上傳失敗: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
};
