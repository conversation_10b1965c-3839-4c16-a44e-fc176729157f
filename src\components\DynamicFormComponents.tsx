import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trash2, Plus, Calendar, Users, Target } from "lucide-react";

// 環境路徑選擇表單
export interface EnvironmentalPath {
  id: string;
  name: string;
  action_name: string;
  short_term_goals: string;
  medium_term_goals: string;
  long_term_goals: string;
  implementation_strategy: string;
  expected_benefits: string;
  priority_level: string;
}

interface DynamicPathSelectionProps {
  data: EnvironmentalPath[];
  onChange: (data: EnvironmentalPath[]) => void;
  certificationLevel: string;
  disabled?: boolean;
}

export const DynamicPathSelection: React.FC<DynamicPathSelectionProps> = ({ data, onChange, certificationLevel, disabled = false }) => {
  const environmentalPathways = [
    { value: "transportation", label: "交通", mandatory: true },
    { value: "climate_change", label: "氣候變遷", mandatory: true },
    { value: "consumption_waste", label: "消耗與廢棄物", mandatory: true },
    { value: "sustainable_food", label: "永續食物", mandatory: true },
    { value: "water", label: "水", mandatory: false },
    { value: "energy", label: "能源", mandatory: false },
    { value: "biodiversity", label: "生物多樣性", mandatory: false },
    { value: "healthy_living", label: "健康生活", mandatory: false },
    { value: "marine_environment", label: "海洋環境", mandatory: false },
    { value: "school_grounds", label: "學校場域", mandatory: false },
  ];

  const addPath = () => {
    const newPath: EnvironmentalPath = {
      id: "",
      name: "",
      action_name: "",
      short_term_goals: "",
      medium_term_goals: "",
      long_term_goals: "",
      implementation_strategy: "",
      expected_benefits: "",
      priority_level: "medium",
    };
    const currentData = Array.isArray(data) ? data : [];
    onChange([...currentData, newPath]);
  };

  const updatePath = (index: number, field: keyof EnvironmentalPath, value: string) => {
    if (!Array.isArray(data)) return;
    const updatedPaths = data.map((path, i) => (i === index ? { ...path, [field]: value } : path));
    onChange(updatedPaths);
  };

  const removePath = (index: number) => {
    if (!Array.isArray(data)) {
      console.warn("Data is not an array, cannot remove path");
      return;
    }

    const minPaths = getMinPathRequirement();

    // 允許刪除，但要確保符合最低要求
    if (data.length > minPaths) {
      const updatedPaths = data.filter((_, i) => i !== index);
      onChange(updatedPaths);
    } else {
      alert(`至少需要保留 ${minPaths} 個環境路徑以符合${certificationLevel}認證要求`);
    }
  };

  const getMinPathRequirement = () => {
    switch (certificationLevel) {
      case "bronze":
        return 1;
      case "silver":
        return 2;
      case "green_flag":
        return 3;
      default:
        return 1;
    }
  };

  const isMandatoryRequired = () => {
    return certificationLevel === "silver" || certificationLevel === "green_flag";
  };

  const validatePaths = () => {
    // Safety check: ensure data is an array
    if (!Array.isArray(data)) {
      return false;
    }

    const minPaths = getMinPathRequirement();
    const hasMandatory = data.some((path) => environmentalPathways.find((ep) => ep.value === path.id && ep.mandatory));

    return data.length >= minPaths && (!isMandatoryRequired() || hasMandatory);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium text-gray-700">環境路徑選擇</h4>
        <div className="font-size-sm text-gray-500">
          需選擇至少 {getMinPathRequirement()} 個路徑
          {isMandatoryRequired() && "（包含至少1個必選路徑）"}
        </div>
      </div>

      {!validatePaths() && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <p className="text-yellow-800 font-size-sm">⚠️ 請確保符合認證等級的路徑要求</p>
        </div>
      )}

      {(Array.isArray(data) ? data : []).map((path, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="font-size-base">環境路徑 {Number.isInteger(index) ? index + 1 : 1}</CardTitle>
              {Array.isArray(data) && data.length > getMinPathRequirement() && !disabled && (
                <Button variant="outline" size="sm" onClick={() => removePath(index)} className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">
                環境路徑 <span className="text-red-500">*</span>
              </label>
              <select
                value={path.id}
                onChange={(e) => updatePath(index, "id", e.target.value)}
                disabled={disabled}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">請選擇環境路徑</option>
                {environmentalPathways.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                    {option.mandatory && " (必選)"}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">行動方案名稱</label>
              <Input
                value={path.action_name}
                onChange={(e) => updatePath(index, "action_name", e.target.value)}
                placeholder="請輸入行動方案名稱"
                disabled={disabled}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">短期目標（1年內）</label>
                <Textarea
                  value={path.short_term_goals}
                  onChange={(e) => updatePath(index, "short_term_goals", e.target.value)}
                  placeholder="請描述短期目標"
                  rows={3}
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">中期目標（2-3年）</label>
                <Textarea
                  value={path.medium_term_goals}
                  onChange={(e) => updatePath(index, "medium_term_goals", e.target.value)}
                  placeholder="請描述中期目標"
                  rows={3}
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">長期目標（3年以上）</label>
                <Textarea
                  value={path.long_term_goals}
                  onChange={(e) => updatePath(index, "long_term_goals", e.target.value)}
                  placeholder="請描述長期目標"
                  rows={3}
                  disabled={disabled}
                />
              </div>
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">實施策略</label>
              <Textarea
                value={path.implementation_strategy}
                onChange={(e) => updatePath(index, "implementation_strategy", e.target.value)}
                placeholder="請描述具體的實施策略和方法"
                rows={3}
                disabled={disabled}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">預期效益</label>
                <Textarea
                  value={path.expected_benefits}
                  onChange={(e) => updatePath(index, "expected_benefits", e.target.value)}
                  placeholder="請描述預期效益"
                  rows={2}
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">優先等級</label>
                <select
                  value={path.priority_level}
                  onChange={(e) => updatePath(index, "priority_level", e.target.value)}
                  disabled={disabled}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="high">高</option>
                  <option value="medium">中</option>
                  <option value="low">低</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {!disabled && (
        <Button variant="outline" onClick={addPath} className="w-full border-dashed">
          <Plus className="w-4 h-4 mr-2" />
          新增環境路徑
        </Button>
      )}
    </div>
  );
};

// 具體行動方案表單
export interface ActionPlan {
  plan_name: string;
  description: string;
  timeline: string;
  responsible_team: string;
  resources_needed: string;
  success_indicators: string;
  status: string;
}

interface DynamicActionPlansProps {
  data: ActionPlan[];
  onChange: (data: ActionPlan[]) => void;
  disabled?: boolean;
}

export const DynamicActionPlans: React.FC<DynamicActionPlansProps> = ({ data, onChange, disabled = false }) => {
  const addPlan = () => {
    const newPlan: ActionPlan = {
      plan_name: "",
      description: "",
      timeline: "",
      responsible_team: "",
      resources_needed: "",
      success_indicators: "",
      status: "planning",
    };
    onChange([...data, newPlan]);
  };

  const updatePlan = (index: number, field: keyof ActionPlan, value: string) => {
    const updatedPlans = data.map((plan, i) => (i === index ? { ...plan, [field]: value } : plan));
    onChange(updatedPlans);
  };

  const removePlan = (index: number) => {
    // 允許刪除，但至少保留一個空項目
    const updatedPlans = data.filter((_, i) => i !== index);
    if (updatedPlans.length === 0) {
      onChange([
        {
          plan_name: "",
          description: "",
          timeline: "",
          responsible_team: "",
          resources_needed: "",
          success_indicators: "",
          status: "planning",
        },
      ]);
    } else {
      onChange(updatedPlans);
    }
  };

  const statusOptions = [
    { value: "planning", label: "規劃中" },
    { value: "implementing", label: "執行中" },
    { value: "completed", label: "已完成" },
    { value: "paused", label: "暫停" },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium text-gray-700 flex items-center gap-2">
          <Target className="w-5 h-5" />
          具體行動方案
        </h4>
      </div>

      {data.map((plan, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="font-size-base">行動方案 {Number.isInteger(index) ? index + 1 : 1}</CardTitle>
              {!disabled && (
                <Button variant="outline" size="sm" onClick={() => removePlan(index)} className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">
                  方案名稱 <span className="text-red-500">*</span>
                </label>
                <Input
                  value={plan.plan_name}
                  onChange={(e) => updatePlan(index, "plan_name", e.target.value)}
                  placeholder="請輸入行動方案名稱"
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">執行狀態</label>
                <select
                  value={plan.status}
                  onChange={(e) => updatePlan(index, "status", e.target.value)}
                  disabled={disabled}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">方案描述</label>
              <Textarea
                value={plan.description}
                onChange={(e) => updatePlan(index, "description", e.target.value)}
                placeholder="請詳細描述行動方案內容"
                rows={3}
                disabled={disabled}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">執行時程</label>
                <Input
                  value={plan.timeline}
                  onChange={(e) => updatePlan(index, "timeline", e.target.value)}
                  placeholder="例：2024年3月至6月"
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">負責團隊</label>
                <Input
                  value={plan.responsible_team}
                  onChange={(e) => updatePlan(index, "responsible_team", e.target.value)}
                  placeholder="請輸入負責團隊或人員"
                  disabled={disabled}
                />
              </div>
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">所需資源</label>
              <Textarea
                value={plan.resources_needed}
                onChange={(e) => updatePlan(index, "resources_needed", e.target.value)}
                placeholder="請描述執行此方案所需的人力、物力、財力等資源"
                rows={2}
                disabled={disabled}
              />
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">成功指標</label>
              <Textarea
                value={plan.success_indicators}
                onChange={(e) => updatePlan(index, "success_indicators", e.target.value)}
                placeholder="請描述如何衡量此方案的成功"
                rows={2}
                disabled={disabled}
              />
            </div>
          </CardContent>
        </Card>
      ))}

      {!disabled && (
        <Button variant="outline" onClick={addPlan} className="w-full border-dashed">
          <Plus className="w-4 h-4 mr-2" />
          新增行動方案
        </Button>
      )}
    </div>
  );
};

// 分享活動表單
export interface SharingEvent {
  event_name: string;
  event_date: string;
  event_type: string;
  target_audience: string;
  content_shared: string;
  methods_used: string;
  participants_count: number;
  feedback_received: string;
}

interface DynamicSharingEventsProps {
  data: SharingEvent[];
  onChange: (data: SharingEvent[]) => void;
  disabled?: boolean;
}

export const DynamicSharingEvents: React.FC<DynamicSharingEventsProps> = ({ data, onChange, disabled = false }) => {
  const eventTypes = [
    { value: "presentation", label: "簡報分享" },
    { value: "workshop", label: "工作坊" },
    { value: "exhibition", label: "展覽活動" },
    { value: "conference", label: "研習會議" },
    { value: "assembly", label: "朝會報告" },
    { value: "online", label: "線上分享" },
  ];

  const addEvent = () => {
    const newEvent: SharingEvent = {
      event_name: "",
      event_date: "",
      event_type: "",
      target_audience: "",
      content_shared: "",
      methods_used: "",
      participants_count: 0,
      feedback_received: "",
    };
    onChange([...data, newEvent]);
  };

  const updateEvent = (index: number, field: keyof SharingEvent, value: string | number) => {
    const updatedEvents = data.map((event, i) => (i === index ? { ...event, [field]: value } : event));
    onChange(updatedEvents);
  };

  const removeEvent = (index: number) => {
    // 允許刪除所有項目，但至少保留一個空項目給用戶填寫
    const updatedEvents = data.filter((_, i) => i !== index);
    if (updatedEvents.length === 0) {
      // 如果全部刪除，保留一個空項目
      onChange([
        {
          event_name: "",
          event_date: "",
          event_type: "",
          target_audience: "",
          content_shared: "",
          methods_used: "",
          participants_count: 0,
          feedback_received: "",
        },
      ]);
    } else {
      onChange(updatedEvents);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium text-gray-700 flex items-center gap-2">
          <Users className="w-5 h-5" />
          分享活動記錄
        </h4>
      </div>

      {data.map((event, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="font-size-base">分享活動 {Number.isInteger(index) ? index + 1 : 1}</CardTitle>
              {!disabled && (
                <Button variant="outline" size="sm" onClick={() => removeEvent(index)} className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">
                  活動名稱 <span className="text-red-500">*</span>
                </label>
                <Input
                  value={event.event_name}
                  onChange={(e) => updateEvent(index, "event_name", e.target.value)}
                  placeholder="請輸入分享活動名稱"
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">活動日期</label>
                <Input type="date" value={event.event_date} onChange={(e) => updateEvent(index, "event_date", e.target.value)} disabled={disabled} />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">活動類型</label>
                <select
                  value={event.event_type}
                  onChange={(e) => updateEvent(index, "event_type", e.target.value)}
                  disabled={disabled}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">請選擇活動類型</option>
                  {eventTypes.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">目標對象</label>
                <Input
                  value={event.target_audience}
                  onChange={(e) => updateEvent(index, "target_audience", e.target.value)}
                  placeholder="例：全校師生、家長、社區居民"
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">參與人數</label>
                <Input
                  type="number"
                  value={event.participants_count}
                  onChange={(e) => updateEvent(index, "participants_count", parseInt(e.target.value) || 0)}
                  placeholder="0"
                  min="0"
                  disabled={disabled}
                />
              </div>
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">分享內容</label>
              <Textarea
                value={event.content_shared}
                onChange={(e) => updateEvent(index, "content_shared", e.target.value)}
                placeholder="請描述分享的主要內容"
                rows={3}
                disabled={disabled}
              />
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">分享方式</label>
              <Textarea
                value={event.methods_used}
                onChange={(e) => updateEvent(index, "methods_used", e.target.value)}
                placeholder="例：簡報、影片、互動體驗、實作展示等"
                rows={2}
                disabled={disabled}
              />
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">回饋反應</label>
              <Textarea
                value={event.feedback_received}
                onChange={(e) => updateEvent(index, "feedback_received", e.target.value)}
                placeholder="請描述參與者的回饋或反應"
                rows={2}
                disabled={disabled}
              />
            </div>
          </CardContent>
        </Card>
      ))}

      {!disabled && (
        <Button variant="outline" onClick={addEvent} className="w-full border-dashed">
          <Plus className="w-4 h-4 mr-2" />
          新增分享活動
        </Button>
      )}
    </div>
  );
};

// 監控指標表單（已有但改進）
export interface MonitoringIndicator {
  indicator_name: string;
  measurement_method?: string;
  assessment_method?: string;
  target_value?: string;
  success_criteria?: string;
  monitoring_frequency: string;
  current_value?: string;
  achievement_rate?: number;
}

interface DynamicIndicatorsProps {
  data: MonitoringIndicator[];
  onChange: (data: MonitoringIndicator[]) => void;
  type: "quantitative" | "qualitative";
  disabled?: boolean;
}

export const DynamicIndicators: React.FC<DynamicIndicatorsProps> = ({ data, onChange, type, disabled = false }) => {
  const addIndicator = () => {
    const newIndicator: MonitoringIndicator = {
      indicator_name: "",
      monitoring_frequency: "monthly",
      current_value: "",
      achievement_rate: 0,
    };

    if (type === "quantitative") {
      newIndicator.measurement_method = "";
      newIndicator.target_value = "";
    } else {
      newIndicator.assessment_method = "";
      newIndicator.success_criteria = "";
    }

    onChange([...data, newIndicator]);
  };

  const updateIndicator = (index: number, field: keyof MonitoringIndicator, value: string | number) => {
    const updatedIndicators = data.map((indicator, i) => (i === index ? { ...indicator, [field]: value } : indicator));
    onChange(updatedIndicators);
  };

  const removeIndicator = (index: number) => {
    // 允許刪除，但至少保留一個空項目
    const updatedIndicators = data.filter((_, i) => i !== index);
    if (updatedIndicators.length === 0) {
      const newIndicator: MonitoringIndicator = {
        indicator_name: "",
        monitoring_frequency: "monthly",
        current_value: "",
        achievement_rate: 0,
      };

      if (type === "quantitative") {
        newIndicator.measurement_method = "";
        newIndicator.target_value = "";
      } else {
        newIndicator.assessment_method = "";
        newIndicator.success_criteria = "";
      }

      onChange([newIndicator]);
    } else {
      onChange(updatedIndicators);
    }
  };

  const frequencyOptions = [
    { value: "daily", label: "每日" },
    { value: "weekly", label: "每週" },
    { value: "monthly", label: "每月" },
    { value: "quarterly", label: "每季" },
    { value: "annually", label: "每年" },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium text-gray-700">{type === "quantitative" ? "量化" : "質化"}監控指標</h4>
        <Badge variant="outline">{type === "quantitative" ? "可量化數據" : "質性評估"}</Badge>
      </div>

      {data.map((indicator, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="font-size-base">指標 {Number.isInteger(index) ? index + 1 : 1}</CardTitle>
              {!disabled && (
                <Button variant="outline" size="sm" onClick={() => removeIndicator(index)} className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">
                指標名稱 <span className="text-red-500">*</span>
              </label>
              <Input
                value={indicator.indicator_name}
                onChange={(e) => updateIndicator(index, "indicator_name", e.target.value)}
                placeholder="請輸入指標名稱"
                disabled={disabled}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">{type === "quantitative" ? "測量方法" : "評估方法"}</label>
                <Textarea
                  value={type === "quantitative" ? indicator.measurement_method : indicator.assessment_method}
                  onChange={(e) => updateIndicator(index, type === "quantitative" ? "measurement_method" : "assessment_method", e.target.value)}
                  placeholder={type === "quantitative" ? "請描述如何測量此指標" : "請描述如何評估此指標"}
                  rows={3}
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">{type === "quantitative" ? "目標數值" : "成功標準"}</label>
                <Textarea
                  value={type === "quantitative" ? indicator.target_value : indicator.success_criteria}
                  onChange={(e) => updateIndicator(index, type === "quantitative" ? "target_value" : "success_criteria", e.target.value)}
                  placeholder={type === "quantitative" ? "請設定目標數值" : "請描述成功標準"}
                  rows={3}
                  disabled={disabled}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">監控頻率</label>
                <select
                  value={indicator.monitoring_frequency}
                  onChange={(e) => updateIndicator(index, "monitoring_frequency", e.target.value)}
                  disabled={disabled}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  {frequencyOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">目前數值/狀況</label>
                <Input
                  value={indicator.current_value}
                  onChange={(e) => updateIndicator(index, "current_value", e.target.value)}
                  placeholder="請輸入目前測量值或狀況"
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">達成率 (%)</label>
                <Input
                  type="number"
                  value={indicator.achievement_rate}
                  onChange={(e) => updateIndicator(index, "achievement_rate", parseInt(e.target.value) || 0)}
                  placeholder="0"
                  min="0"
                  max="100"
                  disabled={disabled}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {!disabled && (
        <Button variant="outline" onClick={addIndicator} className="w-full border-dashed">
          <Plus className="w-4 h-4 mr-2" />
          新增{type === "quantitative" ? "量化" : "質化"}指標
        </Button>
      )}
    </div>
  );
};

// 教學活動表單
export interface TeachingActivity {
  activity_name: string;
  subject_area: string;
  grade_level: string;
  duration: string;
  objectives: string;
  methods: string;
  assessment: string;
}

interface DynamicActivitiesProps {
  data: TeachingActivity[];
  onChange: (data: TeachingActivity[]) => void;
  disabled?: boolean;
}

export const DynamicActivities: React.FC<DynamicActivitiesProps> = ({ data, onChange, disabled = false }) => {
  const subjectAreas = [
    { value: "chinese", label: "國語文" },
    { value: "english", label: "英語文" },
    { value: "math", label: "數學" },
    { value: "social", label: "社會" },
    { value: "science", label: "自然科學" },
    { value: "arts", label: "藝術" },
    { value: "pe_health", label: "健康與體育" },
    { value: "technology", label: "科技" },
    { value: "comprehensive", label: "綜合活動" },
  ];

  const gradeLevels = [
    { value: "grade1", label: "一年級" },
    { value: "grade2", label: "二年級" },
    { value: "grade3", label: "三年級" },
    { value: "grade4", label: "四年級" },
    { value: "grade5", label: "五年級" },
    { value: "grade6", label: "六年級" },
  ];

  const addActivity = () => {
    const newActivity: TeachingActivity = {
      activity_name: "",
      subject_area: "",
      grade_level: "",
      duration: "",
      objectives: "",
      methods: "",
      assessment: "",
    };
    onChange([...data, newActivity]);
  };

  const updateActivity = (index: number, field: keyof TeachingActivity, value: string) => {
    const updatedActivities = data.map((activity, i) => (i === index ? { ...activity, [field]: value } : activity));
    onChange(updatedActivities);
  };

  const removeActivity = (index: number) => {
    // 允許刪除，但至少保留一個空項目
    const updatedActivities = data.filter((_, i) => i !== index);
    if (updatedActivities.length === 0) {
      onChange([
        {
          activity_name: "",
          subject_area: "",
          grade_level: "",
          duration: "",
          objectives: "",
          methods: "",
          assessment: "",
        },
      ]);
    } else {
      onChange(updatedActivities);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium text-gray-700">教學活動設計</h4>
      </div>

      {data.map((activity, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="font-size-base">教學活動 {Number.isInteger(index) ? index + 1 : 1}</CardTitle>
              {!disabled && (
                <Button variant="outline" size="sm" onClick={() => removeActivity(index)} className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">
                活動名稱 <span className="text-red-500">*</span>
              </label>
              <Input
                value={activity.activity_name}
                onChange={(e) => updateActivity(index, "activity_name", e.target.value)}
                placeholder="請輸入教學活動名稱"
                disabled={disabled}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">學習領域</label>
                <select
                  value={activity.subject_area}
                  onChange={(e) => updateActivity(index, "subject_area", e.target.value)}
                  disabled={disabled}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">請選擇學習領域</option>
                  {subjectAreas.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">年級</label>
                <select
                  value={activity.grade_level}
                  onChange={(e) => updateActivity(index, "grade_level", e.target.value)}
                  disabled={disabled}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">請選擇年級</option>
                  {gradeLevels.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">活動時間</label>
                <Input
                  value={activity.duration}
                  onChange={(e) => updateActivity(index, "duration", e.target.value)}
                  placeholder="例：2節課（80分鐘）"
                  disabled={disabled}
                />
              </div>
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">學習目標</label>
              <Textarea
                value={activity.objectives}
                onChange={(e) => updateActivity(index, "objectives", e.target.value)}
                placeholder="請描述此教學活動的學習目標"
                rows={3}
                disabled={disabled}
              />
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">教學方法</label>
              <Textarea
                value={activity.methods}
                onChange={(e) => updateActivity(index, "methods", e.target.value)}
                placeholder="請描述教學方法和活動流程"
                rows={3}
                disabled={disabled}
              />
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">評量方式</label>
              <Textarea
                value={activity.assessment}
                onChange={(e) => updateActivity(index, "assessment", e.target.value)}
                placeholder="請描述如何評量學生學習成效"
                rows={2}
                disabled={disabled}
              />
            </div>
          </CardContent>
        </Card>
      ))}

      {!disabled && (
        <Button variant="outline" onClick={addActivity} className="w-full border-dashed">
          <Plus className="w-4 h-4 mr-2" />
          新增教學活動
        </Button>
      )}
    </div>
  );
};

// 社區活動事件表單
export interface CommunityEvent {
  event_name: string;
  event_date: string;
  participants: string;
  description: string;
  outcomes: string;
}

interface DynamicEventsProps {
  data: CommunityEvent[];
  onChange: (data: CommunityEvent[]) => void;
  disabled?: boolean;
}

export const DynamicEvents: React.FC<DynamicEventsProps> = ({ data, onChange, disabled = false }) => {
  const addEvent = () => {
    const newEvent: CommunityEvent = {
      event_name: "",
      event_date: "",
      participants: "",
      description: "",
      outcomes: "",
    };
    onChange([...data, newEvent]);
  };

  const updateEvent = (index: number, field: keyof CommunityEvent, value: string) => {
    const updatedEvents = data.map((event, i) => (i === index ? { ...event, [field]: value } : event));
    onChange(updatedEvents);
  };

  const removeEvent = (index: number) => {
    // 允許刪除，但至少保留一個空項目
    const updatedEvents = data.filter((_, i) => i !== index);
    if (updatedEvents.length === 0) {
      onChange([
        {
          event_name: "",
          event_date: "",
          participants: "",
          description: "",
          outcomes: "",
        },
      ]);
    } else {
      onChange(updatedEvents);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium text-gray-700">社區參與活動</h4>
      </div>

      {data.map((event, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="font-size-base">活動 {Number.isInteger(index) ? index + 1 : 1}</CardTitle>
              {!disabled && (
                <Button variant="outline" size="sm" onClick={() => removeEvent(index)} className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">
                  活動名稱 <span className="text-red-500">*</span>
                </label>
                <Input
                  value={event.event_name}
                  onChange={(e) => updateEvent(index, "event_name", e.target.value)}
                  placeholder="請輸入活動名稱"
                  disabled={disabled}
                />
              </div>
              <div>
                <label className="block font-size-sm font-medium text-gray-700 mb-2">活動日期</label>
                <Input type="date" value={event.event_date} onChange={(e) => updateEvent(index, "event_date", e.target.value)} disabled={disabled} />
              </div>
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">參與對象</label>
              <Input
                value={event.participants}
                onChange={(e) => updateEvent(index, "participants", e.target.value)}
                placeholder="例：學生、教師、家長、社區居民"
                disabled={disabled}
              />
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">活動描述</label>
              <Textarea
                value={event.description}
                onChange={(e) => updateEvent(index, "description", e.target.value)}
                placeholder="請描述活動內容和執行方式"
                rows={3}
                disabled={disabled}
              />
            </div>

            <div>
              <label className="block font-size-sm font-medium text-gray-700 mb-2">活動成果</label>
              <Textarea
                value={event.outcomes}
                onChange={(e) => updateEvent(index, "outcomes", e.target.value)}
                placeholder="請描述活動成果和影響"
                rows={2}
                disabled={disabled}
              />
            </div>
          </CardContent>
        </Card>
      ))}

      {!disabled && (
        <Button variant="outline" onClick={addEvent} className="w-full border-dashed">
          <Plus className="w-4 h-4 mr-2" />
          新增社區活動
        </Button>
      )}
    </div>
  );
};
