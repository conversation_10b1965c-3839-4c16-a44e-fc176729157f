// ========== 用戶服務 - 業務邏輯和資料庫查詢 ==========

import {
  executeQuery,
  executeQuerySingle,
  executeUpdate,
  executeInsert,
  beginTransaction,
  commitTransaction,
  rollbackTransaction,
} from "../config/database-mssql.js";
import { Transaction } from "mssql";
import {
  UserProfile,
  SchoolInfo,
  CertificationInfo,
  UserProfileUpdateRequest,
  PermissionInfo,
  PermissionGroupInfo,
  UserPermissions,
  AccountQueryResult,
  SchoolQueryResult,
  CertificationQueryResult,
  PermissionQueryResult,
  PermissionGroupQueryResult,
  AccountUpdateFields,
  SchoolUpdateFields,
} from "../models/user.js";
import {
  ROLE_TYPES,
  USER_STATUS,
  CERTIFICATION_STATUS,
  REVIEW_STATUS,
  DATA_STATUS,
  PERMISSION_FLAGS,
  USER_ERROR_MESSAGES,
  USER_SUCCESS_MESSAGES,
  SQL_QUERIES,
  isValidEmail,
  isValid<PERSON><PERSON>,
  hasPermission,
  isSchool<PERSON>ole,
  isAdminRole,
  getAllowedAccountFields,
  getAllowedSchoolFields,
  mapCertificationLevelToType,
  mapReviewStatusToCertificationStatus,
  canAccessUserData,
  validateUpdateData,
} from "../constants/user.js";
import { determineRoleTypeFromIntegerFields, mapRoleTypeToFrontendRole } from "../constants/auth.js";
import { LOCALE_CODE, DASHBOARD_STATUS } from "../constants/dashboard.js";

// 權限相關介面
interface PermissionRow {
  sid: string;
  cname: string;
  ename: string;
  description?: string;
  isuse: number;
  dataStatus: number;
}

interface PermissionGroupRow {
  sid: string;
  cname: string;
  ename: string;
  description?: string;
  isuse: number;
  dataStatus: number;
}

export class UserService {
  // 根據 Token 獲取使用者資料
  static async getUserByToken(token: string): Promise<UserProfile | null> {
    try {
      console.log("查詢使用者 Token:", token);

      // 查詢 UserToken 表以驗證令牌，並從 Accounts 表取得角色整數欄位
      const tokenQuery = `
        SELECT 
          ut.Id as TokenId,
          ut.AccountSid,
          ut.Token,
          ut.ExpireDate,
          ut.Status as TokenStatus,
          ut.TokenType,
          ut.CreateDate as TokenCreateDate,
          a.AccountId,
          a.Username as account,
          a.email,
          a.phone,
          a.Status as AccountStatus,
          a.IsSchoolPartner,
          a.IsEpaUser,
          a.IsGuidanceTeam,
          mp.MemberName as member_cname,
          mp.MemberEmail as member_email,
          mp.MemberPhone as member_phone,
          mp.MemberPhotoFileId as member_photo,
          mp.MemberIntroduction as member_Introduction
        FROM UserToken ut
        INNER JOIN Accounts a ON ut.AccountSid = a.AccountId
        LEFT JOIN MemberProfiles mp ON mp.AccountId = a.AccountId AND mp.LocaleCode = 'zh-TW'
        WHERE ut.Token = CAST(@token AS uniqueidentifier)
          AND ut.Status = 1 
          AND a.Status = 1
          AND (ut.ExpireDate IS NULL OR ut.ExpireDate > GETDATE())
      `;

      const tokenResult = await executeQuerySingle<{
        TokenId: string;
        AccountSid: number;
        AccountId: number;
        account: string;
        email?: string;
        phone?: string;
        AccountStatus: number;
        IsSchoolPartner?: number;
        IsEpaUser?: number;
        IsGuidanceTeam?: number;
        TokenCreateDate?: string;
        member_cname?: string;
        member_email?: string;
        member_phone?: string;
        member_photo?: string;
        member_Introduction?: string;
      }>(tokenQuery, { token });

      if (!tokenResult) {
        return null;
      }

      // 根據整數欄位判斷角色類型
      const roleType = determineRoleTypeFromIntegerFields(tokenResult.IsSchoolPartner, tokenResult.IsEpaUser, tokenResult.IsGuidanceTeam);

      const frontendRole = mapRoleTypeToFrontendRole(roleType);

      // 組合會員資料（使用 Accounts 表的角色整數欄位）
      const memberProfile: UserProfile = {
        id: tokenResult.AccountId?.toString() || tokenResult.AccountSid?.toString(),
        account: tokenResult.account || "",
        nickName: tokenResult.member_cname || "Unknown User",
        email: tokenResult.email || tokenResult.member_email || "",
        phone: tokenResult.phone || tokenResult.member_phone || "",
        avatar: tokenResult.member_photo || "",
        roleType: roleType,
        isActive: (tokenResult.AccountStatus || 1) === 1,
        createdTime: new Date(tokenResult.TokenCreateDate || Date.now()),
        updatedTime: new Date(tokenResult.TokenCreateDate || Date.now()),
        remark: tokenResult.member_Introduction || "",
        permissions: ["read", "write"], // 暫時固定權限，後續可以實現動態查詢
        permissionGroups: [frontendRole],
        school: undefined, // 暫時不查詢學校資料，後續可以實現
        certifications: [], // 暫時不查詢認證資料，後續可以實現
      };

      return memberProfile;
    } catch (error) {
      console.error("查詢使用者資料失敗:", error);
      return null;
    }
  }

  // 基於 TokenType 的混合身份映射
  static async getUserByTokenWithRoleMapping(token: string): Promise<UserProfile | null> {
    try {
      // 查詢 UserToken、Accounts 和 MemberProfiles，使用 Accounts 表的角色布林欄位
      const tokenQuery = `
        SELECT 
          ut.Id as TokenId,
          ut.AccountSid,
          ut.Token,
          ut.ExpireDate,
          ut.Status as TokenStatus,
          ut.TokenType,
          ut.CreateDate as TokenCreateDate,
          a.AccountId,
          a.Username,
          a.email,
          a.phone,
          a.Status as AccountStatus,
          a.IsSchoolPartner,
          a.IsEpaUser,
          a.IsGuidanceTeam,
          mp.MemberName,
          mp.MemberEmail,
          mp.MemberPhone,
          mp.MemberPhotoFileId,
          mp.MemberIntroduction
        FROM UserToken ut
        INNER JOIN Accounts a ON ut.AccountSid = a.AccountId
        LEFT JOIN MemberProfiles mp ON mp.AccountId = a.AccountId AND mp.LocaleCode = 'zh-TW'
        WHERE ut.Token = CAST(@token AS uniqueidentifier)
          AND ut.Status = 1 
          AND a.Status = 1
          AND (ut.ExpireDate IS NULL OR ut.ExpireDate > GETDATE())
      `;

      const tokenResult = await executeQuerySingle<{
        TokenId: string;
        AccountSid: number;
        AccountId: number;
        Username: string;
        email?: string;
        phone?: string;
        AccountStatus: number;
        IsSchoolPartner?: number;
        IsEpaUser?: number;
        IsGuidanceTeam?: number;
        TokenCreateDate?: string;
        MemberName?: string;
        MemberEmail?: string;
        MemberPhone?: string;
        MemberPhotoFileId?: string;
        MemberIntroduction?: string;
        TokenType?: string;
      }>(tokenQuery, { token });

      if (!tokenResult) {
        return null;
      }

      // 根據 Accounts 表的整數欄位判斷資料庫中的角色
      const databaseRoleType = determineRoleTypeFromIntegerFields(tokenResult.IsSchoolPartner, tokenResult.IsEpaUser, tokenResult.IsGuidanceTeam);
      const databaseFrontendRole = mapRoleTypeToFrontendRole(databaseRoleType);

      // 💡 混合身份映射策略：
      // 1. 如果是身份專用 Token (xxx_identity)，根據 TokenType 映射身份
      // 2. 否則使用資料庫中從布林欄位判斷的角色

      let finalRoleType: string;
      let finalFrontendRole: string;
      let displayName: string;
      let description: string;

      const isIdentityToken = tokenResult.TokenType?.endsWith("_identity");

      if (isIdentityToken) {
        // 🎭 身份 Token：根據 TokenType 動態映射身份
        switch (tokenResult.TokenType?.toLowerCase()) {
          case "school_identity":
            finalRoleType = "School";
            finalFrontendRole = "school";
            displayName = "學校代表身份";
            description = "學校身份 Token";
            break;
          case "epa_identity":
            finalRoleType = "Government";
            finalFrontendRole = "epa";
            displayName = "環保署人員身份";
            description = "環保署身份 Token";
            break;
          case "tutor_identity":
            finalRoleType = "Tutor";
            finalFrontendRole = "tutor";
            displayName = "輔導員身份";
            description = "輔導員身份 Token";
            break;
          default:
            finalRoleType = databaseRoleType;
            finalFrontendRole = databaseFrontendRole;
            displayName = tokenResult.MemberName || tokenResult.Username || "Unknown User";
            description = tokenResult.MemberIntroduction || "";
        }
      } else {
        // 📋 一般 Token：使用資料庫從布林欄位判斷的身份
        finalRoleType = databaseRoleType;
        finalFrontendRole = databaseFrontendRole;
        displayName = tokenResult.MemberName || "Unknown User";
        description = tokenResult.MemberIntroduction || "";
      }

      // 組合會員資料
      const memberProfile: UserProfile = {
        id: tokenResult.AccountId?.toString() || tokenResult.AccountSid?.toString(),
        account: tokenResult.Username || "",
        nickName: tokenResult.MemberName || "Unknown User",
        email: tokenResult.email || tokenResult.MemberEmail || "",
        phone: tokenResult.phone || tokenResult.MemberPhone || "",
        avatar: tokenResult.MemberPhotoFileId || "",
        roleType: databaseRoleType,
        isActive: (tokenResult.AccountStatus || 1) === 1,
        createdTime: new Date(tokenResult.TokenCreateDate || Date.now()),
        updatedTime: new Date(tokenResult.TokenCreateDate || Date.now()),
        remark: tokenResult.MemberIntroduction || "",
        permissions: ["read", "write"], // 暫時固定權限，後續可以實現動態查詢
        permissionGroups: [databaseFrontendRole],
        school: undefined, // 暫時不查詢學校資料，後續可以實現
        certifications: [], // 暫時不查詢認證資料，後續可以實現
      };

      return memberProfile;
    } catch (error) {
      console.error("查詢使用者資料失敗:", error);
      return null;
    }
  }

  // 創建新令牌
  static async createUserToken(userId: string, tokenType: string = "Login", expiredDays: number = 30): Promise<string> {
    try {
      // 生成 GUID 格式令牌 (讓 SQL Server 自動生成)
      const newGuidQuery = "SELECT NEWID() as newToken";
      const guidResult = await executeQuerySingle<{ newToken: string }>(newGuidQuery);
      const token = guidResult?.newToken || "********-0000-0000-0000-************";

      // 計算過期時間
      const expiredTime = new Date();
      expiredTime.setDate(expiredTime.getDate() + expiredDays);

      // 插入令牌記錄 (修正為實際表格結構 - 使用正確的 AccountSid 欄位)
      const insertQuery = `
        INSERT INTO UserToken (AccountSid, Token, TokenType, ExpireDate, Status, CreateDate)
        VALUES (@userId, @token, @tokenType, @expiredTime, 1, GETDATE())
      `;

      await executeInsert(insertQuery, {
        userId,
        token,
        tokenType,
        expiredTime,
      });

      return token;
    } catch (error) {
      console.error("創建令牌失敗:", error);
      throw error;
    }
  }

  // 撤銷令牌
  static async revokeUserToken(token: string): Promise<boolean> {
    try {
      const updateQuery = `
        UPDATE UserToken 
        SET Status = 0
        WHERE Token = CAST(@token AS uniqueidentifier) AND Status = 1
      `;

      const result = await executeUpdate(updateQuery, { token });
      return result > 0;
    } catch (error) {
      console.error("撤銷令牌失敗:", error);
      return false;
    }
  }

  // 權限管理方法

  // 獲取所有權限
  static async getAllPermissions(): Promise<PermissionRow[]> {
    try {
      const query = `
        SELECT * FROM permission 
        WHERE isuse = 1 AND dataStatus = 0
        ORDER BY cname
      `;
      return await executeQuery<PermissionRow>(query);
    } catch (error) {
      console.error("獲取權限列表失敗:", error);
      return [];
    }
  }

  // 獲取所有權限群組
  static async getAllPermissionGroups(): Promise<PermissionGroupRow[]> {
    try {
      const query = `
        SELECT * FROM permission_group 
        WHERE isuse = 1 AND dataStatus = 0
        ORDER BY cname
      `;
      return await executeQuery<PermissionGroupRow>(query);
    } catch (error) {
      console.error("獲取權限群組列表失敗:", error);
      return [];
    }
  }

  // 獲取權限群組的權限
  static async getPermissionGroupPermissions(groupSid: string): Promise<PermissionRow[]> {
    try {
      const query = `
        SELECT p.* 
        FROM permission p
        INNER JOIN permission_group_map pgm ON p.sid = pgm.permissionSid
        WHERE pgm.groupSid = @groupSid
          AND pgm.dataStatus = 0
          AND p.isuse = 1
          AND p.dataStatus = 0
        ORDER BY p.cname
      `;
      return await executeQuery<PermissionRow>(query, { groupSid });
    } catch (error) {
      console.error("獲取權限群組權限失敗:", error);
      return [];
    }
  }

  // 檢查使用者是否有特定權限
  static async checkUserPermission(accountId: string, permissionCode: string): Promise<boolean> {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM account_permission_group apg
        INNER JOIN permission_group pg ON apg.groupSid = pg.sid
        INNER JOIN permission_group_map pgm ON pg.sid = pgm.groupSid
        INNER JOIN permission p ON pgm.permissionSid = p.sid
        WHERE apg.accountSid = @accountId 
          AND p.ename = @permissionCode
          AND apg.dataStatus = 0
          AND pg.isuse = 1
          AND pgm.dataStatus = 0
          AND p.isuse = 1
      `;

      const result = await executeQuerySingle<{ count: number }>(query, {
        accountId,
        permissionCode,
      });
      return (result?.count || 0) > 0;
    } catch (error) {
      console.error("檢查使用者權限失敗:", error);
      return false;
    }
  }

  // 為使用者分配權限群組
  static async assignUserPermissionGroup(accountId: string, groupSid: string, assignedBy: string): Promise<boolean> {
    try {
      // 檢查是否已經存在
      const existQuery = `
        SELECT COUNT(*) as count 
        FROM account_permission_group 
        WHERE accountSid = @accountId AND groupSid = @groupSid AND dataStatus = 0
      `;

      const existResult = await executeQuerySingle<{ count: number }>(existQuery, { accountId, groupSid });

      if ((existResult?.count || 0) > 0) {
        return true; // 已經存在
      }

      // 插入新的權限群組關聯
      const insertQuery = `
        INSERT INTO account_permission_group (accountSid, groupSid, createTime, createUser, updateTime, updateUser, dataStatus)
        VALUES (@accountId, @groupSid, GETDATE(), @assignedBy, GETDATE(), @assignedBy, 0)
      `;

      const result = await executeInsert(insertQuery, {
        accountId,
        groupSid,
        assignedBy,
      });
      return result > 0;
    } catch (error) {
      console.error("分配權限群組失敗:", error);
      return false;
    }
  }

  // 撤銷使用者權限群組
  static async revokeUserPermissionGroup(accountId: string, groupSid: string, revokedBy: string): Promise<boolean> {
    try {
      const updateQuery = `
        UPDATE account_permission_group 
        SET dataStatus = 1, 
            deleteTime = GETDATE(), 
            deleteUser = @revokedBy,
            updateTime = GETDATE(),
            updateUser = @revokedBy
        WHERE accountSid = @accountId AND groupSid = @groupSid AND dataStatus = 0
      `;

      const result = await executeUpdate(updateQuery, {
        accountId,
        groupSid,
        revokedBy,
      });
      return result > 0;
    } catch (error) {
      console.error("撤銷權限群組失敗:", error);
      return false;
    }
  }

  // 獲取當前使用者基本資料
  static async getCurrentUserProfile(token: string): Promise<UserProfile> {
    try {
      // 從資料庫獲取最新的使用者資料
      const memberProfile = await this.getUserByToken(token);

      if (!memberProfile) {
        throw new Error(USER_ERROR_MESSAGES.USER_NOT_FOUND);
      }

      return memberProfile;
    } catch (error) {
      console.error("❌ [UserService] 獲取當前使用者資料失敗:", error);
      throw error;
    }
  }

  // 根據ID獲取使用者資料
  static async getUserProfileById(userId: string, currentUserId: string, currentUserPermissions: string[]): Promise<UserProfile> {
    try {
      // 檢查權限
      if (!canAccessUserData(currentUserId, userId, currentUserPermissions)) {
        throw new Error(USER_ERROR_MESSAGES.NO_PERMISSION);
      }

      // 查詢指定使用者的完整資料
      const account = await executeQuerySingle<AccountQueryResult>(SQL_QUERIES.GET_ACCOUNT, { userId });

      if (!account) {
        throw new Error(USER_ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // 查詢權限資訊
      const [permissions, permissionGroups] = await Promise.all([this.getUserPermissions(userId), this.getUserPermissionGroups(userId)]);

      // 如果是學校身份，查詢學校資訊
      let school: SchoolInfo | undefined;
      if (account.RoleType === ROLE_TYPES.SCHOOL) {
        school = await this.getSchoolInfoByAccountId(userId);
      }

      // 查詢認證資訊
      const certifications = await this.getUserCertifications(userId);

      // 組合會員資料
      const memberProfile: UserProfile = {
        id: account.Id,
        account: account.Account,
        nickName: account.NickName,
        email: account.Email,
        phone: account.Phone,
        avatar: account.Avatar,
        roleType: account.RoleType,
        isActive: account.IsActive,
        createdTime: account.CreatedTime,
        updatedTime: account.UpdatedTime,
        remark: account.Remark,
        permissions: permissions.map((p) => p.code),
        permissionGroups: permissionGroups.map((pg) => pg.code),
        school,
        certifications,
      };

      return memberProfile;
    } catch (error) {
      console.error("❌ [UserService] 獲取使用者資料失敗:", error);
      throw error;
    }
  }

  // 更新使用者基本資料
  static async updateUserProfile(userId: string, updateData: UserProfileUpdateRequest, token: string): Promise<UserProfile> {
    try {
      // 檢查帳號是否存在
      const account = await executeQuerySingle<AccountQueryResult>(SQL_QUERIES.GET_ACCOUNT, { userId });

      if (!account) {
        throw new Error(USER_ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // 驗證更新資料
      const validation = validateUpdateData(updateData);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // 根據身份類型定義允許更新的欄位
      const allowedAccountFields = getAllowedAccountFields(account.RoleType);
      const allowedSchoolFields = getAllowedSchoolFields();

      // 準備帳號更新欄位
      const accountUpdateFields = this.prepareAccountUpdateFields(updateData, allowedAccountFields);
      const hasValidAccountFields = Object.keys(accountUpdateFields).length > 0;

      // 開始事務
      const transaction = await beginTransaction();

      try {
        // 更新 Account 表
        if (hasValidAccountFields) {
          await this.updateAccountFields(transaction, userId, accountUpdateFields);
        }

        // 如果是學校身份且提供了學校資料，更新 School 表
        if (account.RoleType === ROLE_TYPES.SCHOOL && updateData.school) {
          await this.updateSchoolFields(transaction, userId, updateData.school, allowedSchoolFields);
        }

        await commitTransaction(transaction);

        // 重新獲取更新後的資料
        const updatedProfile = await this.getUserByToken(token);
        if (!updatedProfile) {
          throw new Error(USER_ERROR_MESSAGES.USER_NOT_FOUND);
        }

        return updatedProfile;
      } catch (error) {
        await rollbackTransaction(transaction);
        throw error;
      }
    } catch (error) {
      console.error("❌ [UserService] 更新使用者資料失敗:", error);
      throw error;
    }
  }

  // 獲取使用者認證資訊
  static async getUserCertificationsOnly(userId: string): Promise<CertificationQueryResult[]> {
    try {
      const certifications = await executeQuery<CertificationQueryResult>(SQL_QUERIES.GET_CERTIFICATIONS, { userId });
      return certifications;
    } catch (error) {
      console.error("❌ [UserService] 獲取認證資料失敗:", error);
      throw error;
    }
  }

  // 獲取學校資訊（僅學校身份）
  static async getSchoolInfo(userId: string): Promise<SchoolQueryResult> {
    try {
      // 首先檢查使用者身份
      const account = await executeQuerySingle<AccountQueryResult>(SQL_QUERIES.GET_ACCOUNT, { userId });

      if (!account || account.RoleType !== ROLE_TYPES.SCHOOL) {
        throw new Error(USER_ERROR_MESSAGES.SCHOOL_ONLY);
      }

      // 查詢學校資訊
      const school = await executeQuerySingle<SchoolQueryResult>(SQL_QUERIES.GET_SCHOOL_BY_ACCOUNT, {
        accountId: userId,
      });

      if (!school) {
        throw new Error(USER_ERROR_MESSAGES.SCHOOL_NOT_FOUND);
      }

      return school;
    } catch (error) {
      console.error("❌ [UserService] 獲取學校資料失敗:", error);
      throw error;
    }
  }

  // 獲取使用者權限資訊
  static async getUserPermissionsInfo(userId: string): Promise<UserPermissions> {
    try {
      const [permissions, permissionGroups] = await Promise.all([
        executeQuery<PermissionQueryResult>(SQL_QUERIES.GET_PERMISSIONS, { accountId: userId }),
        executeQuery<PermissionGroupQueryResult>(SQL_QUERIES.GET_PERMISSION_GROUPS, { accountId: userId }),
      ]);

      return {
        permissions: permissions.map((p) => ({
          code: p.Code,
          name: p.Name,
          description: p.Description,
        })),
        permissionGroups: permissionGroups.map((pg) => ({
          code: pg.Code,
          name: pg.Name,
          description: pg.Description,
        })),
      };
    } catch (error) {
      console.error("❌ [UserService] 獲取權限資料失敗:", error);
      throw error;
    }
  }

  // 根據 AccountId 獲取使用者資料
  static async getUserByAccountId(accountId: string): Promise<UserProfile> {
    try {
      const accountQuery = `
        SELECT 
          a.AccountId,
          a.Username as account,
          a.email,
          a.phone,
          a.Status as AccountStatus,
          a.IsSchoolPartner,
          a.IsEpaUser,
          a.IsGuidanceTeam,
          mp.MemberName as member_cname,
          mp.MemberEmail as member_email,
          mp.MemberPhone as member_phone,
          mp.MemberPhotoFileId as member_photo,
          mp.MemberIntroduction as member_Introduction
        FROM Accounts a
        LEFT JOIN MemberProfiles mp ON mp.AccountId = a.AccountId AND mp.LocaleCode = 'zh-TW'
        WHERE a.AccountId = @accountId AND a.Status = 1
      `;

      const accountResult = await executeQuerySingle<{
        AccountId: number;
        account: string;
        email?: string;
        phone?: string;
        AccountStatus: number;
        IsSchoolPartner?: number;
        IsEpaUser?: number;
        IsGuidanceTeam?: number;
        member_cname?: string;
        member_email?: string;
        member_phone?: string;
        member_photo?: string;
        member_Introduction?: string;
      }>(accountQuery, { accountId });

      if (!accountResult) {
        throw new Error(USER_ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // 根據整數欄位判斷角色類型
      const roleType = determineRoleTypeFromIntegerFields(accountResult.IsSchoolPartner, accountResult.IsEpaUser, accountResult.IsGuidanceTeam);
      const frontendRole = mapRoleTypeToFrontendRole(roleType);

      const userProfile: UserProfile = {
        id: accountResult.AccountId.toString(),
        account: accountResult.account || "",
        nickName: accountResult.member_cname || "Unknown User",
        email: accountResult.email || accountResult.member_email || "",
        phone: accountResult.phone || accountResult.member_phone || "",
        avatar: accountResult.member_photo || "",
        roleType: roleType,
        isActive: (accountResult.AccountStatus || 1) === 1,
        createdTime: new Date(),
        updatedTime: new Date(),
        remark: accountResult.member_Introduction || "",
        permissions: ["read", "write"],
        permissionGroups: [frontendRole],
        school: undefined,
        certifications: [],
      };

      return userProfile;
    } catch (error) {
      console.error("❌ [UserService] 根據 AccountId 獲取使用者資料失敗:", error);
      throw error;
    }
  }

  // ========== 私有輔助方法 ==========

  // 獲取使用者權限
  private static async getUserPermissions(userId: string): Promise<PermissionInfo[]> {
    const permissions = await executeQuery<PermissionQueryResult>(SQL_QUERIES.GET_PERMISSIONS, {
      accountId: userId,
    });

    return permissions.map((p) => ({
      code: p.Code,
      name: p.Name,
      description: p.Description,
    }));
  }

  // 獲取使用者權限群組
  private static async getUserPermissionGroups(userId: string): Promise<PermissionGroupInfo[]> {
    const permissionGroups = await executeQuery<PermissionGroupQueryResult>(SQL_QUERIES.GET_PERMISSION_GROUPS, {
      accountId: userId,
    });

    return permissionGroups.map((pg) => ({
      code: pg.Code,
      name: pg.Name,
      description: pg.Description,
    }));
  }

  // 根據帳號ID獲取學校資訊
  private static async getSchoolInfoByAccountId(accountId: string): Promise<SchoolInfo | undefined> {
    const school = await executeQuerySingle<SchoolQueryResult>(SQL_QUERIES.GET_SCHOOL_BY_ACCOUNT, { accountId });

    if (!school) return undefined;

    return {
      id: school.Id,
      name: school.Name,
      englishName: school.EnglishName,
      code: school.Code,
      address: school.Address,
      phone: school.Phone,
      email: school.Email,
      website: school.Website,
      contactPerson: school.ContactPerson,
      contactPhone: school.ContactPhone,
      contactEmail: school.ContactEmail,
    };
  }

  // 獲取使用者認證資訊
  private static async getUserCertifications(userId: string): Promise<CertificationInfo[]> {
    const certifications = await executeQuery<CertificationQueryResult>(SQL_QUERIES.GET_CERTIFICATIONS, { userId });

    return certifications.map((cert) => {
      const level = cert.Level || 2;
      return {
        id: cert.Id.toString(),
        certificationType: mapCertificationLevelToType(level),
        level: level,
        status: mapReviewStatusToCertificationStatus(cert.ReviewStatus || REVIEW_STATUS.NOT_SUBMITTED),
        applyDate: cert.CreatedTime || new Date(),
        reviewDate: cert.ReviewDate,
        passDate: cert.ApprovedDate,
        expiredDate: cert.ExpiredDate,
        certificateNumber: cert.CertificateNumber,
      };
    });
  }

  // 準備帳號更新欄位
  private static prepareAccountUpdateFields(updateData: UserProfileUpdateRequest, allowedFields: string[]): AccountUpdateFields {
    const accountUpdateFields: AccountUpdateFields = {};

    if (updateData.nickName !== undefined && allowedFields.includes("NickName")) {
      accountUpdateFields.NickName = updateData.nickName;
    }
    if (updateData.email !== undefined && allowedFields.includes("Email")) {
      accountUpdateFields.Email = updateData.email;
    }
    if (updateData.phone !== undefined && allowedFields.includes("Phone")) {
      accountUpdateFields.Phone = updateData.phone;
    }
    if (updateData.avatar !== undefined && allowedFields.includes("Avatar")) {
      accountUpdateFields.Avatar = updateData.avatar;
    }

    return accountUpdateFields;
  }

  // 更新帳號欄位
  private static async updateAccountFields(transaction: Transaction, userId: string, updateFields: AccountUpdateFields): Promise<void> {
    const setClauses: string[] = [];
    const values: Record<string, unknown> = {};

    Object.keys(updateFields).forEach((key) => {
      setClauses.push(`${key} = @${key}`);
      values[key] = updateFields[key as keyof AccountUpdateFields];
    });

    setClauses.push("UpdatedTime = GETDATE()");
    setClauses.push("UpdatedUserId = @userId");
    values.userId = userId;

    const updateQuery = `UPDATE Account SET ${setClauses.join(", ")} WHERE Id = @updateUserId`;
    values.updateUserId = userId;

    await transaction.request().input("updateUserId", userId);
    Object.keys(values).forEach((key) => {
      if (key !== "updateUserId") {
        transaction.request().input(key, values[key]);
      }
    });

    await transaction.request().query(updateQuery);
  }

  // 更新學校欄位
  private static async updateSchoolFields(
    transaction: Transaction,
    userId: string,
    schoolData: NonNullable<UserProfileUpdateRequest["school"]>,
    allowedFields: string[]
  ): Promise<void> {
    const school = await executeQuerySingle<SchoolQueryResult>(SQL_QUERIES.GET_SCHOOL_BY_ACCOUNT, {
      accountId: userId,
    });

    if (!school) return;

    const schoolUpdateFields: SchoolUpdateFields = {};
    let hasValidSchoolFields = false;

    if (schoolData.name !== undefined && allowedFields.includes("Name")) {
      schoolUpdateFields.Name = schoolData.name;
      hasValidSchoolFields = true;
    }
    if (schoolData.englishName !== undefined && allowedFields.includes("EnglishName")) {
      schoolUpdateFields.EnglishName = schoolData.englishName;
      hasValidSchoolFields = true;
    }
    if (schoolData.address !== undefined && allowedFields.includes("Address")) {
      schoolUpdateFields.Address = schoolData.address;
      hasValidSchoolFields = true;
    }
    if (schoolData.phone !== undefined && allowedFields.includes("Phone")) {
      schoolUpdateFields.Phone = schoolData.phone;
      hasValidSchoolFields = true;
    }
    if (schoolData.email !== undefined && allowedFields.includes("Email")) {
      schoolUpdateFields.Email = schoolData.email;
      hasValidSchoolFields = true;
    }
    if (schoolData.website !== undefined && allowedFields.includes("Website")) {
      schoolUpdateFields.Website = schoolData.website;
      hasValidSchoolFields = true;
    }
    if (schoolData.contactPerson !== undefined && allowedFields.includes("ContactPerson")) {
      schoolUpdateFields.ContactPerson = schoolData.contactPerson;
      hasValidSchoolFields = true;
    }
    if (schoolData.contactPhone !== undefined && allowedFields.includes("ContactPhone")) {
      schoolUpdateFields.ContactPhone = schoolData.contactPhone;
      hasValidSchoolFields = true;
    }
    if (schoolData.contactEmail !== undefined && allowedFields.includes("ContactEmail")) {
      schoolUpdateFields.ContactEmail = schoolData.contactEmail;
      hasValidSchoolFields = true;
    }

    if (hasValidSchoolFields) {
      const schoolSetClauses: string[] = [];
      const schoolValues: Record<string, unknown> = {};

      Object.keys(schoolUpdateFields).forEach((key) => {
        schoolSetClauses.push(`${key} = @school${key}`);
        schoolValues[`school${key}`] = schoolUpdateFields[key as keyof SchoolUpdateFields];
      });

      schoolSetClauses.push("UpdatedTime = GETDATE()");
      schoolSetClauses.push("UpdatedUserId = @schoolUpdatedUserId");
      schoolValues.schoolUpdatedUserId = userId;
      schoolValues.schoolId = school.Id;

      const schoolUpdateQuery = `UPDATE School SET ${schoolSetClauses.join(", ")} WHERE Id = @schoolId`;

      Object.keys(schoolValues).forEach((key) => {
        transaction.request().input(key, schoolValues[key]);
      });

      await transaction.request().query(schoolUpdateQuery);
    }
  }
}
