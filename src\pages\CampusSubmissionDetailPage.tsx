// 校園新聞投稿 / 投稿詳情
import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ArrowLeft,
  Edit3,
  Trash2,
  AlertCircle,
  Eye,
  FileText,
  Link as LinkIcon,
  Image,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import campusSubmissionService, {
  CampusSubmissionDetail,
} from "@/services/campusSubmissionService";
import { resolveApiAssetUrl } from "@/utils/pathUtils";

const CampusSubmissionDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // 狀態管理
  const [submission, setSubmission] = useState<CampusSubmissionDetail | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [operationLoading, setOperationLoading] = useState(false);

  // 對話框狀態
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [modifyDialog, setModifyDialog] = useState(false);
  const [editDialog, setEditDialog] = useState(false);

  // 載入投稿詳情
  const loadSubmissionDetail = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      const response = await campusSubmissionService.getSubmissionDetail(id);

      if (response.success) {
        setSubmission(response.data);
      } else {
        setError(response.message || "載入投稿詳情失敗");
      }
    } catch (err) {
      console.error("載入投稿詳情失敗:", err);
      setError(err instanceof Error ? err.message : "載入投稿詳情時發生錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 初始載入
  useEffect(() => {
    loadSubmissionDetail();
  }, [id]);

  // 申請修改投稿
  const handleRequestModify = async () => {
    if (!submission) return;

    try {
      setOperationLoading(true);

      const response = await campusSubmissionService.requestModify(
        submission.submissionId.toString()
      );

      if (response.success) {
        toast({
          title: "申請修改成功",
          description: "投稿狀態已改為審核中，您現在可以編輯內容",
        });

        // 導向編輯頁面
        navigate(`/news/create?edit=${submission.submissionId}`);
      } else {
        toast({
          variant: "destructive",
          title: "申請修改失敗",
          description: response.message || "請稍後再試",
        });
      }
    } catch (error) {
      console.error("申請修改失敗:", error);
      toast({
        variant: "destructive",
        title: "申請修改失敗",
        description:
          error instanceof Error ? error.message : "請稍後再試或聯絡系統管理員",
      });
    } finally {
      setOperationLoading(false);
    }
  };

  // 編輯投稿
  const handleEdit = async () => {
    if (!submission) return;

    try {
      setOperationLoading(true);

      // 如果是未送審狀態，直接進入編輯模式，不需要撤回
      if (submission.status === -1) {
        toast({
          title: "進入編輯模式",
          description: "正在載入編輯頁面...",
        });

        // 直接導向編輯頁面
        navigate(`/news/create?edit=${submission.submissionId}`);
        return;
      }

      // 其他狀態需要先撤回審核狀態
      const response = await campusSubmissionService.withdrawSubmission(
        submission.submissionId.toString()
      );

      if (response.success) {
        toast({
          title: "投稿狀態撤回成功",
          description: "投稿已撤回審核狀態，正在載入編輯頁面...",
        });

        // 重新載入投稿詳情以更新狀態顯示
        await loadSubmissionDetail();

        // 延遲一下讓用戶看到狀態變化，然後導向編輯頁面
        setTimeout(() => {
          navigate(`/news/create?edit=${submission.submissionId}`);
        }, 1000);
      } else {
        toast({
          variant: "destructive",
          title: "進入編輯模式失敗",
          description: response.message || "請稍後再試",
        });
      }
    } catch (error) {
      console.error("進入編輯模式失敗:", error);
      toast({
        variant: "destructive",
        title: "進入編輯模式失敗",
        description:
          error instanceof Error ? error.message : "請稍後再試或聯絡系統管理員",
      });
    } finally {
      setOperationLoading(false);
      setEditDialog(false);
    }
  };

  // 送審投稿
  const handleSubmitForReview = async () => {
    if (!submission) return;

    try {
      setOperationLoading(true);

      const response = await campusSubmissionService.submitForReview(
        submission.submissionId.toString()
      );

      if (response.success) {
        toast({
          title: "送審成功",
          description: "投稿已成功送審，現在等待審核中",
        });

        // 重新載入投稿詳情以更新狀態顯示
        await loadSubmissionDetail();
      } else {
        toast({
          variant: "destructive",
          title: "送審失敗",
          description: response.message || "請稍後再試",
        });
      }
    } catch (error) {
      console.error("送審失敗:", error);
      toast({
        variant: "destructive",
        title: "送審失敗",
        description:
          error instanceof Error ? error.message : "請稍後再試或聯絡系統管理員",
      });
    } finally {
      setOperationLoading(false);
    }
  };

  // 刪除投稿
  const handleDelete = async () => {
    if (!submission) return;

    try {
      setOperationLoading(true);

      const response = await campusSubmissionService.deleteSubmission(
        submission.submissionId.toString()
      );

      if (response.success) {
        toast({
          title: "刪除成功",
          description: `投稿「${response.data.title}」已成功刪除`,
        });

        // 返回列表頁面
        navigate("/news");
      } else {
        toast({
          variant: "destructive",
          title: "刪除失敗",
          description: response.message || "請稍後再試",
        });
      }
    } catch (error) {
      console.error("刪除投稿失敗:", error);
      toast({
        variant: "destructive",
        title: "刪除失敗",
        description:
          error instanceof Error ? error.message : "請稍後再試或聯絡系統管理員",
      });
    } finally {
      setOperationLoading(false);
      setDeleteDialog(false);
    }
  };

  // 載入狀態
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-6">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="space-y-6">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    );
  }

  // 錯誤狀態
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => navigate("/news")}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回新聞投稿列表
          </Button>
          <h1 className="font-size-3xl font-bold text-primary mb-2">
            投稿詳情
          </h1>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        <div className="mt-4">
          <Button onClick={loadSubmissionDetail} variant="outline">
            重新載入
          </Button>
        </div>
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Button
          variant="outline"
          onClick={() => navigate("/news")}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回列表
        </Button>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>找不到指定的投稿記錄</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 頁面標題和返回按鈕 */}
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate("/news")}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回列表
        </Button>
        <h1 className="font-size-3xl font-bold text-primary mb-2">投稿詳情</h1>
        <p className="text-gray-600">查看投稿的詳細內容和相關資訊</p>
      </div>

      {/* 投稿狀態和操作按鈕 */}
      <div className="flex justify-between items-center mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-4">
          <span className="text-gray-600">狀態：</span>
          <span
            className={`font-semibold ${campusSubmissionService.getStatusColorClass(
              submission.status
            )}`}
          >
            {submission.statusText}
          </span>
          <span className="text-gray-600">|</span>
          <span className="text-gray-600">
            投稿日期：
            {campusSubmissionService.formatDate(submission.submissionDate)}
          </span>
        </div>

        {/* 操作按鈕區域 */}
        <div className="flex justify-end space-x-3  ">
          {/* 未送審狀態(-1)的操作按鈕 */}
          {submission.status === -1 && (
            <>
              <Button
                onClick={handleEdit}
                disabled={operationLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {operationLoading ? "載入中..." : "編輯投稿"}
              </Button>
              <Button
                onClick={handleSubmitForReview}
                disabled={operationLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {operationLoading ? "送審中..." : "送審投稿"}
              </Button>
              <Button
                onClick={() => setDeleteDialog(true)}
                disabled={operationLoading}
                variant="outline"
                className="border-red-200 text-red-700 hover:bg-red-50"
              >
                {operationLoading ? "刪除中..." : "刪除投稿"}
              </Button>
            </>
          )}

          {/* 審核中狀態(0)的操作按鈕 */}
          {submission.status === 0 && (
            <>
              <Button
                onClick={() => setEditDialog(true)}
                disabled={operationLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                編輯投稿
              </Button>
            </>
          )}

          {/* 已發布狀態(1)的操作按鈕 */}
          {submission.status === 1 && (
            <>
              <Button
                onClick={handleRequestModify}
                disabled={operationLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {operationLoading ? "處理中..." : "申請修改投稿"}
              </Button>
              <Button
                onClick={() => setEditDialog(true)}
                disabled={operationLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                編輯投稿
              </Button>
            </>
          )}

          {/* 已退件狀態(3)的操作按鈕 */}
          {submission.status === 3 && (
            <>
              <Button
                onClick={() => setEditDialog(true)}
                disabled={operationLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                重新編輯
              </Button>
              <Button
                onClick={() => setDeleteDialog(true)}
                disabled={operationLoading}
                variant="outline"
                className="border-red-200 text-red-700 hover:bg-red-50"
              >
                {operationLoading ? "刪除中..." : "刪除投稿"}
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 投稿內容 */}
      <div className="space-y-6">
        {/* 中英文內容標籤 */}
        <Tabs defaultValue="zh-TW" className="w-full">
          <TabsList className="grid w-full grid-cols-2 h-18">
            <TabsTrigger value="zh-TW">中文內容</TabsTrigger>
            <TabsTrigger value="en">English Content</TabsTrigger>
          </TabsList>

          {/* 中文內容 */}
          <TabsContent value="zh-TW">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  中文版本內容
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    新聞標題
                  </label>
                  <div className="p-3 bg-gray-50 rounded border">
                    {submission.zhTitle || submission.title}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    投稿內容
                  </label>
                  <div
                    className="p-4 bg-gray-50 rounded border min-h-[200px]"
                    dangerouslySetInnerHTML={{
                      __html: submission.zhContent || submission.description,
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 英文內容 */}
          <TabsContent value="en">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  English Version Content
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    News Title
                  </label>
                  <div className="p-3 bg-gray-50 rounded border">
                    {submission.enTitle || submission.title}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Submission Content
                  </label>
                  <div
                    className="p-4 bg-gray-50 rounded border min-h-[200px]"
                    dangerouslySetInnerHTML={{
                      __html: submission.enContent || submission.description,
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 圖片資訊 */}
        {submission.photos && submission.photos.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                投稿圖片
                <span className="text-sm text-gray-500">
                  ({submission.photos.length} 張圖片)
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {submission.photos.map((photo, index) => (
                  <div key={photo.photoId} className="space-y-2">
                    <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={resolveApiAssetUrl(photo.url)}
                        alt={photo.title || `投稿圖片 ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/placeholder.svg";
                        }}
                      />
                    </div>
                    {photo.title && (
                      <div className="text-sm text-gray-600">{photo.title}</div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 附件資訊 */}
        {submission.attachments && submission.attachments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                附件資訊
                <span className="text-sm text-gray-500">
                  ({submission.attachments.length} 個附件)
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {submission.attachments.map((attachment, index) => (
                  <div
                    key={attachment.attachmentId}
                    className="flex items-center gap-3 p-3 bg-gray-50 rounded"
                  >
                    <FileText className="h-4 w-4 text-gray-500" />
                    <div className="flex-1">
                      <div className="font-medium">
                        {attachment.title || `附件 ${index + 1}`}
                      </div>
                      <div className="text-sm text-gray-500">
                        類型: {attachment.contentTypeCode}
                      </div>
                    </div>
                    {attachment.altUrl && (
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={attachment.altUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          查看
                        </a>
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 基本資訊 */}
        <Card>
          <CardHeader>
            <CardTitle>基本資訊</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">投稿編號：</span>
                <span className="font-medium">{submission.submissionId}</span>
              </div>
              <div>
                <span className="text-gray-600">徽章類型：</span>
                <span className="font-medium">
                  {submission.badgeType === 1
                    ? "綠旗學校"
                    : submission.badgeType === 2
                    ? "銀牌學校"
                    : "一般投稿"}
                </span>
              </div>
              <div>
                <span className="text-gray-600">建立時間：</span>
                <span className="font-medium">
                  {campusSubmissionService.formatDate(submission.createdTime)}
                </span>
              </div>
              {submission.updatedTime && (
                <div>
                  <span className="text-gray-600">更新時間：</span>
                  <span className="font-medium">
                    {campusSubmissionService.formatDate(submission.updatedTime)}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 編輯投稿確認對話框 */}
      <Dialog open={editDialog} onOpenChange={setEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit3 className="h-5 w-5" />
              {submission.status === -1
                ? "編輯投稿"
                : submission.status === 3
                ? "重新編輯投稿"
                : "編輯投稿"}
            </DialogTitle>
            <DialogDescription>
              您確定要編輯投稿「{submission.title}」嗎？
              <br />
              {submission.status === -1 ? (
                <span className="text-blue-600 font-medium">
                  ✏️ 編輯完成後，您可以選擇送審或繼續編輯。
                </span>
              ) : submission.status === 3 ? (
                <span className="text-green-600 font-medium">
                  🔄 重新編輯後需要重新送審。
                </span>
              ) : (
                <span className="text-orange-600 font-medium">
                  ⚠️
                  請注意：編輯投稿將會撤回目前的審核狀態，編輯完成後需要重新送審。
                </span>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialog(false)}>
              取消
            </Button>
            <Button
              onClick={handleEdit}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              確認編輯
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 申請修改確認對話框 */}
      <Dialog open={modifyDialog} onOpenChange={setModifyDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>申請修改投稿</DialogTitle>
            <DialogDescription>
              您確定要申請修改投稿「{submission.title}」嗎？
              <br />
              <span className="text-blue-600 font-medium">
                申請後投稿狀態將改為審核中，您可以重新編輯內容並再次提交審核。
              </span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setModifyDialog(false)}
              disabled={operationLoading}
            >
              取消
            </Button>
            <Button
              onClick={handleRequestModify}
              disabled={operationLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {operationLoading ? "處理中..." : "確認申請"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 刪除確認對話框 */}
      <Dialog open={deleteDialog} onOpenChange={setDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>確認刪除投稿</DialogTitle>
            <DialogDescription>
              您確定要刪除投稿「{submission.title}」嗎？
              <br />
              <span className="text-red-600 font-medium">此操作無法復原。</span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog(false)}
              disabled={operationLoading}
            >
              取消
            </Button>
            <Button
              onClick={handleDelete}
              disabled={operationLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {operationLoading ? "刪除中..." : "確定刪除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CampusSubmissionDetailPage;
