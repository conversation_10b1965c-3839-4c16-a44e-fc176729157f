// ========== 憑證服務 - 業務邏輯和資料庫操作 ==========

import { executeQuery, executeInsert, executeUpdate } from "../config/database-mssql.js";
import crypto from "crypto";
import {
  CertificateValidateRequest,
  CertificateBindRequest,
  CertificateLoginRequest,
  CertificateValidationResult,
  CertificateBindResult,
  CertificateBindingInfo,
  CertificateLoginResult,
  CertificateTestResult,
  AccountQueryResult,
  AccountFullQueryResult,
  BindingQueryResult,
} from "../models/certificate.js";
import {
  ACCOUNT_STATUS,
  TOKEN_TYPES,
  CERTIFICATE_ACTIONS,
  CERTIFICATE_ERROR_MESSAGES,
  CERTIFICATE_SUCCESS_MESSAGES,
  SQL_QUERIES,
  validateCertificateData,
  parseCertificateInfo,
  validateCertificatePassword,
  hasPermissionForAccount,
  generateTokenExpiry,
  formatBindingInfo,
  formatLoginResult,
  generateLogDescription,
  isValidAccount,
} from "../constants/certificate.js";

export class CertificateService {
  // 驗證憑證
  static async validateCertificate(request: CertificateValidateRequest): Promise<CertificateValidationResult> {
    try {
      const { certificateData } = request;

      if (!certificateData) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.MISSING_CERTIFICATE_DATA);
      }

      // 驗證憑證格式
      const isValid = validateCertificateData(certificateData);

      if (!isValid) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.INVALID_CERTIFICATE);
      }

      // 解析憑證資訊
      const certificateInfo = parseCertificateInfo(certificateData);

      return {
        isValid: true,
        certificateInfo,
      };
    } catch (error) {
      console.error("❌ [CertificateService] 憑證驗證失敗:", error);
      throw error;
    }
  }

  // 綁定憑證
  static async bindCertificate(request: CertificateBindRequest, userId: string): Promise<CertificateBindResult> {
    try {
      const { certificateInfo, accountId } = request;

      if (!certificateInfo || !accountId) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.MISSING_BIND_INFO);
      }

      // 驗證用戶權限（只能綁定自己的帳號）
      if (!hasPermissionForAccount(userId, accountId)) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.PERMISSION_DENIED_BIND);
      }

      // 模擬綁定成功
      console.log("憑證綁定成功:", {
        certificateId: certificateInfo.serialNumber,
        subject: certificateInfo.subject,
        accountId: accountId,
        timestamp: new Date().toISOString(),
      });

      return {
        certificateId: certificateInfo.serialNumber,
        bindTime: new Date().toISOString(),
        status: "demo_success",
      };
    } catch (error) {
      console.error("❌ [CertificateService] 憑證綁定失敗:", error);
      throw error;
    }
  }

  // 解除憑證綁定
  static async unbindCertificate(accountId: string, userId: string): Promise<void> {
    try {
      // 驗證用戶權限（只能解除自己的綁定）
      if (!hasPermissionForAccount(userId, accountId)) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.PERMISSION_DENIED_UNBIND);
      }

      // 檢查帳號是否存在憑證綁定
      const currentAccount = await this.getAccountCertificate(accountId);
      if (!currentAccount) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.ACCOUNT_NOT_FOUND);
      }

      if (!currentAccount.citizen_digital_number) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.NO_CERTIFICATE_BOUND);
      }

      // 解除憑證綁定
      const affectedRows = await this.updateCertificateBinding(accountId, userId);

      if (affectedRows === 0) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.UNBIND_FAILED);
      }

      // 記錄憑證解綁日誌
      await this.logCertificateAction(accountId, CERTIFICATE_ACTIONS.UNBIND, generateLogDescription(CERTIFICATE_ACTIONS.UNBIND), "unknown", "unknown");
    } catch (error) {
      console.error("❌ [CertificateService] 解除憑證綁定失敗:", error);
      throw error;
    }
  }

  // 查詢憑證綁定狀態
  static async getCertificateBindings(userId: string): Promise<CertificateBindingInfo> {
    try {
      // 查詢用戶的憑證綁定狀態
      const bindings = await executeQuery<BindingQueryResult>(SQL_QUERIES.GET_USER_BINDINGS, { userId });

      if (bindings.length === 0) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.ACCOUNT_NOT_FOUND);
      }

      const binding = bindings[0];
      return formatBindingInfo(binding);
    } catch (error) {
      console.error("❌ [CertificateService] 查詢憑證綁定失敗:", error);
      throw error;
    }
  }

  // 憑證登入
  static async loginWithCertificate(request: CertificateLoginRequest, ipAddress: string, userAgent: string): Promise<CertificateLoginResult> {
    try {
      const { certificateData, password } = request;

      if (!certificateData || !password) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.MISSING_LOGIN_CREDENTIALS);
      }

      // 驗證憑證和密碼
      const certificateInfo = parseCertificateInfo(certificateData);
      const isPasswordValid = validateCertificatePassword(certificateData, password);

      if (!isPasswordValid) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.INVALID_PASSWORD);
      }

      // 查詢綁定此憑證的帳號
      const accounts = await this.getAccountByCertificate(certificateInfo.serialNumber);

      if (accounts.length === 0) {
        throw new Error(CERTIFICATE_ERROR_MESSAGES.NO_BOUND_ACCOUNT);
      }

      const account = accounts[0];

      // 生成登入 Token
      const token = crypto.randomUUID();
      const expiryDate = generateTokenExpiry();

      // 保存 Token 到資料庫
      await this.saveLoginToken(account.accountId, token, expiryDate);

      // 記錄登入日誌
      await this.logCertificateAction(account.accountId, CERTIFICATE_ACTIONS.LOGIN, generateLogDescription(CERTIFICATE_ACTIONS.LOGIN), ipAddress, userAgent);

      return formatLoginResult(account, token, expiryDate);
    } catch (error) {
      console.error("❌ [CertificateService] 憑證登入失敗:", error);
      throw error;
    }
  }

  // 測試憑證API
  static async testCertificateAPI(user: { id: string; account: string; roleType: string }): Promise<CertificateTestResult> {
    try {
      return {
        userId: user.id,
        userAccount: user.account,
        userRole: user.roleType,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ [CertificateService] 憑證測試失敗:", error);
      throw error;
    }
  }

  // ========== 私有輔助方法 ==========

  // 獲取帳號憑證資訊
  private static async getAccountCertificate(accountId: string): Promise<AccountQueryResult | null> {
    const results = await executeQuery<AccountQueryResult>(SQL_QUERIES.CHECK_ACCOUNT_CERTIFICATE, { accountId });
    return results.length > 0 ? results[0] : null;
  }

  // 更新憑證綁定
  private static async updateCertificateBinding(accountId: string, userId: string): Promise<number> {
    const result = await executeUpdate(SQL_QUERIES.UPDATE_UNBIND_CERTIFICATE, {
      userId,
      accountId,
    });
    return result.affectedRows || 0;
  }

  // 根據憑證查詢帳號
  private static async getAccountByCertificate(certificateId: string): Promise<AccountFullQueryResult[]> {
    return await executeQuery<AccountFullQueryResult>(SQL_QUERIES.GET_ACCOUNT_BY_CERTIFICATE, {
      certificateId,
      activeStatus: ACCOUNT_STATUS.ACTIVE,
    });
  }

  // 保存登入Token
  private static async saveLoginToken(accountId: string, token: string, expiryDate: Date): Promise<void> {
    await executeInsert(SQL_QUERIES.INSERT_TOKEN, {
      accountId,
      token,
      tokenType: TOKEN_TYPES.CERTIFICATE_LOGIN,
      expiredTime: expiryDate,
    });
  }

  // 記錄憑證操作日誌
  private static async logCertificateAction(accountId: string, action: string, description: string, ipAddress: string, userAgent: string): Promise<void> {
    await executeInsert(SQL_QUERIES.INSERT_ACTION_LOG, {
      accountId,
      action,
      description,
      ipAddress,
      userAgent,
    });
  }
}
