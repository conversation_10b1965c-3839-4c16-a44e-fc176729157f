# Question Templates 組件結構

此目錄包含了重構後的問題模板組件，提高了可維護性和模組化程度。

## 目錄結構

```
question-templates/
├── types/               # 類型定義
│   └── index.ts        # 所有介面定義
├── shared/             # 共用組件和工具
│   ├── index.ts        # 共用組件匯出
│   ├── save-button.tsx # 保存按鈕組件
│   ├── format-answer.ts # 答案格式化工具
│   └── utils.ts        # 工具函數（日期驗證等）
├── templates/          # 各個模板組件
│   ├── template-01-yes-no.tsx      # 模板1: 是非選擇題
│   ├── template-02-team-member.tsx # 模板2: 團隊成員
│   ├── template-03-meeting-record.tsx # 模板3: 會議記錄
│   └── template-16-textarea.tsx    # 模板16: 文字區域
├── factory/            # 工廠函數和預設資料
│   ├── template-factory.tsx # 模板工廠函數
│   └── default-data.ts     # 預設資料生成
├── index.ts            # 主要匯出檔案
└── README.md           # 說明文件
```

## 主要改進

1. **模組化設計**: 每個模板組件獨立成檔案
2. **類型安全**: 集中管理所有 TypeScript 介面
3. **共用組件**: 提取可重用的組件到 shared 目錄
4. **UI 組件分離**: DatePicker 移動到 `ui/` 目錄
5. **向後相容**: 保持原有的匯入路徑有效

## 使用方式

### 匯入整個模組
```typescript
import { QuestionTemplateFactory, SaveButton, getDefaultTemplateData } from "@/components/question-templates";
```

### 匯入特定模板
```typescript
import { YesNoTemplate, TeamMemberTemplate } from "@/components/question-templates";
```

### 匯入類型定義
```typescript
import type { YesNoData, TeamMemberData, TemplateProps } from "@/components/question-templates";
```

### 向後相容性
原有的匯入方式仍然有效：
```typescript
import { QuestionTemplateFactory } from "@/components/QuestionTemplates";
```

## 新增模板

1. 在 `types/index.ts` 中新增資料結構介面
2. 在 `templates/` 目錄中建立新的模板組件檔案
3. 更新 `factory/template-factory.tsx` 中的工廠函數
4. 更新 `factory/default-data.ts` 中的預設資料
5. 在 `index.ts` 中匯出新的模板組件

## 已實現的模板

- ✅ 模板1: 是非選擇題 (YesNoTemplate)
- ✅ 模板2: 團隊成員 (TeamMemberTemplate)  
- ✅ 模板3: 會議記錄 (MeetingRecordTemplate)
- ✅ 模板16: 文字區域 (TextAreaTemplate)
- 🔄 其他模板: 暫時使用 TextAreaTemplate

## 共用組件

### DatePicker
已移動到 `@/components/ui/date-picker`，提供統一的日期選擇功能。

### SaveButton
提供標準化的保存功能，支援：
- 資料驗證
- 錯誤處理
- 載入狀態
- 成功/失敗回饋

### 工具函數
- `validateDateIntervals`: 驗證日期間隔
- `formatAnswerForSave`: 格式化答案資料
- `isQuestionCompleted`: 檢查問題完成狀態
