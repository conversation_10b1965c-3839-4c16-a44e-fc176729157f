/**
 * API 錯誤處理統一規範
 */

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  statusCode?: number;
}

export class ApiErrorHandler {
  
  // 標準錯誤碼定義
  static readonly ERROR_CODES = {
    // 認證錯誤
    TOKEN_EXPIRED: 'TOKEN_EXPIRED',
    TOKEN_INVALID: 'TOKEN_INVALID',
    UNAUTHORIZED: 'UNAUTHORIZED',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    
    // 請求錯誤
    BAD_REQUEST: 'BAD_REQUEST',
    NOT_FOUND: 'NOT_FOUND',
    VALIDATION_FAILED: 'VALIDATION_FAILED',
    
    // 伺服器錯誤
    INTERNAL_ERROR: 'INTERNAL_ERROR',
    DATABASE_ERROR: 'DATABASE_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    
    // 業務邏輯錯誤
    OPERATION_FAILED: 'OPERATION_FAILED',
    RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  } as const;

  // 錯誤訊息對應
  static readonly ERROR_MESSAGES = {
    [ApiErrorHandler.ERROR_CODES.TOKEN_EXPIRED]: '登入已過期，請重新登入',
    [ApiErrorHandler.ERROR_CODES.TOKEN_INVALID]: '登入資訊無效，請重新登入',
    [ApiErrorHandler.ERROR_CODES.UNAUTHORIZED]: '未授權存取，請先登入',
    [ApiErrorHandler.ERROR_CODES.PERMISSION_DENIED]: '權限不足，無法執行此操作',
    
    [ApiErrorHandler.ERROR_CODES.BAD_REQUEST]: '請求格式錯誤',
    [ApiErrorHandler.ERROR_CODES.NOT_FOUND]: '找不到要求的資源',
    [ApiErrorHandler.ERROR_CODES.VALIDATION_FAILED]: '資料驗證失敗',
    
    [ApiErrorHandler.ERROR_CODES.INTERNAL_ERROR]: '系統內部錯誤',
    [ApiErrorHandler.ERROR_CODES.DATABASE_ERROR]: '資料庫連接錯誤',
    [ApiErrorHandler.ERROR_CODES.NETWORK_ERROR]: '網路連接失敗',
    
    [ApiErrorHandler.ERROR_CODES.OPERATION_FAILED]: '操作執行失敗',
    [ApiErrorHandler.ERROR_CODES.RESOURCE_CONFLICT]: '資源衝突，請重新整理後再試',
  } as const;

  /**
   * 根據 HTTP 狀態碼判斷錯誤類型
   */
  static mapHttpStatusToErrorCode(status: number): string {
    switch (status) {
      case 400: return ApiErrorHandler.ERROR_CODES.BAD_REQUEST;
      case 401: return ApiErrorHandler.ERROR_CODES.UNAUTHORIZED;
      case 403: return ApiErrorHandler.ERROR_CODES.PERMISSION_DENIED;
      case 404: return ApiErrorHandler.ERROR_CODES.NOT_FOUND;
      case 409: return ApiErrorHandler.ERROR_CODES.RESOURCE_CONFLICT;
      case 422: return ApiErrorHandler.ERROR_CODES.VALIDATION_FAILED;
      case 500: return ApiErrorHandler.ERROR_CODES.INTERNAL_ERROR;
      default: return ApiErrorHandler.ERROR_CODES.INTERNAL_ERROR;
    }
  }

  /**
   * 建立標準化錯誤物件
   */
  static createError(code: string, message?: string, details?: any, statusCode?: number): ApiError {
    return {
      code,
      message: message || ApiErrorHandler.ERROR_MESSAGES[code as keyof typeof ApiErrorHandler.ERROR_MESSAGES] || '未知錯誤',
      details,
      statusCode,
    };
  }

  /**
   * 從 HTTP 回應建立錯誤
   */
  static fromHttpResponse(status: number, responseData?: any): ApiError {
    const code = ApiErrorHandler.mapHttpStatusToErrorCode(status);
    const message = responseData?.message || responseData?.error;
    const details = responseData?.details;
    
    return ApiErrorHandler.createError(code, message, details, status);
  }

  /**
   * 從網路錯誤建立錯誤
   */
  static fromNetworkError(error: Error): ApiError {
    return ApiErrorHandler.createError(
      ApiErrorHandler.ERROR_CODES.NETWORK_ERROR,
      `網路連接失敗: ${error.message}`,
      { originalError: error.name }
    );
  }

  /**
   * 檢查是否為認證相關錯誤
   */
  static isAuthError(error: ApiError): boolean {
    const authErrorCodes = [
      ApiErrorHandler.ERROR_CODES.TOKEN_EXPIRED,
      ApiErrorHandler.ERROR_CODES.TOKEN_INVALID,
      ApiErrorHandler.ERROR_CODES.UNAUTHORIZED,
      ApiErrorHandler.ERROR_CODES.PERMISSION_DENIED,
    ];
    return authErrorCodes.includes(error.code as any);
  }

  /**
   * 檢查是否需要重新登入
   */
  static requiresReauth(error: ApiError): boolean {
    const reauthErrorCodes = [
      ApiErrorHandler.ERROR_CODES.TOKEN_EXPIRED,
      ApiErrorHandler.ERROR_CODES.TOKEN_INVALID,
      ApiErrorHandler.ERROR_CODES.UNAUTHORIZED,
    ];
    return reauthErrorCodes.includes(error.code as any);
  }

  /**
   * 格式化錯誤訊息用於顯示
   */
  static formatErrorMessage(error: ApiError): string {
    if (error.details && typeof error.details === 'object') {
      // 如果有驗證錯誤詳細資訊
      if (error.code === ApiErrorHandler.ERROR_CODES.VALIDATION_FAILED && error.details.fields) {
        const fieldErrors = Object.entries(error.details.fields)
          .map(([field, msg]) => `${field}: ${msg}`)
          .join(', ');
        return `${error.message} (${fieldErrors})`;
      }
    }
    
    return error.message;
  }
}

export default ApiErrorHandler;