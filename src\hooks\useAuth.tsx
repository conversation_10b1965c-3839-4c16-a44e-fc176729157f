import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from "react";
import { authAPI, User } from "@/api/authAPI";
import authService, { AuthState } from "@/services/authService";
import { useToken } from "@/contexts/TokenContext";

// 認證 Context 類型
interface AuthContextType {
  authState: AuthState;
  login: (token: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
  isLoading: boolean;
  roleCheckMessage: string | null;
  clearRoleCheckMessage: () => void;

  // 整合 TokenContext 的功能
  withTemporaryToken: <T>(tempToken: string, callback: () => Promise<T>) => Promise<T>;
}

// 創建 Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// AuthProvider 組件
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 整合 TokenContext
  const { token: contextToken, setToken: setContextToken, clearToken: clearContextToken, withTemporaryToken } = useToken();

  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [roleCheckMessage, setRoleCheckMessage] = useState<string | null>(null);

  // 使用 ref 來存儲定時器，避免重複創建
  const roleCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isCheckingRoleRef = useRef(false);

  // 清除角色檢查訊息
  const clearRoleCheckMessage = useCallback(() => {
    setRoleCheckMessage(null);
  }, []);

  // 更新認證狀態
  const updateAuthState = useCallback(() => {
    const newAuthState = authService.getAuthState();
    setAuthState(newAuthState);

    // 同步 Token 到 Context
    if (newAuthState.token !== contextToken) {
      if (newAuthState.token) {
        setContextToken(newAuthState.token);
        console.log("🔄 [AuthProvider] 同步 Token 到 Context");
      } else if (contextToken) {
        clearContextToken();
        console.log("🔄 [AuthProvider] 清除 Context 中的 Token");
      }
    }
  }, [contextToken, setContextToken, clearContextToken]);

  // 🔒 新增：處理角色權限被拒絕的情況
  const handleRolePermissionDenied = useCallback(
    async (userRole: string, allowedRoles: string[]) => {
      console.log(`角色權限被拒絕: ${userRole}, 允許的角色: ${allowedRoles.join(", ")}`);

      // 🚪 立即執行強制登出操作，確保使用者被完全登出
      try {
        console.log("執行角色權限被拒絕的強制登出...");

        // 停止定期檢查
        if (roleCheckIntervalRef.current) {
          clearInterval(roleCheckIntervalRef.current);
          roleCheckIntervalRef.current = null;
        }

        // 強制清除認證狀態
        await authService.logout();
        updateAuthState();

        // 清除 TokenContext 中的 Token
        clearContextToken();

        console.log("角色權限被拒絕強制登出完成");
      } catch (error) {
        console.error("角色權限被拒絕強制登出失敗:", error);
        // 最後手段：直接清除狀態
        try {
          clearContextToken();
          updateAuthState();
          console.log("強制清除本地資料完成");
        } catch (localError) {
          console.error("清除本地資料失敗:", localError);
        }
      }

      // 設置角色檢查訊息
      setRoleCheckMessage(`您的帳號角色 '${userRole}' 不被允許存取此系統。\n允許的角色: ${allowedRoles.join(", ")}`);

      // 5秒後自動清除訊息並重定向到登入頁面
      setTimeout(() => {
        setRoleCheckMessage(null);
        console.log("自動重定向到登入頁面");
        window.location.href = "/login";
      }, 5000);
    },
    [updateAuthState]
  );

  // 🔒 新增：定期角色檢查
  const performPeriodicRoleCheck = useCallback(async () => {
    // 防止重複檢查
    if (isCheckingRoleRef.current) {
      return;
    }

    try {
      isCheckingRoleRef.current = true;

      // 只有在已認證的情況下才進行檢查
      if (!authState.isAuthenticated) {
        return;
      }

      // 使用新版 API 進行角色檢查
      const result = await authAPI.checkTokenStatus();
      const isValid = result.success && result.data?.valid;

      // 只有在 Token 狀態真正發生變化時才更新狀態
      if (!isValid && authState.isAuthenticated) {
        console.log("定期角色檢查失敗，使用者將被登出");
        updateAuthState();
      }
      // 如果 Token 仍然有效，不需要更新狀態，避免不必要的重新渲染
    } catch (error) {
      console.error("定期角色檢查錯誤:", error);
    } finally {
      isCheckingRoleRef.current = false;
    }
  }, [authState.isAuthenticated, updateAuthState]);

  // 🔒 新增：啟動定期角色檢查
  const startPeriodicRoleCheck = useCallback(() => {
    // 清除現有的定時器
    if (roleCheckIntervalRef.current) {
      clearInterval(roleCheckIntervalRef.current);
    }

    // 只有在已認證時才啟動定期檢查
    if (authState.isAuthenticated) {
      // 立即執行一次檢查
      performPeriodicRoleCheck();

      // 設置定期檢查 (30 秒間隔)
      roleCheckIntervalRef.current = setInterval(() => {
        performPeriodicRoleCheck();
      }, 30000); // 30 秒
    }
  }, [authState.isAuthenticated, performPeriodicRoleCheck]);

  // 🔒 新增：停止定期角色檢查
  const stopPeriodicRoleCheck = useCallback(() => {
    if (roleCheckIntervalRef.current) {
      console.log("停止定期角色權限檢查");
      clearInterval(roleCheckIntervalRef.current);
      roleCheckIntervalRef.current = null;
    }
  }, []);

  // 登入
  const login = async (token: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // 先設定 Token 到 Context
      setContextToken(token);

      const result = await authService.handleExternalLogin(token);

      if (result.success) {
        updateAuthState();
        console.log("✅ [AuthProvider] 登入成功，Token 已同步到 Context");
        return true;
      } else {
        // 登入失敗，清除 Context 中的 Token
        clearContextToken();

        // 檢查是否為角色權限被拒絕的情況
        if (result.message && result.message.includes("角色權限被拒絕")) {
          // 角色權限被拒絕
          handleRolePermissionDenied("Unknown", ["School", "Government", "Tutor"]);
        }
      }
      return false;
    } catch (error) {
      console.error("Login error:", error);
      // 發生錯誤時清除 Context 中的 Token
      clearContextToken();
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 登出
  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // 停止定期檢查
      stopPeriodicRoleCheck();

      // 先清除 Context 中的 Token
      clearContextToken();

      await authService.logout();
      updateAuthState();

      // 清除角色檢查訊息
      setRoleCheckMessage(null);

      console.log("✅ [AuthProvider] 登出成功，已清除 Context 中的 Token");
    } catch (error) {
      console.error("Logout error:", error);
      // 發生錯誤時仍要清除 Context 中的 Token
      clearContextToken();
    } finally {
      setIsLoading(false);
    }
  };

  // 檢查認證狀態 (更新版本，包含角色檢查)
  const checkAuth = async (): Promise<boolean> => {
    try {
      const isValid = await authService.checkTokenStatus();
      if (!isValid && authState.isAuthenticated) {
        // Token 已過期或角色權限被拒絕，清除認證狀態
        await authService.logout();
        updateAuthState();
        stopPeriodicRoleCheck();
      }
      return isValid;
    } catch (error) {
      console.error("Auth check error:", error);
      return false;
    }
  };

  // 🔒 監聽認證狀態變化，控制定期檢查
  useEffect(() => {
    if (authState.isAuthenticated) {
      startPeriodicRoleCheck();
    } else {
      stopPeriodicRoleCheck();
    }

    // 清理函數
    return () => {
      if (!authState.isAuthenticated) {
        stopPeriodicRoleCheck();
      }
    };
  }, [authState.isAuthenticated, startPeriodicRoleCheck, stopPeriodicRoleCheck]);

  // 初始化認證狀態
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        updateAuthState();

        // 如果有 token，檢查是否仍然有效（包含角色檢查）
        if (authService.getToken()) {
          console.log("檢查現有 Token 的角色權限...");
          await checkAuth();
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // 設置認證服務的回調
  useEffect(() => {
    authService.setCallbacks({
      onLoginSuccess: (user: User) => {
        console.log("Login successful:", user);
        updateAuthState();
      },
      onLogout: () => {
        console.log("Logout successful");
        updateAuthState();
        stopPeriodicRoleCheck();
        setRoleCheckMessage(null);
      },
      onTokenExpired: () => {
        console.log("Token expired");
        updateAuthState();
        stopPeriodicRoleCheck();
      },
      onRolePermissionDenied: handleRolePermissionDenied, // 🔒 新增角色權限被拒絕的回調
    });
  }, [updateAuthState, stopPeriodicRoleCheck, handleRolePermissionDenied]);

  // 🔒 組件卸載時清理定時器
  useEffect(() => {
    return () => {
      stopPeriodicRoleCheck();
    };
  }, [stopPeriodicRoleCheck]);

  const contextValue: AuthContextType = {
    authState,
    login,
    logout,
    checkAuth,
    isLoading,
    roleCheckMessage,
    clearRoleCheckMessage,
    withTemporaryToken, // 整合 TokenContext 的 withTemporaryToken 功能
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// useAuth Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// 檢查是否已認證的 Hook
export const useIsAuthenticated = (): boolean => {
  const { authState } = useAuth();
  return authState.isAuthenticated;
};

// 獲取當前用戶的 Hook
export const useCurrentUser = (): User | null => {
  const { authState } = useAuth();
  return authState.user;
};

// 檢查用戶權限的 Hook
export const useHasPermission = (permission: string): boolean => {
  const { authState } = useAuth();
  return authState.user?.permissions.includes(permission) || false;
};

// 🔒 新增：檢查用戶角色的 Hook
export const useHasRole = (roleType: string): boolean => {
  return authService.hasRole(roleType);
};

// 🔒 新增：獲取允許角色列表的 Hook
export const useAllowedRoles = (): string[] => {
  return authService.getAllowedRoles();
};

// 🔒 新增：角色檢查訊息的 Hook
export const useRoleCheckMessage = (): { message: string | null; clearMessage: () => void } => {
  const { roleCheckMessage, clearRoleCheckMessage } = useAuth();
  return {
    message: roleCheckMessage,
    clearMessage: clearRoleCheckMessage,
  };
};

export default useAuth;
