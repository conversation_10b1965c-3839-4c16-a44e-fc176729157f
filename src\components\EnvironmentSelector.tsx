import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Copy, ExternalLink, Globe, Settings, Check } from "lucide-react";
import environmentManager, { EnvironmentConfig, switchEnvironment, getCurrentEnvironment } from "@/config/environment";
import authService from "@/services/authService";

interface EnvironmentSelectorProps {
  compact?: boolean;
  showQuickLinks?: boolean;
  onEnvironmentChange?: (env: EnvironmentConfig) => void;
}

const EnvironmentSelector: React.FC<EnvironmentSelectorProps> = ({ compact = false, showQuickLinks = true, onEnvironmentChange }) => {
  const [currentEnv, setCurrentEnv] = useState<EnvironmentConfig>(getCurrentEnvironment());
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
  const [isChanging, setIsChanging] = useState(false);

  // 處理環境切換
  const handleEnvironmentChange = async (envName: string) => {
    if (envName === currentEnv.name) return;

    setIsChanging(true);

    try {
      console.log(`🌍 [EnvironmentSelector] 切換環境: ${currentEnv.name} → ${envName}`);

      // 切換環境
      const success = switchEnvironment(envName);
      if (success) {
        const newEnv = getCurrentEnvironment();
        setCurrentEnv(newEnv);

        // 更新 authService 的 API URL
        authService.updateApiBaseUrl();

        // 觸發回調
        onEnvironmentChange?.(newEnv);

        console.log(`✅ [EnvironmentSelector] 環境切換成功: ${newEnv.displayName}`);
      } else {
        console.error("❌ [EnvironmentSelector] 環境切換失敗");
      }
    } catch (error) {
      console.error("💥 [EnvironmentSelector] 環境切換錯誤:", error);
    } finally {
      setIsChanging(false);
    }
  };

  // 複製 URL 到剪貼板
  const copyToClipboard = async (url: string, role: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(role);

      // 3秒後清除複製狀態
      setTimeout(() => setCopiedUrl(null), 3000);

      console.log(`📋 [EnvironmentSelector] 已複製 ${role} 連結`);
    } catch (error) {
      console.error("💥 [EnvironmentSelector] 複製連結失敗:", error);
    }
  };

  // 打開新視窗
  const openInNewTab = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  // 監聽環境變化
  useEffect(() => {
    const handleStorageChange = () => {
      const newEnv = getCurrentEnvironment();
      if (newEnv.name !== currentEnv.name) {
        setCurrentEnv(newEnv);
        authService.updateApiBaseUrl();
        onEnvironmentChange?.(newEnv);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [currentEnv.name, onEnvironmentChange]);

  // 獲取可用環境
  const availableEnvironments = environmentManager.getAvailableEnvironments();

  // 獲取快速登入連結
  const quickLinks = currentEnv.features.showTestAccounts ? environmentManager.getQuickLoginLinks() : [];

  // 緊湊模式
  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <Globe className="h-4 w-4 text-muted-foreground" />
        <Select value={currentEnv.name} onValueChange={handleEnvironmentChange} disabled={isChanging}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {availableEnvironments.map((env) => (
              <SelectItem key={env.name} value={env.name}>
                {env.displayName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Badge variant={currentEnv.name === "production" ? "destructive" : "secondary"}>{currentEnv.displayName}</Badge>
      </div>
    );
  }

  // 完整模式
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 font-size-lg">
          <Settings className="h-5 w-5" />
          環境設定
        </CardTitle>
        <CardDescription className="font-size-sm">選擇要連接的環境，不同環境有不同的 API 端點和測試資料</CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 環境選擇器 */}
        <div className="space-y-2">
          <label className="font-size-sm font-medium">當前環境</label>
          <Select value={currentEnv.name} onValueChange={handleEnvironmentChange} disabled={isChanging}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableEnvironments.map((env) => (
                <SelectItem key={env.name} value={env.name}>
                  <div className="flex items-center justify-between w-full">
                    <span>{env.displayName}</span>
                    <Badge variant={env.name === "production" ? "destructive" : "secondary"} className="ml-2">
                      {env.name.toUpperCase()}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 環境資訊 */}
        <div className="bg-muted p-3 rounded-lg space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-size-sm font-medium">API 端點:</span>
            <code className="font-size-xs bg-background px-2 py-1 rounded">{currentEnv.apiBaseUrl}</code>
          </div>
          <div className="flex items-center justify-between">
            <span className="font-size-sm font-medium">前端 URL:</span>
            <code className="font-size-xs bg-background px-2 py-1 rounded">{currentEnv.frontendUrl}</code>
          </div>
          <div className="flex items-center justify-between">
            <span className="font-size-sm font-medium">官方網站:</span>
            <div className="flex items-center gap-1">
              <code className="font-size-xs bg-background px-2 py-1 rounded">{currentEnv.officialWebsite.homeUrl}</code>
              <Button size="sm" variant="outline" onClick={() => openInNewTab(currentEnv.officialWebsite.homeUrl)} className="h-6 w-6 p-0" title="前往官方網站">
                <ExternalLink className="h-3 w-3" />
              </Button>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="font-size-sm font-medium">官方登入:</span>
            <div className="flex items-center gap-1">
              <code className="font-size-xs bg-background px-2 py-1 rounded">{currentEnv.officialWebsite.loginUrl}</code>
              <Button
                size="sm"
                variant="outline"
                onClick={() => openInNewTab(currentEnv.officialWebsite.loginUrl)}
                className="h-6 w-6 p-0"
                title="前往官方登入頁">
                <ExternalLink className="h-3 w-3" />
              </Button>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="font-size-sm font-medium">調試模式:</span>
            <Badge variant={currentEnv.features.debugMode ? "default" : "secondary"}>{currentEnv.features.debugMode ? "啟用" : "停用"}</Badge>
          </div>
        </div>

        {/* 快速登入連結 */}
        {showQuickLinks && quickLinks.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="font-size-sm font-medium">快速登入連結</span>
              <Badge variant="outline" className="font-size-xs">
                測試用
              </Badge>
            </div>

            <div className="space-y-2">
              {quickLinks.map((link) => (
                <div key={link.role} className="flex items-center gap-2 p-2 bg-muted rounded">
                  <div className="flex-1">
                    <div className="font-size-sm font-medium">{link.name}</div>
                    <code className="font-size-xs text-muted-foreground break-all">{link.url}</code>
                  </div>

                  <div className="flex gap-1">
                    <Button size="sm" variant="outline" onClick={() => copyToClipboard(link.url, link.role)} className="h-8 w-8 p-0">
                      {copiedUrl === link.role ? <Check className="h-3 w-3 text-green-600" /> : <Copy className="h-3 w-3" />}
                    </Button>

                    <Button size="sm" variant="outline" onClick={() => openInNewTab(link.url)} className="h-8 w-8 p-0">
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 環境警告 */}
        {currentEnv.name === "production" && (
          <Alert>
            <AlertDescription className="font-size-sm">⚠️ 您正在使用正式環境，請確保您有正確的權限和憑證。</AlertDescription>
          </Alert>
        )}

        {currentEnv.name === "testing" && (
          <Alert>
            <AlertDescription className="font-size-sm">🧪 您正在使用測試環境，此環境的資料可能會被重置。</AlertDescription>
          </Alert>
        )}

        {/* 操作按鈕 */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              environmentManager.reset();
              setCurrentEnv(getCurrentEnvironment());
              authService.updateApiBaseUrl();
            }}>
            重置為預設
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              console.log("🔍 [Environment] 當前環境資訊:", {
                environment: currentEnv,
                authServiceUrl: authService.getCurrentApiUrl(),
                localStorage: localStorage.getItem("ecocampus_environment"),
              });
            }}>
            顯示調試資訊
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnvironmentSelector;
