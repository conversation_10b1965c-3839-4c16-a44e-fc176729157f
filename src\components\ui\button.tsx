import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

/*
button.tsx — 客製按鈕組件button
這是你專案裡的主要按鈕元件，有 variant + size 控制樣式。

<Button variant="primary" size="lg">送出</Button>
支援的樣式：

variant: default, primary, secondary, outline

size: sm, default, lg

這個也用了 cva() 寫得比較乾淨，可用 asChild 替代成其它 tag，例如 <Link>。
*/
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-semibold font-size-base ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-gray-700 text-white hover:bg-gray-800",
        primary: "bg-green-700 text-white hover:bg-green-800",
        secondary:
          "bg-white border border-gray-400 text-gray-900 hover:bg-gray-100",
        outline:
          "border border-green-800 bg-white text-green-900 hover:bg-green-50",
        link: "text-green-700 hover:underline p-0 bg-transparent",
      },
      size: {
        default: "h-10 px-6 py-2",
        sm: "h-8 px-4 py-1",
        lg: "h-12 px-8 py-3 font-size-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
