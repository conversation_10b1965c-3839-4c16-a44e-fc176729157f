// ========== 儀表板相關常數 ==========

// 認證等級映射
export const CERTIFICATION_LEVEL_NAMES: Record<number, string> = {
  1: "銅牌",
  2: "銀牌",
  3: "綠旗",
  4: "白金",
  5: "鑽石",
  6: "頂級",
};

// 審核狀態
export const DASHBOARD_REVIEW_STATUS = {
  UNDER_REVIEW: 0,
  APPROVED: 1,
  REJECTED: 2,
  NEEDS_SUPPLEMENT: 3,
  NOT_SUBMITTED: 4,
} as const;

// 資料狀態
export const DASHBOARD_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;

// 允許查看統計的角色
export const AUTHORIZED_ROLES = ["Government", "Tutor"] as const;

// 預設限制值
export const DEFAULT_LIMITS = {
  LATEST_CERTIFICATIONS: 6,
  SCHOOL_ARTICLES: 5,
  PASSED_CERTIFICATIONS: 10,
} as const;

// 語言代碼
export const LOCALE_CODE = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;

// 錯誤訊息
export const DASHBOARD_ERROR_MESSAGES = {
  INVALID_TOKEN: "無效的使用者令牌",
  INSUFFICIENT_PERMISSIONS: "權限不足",
  EPA_TUTOR_ONLY: "僅限縣市政府和輔導人員查看統計資料",
  SCHOOL_ONLY: "僅限學校身份查看此資料",
  CITY_NOT_FOUND: "找不到指定的縣市",
  USER_CITY_NOT_FOUND: "找不到用戶的縣市資料",
  SCHOOL_NOT_FOUND: "找不到學校資料或您沒有權限",
  USER_NOT_AUTHENTICATED: "使用者未認證",
  NO_CURRENT_CERTIFICATION: "目前沒有申請中的認證",
  NO_SCHOOL_ASSIGNED: "該帳號尚未分配學校",
  INTERNAL_ERROR: "內部系統錯誤",
} as const;

// 成功訊息
export const DASHBOARD_SUCCESS_MESSAGES = {
  TEST_SUCCESS: "測試成功",
  CITY_STATISTICS_SUCCESS: "縣市統計資料獲取成功",
  LATEST_CERTIFICATIONS_SUCCESS: "最新認證資料獲取成功",
  SCHOOL_CERTIFICATION_SUCCESS: "學校申請中認證獲取成功",
  SCHOOL_ARTICLES_SUCCESS: "學校文章獲取成功",
  SCHOOL_PASSED_CERTIFICATIONS_SUCCESS: "學校已通過認證獲取成功",
} as const;

// 工具函數：獲取認證等級名稱
export const getCertificationLevelName = (level: number): string => {
  return CERTIFICATION_LEVEL_NAMES[level] || "未知";
};

// 工具函數：檢查是否為授權角色
export const isAuthorizedRole = (roleType: string): boolean => {
  return AUTHORIZED_ROLES.includes(roleType as (typeof AUTHORIZED_ROLES)[number]);
};

// 工具函數：檢查是否為學校角色
export const isSchoolRole = (roleType: string): boolean => {
  return roleType === "School";
};

// 工具函數：格式化日期
export const formatDate = (date: Date | string): string => {
  if (!date) return "";
  return new Date(date).toISOString().split("T")[0];
};

// 工具函數：格式化統計結果
export const formatStatisticsResult = (
  cityInfo: { cityId: number; cityName: string },
  statistics: {
    totalSchools?: number;
    bronzeCount?: number;
    silverCount?: number;
    greenFlagCount?: number;
  } | null
) => {
  return {
    cityId: cityInfo.cityId,
    cityName: cityInfo.cityName,
    bronzeCount: statistics?.bronzeCount || 0,
    silverCount: statistics?.silverCount || 0,
    greenFlagCount: statistics?.greenFlagCount || 0,
    totalSchools: statistics?.totalSchools || 0,
  };
};

// 工具函數：獲取測試資料
export const getTestData = () => {
  return {
    cityId: 1,
    cityName: "基隆市",
    bronzeCount: 1,
    silverCount: 1,
    greenFlagCount: 0,
    totalSchools: 2,
  };
};
