import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// 假資料 - 認證種類
const certificationTypes = [
  { id: "bronze", label: "銅牌", checked: true },
  { id: "silver", label: "銀牌", checked: true },
  { id: "green", label: "綠旗", checked: false },
];

// 假資料 - 環境路徑
const environmentalPaths = [
  { id: "traffic", label: "交通" },
  { id: "energy", label: "能源" },
  { id: "water", label: "水" },
  { id: "climate", label: "氣候變遷" },
  { id: "school_grounds", label: "學校場地" },
  { id: "consumption", label: "消費與環保物品" },
  { id: "healthy_living", label: "健康生活" },
  { id: "healthy_campus", label: "健康校園" },
  { id: "biodiversity", label: "生物多樣性" },
  { id: "sustainable_food", label: "永續食物" },
  { id: "forest", label: "森林" },
  { id: "watershed", label: "流域、海洋、濕地" },
];

// 假資料 - 學校列表
const schoolsData = [
  {
    id: 1,
    name: "市立信義國小",
    bronze: { date: "2016-10-13", renewal: "R1" },
    silver: { date: "2018-10-31", renewal: "R2" },
    green: { date: "", renewal: "R3" },
  },
  {
    id: 2,
    name: "市立長樂國小",
    bronze: { date: "2021-10-17", renewal: "R1" },
    silver: { date: "", renewal: "R2" },
    green: { date: "", renewal: "R3" },
  },
];

// 排序選項
const sortOptions = [
  { value: "name", label: "校名" },
  { value: "bronze_date", label: "銅牌取得日期" },
  { value: "silver_date", label: "銀牌取得日期" },
  { value: "green_date", label: "綠旗取得日期" },
];

const SchoolManagementPage = () => {
  const [selectedCertifications, setSelectedCertifications] = useState<
    string[]
  >(["bronze", "silver"]);
  const [selectedPaths, setSelectedPaths] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState("name");

  // 處理認證種類選擇
  const handleCertificationChange = (certId: string, checked: boolean) => {
    if (checked) {
      setSelectedCertifications([...selectedCertifications, certId]);
    } else {
      setSelectedCertifications(
        selectedCertifications.filter((id) => id !== certId)
      );
    }
  };

  // 處理環境路徑選擇
  const handlePathChange = (pathId: string, checked: boolean) => {
    if (checked) {
      setSelectedPaths([...selectedPaths, pathId]);
    } else {
      setSelectedPaths(selectedPaths.filter((id) => id !== pathId));
    }
  };

  // 渲染認證狀態
  const renderCertificationStatus = (
    cert: { date: string; renewal: string },
    type: string
  ) => {
    if (!cert.date) {
      return <span className="text-gray-400">-- / -- / --</span>;
    }

    const typeLabel =
      type === "bronze" ? "銅牌" : type === "silver" ? "銀牌" : "綠旗";
    return (
      <div className="text-center">
        <div className="font-medium">
          {typeLabel} {cert.date}
        </div>
        <div className="font-size-sm text-gray-500">
          {cert.renewal} -- / -- / --
        </div>
      </div>
    );
  };

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted py-6">
      <div className="container mx-auto px-4 max-w-7xl">
        <h1 className="font-size-3xl font-bold text-primary mb-8">學校資訊</h1>

        {/* 搜尋篩選區塊 */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 認證種類篩選 */}
              <div>
                <Label className="font-size-base font-semibold mb-4 block">
                  通過的認證種類
                </Label>
                <div className="flex flex-wrap gap-4">
                  {certificationTypes.map((cert) => (
                    <div key={cert.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={cert.id}
                        checked={selectedCertifications.includes(cert.id)}
                        onCheckedChange={(checked) =>
                          handleCertificationChange(cert.id, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={cert.id}
                        className="font-size-sm font-normal"
                      >
                        {cert.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* 環境路徑篩選 */}
              <div>
                <Label className="font-size-base font-semibold mb-4 block">
                  環境路徑
                </Label>
                <div className="grid grid-cols-2 gap-3">
                  {environmentalPaths.map((path) => (
                    <div key={path.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={path.id}
                        checked={selectedPaths.includes(path.id)}
                        onCheckedChange={(checked) =>
                          handlePathChange(path.id, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={path.id}
                        className="font-size-sm font-normal"
                      >
                        {path.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 學校列表區塊 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="font-size-xl">基隆市的生態學校</CardTitle>
              <div className="flex items-center gap-2">
                <Label htmlFor="sort-select" className="font-size-sm">
                  排序方式
                </Label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger id="sort-select" className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {schoolsData.map((school) => (
                <div key={school.id} className="border rounded-lg p-6 bg-white">
                  <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 items-center">
                    {/* 學校名稱 */}
                    <div className="lg:col-span-1">
                      <h3 className="font-semibold font-size-lg text-primary">
                        {school.name}
                      </h3>
                    </div>

                    {/* 認證狀態 */}
                    <div className="lg:col-span-3">
                      <div className="grid grid-cols-3 gap-4 font-size-sm">
                        {renderCertificationStatus(school.bronze, "bronze")}
                        {renderCertificationStatus(school.silver, "silver")}
                        {renderCertificationStatus(school.green, "green")}
                      </div>
                    </div>

                    {/* 操作按鈕 */}
                    <div className="lg:col-span-1 flex justify-end">
                      <Link to={`/school-management/detail/${school.id}`}>
                        <Button variant="outline" size="sm">
                          查看紀錄
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  );
};

export default SchoolManagementPage;
