import sql from "mssql";
import { FileConfigManager } from "./config-manager.js";
import { UserProfile } from "../models/user.js";

// SQL Server 資料庫配置
interface DatabaseConfig {
  server: string;
  user: string;
  password: string;
  database: string;
  port: number;
  options: {
    encrypt: boolean;
    trustServerCertificate: boolean;
    enableArithAbort: boolean;
    instanceName?: string;
  };
  pool: {
    max: number;
    min: number;
    idleTimeoutMillis: number;
    acquireTimeoutMillis: number;
  };
  requestTimeout: number;
  connectionTimeout: number;
}

const getConfig = (): DatabaseConfig => {
  const configManager = FileConfigManager.getInstance();
  const dbConfig = configManager.getDatabaseConfig();

  return {
    server: dbConfig.host,
    user: dbConfig.user,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
    options: {
      encrypt: false, // 開發環境通常不需要加密
      trustServerCertificate: true,
      enableArithAbort: true,
    },
    pool: {
      max: 10,
      min: 0,
      idleTimeoutMillis: 30000,
      acquireTimeoutMillis: 60000,
    },
    requestTimeout: 30000,
    connectionTimeout: 15000,
  };
};

// 連接池
let pool: sql.ConnectionPool | null = null;

// 初始化資料庫連接
export const initDatabase = async (): Promise<void> => {
  try {
    if (pool) {
      await pool.close();
    }

    const config = getConfig();
    pool = new sql.ConnectionPool(config);

    await pool.connect();

    // 測試查詢以確保連接正常
    const request = pool.request();
    await request.query("SELECT 1");
  } catch (error) {
    console.error("❌ SQL Server 連接失敗:", error);
    throw error;
  }
};

// 獲取連接池
export const getPool = (): sql.ConnectionPool => {
  if (!pool || !pool.connected) {
    throw new Error("SQL Server 連接池尚未初始化或已斷開");
  }
  return pool;
};

// 執行查詢的輔助函數
export const executeQuery = async <T = unknown>(query: string, params: Record<string, unknown> = {}): Promise<T[]> => {
  try {
    const poolConnection = getPool();
    const request = poolConnection.request();

    // 添加參數
    Object.keys(params).forEach((key) => {
      request.input(key, params[key]);
    });

    const result = await request.query(query);
    return result.recordset as T[];
  } catch (error) {
    console.error("查詢執行失敗:", { query, params, error });
    throw error;
  }
};

// 執行單個查詢並返回第一行
export const executeQuerySingle = async <T = unknown>(query: string, params: Record<string, unknown> = {}): Promise<T | null> => {
  const results = await executeQuery<T>(query, params);
  return results.length > 0 ? results[0] : null;
};

// 執行插入並返回插入的 ID
export const executeInsert = async (query: string, params: Record<string, unknown> = {}): Promise<number> => {
  try {
    const poolConnection = getPool();
    const request = poolConnection.request();

    // 添加參數
    Object.keys(params).forEach((key) => {
      request.input(key, params[key]);
    });

    // 在 SQL Server 中，我們需要使用 OUTPUT INSERTED.id 或 SCOPE_IDENTITY()
    const result = await request.query(query);

    // 如果查詢包含 OUTPUT 子句，返回插入的 ID
    if (result.recordset && result.recordset.length > 0) {
      // 嘗試不同的欄位名稱格式 (大小寫不同)
      return result.recordset[0].Id || result.recordset[0].id || result.recordset[0].CertificationId || result.recordset[0].sid || result.recordset[0].Sid;
    }

    // 否則使用 SCOPE_IDENTITY()
    const identityResult = await request.query("SELECT SCOPE_IDENTITY() as id");
    return identityResult.recordset[0]?.id || 0;
  } catch (error) {
    console.error("插入執行失敗:", { query, params, error });
    throw error;
  }
};

// 執行更新並返回影響的行數
export const executeUpdate = async (query: string, params: Record<string, unknown> = {}): Promise<number> => {
  try {
    const poolConnection = getPool();
    const request = poolConnection.request();

    // 添加參數
    Object.keys(params).forEach((key) => {
      request.input(key, params[key]);
    });

    const result = await request.query(query);
    return result.rowsAffected[0] || 0;
  } catch (error) {
    console.error("更新執行失敗:", { query, params, error });
    throw error;
  }
};

// 開始事務
export const beginTransaction = async (): Promise<sql.Transaction> => {
  const poolConnection = getPool();
  const transaction = new sql.Transaction(poolConnection);
  await transaction.begin();
  return transaction;
};

// 提交事務
export const commitTransaction = async (transaction: sql.Transaction): Promise<void> => {
  await transaction.commit();
};

// 回滾事務
export const rollbackTransaction = async (transaction: sql.Transaction): Promise<void> => {
  await transaction.rollback();
};

// 關閉連接池
export const closeDatabase = async (): Promise<void> => {
  if (pool && pool.connected) {
    await pool.close();
    pool = null;
    console.log("✅ SQL Server 連接池已關閉");
  }
};

// 探索資料庫結構的函數
export const exploreDatabase = async (): Promise<{
  tables: TableInfo[];
  views: ViewInfo[];
  procedures: ProcedureInfo[];
}> => {
  try {
    const poolConnection = getPool();
    const request = poolConnection.request();

    // 查詢所有表格
    const tablesQuery = `
      SELECT 
        TABLE_SCHEMA,
        TABLE_NAME,
        TABLE_TYPE
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_SCHEMA, TABLE_NAME
    `;
    const tablesResult = await request.query(tablesQuery);

    // 查詢所有視圖
    const viewsQuery = `
      SELECT 
        TABLE_SCHEMA,
        TABLE_NAME,
        VIEW_DEFINITION
      FROM INFORMATION_SCHEMA.VIEWS
      ORDER BY TABLE_SCHEMA, TABLE_NAME
    `;
    const viewsResult = await request.query(viewsQuery);

    // 查詢所有預存程序
    const proceduresQuery = `
      SELECT 
        ROUTINE_SCHEMA,
        ROUTINE_NAME,
        ROUTINE_TYPE,
        ROUTINE_DEFINITION
      FROM INFORMATION_SCHEMA.ROUTINES
      WHERE ROUTINE_TYPE = 'PROCEDURE'
      ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
    `;
    const proceduresResult = await request.query(proceduresQuery);

    // 為每個表格獲取詳細的欄位資訊
    const tables: TableInfo[] = [];
    for (const table of tablesResult.recordset) {
      const columnsQuery = `
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          CHARACTER_MAXIMUM_LENGTH,
          NUMERIC_PRECISION,
          NUMERIC_SCALE,
          ORDINAL_POSITION
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = '${table.TABLE_SCHEMA}' 
          AND TABLE_NAME = '${table.TABLE_NAME}'
        ORDER BY ORDINAL_POSITION
      `;
      const columnsResult = await poolConnection.request().query(columnsQuery);

      // 查詢主鍵資訊
      const primaryKeyQuery = `
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = '${table.TABLE_SCHEMA}' 
          AND TABLE_NAME = '${table.TABLE_NAME}'
          AND CONSTRAINT_NAME LIKE 'PK_%'
        ORDER BY ORDINAL_POSITION
      `;
      const primaryKeyResult = await poolConnection.request().query(primaryKeyQuery);

      // 查詢外鍵資訊 (修正 SQL Server 語法)
      const foreignKeyQuery = `
        SELECT 
          fk.COLUMN_NAME,
          pk.TABLE_SCHEMA as REFERENCED_TABLE_SCHEMA,
          pk.TABLE_NAME as REFERENCED_TABLE_NAME,
          pk.COLUMN_NAME as REFERENCED_COLUMN_NAME,
          fk.CONSTRAINT_NAME
        FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
        INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE fk 
          ON rc.CONSTRAINT_NAME = fk.CONSTRAINT_NAME
        INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE pk 
          ON rc.UNIQUE_CONSTRAINT_NAME = pk.CONSTRAINT_NAME
        WHERE fk.TABLE_SCHEMA = '${table.TABLE_SCHEMA}' 
          AND fk.TABLE_NAME = '${table.TABLE_NAME}'
      `;
      const foreignKeyResult = await poolConnection.request().query(foreignKeyQuery);

      tables.push({
        schema: table.TABLE_SCHEMA,
        name: table.TABLE_NAME,
        columns: columnsResult.recordset,
        primaryKeys: primaryKeyResult.recordset.map((pk) => pk.COLUMN_NAME),
        foreignKeys: foreignKeyResult.recordset,
      });
    }

    return {
      tables,
      views: viewsResult.recordset,
      procedures: proceduresResult.recordset,
    };
  } catch (error) {
    console.error("探索資料庫結構失敗:", error);
    throw error;
  }
};

// 資料庫結構介面定義
export interface TableInfo {
  schema: string;
  name: string;
  columns: ColumnInfo[];
  primaryKeys: string[];
  foreignKeys: ForeignKeyInfo[];
}

export interface ColumnInfo {
  COLUMN_NAME: string;
  DATA_TYPE: string;
  IS_NULLABLE: string;
  COLUMN_DEFAULT: string | null;
  CHARACTER_MAXIMUM_LENGTH: number | null;
  NUMERIC_PRECISION: number | null;
  NUMERIC_SCALE: number | null;
  ORDINAL_POSITION: number;
}

export interface ForeignKeyInfo {
  COLUMN_NAME: string;
  REFERENCED_TABLE_SCHEMA: string;
  REFERENCED_TABLE_NAME: string;
  REFERENCED_COLUMN_NAME: string;
  CONSTRAINT_NAME: string;
}

export interface ViewInfo {
  TABLE_SCHEMA: string;
  TABLE_NAME: string;
  VIEW_DEFINITION: string;
}

export interface ProcedureInfo {
  ROUTINE_SCHEMA: string;
  ROUTINE_NAME: string;
  ROUTINE_TYPE: string;
  ROUTINE_DEFINITION: string;
}

// EcoCampus_Maria 資料表結構定義 (基於實際發現的表格)
export interface CustomMemberRow {
  sid: number;
  groups_sid?: number;
  account: string;
  citizen_digital_number?: string;
  password: string;
  password_salt: string;
  member_cname: string;
  member_cname_en?: string;
  member_address?: string;
  member_tel?: string;
  member_phone?: string;
  member_email: string;
  member_url?: string;
  city_sid?: number;
  area_sid?: number;
  job_sid?: number;
  place_sid?: number;
  member_role: string;
  isuse: number;
  is_del: number;
  createdate: Date;
  updatedate: Date;
}

export interface CustomCertificationRow {
  sid: number;
  member_sid: number;
  level: number;
  review: string;
  reviewdate?: Date;
  passdate?: Date;
  certificate_sid?: number;
  is_del: number;
  createdate: Date;
  updatedate: Date;
}

export interface QuestionRow {
  QuestionId: number;
  Title: string;
  ParentQuestionId: number | null;
  StepNumber: number | null;
  IsRenewed: boolean;
  CreatedTime: Date;
  CreatedUserId: string;
  UpdatedTime: Date | null;
  UpdatedUserId: string | null;
  Status: number;
  DeletedTime: Date | null;
  DeletedUserId: string | null;
  SortOrder: number;
  QuestionTemplate: number | null;
}

export interface CustomCertificationAnswerRow {
  sid: number;
  certification_sid: number;
  question_sid: number;
  use_certification_answer_record_sid: number;
  sequence: number;
  createdate: Date;
  updatedate: Date;
}

// 新版 Ecocampus 資料庫介面定義 (基於實際資料庫結構)

// Account 表格 - 會員帳號資料
export interface AccountRow {
  Id: string; // bigint, 主鍵
  CmsUserId: string; // bigint
  Account: string; // nvarchar(50)
  NickName?: string; // nvarchar(100)
  Password: string; // nvarchar(255)
  Email?: string; // nvarchar(255)
  Phone?: string; // nvarchar(20)
  Avatar?: string; // nvarchar(255)
  IsActive: boolean; // bit
  RoleType: string; // nvarchar(50) - 如 'School', 'Government', 'Tutor' (已棄用，改用布林欄位)
  CreatedTime: Date; // datetime2
  UpdatedTime?: Date; // datetime2
  CreatedUserId?: string; // bigint
  UpdatedUserId?: string; // bigint
  Remark?: string; // nvarchar(500)

  // 🆕 新增：角色判斷布林欄位
  is_school_partner?: boolean; // bit - 是否為學校夥伴身份
  is_epa_user?: boolean; // bit - 是否為環保署使用者身份
  is_guidance_team?: boolean; // bit - 是否為輔導團隊身份
}

// UserToken 表格 - 令牌管理
export interface UserTokenRow {
  Id: string; // bigint, 主鍵
  UserId: string; // bigint, 外鍵到 Account.Id
  Token: string; // nvarchar(500)
  TokenType: string; // nvarchar(50) - 如 'Login', 'API', 'External'
  ExpiredTime?: Date; // datetime2
  IsActive: boolean; // bit
  CreatedTime: Date; // datetime2
  UpdatedTime?: Date; // datetime2
  CreatedUserId?: string; // bigint
  UpdatedUserId?: string; // bigint
  Remark?: string; // nvarchar(500)
}

// permission 表格 - 權限定義 (實際表格名稱)
export interface PermissionRow {
  sid: string; // bigint, 主鍵
  cname: string; // nvarchar(100) - 權限名稱
  ename: string; // nvarchar(50) - 權限代碼
  remark?: string; // nvarchar(500) - 描述
  isuse: number; // int - 是否啟用 (1=啟用, 0=停用)
  dataStatus: number; // tinyint - 資料狀態
  createTime: Date; // datetime2
  updateTime?: Date; // datetime2
  createUser?: string; // bigint
  updateUser?: string; // bigint
}

// permission_group 表格 - 權限群組 (實際表格名稱)
export interface PermissionGroupRow {
  sid: string; // bigint, 主鍵
  cname: string; // nvarchar(100) - 群組名稱
  ename: string; // nvarchar(50) - 群組代碼
  remark?: string; // nvarchar(500) - 描述
  isuse: number; // int - 是否啟用
  dataStatus: number; // tinyint - 資料狀態
  createTime: Date; // datetime2
  updateTime?: Date; // datetime2
  createUser?: string; // bigint
  updateUser?: string; // bigint
}

// permission_group_map 表格 - 權限群組權限關聯 (實際表格名稱)
export interface PermissionGroupMapRow {
  permissionSid: string; // bigint, 權限ID
  groupSid: string; // bigint, 群組ID
  createTime: Date; // datetime2
  createUser?: string; // bigint
  updateTime: Date; // datetime2
  updateUser?: string; // bigint
  dataStatus: number; // tinyint - 資料狀態
  deleteTime?: Date; // datetime2
  deleteUser?: string; // bigint
}

// account_permission_group 表格 - 會員權限群組關聯 (實際表格名稱)
export interface AccountPermissionGroupRow {
  accountSid: string; // bigint, 帳號ID
  groupSid: string; // bigint, 群組ID
  createTime: Date; // datetime2
  createUser?: string; // bigint
  updateTime: Date; // datetime2
  updateUser?: string; // bigint
  dataStatus: number; // tinyint - 資料狀態 (0=正常, 1=刪除)
  deleteTime?: Date; // datetime2
  deleteUser?: string; // bigint
}

// Certification 表格 - 認證資料 (基於實際資料庫表結構)
export interface CertificationRow {
  CertificationId: string; // bigint, 主鍵
  SchoolId: number; // int, 外鍵到 Schools.Id
  Level?: number; // tinyint
  ReviewStatus: number; // tinyint (0=審核中, 1=通過, 2=退件, 3=補件, 4=尚未審核)
  ReviewDate?: Date; // datetime2
  ApprovedDate?: Date; // datetime2
  RejectedDate?: Date; // datetime2
  SupplementationDate?: Date; // datetime2
  CertificateId?: number; // int
  RewardHistory?: string; // nvarchar(max)
  PdfFileId?: string; // uniqueidentifier
  AddType: string; // varchar(20), 預設 'Frontend'
  CreatedTime: Date; // datetime2, 預設 sysdatetime()
  CreatedUserId: string; // bigint
  UpdatedTime?: Date; // datetime2
  UpdatedUserId?: string; // bigint
  Status: number; // tinyint, 預設 1
  DeletedTime?: Date; // datetime2
  DeletedUserId?: string; // bigint
  SortOrder: number; // int, 預設 0
  ApplicantName?: string; // 顯示用欄位，來自 JOIN 查詢
}

// School 表格 - 學校資料
export interface SchoolRow {
  Id: string; // bigint, 主鍵
  Name: string; // nvarchar(255)
  EnglishName?: string; // nvarchar(255)
  Code?: string; // nvarchar(50)
  Address?: string; // nvarchar(500)
  Phone?: string; // nvarchar(20)
  Email?: string; // nvarchar(255)
  Website?: string; // nvarchar(255)
  ContactPerson?: string; // nvarchar(100)
  ContactPhone?: string; // nvarchar(20)
  ContactEmail?: string; // nvarchar(255)
  CityId?: string; // bigint
  AreaId?: string; // bigint
  SchoolType?: string; // nvarchar(50)
  EstablishmentType?: string; // nvarchar(50)
  StudentCount?: number; // int
  IsActive: boolean; // bit
  CreatedTime: Date; // datetime2
  UpdatedTime?: Date; // datetime2
  CreatedUserId?: string; // bigint
  UpdatedUserId?: string; // bigint
  Remark?: string; // nvarchar(500)
}

// Article 表格 - 文章/新聞資料
export interface ArticleRow {
  Id: string; // bigint, 主鍵
  Title: string; // nvarchar(255)
  Content?: string; // nvarchar(max)
  Summary?: string; // nvarchar(500)
  AuthorId: string; // bigint, 外鍵到 Account.Id
  Category?: string; // nvarchar(100)
  Tags?: string; // nvarchar(500)
  Status: string; // nvarchar(50)
  PublishDate?: Date; // datetime2
  ViewCount: number; // int
  IsActive: boolean; // bit
  CreatedTime: Date; // datetime2
  UpdatedTime?: Date; // datetime2
  CreatedUserId?: string; // bigint
  UpdatedUserId?: string; // bigint
}

// Token 驗證回應介面
export interface TokenValidationResult {
  valid: boolean;
  user?: UserProfile;
  message?: string;
}

// 🆕 輔助函數：根據 Accounts 資料表的整數欄位判斷角色類型
function determineRoleTypeFromIntegerFields(IsSchoolPartner?: number, IsEpaUser?: number, IsGuidanceTeam?: number): string {
  // 按優先順序判斷角色，如果多個為 1，取第一個匹配的
  if (IsSchoolPartner === 1) {
    return "School";
  }
  if (IsEpaUser === 1) {
    return "Government";
  }
  if (IsGuidanceTeam === 1) {
    return "Tutor";
  }

  // 如果都沒有設定或都為 0，預設為學校身份
  return "School";
}

// 🆕 輔助函數：將角色類型轉換為前端識別的角色字串
function mapRoleTypeToFrontendRole(roleType: string): string {
  switch (roleType) {
    case "School":
      return "school";
    case "Government":
      return "epa";
    case "Tutor":
      return "tutor";
    default:
      return "school";
  }
}

// 🔄 基於 TokenType 的混合身份映射（更新為使用 Account 表的布林欄位）

export default getConfig;
