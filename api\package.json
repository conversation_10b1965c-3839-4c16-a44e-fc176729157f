{"name": "ecocampus-api", "version": "1.0.0", "description": "EcoCampus API Server", "main": "server.ts", "type": "module", "scripts": {"start": "tsx server.ts", "dev": "tsx watch server.ts", "build": "tsc", "test": "jest", "docs": "tsx tools/api-documentation-generator.ts", "docs:generate": "node tools/generate-api-docs.js", "docs:watch": "tsx watch tools/api-documentation-generator.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "mssql": "^10.0.1", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "crypto": "^1.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "tsx": "^4.6.0", "typescript": "^5.3.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["ecocampus", "api", "express", "typescript", "mssql"], "author": "EcoCampus Team", "license": "MIT"}