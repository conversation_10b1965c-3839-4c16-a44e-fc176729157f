/**
 * useTemporaryToken Hook - 提供 React 元件中使用臨時 Token 的便捷方法
 * 
 * 主要功能：
 * - 提供 withTemporaryToken 高階函數
 * - Token 狀態管理和監控
 * - 錯誤處理和清理機制
 * - TypeScript 類型安全支援
 */

import { useCallback, useEffect, useState } from 'react';
import { authService } from '@/services/authService';
import { TokenState } from '@/services/TokenManager';

/**
 * 臨時 Token Hook 選項
 */
export interface UseTemporaryTokenOptions {
  /** 是否自動清理臨時 Token */
  autoCleanup?: boolean;
  /** 監控 Token 狀態變化 */
  monitorState?: boolean;
}

/**
 * 臨時 Token Hook 返回值
 */
export interface UseTemporaryTokenResult {
  /** 當前 Token 狀態 */
  tokenState: TokenState;
  /** 使用臨時 Token 執行操作的函數 */
  withTemporaryToken: <T>(tempToken: string, callback: () => Promise<T>) => Promise<T>;
  /** 推送臨時 Token */
  pushTemporaryToken: (tempToken: string) => void;
  /** 彈出臨時 Token */
  popTemporaryToken: () => string | null;
  /** 清除所有臨時 Token */
  clearTemporaryTokens: () => void;
  /** 是否處於臨時模式 */
  isTemporaryMode: boolean;
  /** Token 堆疊深度 */
  stackDepth: number;
}

/**
 * useTemporaryToken Hook
 * 
 * @param options Hook 選項
 * @returns UseTemporaryTokenResult
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const {
 *     withTemporaryToken,
 *     isTemporaryMode,
 *     tokenState
 *   } = useTemporaryToken();
 *
 *   const handleSpecialOperation = async () => {
 *     await withTemporaryToken('special-token', async () => {
 *       // 使用特殊 Token 執行 API 調用
 *       return api.specialOperation();
 *     });
 *   };
 *
 *   return (
 *     <div>
 *       <p>臨時模式: {isTemporaryMode ? '是' : '否'}</p>
 *       <button onClick={handleSpecialOperation}>
 *         執行特殊操作
 *       </button>
 *     </div>
 *   );
 * }
 * ```
 */
export function useTemporaryToken(
  options: UseTemporaryTokenOptions = {}
): UseTemporaryTokenResult {
  const {
    autoCleanup = true,
    monitorState = true
  } = options;

  // Token 狀態管理
  const [tokenState, setTokenState] = useState<TokenState>(() => 
    authService.getTokenState()
  );

  // 監控 Token 狀態變化
  useEffect(() => {
    if (!monitorState) return;

    let intervalId: NodeJS.Timeout;

    // 定期檢查 Token 狀態
    const checkTokenState = () => {
      const newState = authService.getTokenState();
      setTokenState(newState);
    };

    // 每秒檢查一次狀態變化
    intervalId = setInterval(checkTokenState, 1000);

    // 組件卸載時清理
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [monitorState]);

  // 組件卸載時自動清理
  useEffect(() => {
    return () => {
      if (autoCleanup) {
        try {
          authService.clearTemporaryTokens();
        } catch (error) {
          console.warn('⚠️ [useTemporaryToken] 自動清理失敗:', error);
        }
      }
    };
  }, [autoCleanup]);

  // withTemporaryToken 方法
  const withTemporaryToken = useCallback(
    async <T>(tempToken: string, callback: () => Promise<T>): Promise<T> => {
      try {
        const result = await authService.withTemporaryToken(tempToken, callback);
        
        // 更新狀態
        if (monitorState) {
          setTokenState(authService.getTokenState());
        }
        
        return result;
      } catch (error) {
        console.error('❌ [useTemporaryToken] 臨時 Token 操作失敗:', error);
        
        // 更新狀態
        if (monitorState) {
          setTokenState(authService.getTokenState());
        }
        
        throw error;
      }
    },
    [monitorState]
  );

  // pushTemporaryToken 方法
  const pushTemporaryToken = useCallback(
    (tempToken: string) => {
      try {
        authService.pushTemporaryToken(tempToken);
        
        if (monitorState) {
          setTokenState(authService.getTokenState());
        }
      } catch (error) {
        console.error('❌ [useTemporaryToken] 推送臨時 Token 失敗:', error);
        throw error;
      }
    },
    [monitorState]
  );

  // popTemporaryToken 方法
  const popTemporaryToken = useCallback(
    (): string | null => {
      try {
        const result = authService.popTemporaryToken();
        
        if (monitorState) {
          setTokenState(authService.getTokenState());
        }
        
        return result;
      } catch (error) {
        console.error('❌ [useTemporaryToken] 彈出臨時 Token 失敗:', error);
        return null;
      }
    },
    [monitorState]
  );

  // clearTemporaryTokens 方法
  const clearTemporaryTokens = useCallback(
    () => {
      try {
        authService.clearTemporaryTokens();
        
        if (monitorState) {
          setTokenState(authService.getTokenState());
        }
      } catch (error) {
        console.error('❌ [useTemporaryToken] 清除臨時 Token 失敗:', error);
      }
    },
    [monitorState]
  );

  return {
    tokenState,
    withTemporaryToken,
    pushTemporaryToken,
    popTemporaryToken,
    clearTemporaryTokens,
    isTemporaryMode: tokenState.isTemporaryMode,
    stackDepth: tokenState.stackDepth
  };
}

/**
 * withTemporaryToken 高階元件（HOC）
 * 
 * @param WrappedComponent 要包裝的元件
 * @returns 包裝後的元件
 * 
 * @example
 * ```tsx
 * const EnhancedComponent = withTemporaryToken(MyComponent);
 * ```
 */
export function withTemporaryToken<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  const WithTemporaryTokenComponent = (props: P) => {
    const temporaryTokenProps = useTemporaryToken();

    return (
      <WrappedComponent 
        {...props} 
        {...temporaryTokenProps} 
      />
    );
  };

  WithTemporaryTokenComponent.displayName = 
    `withTemporaryToken(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithTemporaryTokenComponent;
}

export default useTemporaryToken;