import React, { useState } from "react";
import { <PERSON>, useParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft } from "lucide-react";
import { buildAssetUrl, resolveApiAssetUrl } from "@/utils/pathUtils";

// 假資料 - 學校詳細資訊
const schoolDetailData = {
  id: 1,
  account: "ShinYi",
  chineseName: "市立信義國小",
  englishName: "Shin Yi Elementary School",
  address: "200基隆市仁愛區仁二路135號",
  phone: "",
  website: "http://www.syps.kl.edu.tw",
  logo: "/placeholder.svg", // 預設校徽圖片
  introduction: "尚無學校介紹",

  // 校長資訊
  principal: {
    name: "賴麗雯",
    phone: "(02) *********80",
    mobile: "*********",
    email: "<EMAIL>",
  },

  // 聯絡人資訊
  contact: {
    name: "陳金蓮老師/陳立玲主任",
    phone: "02-********",
    mobile: "**********/ ********",
    email: "<EMAIL>",
  },

  // 師生統計
  statistics: {
    reportDate: "",
    staff: 0,
    grades: {
      elementary: [228, 236, 255, 258, 231, 277], // 一到六年級
      middle: [0, 0, 0], // 七到九年級
      high: [0, 0, 0], // 高中一到三年級
    },
  },

  // 得獎歷程
  awards: [
    {
      type: "銀牌",
      submitDate: "-- / -- / --",
      renewalDate: "-- / -- / --",
      passDate: "2018-10-31",
      status: "通過",
      icon: "/img/medal-silver.png",
    },
    {
      type: "銅牌",
      submitDate: "-- / -- / --",
      renewalDate: "-- / -- / --",
      passDate: "2016-10-13",
      status: "通過",
      icon: "/img/medal-bronze.png",
    },
  ],
};

const SchoolDetailPage = () => {
  const { schoolId } = useParams<{ schoolId: string }>();
  const [activeTab, setActiveTab] = useState("basic");

  // 在實際應用中，這裡會根據 schoolId 從 API 獲取學校資料
  const school = schoolDetailData;

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted py-6">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* 返回按鈕 */}
        <div className="mb-6">
          <Link to="/school-management">
            <Button variant="outline" size="sm" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              回上一頁
            </Button>
          </Link>
        </div>

        {/* 學校標題資訊 */}
        <div className="flex items-center gap-4 mb-8">
          <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
            <img
              src={
                school.logo.startsWith("http")
                  ? school.logo
                  : resolveApiAssetUrl(school.logo)
              }
              alt="校徽"
              className="w-full h-full object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                if (target.src !== buildAssetUrl("img/placeholder.svg")) {
                  target.src = buildAssetUrl("img/placeholder.svg");
                }
              }}
            />
          </div>
          <div>
            <h1 className="font-size-3xl font-bold text-primary">
              {school.chineseName}
            </h1>
            <p className="font-size-lg text-muted-foreground">
              {school.englishName}
            </p>
          </div>
        </div>

        {/* 主要內容 Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">基本資訊</TabsTrigger>
            <TabsTrigger value="awards">得獎歷程</TabsTrigger>
          </TabsList>

          {/* 基本資訊 Tab */}
          <TabsContent value="basic" className="space-y-6">
            {/* 學校資訊卡片 */}
            <Card>
              <CardHeader>
                <CardTitle>學校資訊</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        帳號
                      </label>
                      <p className="font-size-base">{school.account}</p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        中文校名
                      </label>
                      <p className="font-size-base">{school.chineseName}</p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        英文校名
                      </label>
                      <p className="font-size-base">{school.englishName}</p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        地址
                      </label>
                      <p className="font-size-base">{school.address}</p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        電話
                      </label>
                      <p className="font-size-base">
                        {school.phone || "未提供"}
                      </p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        學校網址
                      </label>
                      <p className="font-size-base">
                        {school.website ? (
                          <a
                            href={school.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {school.website}
                          </a>
                        ) : (
                          "未提供"
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        校徽
                      </label>
                      <div className="mt-2">
                        <img
                          src={
                            school.logo.startsWith("http")
                              ? school.logo
                              : resolveApiAssetUrl(school.logo)
                          }
                          alt="校徽"
                          className="w-32 h-32 object-contain border rounded-lg bg-gray-50"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            if (
                              target.src !==
                              buildAssetUrl("img/placeholder.svg")
                            ) {
                              target.src = buildAssetUrl("img/placeholder.svg");
                            }
                          }}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        學校介紹
                      </label>
                      <p className="font-size-base text-gray-500">
                        {school.introduction}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 校長資訊卡片 */}
            <Card>
              <CardHeader>
                <CardTitle>校長資訊</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        姓名
                      </label>
                      <p className="font-size-base">{school.principal.name}</p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        辦公室電話
                      </label>
                      <p className="font-size-base">{school.principal.phone}</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        手機號碼
                      </label>
                      <p className="font-size-base">
                        {school.principal.mobile}
                      </p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        電子郵件
                      </label>
                      <p className="font-size-base">{school.principal.email}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 聯絡人資訊卡片 */}
            <Card>
              <CardHeader>
                <CardTitle>聯絡人資訊</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        姓名
                      </label>
                      <p className="font-size-base">{school.contact.name}</p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        辦公室電話
                      </label>
                      <p className="font-size-base">{school.contact.phone}</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        手機號碼
                      </label>
                      <p className="font-size-base">{school.contact.mobile}</p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground">
                        電子郵件
                      </label>
                      <p className="font-size-base">{school.contact.email}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 師生統計卡片 */}
            <Card>
              <CardHeader>
                <CardTitle>師生統計</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground ">
                        填寫日期
                      </label>
                      <p className="font-size-base">
                        {school.statistics.reportDate || "未填寫"}
                      </p>
                    </div>
                    <div>
                      <label className="font-size-sm font-medium text-muted-foreground  ">
                        校內員工
                      </label>
                      <p className="font-size-base">
                        {school.statistics.staff}人
                      </p>
                    </div>
                  </div>

                  {/* 各年級學生統計 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* 國小 */}
                    <div>
                      <h4 className="font-semibold mb-3  ">國小學生人數</h4>
                      <div className="space-y-2">
                        {school.statistics.grades.elementary.map(
                          (count, index) => (
                            <div key={index} className="flex gap-10">
                              <span className="font-size-sm">
                                國小
                                {["一", "二", "三", "四", "五", "六"][index]}
                                年級
                              </span>
                              <span className="font-size-sm font-medium">
                                {count}人
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    </div>

                    {/* 國中 */}
                    <div>
                      <h4 className="font-semibold mb-3 ">國中學生人數</h4>
                      <div className="space-y-2">
                        {school.statistics.grades.middle.map((count, index) => (
                          <div key={index} className="flex gap-10">
                            <span className="font-size-sm">
                              國中{["七", "八", "九"][index]}年級
                            </span>
                            <span className="font-size-sm font-medium">
                              {count}人
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 高中 */}
                    <div>
                      <h4 className="font-semibold mb-3">高中學生人數</h4>
                      <div className="space-y-2">
                        {school.statistics.grades.high.map((count, index) => (
                          <div key={index} className="flex gap-10 ">
                            <span className="font-size-sm">
                              高中職{["一", "二", "三"][index]}年級
                            </span>
                            <span className="font-size-sm font-medium">
                              {count}人
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 得獎歷程 Tab */}
          <TabsContent value="awards" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>認證歷程</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {school.awards.map((award, index) => (
                    <div key={index} className="border rounded-lg p-6">
                      <div className="flex items-center gap-4 mb-4">
                        <img
                          src={
                            award.icon.startsWith("http")
                              ? award.icon
                              : buildAssetUrl(award.icon)
                          }
                          alt={award.type}
                          className="w-16 h-16 object-contain"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            if (
                              target.src !==
                              buildAssetUrl("img/medal-default.svg")
                            ) {
                              target.src = buildAssetUrl(
                                "img/medal-default.svg"
                              );
                            }
                          }}
                        />
                        <div>
                          <h3 className="font-size-xl font-semibold">
                            {award.type}
                          </h3>
                          <Badge
                            variant={
                              award.status === "通過" ? "default" : "secondary"
                            }
                          >
                            {award.status}
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 font-size-sm">
                        <div>
                          <label className="font-medium text-muted-foreground">
                            送審日期
                          </label>
                          <p>{award.submitDate}</p>
                        </div>
                        <div>
                          <label className="font-medium text-muted-foreground">
                            更新日期
                          </label>
                          <p>{award.renewalDate}</p>
                        </div>
                        <div>
                          <label className="font-medium text-muted-foreground">
                            通過日期
                          </label>
                          <p>{award.passDate}</p>
                        </div>
                      </div>

                      <div className="mt-4 flex justify-end">
                        <Button variant="outline" size="sm">
                          查看紀錄
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </main>
  );
};

export default SchoolDetailPage;
