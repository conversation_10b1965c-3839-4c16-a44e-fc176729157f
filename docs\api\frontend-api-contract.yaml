openapi: 3.0.3
info:
  title: EcoCampus Frontend API Contract
  description: |
    **前後端協作契約**
    
    此文件是前端與後端工程師之間的正式協作契約。
    
    **工作流程**：
    1. 前端工程師根據頁面需求，在此文件定義所需的 API 端點
    2. API 工程師根據此契約實現對應的 BFF 端點
    3. 整合測試以此契約為準，不接受口頭修改
    
    **契約更新規則**：
    - 任何 API 變更都必須先更新此契約文件
    - 前端提 PR 修改契約 → API 工程師 review → 合併後實現
    - 禁止在開發過程中口頭變更 API 規格
    
  version: 1.0.0
  contact:
    name: EcoCampus 開發團隊
    url: https://github.com/ecocampus-team
servers:
  - url: http://localhost:3001/api/bff
    description: 開發環境 BFF API
  - url: https://ecocampus.gov.tw/apply/api/bff
    description: 正式環境 BFF API

paths:
  # ===== 使用者儀表板頁面 =====
  /user-dashboard:
    get:
      tags:
        - Frontend Pages
      summary: 使用者儀表板頁面資料
      description: |
        **前端頁面**: 使用者主儀表板
        **用途**: 顯示使用者個人資訊、學校認證狀態、待辦事項
        **前端負責人**: [待填入]
        **API 負責人**: [待填入]
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 儀表板資料
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      userInfo:
                        $ref: '#/components/schemas/UserBasicInfo'
                      schoolInfo:
                        $ref: '#/components/schemas/SchoolInfo'
                      certificationSummary:
                        $ref: '#/components/schemas/CertificationSummary'
                      pendingTasks:
                        type: array
                        items:
                          $ref: '#/components/schemas/PendingTask'
                      recentActivities:
                        type: array
                        items:
                          $ref: '#/components/schemas/RecentActivity'

  # ===== 認證申請頁面 =====
  /certification-application-form:
    get:
      tags:
        - Frontend Pages
      summary: 認證申請表單初始資料
      description: |
        **前端頁面**: 認證申請表單
        **用途**: 載入表單初始資料、選項清單、使用者已填寫內容
      security:
        - BearerAuth: []
      parameters:
        - name: certificationLevel
          in: query
          schema:
            type: string
            enum: [bronze, silver, gold, green_flag]
          required: true
      responses:
        '200':
          description: 表單初始資料
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      formConfig:
                        $ref: '#/components/schemas/FormConfig'
                      prefillData:
                        $ref: '#/components/schemas/FormPrefillData'
                      dynamicOptions:
                        $ref: '#/components/schemas/DynamicOptions'

    post:
      tags:
        - Frontend Pages
      summary: 提交認證申請
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CertificationApplicationRequest'
      responses:
        '201':
          description: 申請提交成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      applicationId:
                        type: integer
                      status:
                        type: string
                      submittedAt:
                        type: string
                        format: date-time
                  message:
                    type: string

  # ===== 審查管理頁面（環保署/輔導員） =====
  /review-dashboard:
    get:
      tags:
        - Frontend Pages  
      summary: 審查者儀表板資料
      description: |
        **前端頁面**: 審查者主頁面
        **角色**: 環保署人員、輔導員
        **用途**: 顯示待審查項目、統計資料
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 審查儀表板資料
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      reviewerInfo:
                        $ref: '#/components/schemas/ReviewerInfo'
                      pendingReviews:
                        type: array
                        items:
                          $ref: '#/components/schemas/PendingReview'
                      reviewStats:
                        $ref: '#/components/schemas/ReviewStats'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 使用現有的 Token 認證系統

  schemas:
    UserBasicInfo:
      type: object
      properties:
        userId:
          type: integer
        username:
          type: string
        email:
          type: string
        role:
          type: string
          enum: [school_admin, epa_staff, counselor]
        lastLoginAt:
          type: string
          format: date-time

    SchoolInfo:
      type: object
      properties:
        schoolId:
          type: integer
        schoolName:
          type: string
        address:
          type: string
        contactPerson:
          type: string
        phone:
          type: string
        certificationCount:
          type: integer

    CertificationSummary:
      type: object
      properties:
        totalCertifications:
          type: integer
        activeCertifications:
          type: integer
        expiringSoon:
          type: integer
        currentLevel:
          type: string
          enum: [bronze, silver, gold, green_flag, none]

    PendingTask:
      type: object
      properties:
        taskId:
          type: integer
        taskType:
          type: string
        title:
          type: string
        dueDate:
          type: string
          format: date
        priority:
          type: string
          enum: [low, medium, high]

    RecentActivity:
      type: object
      properties:
        activityId:
          type: integer
        activityType:
          type: string
        description:
          type: string
        timestamp:
          type: string
          format: date-time

    FormConfig:
      type: object
      properties:
        sections:
          type: array
          items:
            type: object
        requiredFields:
          type: array
          items:
            type: string
        validationRules:
          type: object

    FormPrefillData:
      type: object
      description: 使用者已填寫的資料（草稿或從上次申請繼承）

    DynamicOptions:
      type: object
      properties:
        counties:
          type: array
          items:
            type: object
        schoolTypes:
          type: array  
          items:
            type: object

    CertificationApplicationRequest:
      type: object
      properties:
        certificationLevel:
          type: string
        applicationData:
          type: object
        attachments:
          type: array
          items:
            type: object

    ReviewerInfo:
      type: object
      properties:
        reviewerId:
          type: integer
        name:
          type: string
        role:
          type: string
        department:
          type: string

    PendingReview:
      type: object
      properties:
        reviewId:
          type: integer
        applicationId:
          type: integer
        schoolName:
          type: string
        certificationLevel:
          type: string
        submittedAt:
          type: string
          format: date-time
        priority:
          type: string

    ReviewStats:
      type: object
      properties:
        totalPending:
          type: integer
        completedThisWeek:
          type: integer
        averageReviewTime:
          type: number
        
tags:
  - name: Frontend Pages
    description: |
      **為特定前端頁面設計的 API 端點**
      
      這些端點直接對應前端的頁面需求，每個端點都應該：
      1. 包含該頁面所需的所有資料，減少多次 API 呼叫
      2. 資料結構與前端元件設計保持一致
      3. 考慮頁面載入效能，避免過多的資料傳輸