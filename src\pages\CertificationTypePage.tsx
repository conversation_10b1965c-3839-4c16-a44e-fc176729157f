import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { useAuthService } from "@/services/authService";
import { getApiBaseUrl } from "@/config/environment";
import { buildAssetUrl } from "@/utils/pathUtils";
import { certificationAPI } from "@/services/certificationAPI";

// 新增認證

interface CertificationAvailability {
  id: string;
  name: string;
  level: number;
  available: boolean;
  reason: string;
  frontendId: string;
}

interface CertificationTypeWithIcon extends CertificationAvailability {
  icon: string;
}

const CertificationTypePage = () => {
  const navigate = useNavigate();
  const authService = useAuthService();

  const [certificationTypes, setCertificationTypes] = useState<CertificationTypeWithIcon[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [creating, setCreating] = useState<string | null>(null);

  // 圖標映射函數 - 使用本地圖片資源
  const getCertificationIcon = (level: number): string => {
    if (level === 1) return buildAssetUrl("img/medal-luxury-bronze.png"); // 銅牌
    if (level === 2) return buildAssetUrl("img/medal-luxury-silver.png"); // 銀牌
    if (level >= 3) return buildAssetUrl("img/medal-luxury-green.png"); // 綠旗及其延續認證
    return buildAssetUrl("img/placeholder.svg"); // 預設圖標
  };

  useEffect(() => {
    checkCertificationAvailability();
  }, []);

  const checkCertificationAvailability = async () => {
    try {
      console.log("🔍 [前端] 檢查認證可用性");
      setLoading(true);
      setError(null);

      // 檢查認證狀態
      if (!authService.isAuthenticated()) {
        throw new Error("用戶未登入，請先登入");
      }

      // 使用 certificationAPI 服務
      const result = await certificationAPI.getCertificationAvailability();

      if (!result.success) {
        throw new Error(result.message || "檢查認證可用性失敗");
      }

      console.log("📊 [前端] 認證可用性資料:", result.data);

      // 直接使用 API 返回的認證類型資料
      const availabilityTypes = result.data.availability.map((availability: CertificationAvailability) => ({
        ...availability,
        icon: getCertificationIcon(availability.level), // 動態分配圖標
      }));

      setCertificationTypes(availabilityTypes);
      console.log("✅ [前端] 認證類型更新完成:", availabilityTypes);
    } catch (err) {
      console.error("❌ [前端] 檢查認證可用性失敗:", err);
      setError(err instanceof Error ? err.message : "未知錯誤");
    } finally {
      setLoading(false);
    }
  };

  const handleCertificationSelect = async (certType: CertificationTypeWithIcon) => {
    if (!certType.available || creating) return;

    try {
      console.log("🔍 [前端] 選擇認證類型:", certType);
      setCreating(certType.frontendId);
      setError(null);

      // 使用 certificationAPI 服務
      const result = await certificationAPI.createCertificationApplication({
        certificationType: certType.id, // 使用 API 返回的 id 作為 certificationType
        level: certType.level,
      });

      if (!result.success) {
        throw new Error(result.message || "創建認證失敗");
      }

      console.log("✅ [前端] 認證申請創建成功:", result.data);

      navigate(`/certificate/application/${result.data.certificationId}`);
    } catch (err) {
      console.error("❌ [前端] 創建認證申請失敗:", err);
      const errorMessage = err instanceof Error ? err.message : "未知錯誤";
      setError(errorMessage);

      // 如果是已存在認證的錯誤，刷新可用性狀態
      if (errorMessage.includes("已存在")) {
        checkCertificationAvailability();
      }
    } finally {
      setCreating(null);
    }
  };

  const handleGoBack = () => {
    navigate("/certificate");
  };

  const getButtonText = (certType: CertificationTypeWithIcon) => {
    if (creating === certType.frontendId) {
      return "建立中...";
    }
    if (!certType.available) {
      return certType.reason || "無法申請";
    }
    return "選擇此認證";
  };

  const getButtonVariant = (certType: CertificationTypeWithIcon) => {
    if (creating === certType.frontendId) {
      return "secondary" as const;
    }
    return certType.available ? ("primary" as const) : ("secondary" as const);
  };

  // 載入中狀態
  if (loading) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
        <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" style={{ minHeight: "500px" }}>
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">載入認證類型中...</p>
            </div>
          </div>
        </section>
      </main>
    );
  }

  // 錯誤狀態
  if (error && !certificationTypes.length) {
    return (
      <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
        <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" style={{ minHeight: "500px" }}>
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="text-red-600 mb-4">
                <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="font-size-lg font-medium mb-2 text-red-700">載入失敗</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={checkCertificationAvailability}>重新載入</Button>
            </div>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className="min-h-[calc(100vh-60px)] bg-muted flex justify-center items-center py-12">
      <section className="bg-white w-full max-w-[90rem] rounded-lg shadow-lg px-8 py-10" aria-label="選擇認證類型" style={{ minHeight: "500px" }}>
        <div className="flex justify-between items-center mb-8">
          <h1 className="font-size-3xl font-bold text-primary" tabIndex={0}>
            選擇認證類型
          </h1>
          <Button variant="outline" onClick={handleGoBack}>
            返回
          </Button>
        </div>

        {/* 錯誤提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-700 font-medium">錯誤：</span>
              <span className="text-red-600 ml-1">{error}</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {certificationTypes.map((certType) => (
            <Card
              key={certType.frontendId}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                !certType.available || creating ? "opacity-50 cursor-not-allowed bg-gray-100" : "hover:scale-105"
              }`}
              onClick={() => handleCertificationSelect(certType)}>
              <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
                <div className="relative">
                  <img
                    src={certType.icon}
                    alt={`${certType.name}圖示`}
                    className={`w-[90px] h-[90px] object-contain ${!certType.available || creating === certType.frontendId ? "opacity-40" : ""}`}
                    onError={(e) => {
                      // 多層備用圖片邏輯
                      const target = e.target as HTMLImageElement;
                      const currentSrc = target.src;

                      if (currentSrc.includes("luxury")) {
                        // 如果豪華版失敗，回退到基本版
                        const basicPath = currentSrc
                          .replace("medal-luxury-bronze.png", "medal-bronze.png")
                          .replace("medal-luxury-silver.png", "medal-silver.png")
                          .replace("medal-luxury-green.png", "medal-greenflag.png");
                        target.src = basicPath;
                      } else if (!currentSrc.includes("placeholder")) {
                        // 如果基本版也失敗，使用預設圖標
                        target.src = buildAssetUrl("img/placeholder.svg");
                      }
                    }}
                  />
                  {(!certType.available || creating === certType.frontendId) && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-black font-size-xs font-semibold text-center px-2">
                        {creating === certType.frontendId ? "建立中" : certType.reason || "不可申請"}
                      </span>
                    </div>
                  )}
                </div>
                <h3 className="font-size-xl font-semibold text-center">{certType.name}</h3>
                {!certType.available && certType.reason && <p className="text-sm text-gray-500 text-center">{certType.reason}</p>}
                <Button
                  variant={getButtonVariant(certType)}
                  disabled={!certType.available || creating === certType.frontendId}
                  className="w-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCertificationSelect(certType);
                  }}>
                  {getButtonText(certType)}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </main>
  );
};

export default CertificationTypePage;
