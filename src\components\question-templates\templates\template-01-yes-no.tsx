import React from "react";
import type { YesNoData, TemplateProps } from "../types";

// 模板1 - 是非選擇題
export const YesNoTemplate: React.FC<TemplateProps<YesNoData>> = ({ data, onChange, disabled = false }) => {
  const handleChange = (value: string) => {
    onChange({ is_yes_no: value });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-3">
        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="radio"
            name="yes_no"
            value="1"
            checked={data.is_yes_no === "1"}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className="w-4 h-4 text-blue-600 focus:ring-blue-500"
          />
          <span className="font-size-sm font-medium text-gray-700">是</span>
        </label>
        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="radio"
            name="yes_no"
            value="0"
            checked={data.is_yes_no === "0"}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            className="w-4 h-4 text-blue-600 focus:ring-blue-500"
          />
          <span className="font-size-sm font-medium text-gray-700">否</span>
        </label>
      </div>
    </div>
  );
};
