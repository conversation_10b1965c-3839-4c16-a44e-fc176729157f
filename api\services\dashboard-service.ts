// ========== 儀表板服務 - 業務邏輯和資料庫查詢 ==========

import { executeQuery, executeQuerySingle } from "../config/database-mssql.js";
import { UserService } from "./user-service.js";
import {
  CityStatistics,
  LatestCertification,
  SchoolCertificationStatus,
  SchoolInfo,
  UserCityInfo,
  CityQueryResult,
  StatisticsQueryResult,
  CertificationQueryResult,
  LatestCertificationQueryResult,
  SchoolArticle,
  SchoolPassedCertification,
} from "../models/dashboard.js";
import {
  DASHBOARD_STATUS,
  DASHBOARD_REVIEW_STATUS,
  LOCALE_CODE,
  DEFAULT_LIMITS,
  AUTHORIZED_ROLES,
  DASHBOARD_ERROR_MESSAGES,
  getCertificationLevelName,
  isAuthorizedRole,
  isSchoolRole,
  formatDate,
  formatStatisticsResult,
  getTestData,
} from "../constants/dashboard.js";

export class DashboardService {
  // 獲取測試資料
  static async getTestData(): Promise<CityStatistics> {
    return getTestData();
  }

  // 驗證用戶權限（EPA和輔導員）
  static async validateUserPermissions(token: string): Promise<{ valid: boolean; user?: { roleType: string }; message?: string }> {
    try {
      const user = await UserService.getUserByToken(token);
      if (!user) {
        return { valid: false, message: DASHBOARD_ERROR_MESSAGES.INVALID_TOKEN };
      }

      if (!isAuthorizedRole(user.roleType)) {
        return { valid: false, message: DASHBOARD_ERROR_MESSAGES.EPA_TUTOR_ONLY };
      }

      return { valid: true, user };
    } catch (error) {
      console.error("❌ [DashboardService] 驗證用戶權限失敗:", error);
      return { valid: false, message: DASHBOARD_ERROR_MESSAGES.INVALID_TOKEN };
    }
  }

  // 獲取指定縣市的統計資料
  static async getCityStatistics(cityId: number, token: string): Promise<CityStatistics> {
    try {
      // 驗證權限
      const authResult = await this.validateUserPermissions(token);
      if (!authResult.valid) {
        throw new Error(authResult.message);
      }

      // 查詢縣市基本資訊
      const cityQuery = `
        SELECT 
          c.CountyId as cityId, 
          ct.Name as cityName 
        FROM Counties c
        INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId
        WHERE c.CountyId = @cityId 
          AND c.Status = @activeStatus 
          AND ct.LocaleCode = @localeCode
      `;

      const cityInfo = await executeQuerySingle<CityQueryResult>(cityQuery, {
        cityId,
        activeStatus: DASHBOARD_STATUS.ACTIVE,
        localeCode: LOCALE_CODE.ZH_TW,
      });

      if (!cityInfo) {
        throw new Error(DASHBOARD_ERROR_MESSAGES.CITY_NOT_FOUND);
      }

      // 查詢學校認證統計
      const statisticsQuery = `
        SELECT 
          COUNT(DISTINCT s.Id) as totalSchools,
          SUM(CASE WHEN c.Level = 1 AND c.ReviewStatus = @approvedStatus THEN 1 ELSE 0 END) as bronzeCount,
          SUM(CASE WHEN c.Level = 2 AND c.ReviewStatus = @approvedStatus THEN 1 ELSE 0 END) as silverCount,
          SUM(CASE WHEN c.Level >= 3 AND c.ReviewStatus = @approvedStatus THEN 1 ELSE 0 END) as greenFlagCount
        FROM Schools s
        LEFT JOIN Certifications c ON s.Id = c.SchoolId 
          AND c.ReviewStatus = @approvedStatus
          AND c.Status = @activeStatus
        WHERE s.CountyId = @cityId AND s.Status = @activeStatus
      `;

      const statistics = await executeQuerySingle<StatisticsQueryResult>(statisticsQuery, {
        cityId,
        approvedStatus: DASHBOARD_REVIEW_STATUS.APPROVED,
        activeStatus: DASHBOARD_STATUS.ACTIVE,
      });

      return formatStatisticsResult(cityInfo, statistics);
    } catch (error) {
      console.error("❌ [DashboardService] 獲取縣市統計失敗:", error);
      throw error;
    }
  }

  // 獲取當前用戶所屬縣市的統計資料
  static async getMyCityStatistics(token: string): Promise<CityStatistics> {
    try {
      // 獲取用戶的縣市ID
      const userCityInfo = await this.getUserCityInfo(token);
      if (!userCityInfo) {
        throw new Error(DASHBOARD_ERROR_MESSAGES.USER_CITY_NOT_FOUND);
      }

      // 查詢該縣市的學校認證統計
      const statisticsQuery = `
        SELECT 
          COUNT(DISTINCT s.Id) as totalSchools,
          SUM(CASE WHEN c.Level = 1 AND c.ReviewStatus = @approvedStatus THEN 1 ELSE 0 END) as bronzeCount,
          SUM(CASE WHEN c.Level = 2 AND c.ReviewStatus = @approvedStatus THEN 1 ELSE 0 END) as silverCount,
          SUM(CASE WHEN c.Level >= 3 AND c.ReviewStatus = @approvedStatus THEN 1 ELSE 0 END) as greenFlagCount
        FROM Schools s
        LEFT JOIN Certifications c ON s.Id = c.SchoolId 
          AND c.ReviewStatus = @approvedStatus
          AND c.Status = @activeStatus
        WHERE s.CountyId = @countyId AND s.Status = @activeStatus
      `;

      const statistics = await executeQuerySingle<StatisticsQueryResult>(statisticsQuery, {
        countyId: userCityInfo.countyId,
        approvedStatus: DASHBOARD_REVIEW_STATUS.APPROVED,
        activeStatus: DASHBOARD_STATUS.ACTIVE,
      });

      return formatStatisticsResult({ cityId: userCityInfo.countyId, cityName: userCityInfo.cityName }, statistics);
    } catch (error) {
      console.error("❌ [DashboardService] 獲取我的縣市統計失敗:", error);
      throw error;
    }
  }

  // 獲取指定縣市的最新認證
  static async getLatestCertifications(cityId: number, token: string, limit: number = DEFAULT_LIMITS.LATEST_CERTIFICATIONS): Promise<LatestCertification[]> {
    try {
      // 驗證權限
      const authResult = await this.validateUserPermissions(token);
      if (!authResult.valid) {
        throw new Error(authResult.message);
      }

      // 查詢最新認證資料
      const certificationsQuery = `
        SELECT TOP (@limit)
          sc.Name as schoolName,
          CASE 
            WHEN c.Level = 1 THEN '銅牌'
            WHEN c.Level = 2 THEN '銀牌'
            WHEN c.Level = 3 THEN '綠旗'
            WHEN c.Level = 4 THEN '白金'
            WHEN c.Level = 5 THEN '鑽石'
            WHEN c.Level = 6 THEN '頂級'
            ELSE '未知'
          END as certificationLevel,
          FORMAT(c.ApprovedDate, 'yyyy-MM-dd') as passDate,
          ct.Name as cityName
        FROM Certifications c
        INNER JOIN Schools s ON c.SchoolId = s.Id
        INNER JOIN SchoolContents sc ON s.Id = sc.SchoolId AND sc.LocaleCode = @localeCode
        INNER JOIN Counties co ON s.CountyId = co.CountyId
        INNER JOIN CountyTranslations ct ON co.CountyId = ct.CountyId AND ct.LocaleCode = @localeCode
        WHERE s.CountyId = @cityId 
          AND c.ReviewStatus = @approvedStatus
          AND c.Status = @activeStatus
          AND c.ApprovedDate IS NOT NULL
        ORDER BY c.ApprovedDate DESC
      `;

      const certifications = await executeQuery<LatestCertificationQueryResult>(certificationsQuery, {
        cityId,
        limit,
        localeCode: LOCALE_CODE.ZH_TW,
        approvedStatus: DASHBOARD_REVIEW_STATUS.APPROVED,
        activeStatus: DASHBOARD_STATUS.ACTIVE,
      });

      return certifications;
    } catch (error) {
      console.error("❌ [DashboardService] 獲取最新認證失敗:", error);
      throw error;
    }
  }

  // 獲取當前用戶所屬縣市的最新認證
  static async getMyLatestCertifications(token: string, limit: number = DEFAULT_LIMITS.LATEST_CERTIFICATIONS): Promise<LatestCertification[]> {
    try {
      // 獲取用戶的縣市ID
      const userCityInfo = await this.getUserCityInfo(token);
      if (!userCityInfo) {
        throw new Error(DASHBOARD_ERROR_MESSAGES.USER_CITY_NOT_FOUND);
      }

      // 查詢該縣市的最新認證資料
      const certificationsQuery = `
        SELECT TOP (@limit)
          sc.Name as schoolName,
          CASE 
            WHEN c.Level = 1 THEN '銅牌'
            WHEN c.Level = 2 THEN '銀牌'
            WHEN c.Level = 3 THEN '綠旗'
            WHEN c.Level = 4 THEN '白金'
            WHEN c.Level = 5 THEN '鑽石'
            WHEN c.Level = 6 THEN '頂級'
            ELSE '未知'
          END as certificationLevel,
          FORMAT(c.ApprovedDate, 'yyyy-MM-dd') as passDate,
          ct.Name as cityName
        FROM Certifications c
        INNER JOIN Schools s ON c.SchoolId = s.Id
        INNER JOIN SchoolContents sc ON s.Id = sc.SchoolId AND sc.LocaleCode = @localeCode
        INNER JOIN Counties co ON s.CountyId = co.CountyId
        INNER JOIN CountyTranslations ct ON co.CountyId = ct.CountyId AND ct.LocaleCode = @localeCode
        WHERE s.CountyId = @countyId 
          AND c.ReviewStatus = @approvedStatus
          AND c.Status = @activeStatus
          AND c.ApprovedDate IS NOT NULL
        ORDER BY c.ApprovedDate DESC
      `;

      const certifications = await executeQuery<LatestCertificationQueryResult>(certificationsQuery, {
        countyId: userCityInfo.countyId,
        limit,
        localeCode: LOCALE_CODE.ZH_TW,
        approvedStatus: DASHBOARD_REVIEW_STATUS.APPROVED,
        activeStatus: DASHBOARD_STATUS.ACTIVE,
      });

      return certifications;
    } catch (error) {
      console.error("❌ [DashboardService] 獲取我的最新認證失敗:", error);
      throw error;
    }
  }

  // 獲取學校當前申請中的認證
  static async getSchoolCurrentCertification(token: string): Promise<SchoolCertificationStatus | null> {
    try {
      // 獲取學校資訊
      const schoolInfo = await this.getSchoolInfoByToken(token);
      if (!schoolInfo) {
        throw new Error(DASHBOARD_ERROR_MESSAGES.SCHOOL_NOT_FOUND);
      }

      // 查詢學校當前申請中的認證 (ReviewStatus = 0 表示審核中)
      const certificationQuery = `
        SELECT TOP (1)
          c.CertificationId,
          c.Level,
          c.ReviewStatus,
          c.CreatedTime as applyDate,
          c.ReviewDate,
          c.ApprovedDate as passDate,
          ctt.Name as levelName
        FROM Certifications c
        INNER JOIN CertificationTypes ct ON c.Level = ct.Id
        INNER JOIN CertificationTypeTranslations ctt ON ct.Id = ctt.CertificationTypeId
        WHERE c.SchoolId = @schoolId 
          AND c.ReviewStatus = @underReviewStatus
          AND c.Status = @activeStatus
          AND ctt.LocaleCode = @localeCode
        ORDER BY c.CreatedTime DESC
      `;

      const certification = await executeQuerySingle<CertificationQueryResult>(certificationQuery, {
        schoolId: parseInt(schoolInfo.schoolId),
        underReviewStatus: DASHBOARD_REVIEW_STATUS.UNDER_REVIEW,
        activeStatus: DASHBOARD_STATUS.ACTIVE,
        localeCode: LOCALE_CODE.ZH_TW,
      });

      if (!certification) {
        return null;
      }

      return {
        certificationId: certification.CertificationId,
        level: certification.Level,
        levelName: certification.levelName,
        status: "審核中",
        reviewStatus: certification.ReviewStatus,
        applyDate: formatDate(certification.applyDate),
        reviewDate: certification.ReviewDate ? formatDate(certification.ReviewDate) : undefined,
        passDate: certification.passDate ? formatDate(certification.passDate) : undefined,
      };
    } catch (error) {
      console.error("❌ [DashboardService] 獲取學校申請中認證失敗:", error);
      throw error;
    }
  }

  // ========== 私有輔助方法 ==========

  // 根據 Token 獲取學校資訊
  private static async getSchoolInfoByToken(token: string): Promise<SchoolInfo | null> {
    const schoolQuery = `
      SELECT 
        s.Id as schoolId,
        sc.Name as schoolName,
        a.AccountId
      FROM UserToken ut
      INNER JOIN Accounts a ON ut.AccountSid = a.AccountId
      INNER JOIN Schools s ON a.SchoolId = s.Id
      INNER JOIN SchoolContents sc ON s.Id = sc.SchoolId AND sc.LocaleCode = @localeCode
      WHERE ut.Token = CAST(@token AS uniqueidentifier)
        AND ut.Status = @activeStatus 
        AND a.Status = @activeStatus
        AND s.Status = @activeStatus
        AND a.SchoolId IS NOT NULL
    `;

    return await executeQuerySingle<SchoolInfo>(schoolQuery, {
      token,
      localeCode: LOCALE_CODE.ZH_TW,
      activeStatus: DASHBOARD_STATUS.ACTIVE,
    });
  }

  // 根據 Token 獲取用戶縣市資訊
  private static async getUserCityInfo(token: string): Promise<UserCityInfo | null> {
    const userQuery = `
      SELECT 
        a.CountyId as countyId,
        ct.Name as cityName
      FROM UserToken ut
      INNER JOIN Accounts a ON ut.AccountSid = a.AccountId
      INNER JOIN Counties c ON a.CountyId = c.CountyId
      INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId AND ct.LocaleCode = @localeCode
      WHERE ut.Token = CAST(@token AS uniqueidentifier)
        AND ut.Status = @activeStatus
        AND a.Status = @activeStatus
        AND c.Status = @activeStatus
    `;

    return await executeQuerySingle<UserCityInfo>(userQuery, {
      token,
      localeCode: LOCALE_CODE.ZH_TW,
      activeStatus: DASHBOARD_STATUS.ACTIVE,
    });
  }

  // 獲取學校最新投稿文章
  static async getSchoolLatestArticles(accountId: number, limit: number = 6): Promise<SchoolArticle[]> {
    try {
      // 獲取學校資訊
      const schoolInfo = await this.getSchoolInfoByAccountId(accountId);
      if (!schoolInfo) {
        throw new Error(DASHBOARD_ERROR_MESSAGES.SCHOOL_NOT_FOUND);
      }

      // 模擬文章資料（實際實作時應該查詢資料庫）
      const mockArticles: SchoolArticle[] = [
        {
          articleId: "1",
          title: "環保教育實踐案例分享",
          summary: "本校在環保教育方面的創新實踐，包含課程設計和活動規劃...",
          status: "已發布",
          publishDate: "2024-01-15",
          createDate: "2024-01-10",
        },
        {
          articleId: "2",
          title: "綠色校園建設成果報告",
          summary: "透過師生共同努力，成功建立綠色校園環境，提升環保意識...",
          status: "已發布",
          publishDate: "2024-01-10",
          createDate: "2024-01-05",
        },
        {
          articleId: "3",
          title: "廢棄物減量計畫執行心得",
          summary: "實施廢棄物減量計畫的過程和成果，分享實務經驗...",
          status: "審核中",
          createDate: "2024-01-08",
        },
        {
          articleId: "4",
          title: "節能減碳校園改造計畫",
          summary: "校園節能設施改造的規劃和執行過程，以及節能效果評估...",
          status: "已發布",
          publishDate: "2024-01-05",
          createDate: "2024-01-01",
        },
        {
          articleId: "5",
          title: "環保社團活動記錄",
          summary: "環保社團舉辦的各項活動記錄，包含學生參與情況和學習成果...",
          status: "已發布",
          publishDate: "2024-01-01",
          createDate: "2023-12-28",
        },
        {
          articleId: "6",
          title: "永續發展教育課程設計",
          summary: "將永續發展概念融入各科課程的教學設計和實施經驗...",
          status: "草稿",
          createDate: "2023-12-25",
        },
      ];

      // 根據限制返回指定數量的文章
      return mockArticles.slice(0, limit);
    } catch (error) {
      console.error("❌ [DashboardService] 獲取學校最新投稿文章失敗:", error);
      throw error;
    }
  }

  // 獲取學校已通過的認證
  static async getSchoolPassedCertifications(accountId: number, limit: number = 10): Promise<SchoolPassedCertification[]> {
    try {
      // 獲取學校資訊
      const schoolInfo = await this.getSchoolInfoByAccountId(accountId);
      if (!schoolInfo) {
        throw new Error(DASHBOARD_ERROR_MESSAGES.SCHOOL_NOT_FOUND);
      }

      // 查詢已通過的認證
      const certificationQuery = `
        DECLARE @LEVELCODEMAPPING TABLE (Level INT, LevelName NVARCHAR(50));
        INSERT INTO @LEVELCODEMAPPING (Level, LevelName)
        VALUES
          (1, 'BRONZE'),
          (2, 'SILVER'),
          (3, 'FLAG'),
          (4, 'R1'),
          (5, 'R2'),
          (6, 'R3');

        SELECT TOP (@limit)
          c.CertificationId,
          c.Level,
          c.ApprovedDate as passDate,
          ctt.Name as levelName
        FROM Certifications c
        INNER JOIN @LEVELCODEMAPPING l ON c.Level = l.Level
        INNER JOIN CertificationTypes ct ON ct.Level = l.LevelName
        INNER JOIN CertificationTypeTranslations ctt ON ct.Id = ctt.CertificationTypeId
        WHERE c.SchoolId = @schoolId 
          AND c.ReviewStatus = @approvedStatus
          AND c.Status = @activeStatus
          AND ctt.LocaleCode = @localeCode
        ORDER BY c.ApprovedDate DESC
      `;

      const certifications = await executeQuery<{
        CertificationId: string;
        Level: number;
        passDate: Date;
        levelName: string;
      }>(certificationQuery, {
        limit,
        schoolId: parseInt(schoolInfo.schoolId),
        approvedStatus: DASHBOARD_REVIEW_STATUS.APPROVED,
        activeStatus: DASHBOARD_STATUS.ACTIVE,
        localeCode: LOCALE_CODE.ZH_TW,
      });

      return certifications.map((cert) => ({
        certificationId: cert.CertificationId,
        level: cert.Level,
        levelName: cert.levelName,
        passDate: formatDate(cert.passDate),
      }));
    } catch (error) {
      console.error("❌ [DashboardService] 獲取學校已通過認證失敗:", error);
      throw error;
    }
  }

  // 根據 AccountId 獲取學校資訊
  private static async getSchoolInfoByAccountId(accountId: number): Promise<SchoolInfo | null> {
    const schoolQuery = `
      SELECT 
        s.Id as schoolId,
        sc.Name as schoolName,
        a.AccountId
      FROM Accounts a
      INNER JOIN Schools s ON a.SchoolId = s.Id
      INNER JOIN SchoolContents sc ON s.Id = sc.SchoolId AND sc.LocaleCode = @localeCode
      WHERE a.AccountId = @accountId
        AND a.Status = @activeStatus
        AND s.Status = @activeStatus
        AND a.SchoolId IS NOT NULL
    `;

    return await executeQuerySingle<SchoolInfo>(schoolQuery, {
      accountId,
      localeCode: LOCALE_CODE.ZH_TW,
      activeStatus: DASHBOARD_STATUS.ACTIVE,
    });
  }
}
