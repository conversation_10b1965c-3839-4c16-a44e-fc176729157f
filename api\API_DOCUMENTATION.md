# EcoCampus API 文檔

> 自動生成的 API 文檔  
> 生成時間: 2025-08-21T08:49:16.299Z  
> 版本: 1.0.0

## 📊 摘要統計

- **總路由數**: 14
- **總模型數**: 13
- **總常數數**: 13

### 路由類別統計

- **admin**: 4 個端點
- **answer**: 4 個端點
- **auth**: 6 個端點
- **campus-submissions**: 7 個端點
- **certificate**: 6 個端點
- **certification**: 6 個端點
- **dashboard**: 8 個端點
- **file**: 9 個端點
- **location**: 3 個端點
- **metrics**: 4 個端點
- **question**: 5 個端點
- **simple-profile**: 2 個端點
- **template-answer**: 2 個端點
- **user**: 6 個端點

---

## 🛣️ 路由端點

### admin

**文件**: `routes/admin.ts`

#### 端點列表

- **POST** `/mark-answer-status`
- **GET** `/action-logs/:certificationId`
- **POST** `/save-review-comment`
- **GET** `/review-comments/:certificationId`

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../services/admin-service.js`
- `../utils/logger.js`
- `../constants/admin.js`

#### 導出項目



---
### answer

**文件**: `routes/answer.ts`

#### 端點列表

- **POST** `/save`
- **GET** `/certification/:certificationId`
- **GET** `/question/:questionId/certification/:certificationId`
- **DELETE** `/:answerId`

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../services/answer-service.js`
- `../utils/response-helpers.js`
- `../constants/answer.js`

#### 導出項目



---
### auth

**文件**: `routes/auth.ts`

#### 端點列表

- **GET** `/token-status`
- **POST** `/token-login` - Token 直接登入 API
- **POST** `/password-login` - 密碼登入 API
- **POST** `/change-password`
- **POST** `/logout` - 登出 API
- **POST** `/admin-reset-password`

#### 導入模組

- `express`
- `../services/auth-service.js`
- `../constants/auth.js`
- `../utils/logger.js`

#### 導出項目



---
### campus-submissions

**文件**: `routes/campus-submissions.ts`

#### 端點列表

- **GET** `/` - 獲取單一校園投稿詳細資料
- **GET** `/:id` - 獲取單一校園投稿詳細資料
- **DELETE** `/:id` - 獲取單一校園投稿詳細資料
- **POST** `/:submissionId/photos`
- **POST** `/:submissionId/attachments`
- **DELETE** `/:submissionId/photos/:filename`
- **DELETE** `/:submissionId/attachments/:filename`

#### 導入模組

- `express`
- `multer`
- `path`
- `../middleware/auth.js`
- `../services/campus-submission-service.js`
- `../services/campus-submission-file-service.js`
- `../constants/campus-submission.js`
- `../utils/logger.js`
- `../constants/file.js`

#### 導出項目



---
### certificate

**文件**: `routes/certificate.ts`

#### 端點列表

- **POST** `/validate`
- **POST** `/bind`
- **DELETE** `/unbind/:accountId`
- **GET** `/bindings`
- **POST** `/login`
- **GET** `/test`

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../services/certificate-service.js`
- `../constants/certificate.js`

#### 導出項目



---
### certification

**文件**: `routes/certification.ts`

#### 端點列表

- **GET** `/list`
- **GET** `/availability`
- **GET** `/:certificationId`
- **POST** `/create`
- **PUT** `/:certificationId`
- **DELETE** `/:certificationId`

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../services/certification-service.js`
- `../constants/certification.js`

#### 導出項目



---
### dashboard

**文件**: `routes/dashboard.ts`

#### 端點列表

- **GET** `/test` - 測試端點（無需認證）
- **GET** `/city-statistics/:cityId` - 獲取指定縣市的生態學校統計
- **GET** `/my-city-statistics` - 獲取當前用戶所屬縣市的統計資料
- **GET** `/school/passed-certifications`
- **GET** `/school/latest-articles` - 獲取學校最新投稿文章
- **GET** `/latest-certifications/:cityId` - 獲取指定縣市的最新認證（最多6則）
- **GET** `/my-latest-certifications` - 獲取當前用戶所屬縣市的最新認證
- **GET** `/school/current-certification` - 獲取學校當前申請中的認證

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../services/dashboard-service.js`
- `../constants/dashboard.js`
- `../utils/logger.js`
- `../models/certification.js`

#### 導出項目



---
### file

**文件**: `routes/file.ts`

#### 端點列表

- **POST** `/upload`
- **POST** `/upload-single`
- **DELETE** `/:filename` - 刪除檔案
- **GET** `/info/:filename` - 獲取檔案資訊
- **GET** `/download/:filename` - 下載檔案
- **GET** `/supported-types`
- **POST** `/upload-school-logo`
- **GET** `/school-logo/:accountId` - 取得校徽 API
- **GET** `/upload-diagnostics`

#### 導入模組

- `express`
- `multer`
- `path`
- `url`
- `../middleware/auth.js`
- `../services/file-service.js`
- `../config/config-manager.js`

#### 導出項目



---
### location

**文件**: `routes/location.ts`

#### 端點列表

- **GET** `/cities`
- **GET** `/areas/:cityId`
- **GET** `/hierarchy`

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../services/location-service.js`
- `../utils/logger.js`
- `../constants/location.js`

#### 導出項目



---
### metrics

**文件**: `routes/metrics.ts`

#### 端點列表

- **GET** `/metrics` - *  * GET /metrics  * 獲取系統監控指標（JSON 格式）
- **GET** `/metrics/prometheus` - *  * GET /metrics/prometheus  * 獲取 Prometheus 格式的指標
- **POST** `/metrics/reset` - *  * POST /metrics/reset  * 重置監控指標（僅限開發環境）
- **GET** `/health` - *  * GET /health  * 健康檢查端點

#### 導入模組

- `express`
- `@/shared/monitoring/AuthMetrics`

#### 導出項目



---
### question

**文件**: `routes/question.ts`

#### 端點列表

- **GET** `/` - 根據步驟和認證等級獲取問題
- **GET** `/certification-steps`
- **GET** `/form-questions` - 獲取表單問題配置 (支援步驟和父問題雙層分組)
- **GET** `/analyze-structure` - 分析問題父子關係結構 (必須在/:questionId之前定義)
- **GET** `/:questionId` - 根據問題ID獲取問題詳情

#### 導入模組

- `express`
- `../services/question-service.js`
- `../constants/question.js`

#### 導出項目



---
### simple-profile

**文件**: `routes/simple-profile.ts`

#### 端點列表

- **GET** `/` - 簡化版基本資料維護 API - 獲取會員資料
- **PUT** `/` - 簡化版基本資料維護 API - 獲取會員資料

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../config/database-mssql.js`
- `../utils/logger`

#### 導出項目



---
### template-answer

**文件**: `routes/template-answer.ts`

#### 端點列表

- **POST** `/save`
- **POST** `/validate`

#### 導入模組

- `express`
- `../services/template-answer-service.js`
- `../constants/template-answer.js`

#### 導出項目



---
### user

**文件**: `routes/user.ts`

#### 端點列表

- **GET** `/profile` - 獲取使用者基本資料 (依 ID)
- **GET** `/profile/:userId` - 獲取使用者基本資料 (依 ID)
- **PUT** `/profile` - 獲取使用者基本資料 (依 ID)
- **GET** `/certifications`
- **GET** `/school`
- **GET** `/permissions`

#### 導入模組

- `express`
- `../middleware/auth.js`
- `../services/user-service.js`
- `../constants/user.js`

#### 導出項目



---

## 📋 資料模型

### admin

**文件**: `models/admin.ts`

#### 接口定義


**AuthenticatedUser**

```typescript
interface AuthenticatedUser {
  id: string;
  account: string;
  roleType: string;
  permissions: string[];
  permissionGroups: string[];
  isActive: boolean;
}
```


**AnswerStatusMarkRequest**

```typescript
interface AnswerStatusMarkRequest {
  certificationId: string;
  questionId: string;
  status: string;
  action: string;
}
```


**ReviewCommentRequest**

```typescript
interface ReviewCommentRequest {
  certificationId: string;
  stepId: string;
  comment: string;
}
```


**AnswerStatusInfo**

```typescript
interface AnswerStatusInfo {
  answerId: number;
  questionId: number;
  previousStatus: number;
  newStatus: number;
  statusDescription: string;
  action: string;
  timestamp: string;
}
```


**AdminActionLog**

```typescript
interface AdminActionLog {
  actionLogId: number;
  certificationId: number;
  questionId: number;
  answerId: number;
  action: string;
  previousStatus: number;
  newStatus: number;
  actionTime: Date;
  notes: string;
  adminUsername: string;
  questionTitle: string;
}
```


**ReviewComment**

```typescript
interface ReviewComment {
  certificationStepRecordId: number;
  certificationId: number;
  stepNumber: number;
  comment: string;
  createdTime: Date;
  updatedTime: Date;
  adminUsername: string;
}
```


**ReviewCommentSaveResult**

```typescript
interface ReviewCommentSaveResult {
  recordId?: number;
  action: string;
  timestamp: string;
}
```


**CertificationQueryResult**

```typescript
interface CertificationQueryResult {
  CertificationId: number;
  ReviewStatus: number;
  Status: number;
}
```


**AnswerQueryResult**

```typescript
interface AnswerQueryResult {
  AnswerId: number;
  QuestionId: number;
  AnswerStatus: number;
}
```


**ActionLogQueryResult**

```typescript
interface ActionLogQueryResult {
  ActionLogId: number;
  CertificationId: number;
  QuestionId: number;
  AnswerId: number;
  Action: string;
  PreviousStatus: number;
  NewStatus: number;
  ActionTime: Date;
  Notes: string;
  AdminUsername: string;
  QuestionTitle: string;
}
```


**ReviewCommentQueryResult**

```typescript
interface ReviewCommentQueryResult {
  CertificationStepRecordId: number;
  CertificationId: number;
  StepNumber: number;
  Comment: string;
  CreatedTime: Date;
  UpdatedTime: Date;
  AdminUsername: string;
}
```


**StepRecordQueryResult**

```typescript
interface StepRecordQueryResult {
  CertificationStepRecordId: number;
}
```


**AnswerStatusMarkResponse**

```typescript
interface AnswerStatusMarkResponse {
  success: boolean;
  message: string;
  data: AnswerStatusInfo;
}
```


**ActionLogsResponse**

```typescript
interface ActionLogsResponse {
  success: boolean;
  message: string;
  data: AdminActionLog[];
}
```


**ReviewCommentSaveResponse**

```typescript
interface ReviewCommentSaveResponse {
  success: boolean;
  message: string;
  data: ReviewCommentSaveResult;
}
```


**ReviewCommentsResponse**

```typescript
interface ReviewCommentsResponse {
  success: boolean;
  message: string;
  data: ReviewComment[];
}
```


**ActionLogsParams**

```typescript
interface ActionLogsParams {
  certificationId: string;
}
```


**ReviewCommentsParams**

```typescript
interface ReviewCommentsParams {
  certificationId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### answer

**文件**: `models/answer.ts`

#### 接口定義


**AnswerSaveRequest**

```typescript
interface AnswerSaveRequest {
  certificationId: string;
  questionId: number;
  answerData: any;
  templateId?: number;
}
```


**AnswerInfo**

```typescript
interface AnswerInfo {
  answerId: number;
  certificationId: string;
  questionId: number;
  questionTitle: string;
  questionTemplate: number;
  stepNumber: number;
  parentQuestionId: number | null;
  answerData: any;
  answerStatus: number;
  submittedDate: Date | null;
  reviewedDate: Date | null;
  createdTime: Date;
  updatedTime: Date | null;
}
```


**AnswerSaveResult**

```typescript
interface AnswerSaveResult {
  answerId: number;
  questionId: number;
  templateId: number;
  message: string;
}
```


**CertificationAnswersData**

```typescript
interface CertificationAnswersData {
  certificationId: string;
  answers: AnswerInfo[];
  total: number;
}
```


**AnswerRecord**

```typescript
interface AnswerRecord {
  CertificationAnswerId: number;
  CertificationId: string;
  QuestionId: number;
  AnswerText: string;
  AnswerStatus: number;
  SubmittedDate: Date | null;
  ReviewedDate: Date | null;
  CreatedTime: Date;
  UpdatedTime: Date | null;
  QuestionTitle: string;
  QuestionTemplate: number;
  StepNumber: number;
  ParentQuestionId: number | null;
}
```


**UserSchoolQueryResult**

```typescript
interface UserSchoolQueryResult {
  SchoolId: number;
}
```


**CertificationCheckQueryResult**

```typescript
interface CertificationCheckQueryResult {
  SchoolId: number;
  ReviewStatus?: number;
}
```


**QuestionCheckQueryResult**

```typescript
interface QuestionCheckQueryResult {
  QuestionId: number;
  QuestionTemplate: number;
}
```


**ExistingAnswerQueryResult**

```typescript
interface ExistingAnswerQueryResult {
  CertificationAnswerId: number;
}
```


**AnswerCheckQueryResult**

```typescript
interface AnswerCheckQueryResult {
  CertificationAnswerId: number;
  CertificationId: string;
  SchoolId: number;
}
```


**AnswerSaveResponse**

```typescript
interface AnswerSaveResponse {
  success: boolean;
  data: AnswerSaveResult;
  message?: string;
}
```


**CertificationAnswersResponse**

```typescript
interface CertificationAnswersResponse {
  success: boolean;
  data: CertificationAnswersData;
}
```


**SingleAnswerResponse**

```typescript
interface SingleAnswerResponse {
  success: boolean;
  data: AnswerInfo | null;
  message?: string;
}
```


**AnswerDeleteResponse**

```typescript
interface AnswerDeleteResponse {
  success: boolean;
  message: string;
}
```


**CertificationAnswersParams**

```typescript
interface CertificationAnswersParams {
  certificationId: string;
}
```


**SingleAnswerParams**

```typescript
interface SingleAnswerParams {
  questionId: string;
  certificationId: string;
}
```


**AnswerDeleteParams**

```typescript
interface AnswerDeleteParams {
  answerId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### auth

**文件**: `models/auth.ts`

#### 接口定義


**LoginResult**

```typescript
interface LoginResult {
  valid: boolean;
  user?: UserProfile;
  message?: string;
}
```


**TokenValidationResult**

```typescript
interface TokenValidationResult {
  valid: boolean;
  user?: UserProfile;
  message?: string;
}
```


**AccountRow**

```typescript
interface AccountRow {
  AccountId: number;
  Username: string;
  password: string;
  password_salt: string;
  Email?: string;
  Phone?: string;
  Status: number;
  IsSchoolPartner?: number;
  IsEpaUser?: number;
  IsGuidanceTeam?: number;
  SchoolId?: number;
}
```


**MemberProfileRow**

```typescript
interface MemberProfileRow {
  MemberName?: string;
  EnglishName?: string;
  JobTitle?: string;
  ContactNumber?: string;
  Email?: string;
}
```


**SchoolRow**

```typescript
interface SchoolRow {
  Id: number;
  Name?: string;
  EnglishName?: string;
  Code?: string;
  Address?: string;
  Phone?: string;
  Email?: string;
  Website?: string;
}
```


**TokenRow**

```typescript
interface TokenRow {
  UserTokenSid: number;
  Token: string;
  TokenType: string;
  ExpirationDate: Date;
  IsActive: number;
  CreatedTime: Date;
  LastAccessDate?: Date;
}
```


**TokenValidationRequest**

```typescript
interface TokenValidationRequest {
  userToken: string;
}
```


**TokenLoginRequest**

```typescript
interface TokenLoginRequest {
  token: string;
}
```


**PasswordLoginRequest**

```typescript
interface PasswordLoginRequest {
  account: string;
  password: string;
}
```


**ChangePasswordRequest**

```typescript
interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}
```


**AdminResetPasswordRequest**

```typescript
interface AdminResetPasswordRequest {
  accountIds: number[];
  newPassword: string;
}
```


**CreateTokenRequest**

```typescript
interface CreateTokenRequest {
  accountId?: number;
  tokenType?: string;
  validityDays?: number;
}
```


**TokenValidationResponse**

```typescript
interface TokenValidationResponse {
  success: boolean;
  data: {;
  valid: boolean;
  user?: UserProfile;
  details?: {;
  userRole?: string;
  allowedRoles?: string[];
  action?: string;
}
```


**PasswordLoginResponse**

```typescript
interface PasswordLoginResponse {
  success: boolean;
  data: {;
  user?: UserProfile;
  valid?: boolean;
  token?: string;
  details?: {;
  userRole?: string;
  allowedRoles?: string[];
  action?: string;
}
```


**ChangePasswordResponse**

```typescript
interface ChangePasswordResponse {
  success: boolean;
  message: string;
  user?: {;
  id: string;
  account: string;
}
```


**AdminResetPasswordResponse**

```typescript
interface AdminResetPasswordResponse {
  success: boolean;
  message: string;
  results: Array<{;
  accountId: number;
  success: boolean;
  message: string;
}
```


**CreateTokenResponse**

```typescript
interface CreateTokenResponse {
  success: boolean;
  message: string;
  data?: {;
  token: string;
  tokenType: string;
  expirationDate: string;
  accountId: number;
}
```


**LogoutResponse**

```typescript
interface LogoutResponse {
  success: boolean;
  message: string;
}
```


**RoleCheckResponse**

```typescript
interface RoleCheckResponse {
  success: boolean;
  valid: boolean;
  user?: UserProfile;
  message?: string;
  details?: {;
  userRole?: string;
  allowedRoles?: string[];
  action?: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### campus-submission

**文件**: `models/campus-submission.ts`

#### 接口定義


**CampusSubmission**

```typescript
interface CampusSubmission {
  submissionId: string;
  title: string;
  description: string;
  status: number;
  statusText: string;
  submissionDate: string;
  createdTime: string;
  updatedTime?: string;
  badgeType: number;
  featuredStatus: number;
}
```


**CampusSubmissionAttachment**

```typescript
interface CampusSubmissionAttachment {
  attachmentId: string;
  fileEntryId?: string;
  contentTypeCode: string;
  title?: string;
  altUrl?: string;
}
```


**CreateCampusSubmissionRequest**

```typescript
interface CreateCampusSubmissionRequest {
  zhTitle: string;
  zhContent: string;
  enTitle?: string;
  enContent?: string;
  attachments?: CampusSubmissionAttachment[];
}
```


**SchoolInfo**

```typescript
interface SchoolInfo {
  schoolId: number;
  schoolName: string;
  AccountId: number;
}
```


**SubmissionQueryResult**

```typescript
interface SubmissionQueryResult {
  CampusSubmissionId: string;
  title: string;
  description?: string;
  SubmissionStatus: number;
  ReviewStatus?: number;
  CampusSubmissionReviewId?: string;
  ReviewComment?: string;
  SubmissionDate: string;
  CreatedTime: string;
  UpdatedTime?: string;
  BadgeType: number;
  FeaturedStatus: number;
}
```


**SubmissionDetailQueryResult**

```typescript
interface SubmissionDetailQueryResult {
  CampusSubmissionId: string;
  ZhTitle: string;
  ZhContent: string;
  EnTitle?: string;
  EnContent?: string;
  SubmissionStatus: number;
  ReviewStatus?: number;
  ReviewComment?: string;
  SubmissionDate: string;
  CreatedTime: string;
  UpdatedTime?: string;
  BadgeType: number;
  FeaturedStatus: number;
}
```


**AttachmentQueryResult**

```typescript
interface AttachmentQueryResult {
  AttachmentId: string;
  FileEntryId?: string;
  ContentTypeCode: string;
  Title?: string;
  AltUrl?: string;
}
```


**CampusSubmissionListResponse**

```typescript
interface CampusSubmissionListResponse {
  success: boolean;
  data: CampusSubmission[];
  schoolInfo: {;
  schoolId: number;
  schoolName: string;
}
```


**CampusSubmissionDetailResponse**

```typescript
interface CampusSubmissionDetailResponse {
  success: boolean;
  data: CampusSubmissionDetail;
  message?: string;
}
```


**CampusSubmissionCreateResponse**

```typescript
interface CampusSubmissionCreateResponse {
  success: boolean;
  data: {;
  submissionId: string;
  message: string;
}
```


**CampusSubmissionUpdateResponse**

```typescript
interface CampusSubmissionUpdateResponse {
  success: boolean;
  data: {;
  submissionId: string;
  message: string;
}
```


**CampusSubmissionDeleteResponse**

```typescript
interface CampusSubmissionDeleteResponse {
  success: boolean;
  message: string;
}
```


**CampusSubmissionSubmitResponse**

```typescript
interface CampusSubmissionSubmitResponse {
  success: boolean;
  data: {;
  submissionId: string;
  reviewId: string;
  message: string;
}
```


**CampusSubmissionWithdrawResponse**

```typescript
interface CampusSubmissionWithdrawResponse {
  success: boolean;
  data: {;
  submissionId: string;
  reviewId: string;
  message: string;
}
```


**CampusSubmissionListQueryParams**

```typescript
interface CampusSubmissionListQueryParams {
  limit?: string;
  page?: string;
}
```


**CampusSubmissionDetailParams**

```typescript
interface CampusSubmissionDetailParams {
  submissionId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### certificate

**文件**: `models/certificate.ts`

#### 接口定義


**CertificateInfo**

```typescript
interface CertificateInfo {
  subject: string;
  issuer: string;
  serialNumber: string;
  validFrom: string;
  validTo: string;
  fingerprint: string;
}
```


**CertificateValidateRequest**

```typescript
interface CertificateValidateRequest {
  certificateData: string;
}
```


**CertificateBindRequest**

```typescript
interface CertificateBindRequest {
  certificateInfo: CertificateInfo;
  accountId: string;
}
```


**CertificateLoginRequest**

```typescript
interface CertificateLoginRequest {
  certificateData: string;
  password: string;
}
```


**CertificateValidationResult**

```typescript
interface CertificateValidationResult {
  isValid: boolean;
  certificateInfo: CertificateInfo;
}
```


**CertificateBindResult**

```typescript
interface CertificateBindResult {
  certificateId: string;
  bindTime: string;
  status: string;
}
```


**CertificateBindingInfo**

```typescript
interface CertificateBindingInfo {
  accountId: string;
  hasCertificate: boolean;
  certificateId: string | null;
  lastUpdateTime: Date;
}
```


**CertificateLoginResult**

```typescript
interface CertificateLoginResult {
  token: string;
  user: {;
  id: string;
  username: string;
  displayName: string;
  role: string;
}
```


**CertificateTestResult**

```typescript
interface CertificateTestResult {
  userId: string;
  userAccount: string;
  userRole: string;
  timestamp: string;
}
```


**AccountQueryResult**

```typescript
interface AccountQueryResult {
  citizen_digital_number: string;
}
```


**AccountFullQueryResult**

```typescript
interface AccountFullQueryResult {
  sid: string;
  account: string;
  cname: string;
  isuse: number;
  member_role: string;
}
```


**BindingQueryResult**

```typescript
interface BindingQueryResult {
  accountId: string;
  certificateId: string;
  lastUpdateTime: Date;
}
```


**CertificateValidateResponse**

```typescript
interface CertificateValidateResponse {
  success: boolean;
  message: string;
  data?: CertificateValidationResult;
}
```


**CertificateBindResponse**

```typescript
interface CertificateBindResponse {
  success: boolean;
  message: string;
  data?: CertificateBindResult;
}
```


**CertificateUnbindResponse**

```typescript
interface CertificateUnbindResponse {
  success: boolean;
  message: string;
}
```


**CertificateBindingsResponse**

```typescript
interface CertificateBindingsResponse {
  success: boolean;
  message?: string;
  data?: CertificateBindingInfo;
}
```


**CertificateLoginResponse**

```typescript
interface CertificateLoginResponse {
  success: boolean;
  message: string;
  data?: CertificateLoginResult;
}
```


**CertificateTestResponse**

```typescript
interface CertificateTestResponse {
  success: boolean;
  message: string;
  data?: CertificateTestResult;
}
```


**CertificateUnbindParams**

```typescript
interface CertificateUnbindParams {
  accountId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### certification

**文件**: `models/certification.ts`

#### 接口定義


**Certification**

```typescript
interface Certification {
  certificationId: number;
  schoolId: number;
  level: number;
  reviewStatus: number;
  reviewDate?: Date;
  approvedDate?: Date;
  createdTime: Date;
  updatedTime?: Date;
  createdUserId?: number;
  reviewerId?: number;
  addType?: string;
  status: number;
}
```


**CertificationListItem**

```typescript
interface CertificationListItem {
  id: number;
  status: number;
  statusInfo: {;
  label: string;
  icon: string;
  description: string;
  color: string;
  bgColor: string;
}
```


**CertificationStatistics**

```typescript
interface CertificationStatistics {
  total: number;
  drafts: number;
  pending: number;
  passed: number;
  inReview: number;
  returned: number;
}
```


**CertificationAvailability**

```typescript
interface CertificationAvailability {
  id: string;
  name: string;
  level: number;
  available: boolean;
  reason?: string;
  frontendId: string;
}
```


**CertificationTypeInfo**

```typescript
interface CertificationTypeInfo {
  name: string;
  fullName: string;
  level: number;
  isRenewal: boolean;
  icon: string;
}
```


**CertificationRequest**

```typescript
interface CertificationRequest {
  certificationType: string;
  level: number;
}
```


**SchoolAccount**

```typescript
interface SchoolAccount {
  schoolId: number;
}
```


**ExistingCertification**

```typescript
interface ExistingCertification {
  level: number;
  reviewStatus: number;
  approvedDate?: Date;
}
```


**CertificationQueryResult**

```typescript
interface CertificationQueryResult {
  CertificationId: number;
  SchoolId: number;
  Level: number;
  ReviewStatus: number;
  ReviewDate?: Date;
  ApprovedDate?: Date;
  CreatedTime: Date;
  UpdatedTime?: Date;
  CreatedUserId?: number;
  ReviewerId?: number;
  AddType?: string;
  Status: number;
  ApplicantName?: string;
  SchoolName?: string;
  SchoolEnglishName?: string;
  ReviewerAccount?: string;
}
```


**CertificationListResponse**

```typescript
interface CertificationListResponse {
  success: boolean;
  data: {;
  all: CertificationListItem[];
  drafts: CertificationListItem[];
  pending: CertificationListItem[];
  passed: CertificationListItem[];
  statistics: CertificationStatistics;
}
```


**CertificationAvailabilityResponse**

```typescript
interface CertificationAvailabilityResponse {
  success: boolean;
  data: {;
  availability: CertificationAvailability[];
  hasPassedGreenFlag: boolean;
  greenFlagApprovedYearsAgo: number;
  greenFlagR1ApprovedYearsAgo: number;
  greenFlagR2ApprovedYearsAgo: number;
}
```


**CertificationDetailRequest**

```typescript
interface CertificationDetailRequest {
  certificationId: number;
}
```


**CertificationDetailResponse**

```typescript
interface CertificationDetailResponse {
  success: boolean;
  data: Certification;
  message?: string;
}
```


**CertificationCreateResponse**

```typescript
interface CertificationCreateResponse {
  success: boolean;
  data: Certification;
  message: string;
}
```


**CertificationUpdateResponse**

```typescript
interface CertificationUpdateResponse {
  success: boolean;
  data: Certification;
  message: string;
}
```


**CertificationDeleteResponse**

```typescript
interface CertificationDeleteResponse {
  success: boolean;
  message: string;
}
```


**CertificationDetailParams**

```typescript
interface CertificationDetailParams {
  certificationId: number;
}
```


**CertificationUpdateParams**

```typescript
interface CertificationUpdateParams {
  certificationId: number;
}
```


**CertificationDeleteParams**

```typescript
interface CertificationDeleteParams {
  certificationId: number;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### dashboard

**文件**: `models/dashboard.ts`

#### 接口定義


**CityStatistics**

```typescript
interface CityStatistics {
  cityId: number;
  cityName: string;
  bronzeCount: number;
  silverCount: number;
  greenFlagCount: number;
  totalSchools: number;
}
```


**LatestCertification**

```typescript
interface LatestCertification {
  schoolName: string;
  certificationLevel: string;
  passDate: string;
  cityName: string;
}
```


**SchoolCertificationStatus**

```typescript
interface SchoolCertificationStatus {
  certificationId: string;
  level: number;
  levelName: string;
  status: string;
  reviewStatus: number;
  applyDate: string;
  reviewDate?: string;
  passDate?: string;
}
```


**SchoolArticle**

```typescript
interface SchoolArticle {
  articleId: string;
  title: string;
  summary?: string;
  status: string;
  publishDate?: string;
  createDate: string;
}
```


**SchoolPassedCertification**

```typescript
interface SchoolPassedCertification {
  certificationId: string;
  level: number;
  levelName: string;
  passDate: string;
}
```


**SchoolInfo**

```typescript
interface SchoolInfo {
  schoolId: string;
  schoolName: string;
  accountId: number;
}
```


**UserCityInfo**

```typescript
interface UserCityInfo {
  countyId: number;
  cityName: string;
}
```


**CityQueryResult**

```typescript
interface CityQueryResult {
  cityId: number;
  cityName: string;
}
```


**StatisticsQueryResult**

```typescript
interface StatisticsQueryResult {
  totalSchools: number;
  bronzeCount: number;
  silverCount: number;
  greenFlagCount: number;
}
```


**CertificationQueryResult**

```typescript
interface CertificationQueryResult {
  CertificationId: string;
  Level: number;
  ReviewStatus: number;
  applyDate: string;
  ReviewDate?: string;
  passDate?: string;
  levelName: string;
}
```


**LatestCertificationQueryResult**

```typescript
interface LatestCertificationQueryResult {
  schoolName: string;
  certificationLevel: string;
  passDate: string;
  cityName: string;
}
```


**TestResponse**

```typescript
interface TestResponse {
  success: boolean;
  data: CityStatistics;
  message: string;
}
```


**CityStatisticsResponse**

```typescript
interface CityStatisticsResponse {
  success: boolean;
  data: CityStatistics;
  message: string;
}
```


**LatestCertificationsResponse**

```typescript
interface LatestCertificationsResponse {
  success: boolean;
  data: LatestCertification[];
  message: string;
}
```


**SchoolCurrentCertificationResponse**

```typescript
interface SchoolCurrentCertificationResponse {
  success: boolean;
  data: SchoolCertificationStatus | null;
  message: string;
}
```


**SchoolArticlesResponse**

```typescript
interface SchoolArticlesResponse {
  success: boolean;
  data: SchoolArticle[];
  message: string;
}
```


**SchoolPassedCertificationsResponse**

```typescript
interface SchoolPassedCertificationsResponse {
  success: boolean;
  data: SchoolPassedCertification[];
  message: string;
}
```


**CityStatisticsParams**

```typescript
interface CityStatisticsParams {
  cityId: string;
}
```


**LatestCertificationsParams**

```typescript
interface LatestCertificationsParams {
  cityId: string;
}
```


**LatestCertificationsQueryParams**

```typescript
interface LatestCertificationsQueryParams {
  limit?: string;
}
```


**SchoolArticlesQueryParams**

```typescript
interface SchoolArticlesQueryParams {
  limit?: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### file

**文件**: `models/file.ts`

#### 接口定義


**UploadedFile**

```typescript
interface UploadedFile {
  originalName: string;
  filename: string;
  fileUrl: string;
  size: number;
  mimetype: string;
  uploadDate: string;
}
```


**FileInfo**

```typescript
interface FileInfo {
  filename: string;
  size: number;
  createdAt: Date;
  modifiedAt: Date;
  fileUrl: string;
}
```


**SupportedFileTypes**

```typescript
interface SupportedFileTypes {
  images: string[];
  documents: string[];
  videos: string[];
}
```


**SchoolLogoInfo**

```typescript
interface SchoolLogoInfo {
  logoUrl: string | null;
  fileName: string | null;
  fileId?: string;
  fileSize?: number;
  fileType?: string;
}
```


**UploadDiagnostics**

```typescript
interface UploadDiagnostics {
  timestamp: string;
  environment: string;
  config: {;
  basePath: string;
  schoolLogoPath: string;
  generalUploadPath: string;
}
```


**DirectoryStatus**

```typescript
interface DirectoryStatus {
  exists: boolean;
  path: string;
  error?: string;
}
```


**PermissionStatus**

```typescript
interface PermissionStatus {
  readable: boolean;
  writable: boolean;
  writeError?: string;
}
```


**FileEntry**

```typescript
interface FileEntry {
  id: string;
  type: string;
  path: string;
  originalFileName: string;
  originalExtension: string;
  fileName: string;
  extension: string;
  createdTime?: Date;
  updatedTime?: Date;
}
```


**SchoolInfo**

```typescript
interface SchoolInfo {
  id: number;
}
```


**SchoolContent**

```typescript
interface SchoolContent {
  schoolId: number;
  localeCode: string;
  logoFileId: string;
  name?: string;
  createdUserId?: number;
  updatedUserId?: number;
}
```


**FileUploadRequest**

```typescript
interface FileUploadRequest {
  file_type?: string;
  certification_sid?: string;
  question_sid?: string;
}
```


**FileEntryQueryResult**

```typescript
interface FileEntryQueryResult {
  Id: string;
  Type: string;
  Path: string;
  OriginalFileName: string;
  OriginalExtension: string;
  FileName: string;
  Extension: string;
  CreatedTime?: Date;
  UpdatedTime?: Date;
}
```


**SchoolQueryResult**

```typescript
interface SchoolQueryResult {
  Id: number;
}
```


**SchoolLogoQueryResult**

```typescript
interface SchoolLogoQueryResult {
  logoUrl: string;
  FileName: string;
}
```


**FileUploadResponse**

```typescript
interface FileUploadResponse {
  success: boolean;
  message: string;
  data: UploadedFile | UploadedFile[];
}
```


**SingleFileUploadResponse**

```typescript
interface SingleFileUploadResponse {
  success: boolean;
  message: string;
  data: {;
  id: string;
  url: string;
  fileName: string;
  fileType: string;
  fileSize: number;
}
```


**FileDeleteResponse**

```typescript
interface FileDeleteResponse {
  success: boolean;
  message: string;
}
```


**FileInfoResponse**

```typescript
interface FileInfoResponse {
  success: boolean;
  data?: FileInfo;
  message?: string;
}
```


**SupportedTypesResponse**

```typescript
interface SupportedTypesResponse {
  success: boolean;
  data: SupportedFileTypes;
}
```


**SchoolLogoUploadResponse**

```typescript
interface SchoolLogoUploadResponse {
  success: boolean;
  message: string;
  data: SchoolLogoInfo;
}
```


**SchoolLogoGetResponse**

```typescript
interface SchoolLogoGetResponse {
  success: boolean;
  data: SchoolLogoInfo;
}
```


**UploadDiagnosticsResponse**

```typescript
interface UploadDiagnosticsResponse {
  success: boolean;
  message: string;
  data: UploadDiagnostics;
}
```


**FileDeleteParams**

```typescript
interface FileDeleteParams {
  filename: string;
}
```


**FileInfoParams**

```typescript
interface FileInfoParams {
  filename: string;
}
```


**FileDownloadParams**

```typescript
interface FileDownloadParams {
  filename: string;
}
```


**SchoolLogoGetParams**

```typescript
interface SchoolLogoGetParams {
  accountId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### location

**文件**: `models/location.ts`

#### 接口定義


**LocationArea**

```typescript
interface LocationArea {
  id: number;
  name: string;
  cityId: number;
}
```


**LocationCity**

```typescript
interface LocationCity {
  id: number;
  name: string;
}
```


**LocationCityWithAreas**

```typescript
interface LocationCityWithAreas {
  id: number;
  name: string;
  areas: LocationArea[];
}
```


**CityQueryResult**

```typescript
interface CityQueryResult {
  CountyId: number;
  Name: string;
}
```


**AreaQueryResult**

```typescript
interface AreaQueryResult {
  DistrictId: number;
  Name: string;
  CountyId: number;
}
```


**HierarchyQueryResult**

```typescript
interface HierarchyQueryResult {
  CountyId: number;
  CountyName: string;
  DistrictId?: number;
  DistrictName?: string;
}
```


**LocationCitiesResponse**

```typescript
interface LocationCitiesResponse {
  success: boolean;
  message?: string;
  data?: LocationCity[];
  error?: string;
}
```


**LocationAreasResponse**

```typescript
interface LocationAreasResponse {
  success: boolean;
  message?: string;
  data?: LocationArea[];
  error?: string;
}
```


**LocationHierarchyResponse**

```typescript
interface LocationHierarchyResponse {
  success: boolean;
  message?: string;
  data?: LocationCityWithAreas[];
  error?: string;
}
```


**LocationAreasParams**

```typescript
interface LocationAreasParams {
  cityId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### profile

**文件**: `models/profile.ts`

#### 接口定義


**MemberProfile**

```typescript
interface MemberProfile {
  accountId: number;
  account: string;
  email: string;
  telephone?: string;
  phone?: string;
  address?: string;
  countyId?: number;
  isSchoolPartner: number;
  isEpaUser: number;
  isGuidanceTeam: number;
  memberName?: string;
  memberEmail?: string;
  memberTelephone?: string;
  memberPhone?: string;
  memberAddress?: string;
  memberUrl?: string;
  jobTitle?: string;
  placeName?: string;
  memberRole?: string;
  memberIntroduction?: string;
  memberExchange?: string;
  memberNameEn?: string;
  countyNameZh?: string;
  countyNameEn?: string;
}
```


**SchoolInfo**

```typescript
interface SchoolInfo {
  schoolId: number;
  schoolCode?: string;
  schoolPhone?: string;
  schoolMobile?: string;
  schoolEmail?: string;
  schoolCountyId?: number;
  schoolDistrictId?: number;
  schoolName?: string;
  schoolAddress?: string;
  schoolDepartment?: string;
  schoolJobTitle?: string;
  schoolIntroduction?: string;
  schoolWebsite?: string;
  schoolNameEn?: string;
  schoolAddressEn?: string;
  schoolCountyNameZh?: string;
  schoolCountyNameEn?: string;
  logoPath?: string;
}
```


**PrincipalInfo**

```typescript
interface PrincipalInfo {
  principalName?: string;
  principalTelephone?: string;
  principalPhone?: string;
  principalEmail?: string;
}
```


**ContactInfo**

```typescript
interface ContactInfo {
  contactId: number;
  contactName: string;
  jobTitle: string;
  contactTelephone?: string;
  contactPhone?: string;
  contactEmail?: string;
}
```


**SchoolStatistics**

```typescript
interface SchoolStatistics {
  schoolStatisticsId: number;
  staffTotal: number;
  elementary1: number;
  elementary2: number;
  elementary3: number;
  elementary4: number;
  elementary5: number;
  elementary6: number;
  middle7: number;
  middle8: number;
  middle9: number;
  high10: number;
  high11: number;
  high12: number;
  writeDate: Date;
}
```


**MemberQueryResult**

```typescript
interface MemberQueryResult {
  AccountId: number;
  account: string;
  email: string;
  tel: string;
  phone: string;
  address: string;
  account_county_id: number;
  is_school_partner: number;
  is_epa_user: number;
  is_guidance_team: number;
  member_cname_zh: string;
  member_email: string;
  member_tel: string;
  member_phone: string;
  member_address: string;
  member_url: string;
  job_cname: string;
  place_cname: string;
  member_role: string;
  member_Introduction: string;
  member_exchange: string;
  member_cname_en: string;
  county_name_zh: string;
  county_name_en: string;
  school_county_name_zh: string;
  school_county_name_en: string;
  school_id: number;
  school_code: string;
  school_phone: string;
  school_mobile: string;
  school_email: string;
  school_county_id: number;
  school_district_id: number;
  school_name: string;
  school_address: string;
  school_department: string;
  school_job_title: string;
  school_introduction: string;
  school_website: string;
  school_name_en: string;
  school_address_en: string;
  principal_cname: string;
  principal_tel: string;
  principal_phone: string;
  principal_email: string;
  school_logo_path?: string;
}
```


**ContactQueryResult**

```typescript
interface ContactQueryResult {
  contact_cname: string;
  contact_job_title: string;
  contact_tel: string;
  contact_phone: string;
  contact_email: string;
  contact_sid: number;
}
```


**StatisticsQueryResult**

```typescript
interface StatisticsQueryResult {
  staff_total: number;
  elementary1: number;
  elementary2: number;
  elementary3: number;
  elementary4: number;
  elementary5: number;
  elementary6: number;
  middle7: number;
  middle8: number;
  middle9: number;
  hight10: number;
  hight11: number;
  hight12: number;
  write_date: Date;
  school_statistics_sid: number;
}
```


**UpdateMemberProfileRequest**

```typescript
interface UpdateMemberProfileRequest {
  jobTitle?: string;
  memberName?: string;
  memberNameEn?: string;
  officePhone?: string;
  mobilePhone?: string;
  email?: string;
}
```


**UpdateSchoolBasicRequest**

```typescript
interface UpdateSchoolBasicRequest {
  schoolName?: string;
  schoolNameEn?: string;
  countyId?: number;
  districtId?: number;
  address?: string;
  addressEn?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  website?: string;
}
```


**UpdatePrincipalRequest**

```typescript
interface UpdatePrincipalRequest {
  principalName?: string;
  principalPhone?: string;
  principalMobile?: string;
  principalEmail?: string;
}
```


**UpdateContactsRequest**

```typescript
interface UpdateContactsRequest {
  contacts: Array<{;
  contactId?: number;
  contactName: string;
  jobTitle: string;
  contactPhone?: string;
  contactMobile?: string;
  contactEmail?: string;
}
```


**UpdateStatisticsRequest**

```typescript
interface UpdateStatisticsRequest {
  staffTotal?: number;
  elementary1?: number;
  elementary2?: number;
  elementary3?: number;
  elementary4?: number;
  elementary5?: number;
  elementary6?: number;
  middle7?: number;
  middle8?: number;
  middle9?: number;
  high10?: number;
  high11?: number;
  high12?: number;
}
```


**ProfileResponse**

```typescript
interface ProfileResponse {
  success: boolean;
  data: FullProfile;
  message?: string;
}
```


**UpdateProfileResponse**

```typescript
interface UpdateProfileResponse {
  success: boolean;
  message: string;
  data?: {;
  updated: boolean;
  profile?: FullProfile;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### question

**文件**: `models/question.ts`

#### 接口定義


**QuestionModel**

```typescript
interface QuestionModel {
  id: number;
  title: string;
  parentId: number | null;
  step: number;
  sequence: number;
  isRequired: boolean;
  templateId: number | null;
  isRenewed: boolean;
  status: number;
  createdTime?: Date;
  updatedTime?: Date;
}
```


**StepInfo**

```typescript
interface StepInfo {
  step: number;
  title: string;
  description: string;
  questionCount: number;
  isEnabled: boolean;
}
```


**FormQuestion**

```typescript
interface FormQuestion {
  sid: number;
  title: string;
  question_tpl: number;
  is_renew: number;
  step: number;
  sequence: number;
  parent_id: number | null;
}
```


**QuestionGroup**

```typescript
interface QuestionGroup {

}
```


**StepQuestionGroup**

```typescript
interface StepQuestionGroup {

}
```


**StepStatistics**

```typescript
interface StepStatistics {
  step: number;
  isGreenFlag: boolean;
  totalQuestions: number;
  parentGroups: number;
}
```


**ParentGroupAnalysis**

```typescript
interface ParentGroupAnalysis {
  parentId: number;
  childrenCount: number;
  children: Array<{;
  id: number;
  title: string;
  step: number;
  template: number | null;
}
```


**QuestionStructureAnalysis**

```typescript
interface QuestionStructureAnalysis {
  totalQuestions: number;
  stepAnalysis: Array<{;
  step: number;
  isGreenFlag: boolean;
  total: number;
  withParent: number;
  uniqueParents: number;
}
```


**QuestionListResponse**

```typescript
interface QuestionListResponse {
  success: boolean;
  data: QuestionModel[];
  step: number;
  level: number;
  count: number;
  message?: string;
}
```


**CertificationStepsResponse**

```typescript
interface CertificationStepsResponse {
  success: boolean;
  data: StepInfo[];
  level: number;
  totalSteps: number;
  message?: string;
}
```


**FormQuestionsResponse**

```typescript
interface FormQuestionsResponse {
  success: boolean;
  data: {;
  questions: StepQuestionGroup;
  stepInfo: StepStatistics[];
}
```


**QuestionDetailResponse**

```typescript
interface QuestionDetailResponse {
  success: boolean;
  data: QuestionModel | {;
}
```


**QuestionAnalysisResponse**

```typescript
interface QuestionAnalysisResponse {
  success: boolean;
  data: QuestionStructureAnalysis;
  message?: string;
}
```


**QuestionQueryParams**

```typescript
interface QuestionQueryParams {
  step?: string;
  level?: string;
}
```


**CertificationStepsQueryParams**

```typescript
interface CertificationStepsQueryParams {
  level?: string;
}
```


**FormQuestionsQueryParams**

```typescript
interface FormQuestionsQueryParams {
  certificationId?: string;
}
```


**QuestionDetailParams**

```typescript
interface QuestionDetailParams {
  questionId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### template-answer

**文件**: `models/template-answer.ts`

#### 接口定義


**TemplateAnswerSaveRequest**

```typescript
interface TemplateAnswerSaveRequest {
  certification_sid: string;
  question_sid: string;
  template_id: number;
  answer_data: Record<string, unknown>;
  question_title?: string;
}
```


**TemplateAnswerValidateRequest**

```typescript
interface TemplateAnswerValidateRequest {
  template_id: number;
  answer_data: Record<string, unknown>;
}
```


**TemplateAnswerValidationResult**

```typescript
interface TemplateAnswerValidationResult {
  isValid: boolean;
  errors: string[];
}
```


**TemplateAnswerSaveResult**

```typescript
interface TemplateAnswerSaveResult {
  certification_sid: string;
  question_sid: string;
  template_id: number;
  question_title: string;
  answer_json: string;
  raw_answer_data: Record<string, unknown>;
  validation_result: TemplateAnswerValidationResult;
  timestamp: string;
  status: string;
}
```


**YesNoAnswerData**

```typescript
interface YesNoAnswerData {
  is_yes_no: string;
}
```


**TeamMemberAnswerData**

```typescript
interface TeamMemberAnswerData {
  student_list: Array<{;
  input_1: string;
  input_2: string;
  input_3: string;
}
```


**MeetingRecordAnswerData**

```typescript
interface MeetingRecordAnswerData {
  meeting_date_and_theme: Array<{;
  input_1: string;
  input_2: string;
}
```


**ShareMeetingAnswerData**

```typescript
interface ShareMeetingAnswerData {
  is_yes_no: string;
  share_people: {;
  checkbox: string[];
}
```


**RecruitMemberAnswerData**

```typescript
interface RecruitMemberAnswerData {
  is_yes_no: string;
  textarea: string;
}
```


**PhotoRecordAnswerData**

```typescript
interface PhotoRecordAnswerData {
  photo_record: Array<{;
  photo_url: string;
  photo_name: string;
  description?: string;
}
```


**EnvironmentPathAnswerData**

```typescript
interface EnvironmentPathAnswerData {
  improve_path_list: Array<{;
  path: string;
  cname: string;
  date: {;
  input_1: string;
  input_2: string;
}
```


**TextAreaAnswerData**

```typescript
interface TextAreaAnswerData {
  textarea: string;
}
```


**SummaryAnswerData**

```typescript
interface SummaryAnswerData {
  textarea_1: string;
  textarea_2: string;
  textarea_3: string;
  textarea_4: string;
  textarea_5: string;
}
```


**RecertificationMeetingAnswerData**

```typescript
interface RecertificationMeetingAnswerData {
  textarea_1: string;
  textarea_2: string;
  textarea_3: string;
  textarea_4: string;
  textarea_5: string;
  textarea_6: string;
  textarea_7: string;
}
```


**TemplateAnswerSaveResponse**

```typescript
interface TemplateAnswerSaveResponse {
  success: boolean;
  message: string;
  data?: TemplateAnswerSaveResult;
  required?: string[];
  errors?: string[];
  error?: string;
}
```


**TemplateAnswerValidateResponse**

```typescript
interface TemplateAnswerValidateResponse {
  success: boolean;
  message?: string;
  data?: TemplateAnswerValidationResult;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---
### user

**文件**: `models/user.ts`

#### 接口定義


**UserProfile**

```typescript
interface UserProfile {
  id: string;
  account: string;
  nickName: string;
  email?: string;
  phone?: string;
  avatar?: string;
  roleType: string;
  isActive: boolean;
  createdTime: Date;
  updatedTime: Date;
  remark?: string;
  permissions: string[];
  permissionGroups: string[];
  school?: SchoolInfo;
  certifications: CertificationInfo[];
}
```


**SchoolInfo**

```typescript
interface SchoolInfo {
  id: number;
  name: string;
  englishName?: string;
  code?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
}
```


**CertificationInfo**

```typescript
interface CertificationInfo {
  id: string;
  certificationType: string;
  level: number;
  status: string;
  applyDate: Date;
  reviewDate?: Date;
  passDate?: Date;
  expiredDate?: Date;
  certificateNumber?: string;
}
```


**UserProfileUpdateRequest**

```typescript
interface UserProfileUpdateRequest {
  nickName?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  school?: {;
  name?: string;
  englishName?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
}
```


**PermissionInfo**

```typescript
interface PermissionInfo {
  code: string;
  name: string;
  description?: string;
}
```


**PermissionGroupInfo**

```typescript
interface PermissionGroupInfo {
  code: string;
  name: string;
  description?: string;
}
```


**UserPermissions**

```typescript
interface UserPermissions {
  permissions: PermissionInfo[];
  permissionGroups: PermissionGroupInfo[];
}
```


**AccountQueryResult**

```typescript
interface AccountQueryResult {
  Id: string;
  Account: string;
  NickName: string;
  Email?: string;
  Phone?: string;
  Avatar?: string;
  RoleType: string;
  IsActive: boolean;
  CreatedTime: Date;
  UpdatedTime: Date;
  Remark?: string;
  CmsUserId?: number;
}
```


**SchoolQueryResult**

```typescript
interface SchoolQueryResult {
  Id: number;
  Name: string;
  EnglishName?: string;
  Code?: string;
  Address?: string;
  Phone?: string;
  Email?: string;
  Website?: string;
  ContactPerson?: string;
  ContactPhone?: string;
  ContactEmail?: string;
  IsActive: boolean;
  CreatedTime: Date;
  UpdatedTime: Date;
}
```


**CertificationQueryResult**

```typescript
interface CertificationQueryResult {
  Id: number;
  Level?: number;
  ReviewStatus?: number;
  CreatedTime?: Date;
  ReviewDate?: Date;
  ApprovedDate?: Date;
  ExpiredDate?: Date;
  CertificateNumber?: string;
}
```


**PermissionQueryResult**

```typescript
interface PermissionQueryResult {
  Code: string;
  Name: string;
  Description?: string;
}
```


**PermissionGroupQueryResult**

```typescript
interface PermissionGroupQueryResult {
  Code: string;
  Name: string;
  Description?: string;
}
```


**AccountUpdateFields**

```typescript
interface AccountUpdateFields {
  NickName?: string;
  Email?: string;
  Phone?: string;
  Avatar?: string;
}
```


**SchoolUpdateFields**

```typescript
interface SchoolUpdateFields {
  Name?: string;
  EnglishName?: string;
  Address?: string;
  Phone?: string;
  Email?: string;
  Website?: string;
  ContactPerson?: string;
  ContactPhone?: string;
  ContactEmail?: string;
}
```


**UserProfileResponse**

```typescript
interface UserProfileResponse {
  success: boolean;
  data: UserProfile;
  message?: string;
}
```


**UserProfileUpdateResponse**

```typescript
interface UserProfileUpdateResponse {
  success: boolean;
  data: UserProfile;
  message: string;
}
```


**UserCertificationsResponse**

```typescript
interface UserCertificationsResponse {
  success: boolean;
  data: CertificationQueryResult[];
}
```


**UserSchoolResponse**

```typescript
interface UserSchoolResponse {
  success: boolean;
  data: SchoolQueryResult;
}
```


**UserPermissionsResponse**

```typescript
interface UserPermissionsResponse {
  success: boolean;
  data: UserPermissions;
}
```


**UserProfileParams**

```typescript
interface UserProfileParams {
  userId: string;
}
```

#### 導出項目

- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
- `interface`
---

## 🔧 常數定義

### admin

**文件**: `constants/admin.ts`

#### 常數定義


**ANSWER_STATUS**

```typescript
export const ANSWER_STATUS = {
  NOT_FILLED: 0, // 未填寫
  FILLED: 1, // 已填寫
  NEEDS_SUPPLEMENT: 2, // 待補件
  REJECTED: 3, // 退件
  COMPLETED: 4, // 已完成
} as const;
```


**REVIEW_STATUS**

```typescript
export const REVIEW_STATUS = {
  IN_REVIEW: 0, // 審核中
  APPROVED: 1, // 已通過
  REJECTED: 2, // 已拒絕
  NEEDS_SUPPLEMENT: 3, // 需要補件
  NOT_SUBMITTED: 4, // 未提交
} as const;
```


**CERTIFICATION_STATUS**

```typescript
export const CERTIFICATION_STATUS = {
  INACTIVE: 0, // 非活躍
  ACTIVE: 1, // 活躍
  DELETED: 2, // 已刪除
} as const;
```


**ADMIN_ACTION_TYPES**

```typescript
export const ADMIN_ACTION_TYPES = {
  REQUEST_SUPPLEMENT: "request_supplement", // 要求補件
  APPROVE: "approve", // 批准
  REJECT: "reject", // 拒絕
  COMMENT: "comment", // 評論
} as const;
```


**ANSWER_STATUS_DESCRIPTIONS**

```typescript
export const ANSWER_STATUS_DESCRIPTIONS = {
  [ANSWER_STATUS.NOT_FILLED]: "未填寫",
  [ANSWER_STATUS.FILLED]: "已填寫",
  [ANSWER_STATUS.NEEDS_SUPPLEMENT]: "待補件",
  [ANSWER_STATUS.REJECTED]: "退件",
  [ANSWER_STATUS.COMPLETED]: "已審核",
} as const;
```


**ADMIN_ROLES**

```typescript
export const ADMIN_ROLES = {
  GOVERNMENT: "Government",
  ADMIN: "admin",
} as const;
```


**ADMIN_PERMISSIONS**

```typescript
export const ADMIN_PERMISSIONS = {
  ADMIN: "admin",
  REVIEW: "review",
  MANAGE: "manage",
} as const;
```


**ADMIN_PERMISSION_GROUPS**

```typescript
export const ADMIN_PERMISSION_GROUPS = {
  ADMIN: "admin",
  REVIEWER: "reviewer",
  MANAGER: "manager",
} as const;
```


**COMMENT_LIMITS**

```typescript
export const COMMENT_LIMITS = {
  MAX_LENGTH: 20, // 評審意見最大長度
  MIN_LENGTH: 1, // 評審意見最小長度
} as const;
```


**ADMIN_ERROR_MESSAGES**

```typescript
export const ADMIN_ERROR_MESSAGES = {
  MISSING_PARAMETERS: "Missing required parameters",
  INSUFFICIENT_PERMISSIONS: "Insufficient permissions. Admin role required.",
  CERTIFICATION_NOT_FOUND: "Certification not found",
  ANSWER_NOT_FOUND: "Answer not found for the specified question",
  NOT_IN_REVIEW_STATUS: "Can only mark answers for certifications in review status",
  INVALID_ACTION: "Invalid action",
  COMMENT_TOO_LONG: "評審意見不得超過20個字",
  COMMENT_TOO_SHORT: "評審意見不能為空",
  INTERNAL_SERVER_ERROR: "Internal server error",
  CERTIFICATION_NOT_IN_REVIEW: "Certification is not in review status",
} as const;
```


**ADMIN_SUCCESS_MESSAGES**

```typescript
export const ADMIN_SUCCESS_MESSAGES = {
  ANSWER_STATUS_MARKED: "Answer status marked successfully",
  ACTION_LOGS_RETRIEVED: "Action logs retrieved successfully",
  REVIEW_COMMENT_SAVED: "評審意見保存成功",
  REVIEW_COMMENT_UPDATED: "評審意見更新成功",
  REVIEW_COMMENTS_RETRIEVED: "Review comments retrieved successfully",
} as const;
```


**SQL_QUERIES**

```typescript
export const SQL_QUERIES = {
  GET_CERTIFICATION: `
    SELECT CertificationId, ReviewStatus, Status
    FROM Certifications 
    WHERE CertificationId = @certificationId AND Status = @activeStatus
  `,
  GET_ANSWER: `
    SELECT AnswerId, QuestionId, AnswerStatus
    FROM CertificationAnswers 
    WHERE CertificationId = @certificationId AND QuestionId = @questionId
  `,
  UPDATE_ANSWER_STATUS: `
    UPDATE CertificationAnswers 
    SET AnswerStatus = @answerStatus, UpdatedTime = GETDATE()
    WHERE AnswerId = @answerId
  `,
  INSERT_ACTION_LOG: `
    INSERT INTO AdminActionLogs (
      CertificationId, QuestionId, AnswerId, AdminUserId, Action, 
      PreviousStatus, NewStatus, ActionTime, Notes
    ) VALUES (
      @certificationId, @questionId, @answerId, @adminUserId, @action,
      @previousStatus, @newStatus, GETDATE(), @notes
    )
  `,
  GET_ACTION_LOGS: `
    SELECT 
      l.ActionLogId,
      l.CertificationId,
      l.QuestionId,
      l.AnswerId,
      l.Action,
      l.PreviousStatus,
      l.NewStatus,
      l.ActionTime,
      l.Notes,
      a.Username as AdminUsername,
      q.Title as QuestionTitle
    FROM AdminActionLogs l
    LEFT JOIN Accounts a ON l.AdminUserId = a.AccountId
    LEFT JOIN Questions q ON l.QuestionId = q.QuestionId
    WHERE l.CertificationId = @certificationId
    ORDER BY l.ActionTime DESC
  `,
  GET_EXISTING_STEP_RECORD: `
    SELECT CertificationStepRecordId 
    FROM CertificationStepRecords 
    WHERE CertificationId = @certificationId AND StepNumber = @stepNumber
  `,
  UPDATE_STEP_RECORD: `
    UPDATE CertificationStepRecords 
    SET StepOpinion = @comment,
        UpdatedUserId = @adminUserId,
        UpdatedTime = GETDATE()
    WHERE CertificationStepRecordId = @recordId
  `,
  INSERT_STEP_RECORD: `
    INSERT INTO CertificationStepRecords (
      CertificationId, StepNumber, StepOpinion, CreatedUserId, UpdatedUserId, UpdatedTime
    ) VALUES (
      @certificationId, @stepNumber, @comment, @adminUserId, @adminUserId, GETDATE()
    )
  `,
  GET_REVIEW_COMMENTS: `
    SELECT 
      csr.CertificationStepRecordId,
      csr.CertificationId,
      csr.StepNumber,
      csr.StepOpinion as Comment,
      csr.CreatedTime,
      csr.UpdatedTime,
      a.Username as AdminUsername
    FROM CertificationStepRecords csr
    LEFT JOIN Accounts a ON csr.UpdatedUserId = a.AccountId
    WHERE csr.CertificationId = @certificationId 
      AND csr.StepOpinion IS NOT NULL 
      AND csr.StepOpinion != ''
    ORDER BY csr.StepNumber, csr.CreatedTime DESC
  `,
} as const;
```


**isAdmin**

```typescript
export const isAdmin = (user: { roleType?: string;
```


**getAnswerStatusInfo**

```typescript
export const getAnswerStatusInfo = (action: string): { status: number;
```


**isValidAction**

```typescript
export const isValidAction = (action: string): boolean => {
  return Object.values(ADMIN_ACTION_TYPES).includes(action as any);
```


**parseStepId**

```typescript
export const parseStepId = (stepId: string): number => {
  return parseInt(stepId.replace("step_", "")) || parseInt(stepId);
```


**validateComment**

```typescript
export const validateComment = (comment: string): { isValid: boolean;
```


**formatActionLog**

```typescript
export const formatActionLog = (log: any): AdminActionLog => {
  return {
    actionLogId: log.ActionLogId,
    certificationId: log.CertificationId,
    questionId: log.QuestionId,
    answerId: log.AnswerId,
    action: log.Action,
    previousStatus: log.PreviousStatus,
    newStatus: log.NewStatus,
    actionTime: log.ActionTime,
    notes: log.Notes,
    adminUsername: log.AdminUsername,
    questionTitle: log.QuestionTitle,
  };
```


**formatReviewComment**

```typescript
export const formatReviewComment = (comment: any): ReviewComment => {
  return {
    certificationStepRecordId: comment.CertificationStepRecordId,
    certificationId: comment.CertificationId,
    stepNumber: comment.StepNumber,
    comment: comment.Comment,
    createdTime: comment.CreatedTime,
    updatedTime: comment.UpdatedTime,
    adminUsername: comment.AdminUsername,
  };
```


**canAdminOperateCertification**

```typescript
export const canAdminOperateCertification = (certification: { ReviewStatus: number }): boolean => {
  return certification.ReviewStatus === REVIEW_STATUS.IN_REVIEW;
```


**generateActionNote**

```typescript
export const generateActionNote = (action: string, statusDescription: string): string => {
  return `管理員標示答案為${statusDescription}`;
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### answer

**文件**: `constants/answer.ts`

#### 常數定義


**ANSWER_STATUS**

```typescript
export const ANSWER_STATUS = {
  NOT_FILLED: 0, // 未填寫/已刪除
  FILLED: 1, // 已填寫
  NEEDS_SUPPLEMENT: 2, // 待補件
  REJECTED: 3, // 退件
  COMPLETED: 4, // 已完成
} as const;
```


**REVIEW_STATUS**

```typescript
export const REVIEW_STATUS = {
  IN_REVIEW: 0, // 審核中
  APPROVED: 1, // 已通過
  REJECTED: 2, // 已拒絕
  NEEDS_SUPPLEMENT: 3, // 需要補件
  NOT_SUBMITTED: 4, // 未提交
} as const;
```


**QUESTION_STATUS**

```typescript
export const QUESTION_STATUS = {
  INACTIVE: 0, // 非活躍
  ACTIVE: 1, // 活躍
} as const;
```


**CERTIFICATION_STATUS**

```typescript
export const CERTIFICATION_STATUS = {
  INACTIVE: 0, // 非活躍
  ACTIVE: 1, // 活躍
  DELETED: 2, // 已刪除
} as const;
```


**ANSWER_ERROR_MESSAGES**

```typescript
export const ANSWER_ERROR_MESSAGES = {
  MISSING_PARAMETERS: "缺少必要參數：certificationId, questionId, answerData",
  MISSING_ANSWER_ID: "缺少答案ID",
  MISSING_CERTIFICATION_ID: "缺少認證ID",
  MISSING_QUESTION_OR_CERTIFICATION_ID: "缺少問題ID或認證ID",
  USER_NOT_ASSIGNED_SCHOOL: "用戶未分配學校，無權限修改認證",
  NO_PERMISSION_MODIFY: "無權限修改此認證",
  NO_PERMISSION_VIEW: "無權限查看此認證",
  NO_PERMISSION_DELETE: "無權限刪除此答案",
  CERTIFICATION_UNDER_REVIEW: "認證正在審核中，無法修改答案",
  QUESTION_NOT_FOUND: "問題不存在或已被刪除",
  ANSWER_NOT_FOUND: "答案不存在",
  ANSWER_ALREADY_DELETED: "答案不存在或已被刪除",
  SAVE_FAILED: "保存答案失敗，請稍後再試",
  QUERY_FAILED: "查詢答案失敗，請稍後再試",
  DELETE_FAILED: "刪除答案失敗，請稍後再試",
  JSON_PARSE_ERROR: "JSON 解析失敗",
} as const;
```


**ANSWER_SUCCESS_MESSAGES**

```typescript
export const ANSWER_SUCCESS_MESSAGES = {
  ANSWER_SAVED: "答案已保存",
  ANSWER_UPDATED: "答案已更新",
  ANSWER_DELETED: "答案已刪除",
  ANSWERS_RETRIEVED: "答案查詢成功",
  NO_ANSWER_FOUND: "尚未找到此問題的答案",
} as const;
```


**SQL_QUERIES**

```typescript
export const SQL_QUERIES = {
  GET_USER_SCHOOL: "SELECT SchoolId FROM Accounts WHERE AccountId = @userId",
  GET_CERTIFICATION_INFO: "SELECT SchoolId, ReviewStatus FROM Certifications WHERE CertificationId = @certificationId",
  GET_CERTIFICATION_SCHOOL: "SELECT SchoolId FROM Certifications WHERE CertificationId = @certificationId",
  GET_QUESTION_INFO: "SELECT QuestionId, QuestionTemplate FROM Questions WHERE QuestionId = @questionId AND Status = @activeStatus",
  GET_EXISTING_ANSWER: `
    SELECT CertificationAnswerId 
    FROM CertificationAnswers 
    WHERE CertificationId = @certificationId AND QuestionId = @questionId
  `,
  UPDATE_ANSWER: `
    UPDATE CertificationAnswers 
    SET AnswerText = @answerText,
        AnswerStatus = @answerStatus,
        UpdatedTime = GETDATE(),
        UpdatedUserId = @userId
    WHERE CertificationAnswerId = @answerId
  `,
  INSERT_ANSWER: `
    INSERT INTO CertificationAnswers (
      CertificationId,
      QuestionId,
      AnswerText,
      AnswerStatus,
      SubmittedDate,
      CreatedTime,
      CreatedUserId,
      UpdatedTime,
      UpdatedUserId,
      SortOrder
    ) 
    OUTPUT INSERTED.CertificationAnswerId
    VALUES (
      @certificationId,
      @questionId,
      @answerText,
      @answerStatus,
      NULL,
      GETDATE(),
      @userId,
      GETDATE(),
      @userId,
      0
    )
  `,
  GET_CERTIFICATION_ANSWERS: `
    SELECT 
      ca.CertificationAnswerId,
      ca.CertificationId,
      ca.QuestionId,
      ca.AnswerText,
      ca.AnswerStatus,
      ca.SubmittedDate,
      ca.ReviewedDate,
      ca.CreatedTime,
      ca.UpdatedTime,
      q.Title as QuestionTitle,
      q.QuestionTemplate,
      q.StepNumber,
      q.ParentQuestionId
    FROM CertificationAnswers ca
    LEFT JOIN Questions q ON ca.QuestionId = q.QuestionId
    WHERE ca.CertificationId = @certificationId
      AND q.Status = @activeStatus
    ORDER BY q.StepNumber, q.SortOrder, q.QuestionId
  `,
  GET_SINGLE_ANSWER: `
    SELECT 
      ca.CertificationAnswerId,
      ca.CertificationId,
      ca.QuestionId,
      ca.AnswerText,
      ca.AnswerStatus,
      ca.SubmittedDate,
      ca.ReviewedDate,
      ca.CreatedTime,
      ca.UpdatedTime,
      q.Title as QuestionTitle,
      q.QuestionTemplate,
      q.StepNumber,
      q.ParentQuestionId
    FROM CertificationAnswers ca
    LEFT JOIN Questions q ON ca.QuestionId = q.QuestionId
    WHERE ca.CertificationId = @certificationId 
      AND ca.QuestionId = @questionId
      AND q.Status = @activeStatus
  `,
  GET_ANSWER_FOR_DELETE: `
    SELECT 
      ca.CertificationAnswerId,
      ca.CertificationId,
      c.SchoolId
    FROM CertificationAnswers ca
    LEFT JOIN Certifications c ON ca.CertificationId = c.CertificationId
    WHERE ca.CertificationAnswerId = @answerId
  `,
  DELETE_ANSWER: `
    UPDATE CertificationAnswers 
    SET AnswerStatus = @deletedStatus,
        UpdatedTime = GETDATE(),
        UpdatedUserId = @userId
    WHERE CertificationAnswerId = @answerId
  `,
} as const;
```


**validateSaveParameters**

```typescript
export const validateSaveParameters = (request: {
  certificationId?: any;
```


**canModifyCertification**

```typescript
export const canModifyCertification = (reviewStatus: number): boolean => {
  return reviewStatus !== REVIEW_STATUS.IN_REVIEW;
```


**hasPermissionForCertification**

```typescript
export const hasPermissionForCertification = (userSchoolId: number, certificationSchoolId: number): boolean => {
  return userSchoolId === certificationSchoolId;
```


**safeJSONParse**

```typescript
export const safeJSONParse = (jsonString: string | null): any => {
  if (!jsonString) return null;
```


**formatAnswerRecord**

```typescript
export const formatAnswerRecord = (record: any): any => {
  return {
    answerId: record.CertificationAnswerId,
    certificationId: record.CertificationId,
    questionId: record.QuestionId,
    questionTitle: record.QuestionTitle,
    questionTemplate: record.QuestionTemplate,
    stepNumber: record.StepNumber,
    parentQuestionId: record.ParentQuestionId,
    answerData: safeJSONParse(record.AnswerText),
    answerStatus: record.AnswerStatus,
    submittedDate: record.SubmittedDate,
    reviewedDate: record.ReviewedDate,
    createdTime: record.CreatedTime,
    updatedTime: record.UpdatedTime,
  };
```


**generateDebugInfo**

```typescript
export const generateDebugInfo = (request: any): any => {
  return {
    certificationId: request.certificationId,
    questionId: request.questionId,
    hasAnswerData: !!request.answerData,
    userId: request.userId,
  };
```


**isValidId**

```typescript
export const isValidId = (id: any): boolean => {
  return id != null && !isNaN(parseInt(id));
```


**getOperationMessage**

```typescript
export const getOperationMessage = (isUpdate: boolean): string => {
  return isUpdate ? ANSWER_SUCCESS_MESSAGES.ANSWER_UPDATED : ANSWER_SUCCESS_MESSAGES.ANSWER_SAVED;
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### auth

**文件**: `constants/auth.ts`

#### 常數定義


**ALLOWED_ROLE_TYPES**

```typescript
export const ALLOWED_ROLE_TYPES = ["School", "Government", "Tutor"] as const;
```


**ROLE_TYPE_MAP**

```typescript
export const ROLE_TYPE_MAP = {
  SCHOOL: "School",
  GOVERNMENT: "Government",
  TUTOR: "Tutor",
} as const;
```


**ACCOUNT_STATUS**

```typescript
export const ACCOUNT_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;
```


**TOKEN_TYPE**

```typescript
export const TOKEN_TYPE = {
  LOGIN: "Login",
  SCHOOL_IDENTITY: "school_identity",
  EPA_IDENTITY: "epa_identity",
  TUTOR_IDENTITY: "tutor_identity",
} as const;
```


**TOKEN_STATUS**

```typescript
export const TOKEN_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
} as const;
```


**PASSWORD_CONFIG**

```typescript
export const PASSWORD_CONFIG = {
  MIN_LENGTH: 8,
  SALT_LENGTH: 32,
  HASH_ITERATIONS: 10000,
  HMAC_ALGORITHM: "sha256",
} as const;
```


**DEFAULT_TOKEN_VALIDITY_DAYS**

```typescript
export const DEFAULT_TOKEN_VALIDITY_DAYS = {
  LOGIN: 30,
  IDENTITY: 3000,
  ADMIN: 1,
} as const;
```


**AUTH_ERROR_MESSAGES**

```typescript
export const AUTH_ERROR_MESSAGES = {
  TOKEN_REQUIRED: "Token is required",
  CREDENTIALS_REQUIRED: "Account and password are required",
  INVALID_TOKEN: "Invalid token",
  INVALID_CREDENTIALS: "Invalid account or password",
  ACCOUNT_DISABLED: "Access denied. Your account has been deactivated.",
  ROLE_NOT_ALLOWED: "Access denied. Your account role is not authorized to access this system.",
  PASSWORD_TOO_SHORT: `Password must be at least ${PASSWORD_CONFIG.MIN_LENGTH} characters long`,
  PASSWORD_REQUIREMENTS: "Password must contain at least one uppercase letter, one lowercase letter, and one number",
  OLD_PASSWORD_INCORRECT: "Old password is incorrect",
  ACCOUNT_NOT_FOUND: "Account not found",
  TOKEN_GENERATION_FAILED: "Failed to generate token",
  PASSWORD_UPDATE_FAILED: "Failed to update password",
  UNAUTHORIZED: "Unauthorized access",
  INTERNAL_ERROR: "Internal server error",
} as const;
```


**AUTH_SUCCESS_MESSAGES**

```typescript
export const AUTH_SUCCESS_MESSAGES = {
  TOKEN_LOGIN_SUCCESS: "Token login successful",
  PASSWORD_LOGIN_SUCCESS: "Password login successful",
  PASSWORD_CHANGED: "Password changed successfully",
  TOKEN_CREATED: "Token created successfully",
  LOGOUT_SUCCESS: "Logout successful",
  PASSWORDS_RESET: "Passwords reset successfully",
} as const;
```


**determineRoleTypeFromBooleanFields**

```typescript
export const determineRoleTypeFromBooleanFields = (is_school_partner?: boolean, is_epa_user?: boolean, is_guidance_team?: boolean): string => {
  if (is_school_partner) return ROLE_TYPE_MAP.SCHOOL;
```


**determineRoleTypeFromIntegerFields**

```typescript
export const determineRoleTypeFromIntegerFields = (IsSchoolPartner?: number, IsEpaUser?: number, IsGuidanceTeam?: number): string => {
  if (IsSchoolPartner === 1) return ROLE_TYPE_MAP.SCHOOL;
```


**mapRoleTypeToFrontendRole**

```typescript
export const mapRoleTypeToFrontendRole = (roleType: string): string => {
  return FRONTEND_ROLE_MAP[roleType] || "school";
```


**validatePasswordStrength**

```typescript
export const validatePasswordStrength = (password: string): { valid: boolean;
```


**isAllowedRole**

```typescript
export const isAllowedRole = (roleType: string): boolean => {
  return ALLOWED_ROLE_TYPES.includes(roleType as (typeof ALLOWED_ROLE_TYPES)[number]);
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### campus-submission

**文件**: `constants/campus-submission.ts`

#### 常數定義


**SUBMISSION_STATUS**

```typescript
export const SUBMISSION_STATUS = {
  NOT_SUBMITTED: -1,
  UNDER_REVIEW: 0,
  PUBLISHED: 1,
  DELETED: 2,
  REJECTED: 3,
} as const;
```


**REVIEW_STATUS**

```typescript
export const REVIEW_STATUS = {
  WITHDRAWN: 0,
  PUBLISHED: 1,
  REJECTED: 3,
} as const;
```


**BADGE_TYPE**

```typescript
export const BADGE_TYPE = {
  NONE: 0,
  BRONZE: 1,
  SILVER: 2,
  GOLD: 3,
  GREEN_FLAG: 4,
} as const;
```


**FEATURED_STATUS**

```typescript
export const FEATURED_STATUS = {
  NORMAL: 0,
  FEATURED: 1,
} as const;
```


**CONTENT_TYPE_CODE**

```typescript
export const CONTENT_TYPE_CODE = {
  IMAGE: "image",
  VIDEO: "video",
  DOCUMENT: "document",
  LINK: "link",
} as const;
```


**LOCALE_CODE**

```typescript
export const LOCALE_CODE = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;
```


**DEFAULT_PAGINATION**

```typescript
export const DEFAULT_PAGINATION = {
  LIMIT: 50,
  PAGE: 1,
} as const;
```


**DESCRIPTION_SUMMARY_LENGTH**

```typescript
export const DESCRIPTION_SUMMARY_LENGTH = 200;
```


**getStatusText**

```typescript
export const getStatusText = (status: number): string => {
  return STATUS_TEXT_MAP[status] || "未知狀態";
```


**getFinalStatus**

```typescript
export const getFinalStatus = (
  submissionStatus: number,
  reviewStatus?: number,
  hasReview?: boolean,
  reviewComment?: string
): { status: number;
```


**hasValidReview**

```typescript
export const hasValidReview = (reviewComment?: string): boolean => {
  return reviewComment !== undefined && reviewComment !== null && reviewComment !== "";
```


**formatDateToISOString**

```typescript
export const formatDateToISOString = (date: Date | string): string => {
  return new Date(date).toISOString().split("T")[0];
```


**generateDescriptionSummary**

```typescript
export const generateDescriptionSummary = (description: string): string => {
  if (!description) return "";
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### certificate

**文件**: `constants/certificate.ts`

#### 常數定義


**CERTIFICATE_STATUS**

```typescript
export const CERTIFICATE_STATUS = {
  VALID: "valid",
  INVALID: "invalid",
  EXPIRED: "expired",
} as const;
```


**TOKEN_TYPES**

```typescript
export const TOKEN_TYPES = {
  CERTIFICATE_LOGIN: "CERTIFICATE_LOGIN",
  NORMAL_LOGIN: "NORMAL_LOGIN",
} as const;
```


**ACCOUNT_STATUS**

```typescript
export const ACCOUNT_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  SUSPENDED: 2,
} as const;
```


**CERTIFICATE_ACTIONS**

```typescript
export const CERTIFICATE_ACTIONS = {
  BIND: "CERTIFICATE_BIND",
  UNBIND: "CERTIFICATE_UNBIND",
  LOGIN: "CERTIFICATE_LOGIN",
  VALIDATE: "CERTIFICATE_VALIDATE",
} as const;
```


**CERTIFICATE_REQUIREMENTS**

```typescript
export const CERTIFICATE_REQUIREMENTS = {
  MIN_DATA_LENGTH: 10,
  MIN_PASSWORD_LENGTH: 6,
  TOKEN_EXPIRY_DAYS: 30,
} as const;
```


**CERTIFICATE_ERROR_MESSAGES**

```typescript
export const CERTIFICATE_ERROR_MESSAGES = {
  MISSING_CERTIFICATE_DATA: "憑證資料不能為空",
  INVALID_CERTIFICATE: "憑證格式無效或已過期",
  MISSING_BIND_INFO: "憑證資訊和帳號ID不能為空",
  PERMISSION_DENIED_BIND: "只能綁定自己的帳號",
  PERMISSION_DENIED_UNBIND: "只能解除自己的憑證綁定",
  ACCOUNT_NOT_FOUND: "帳號不存在",
  NO_CERTIFICATE_BOUND: "此帳號未綁定任何憑證",
  UNBIND_FAILED: "解除憑證綁定失敗",
  MISSING_LOGIN_CREDENTIALS: "憑證資料和密碼不能為空",
  INVALID_PASSWORD: "憑證密碼錯誤",
  NO_BOUND_ACCOUNT: "未找到綁定此憑證的有效帳號",
  VALIDATION_ERROR: "憑證驗證過程發生錯誤",
  BIND_ERROR: "憑證綁定過程發生錯誤",
  UNBIND_ERROR: "解除憑證綁定過程發生錯誤",
  QUERY_ERROR: "查詢憑證綁定狀態發生錯誤",
  LOGIN_ERROR: "憑證登入過程發生錯誤",
  TEST_FAILED: "憑證測試失敗",
} as const;
```


**CERTIFICATE_SUCCESS_MESSAGES**

```typescript
export const CERTIFICATE_SUCCESS_MESSAGES = {
  VALIDATION_SUCCESS: "憑證驗證成功",
  BIND_SUCCESS: "憑證綁定成功",
  UNBIND_SUCCESS: "憑證綁定已成功解除",
  LOGIN_SUCCESS: "憑證登入成功",
  TEST_SUCCESS: "憑證API測試成功",
} as const;
```


**SQL_QUERIES**

```typescript
export const SQL_QUERIES = {
  CHECK_ACCOUNT_CERTIFICATE: "SELECT citizen_digital_number FROM Account WHERE sid = @accountId",
  UPDATE_UNBIND_CERTIFICATE: `
    UPDATE Account SET 
      citizen_digital_number = NULL,
      UpdateTime = GETDATE(),
      UpdateUser = @userId
    WHERE sid = @accountId
  `,
  GET_USER_BINDINGS: `
    SELECT 
      sid as accountId,
      citizen_digital_number as certificateId,
      UpdateTime as lastUpdateTime
    FROM Account 
    WHERE sid = @userId
  `,
  GET_ACCOUNT_BY_CERTIFICATE: `
    SELECT 
      a.sid as accountId,
      a.account as username,
      a.cname as displayName,
      a.isuse as status,
      mp.member_role as role
    FROM Account a
    LEFT JOIN MemberProfiles mp ON a.sid = mp.account_sid
    WHERE a.citizen_digital_number = @certificateId AND a.isuse = @activeStatus
  `,
  INSERT_TOKEN: `
    INSERT INTO UserToken (AccountSid, Token, TokenType, ExpiredTime, IsActive, CreatedTime)
    VALUES (@accountId, @token, @tokenType, @expiredTime, 1, GETDATE())
  `,
  INSERT_ACTION_LOG: `
    INSERT INTO sys_logs (account_sid, action, description, ip_address, user_agent, create_time)
    VALUES (@accountId, @action, @description, @ipAddress, @userAgent, GETDATE())
  `,
} as const;
```


**CERTIFICATE_PATTERNS**

```typescript
export const CERTIFICATE_PATTERNS = {
  BEGIN_CERTIFICATE: "BEGIN CERTIFICATE",
  COMMON_NAME: "CN=",
  MIN_CERT_LENGTH: 100,
} as const;
```


**DEMO_CERTIFICATE**

```typescript
export const DEMO_CERTIFICATE = {
  SUBJECT: "CN=測試用戶,OU=臺灣自然人憑證,O=政府憑證管理中心,C=TW",
  ISSUER: "CN=政府憑證管理中心,O=行政院,C=TW",
  VALID_FROM: "2023-01-01",
  VALID_TO: "2026-12-31",
  SERIAL_NUMBER_LENGTH: 16,
} as const;
```


**validateCertificateData**

```typescript
export const validateCertificateData = (certificateData: string): boolean => {
  try {
    if (!certificateData || certificateData.length < CERTIFICATE_REQUIREMENTS.MIN_DATA_LENGTH) {
      return false;
```


**parseCertificateInfo**

```typescript
export const parseCertificateInfo = (certificateData: string): any => {
  const crypto = require("crypto");
```


**validateCertificatePassword**

```typescript
export const validateCertificatePassword = (certificateData: string, password: string): boolean => {
  try {
    if (!password || password.length < CERTIFICATE_REQUIREMENTS.MIN_PASSWORD_LENGTH) {
      return false;
```


**hasPermissionForAccount**

```typescript
export const hasPermissionForAccount = (userId: string, targetAccountId: string): boolean => {
  return userId === targetAccountId;
```


**generateTokenExpiry**

```typescript
export const generateTokenExpiry = (): Date => {
  const expiryDate = new Date();
```


**formatBindingInfo**

```typescript
export const formatBindingInfo = (binding: any): any => {
  const hasCertificate = !!binding.certificateId;
```


**formatLoginResult**

```typescript
export const formatLoginResult = (account: any, token: string, expiryDate: Date): any => {
  return {
    token,
    user: {
      id: account.accountId,
      username: account.username,
      displayName: account.displayName,
      role: account.role || "user",
    },
    expiryDate: expiryDate.toISOString(),
  };
```


**generateLogDescription**

```typescript
export const generateLogDescription = (action: string): string => {
  switch (action) {
    case CERTIFICATE_ACTIONS.BIND:
      return "綁定自然人憑證";
```


**isValidAccount**

```typescript
export const isValidAccount = (account: any): boolean => {
  return account && account.status === ACCOUNT_STATUS.ACTIVE;
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### certification

**文件**: `constants/certification.ts`

#### 常數定義


**CERTIFICATION_LEVEL**

```typescript
export const CERTIFICATION_LEVEL = {
  BRONZE: 1,
  SILVER: 2,
  GREEN_FLAG: 3,
  GREEN_FLAG_R1: 4,
  GREEN_FLAG_R2: 5,
  GREEN_FLAG_R3: 6,
} as const;
```


**REVIEW_STATUS**

```typescript
export const REVIEW_STATUS = {
  UNDER_REVIEW: 0,
  APPROVED: 1,
  REJECTED: 2,
  NEEDS_SUPPLEMENT: 3,
  NOT_SUBMITTED: 4,
} as const;
```


**CERTIFICATION_STATUS**

```typescript
export const CERTIFICATION_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;
```


**CERTIFICATION_TYPES**

```typescript
export const CERTIFICATION_TYPES = [
  { id: "Bronze", name: "銅牌", level: CERTIFICATION_LEVEL.BRONZE, requiresGreenFlag: false },
  { id: "Silver", name: "銀牌", level: CERTIFICATION_LEVEL.SILVER, requiresGreenFlag: false },
  { id: "GreenFlag", name: "綠旗", level: CERTIFICATION_LEVEL.GREEN_FLAG, requiresGreenFlag: false },
  { id: "GreenFlag", name: "綠旗R1", level: CERTIFICATION_LEVEL.GREEN_FLAG_R1, requiresGreenFlag: true },
  { id: "GreenFlag", name: "綠旗R2", level: CERTIFICATION_LEVEL.GREEN_FLAG_R2, requiresGreenFlag: true },
  { id: "GreenFlag", name: "綠旗R3", level: CERTIFICATION_LEVEL.GREEN_FLAG_R3, requiresGreenFlag: true },
] as const;
```


**CERTIFICATION_ERROR_MESSAGES**

```typescript
export const CERTIFICATION_ERROR_MESSAGES = {
  USER_NOT_AUTHENTICATED: "使用者未認證",
  SCHOOL_NOT_ASSIGNED: "該帳號尚未分配學校",
  CERTIFICATION_NOT_FOUND: "找不到認證資料",
  DUPLICATE_CERTIFICATION: "已存在相同類型的認證申請",
  GREEN_FLAG_REQUIRED: "申請延續認證需要先通過綠旗認證",
  INVALID_CERTIFICATION_TYPE: "認證類型和等級為必填項目",
  CREATE_FAILED: "建立認證失敗",
  UPDATE_FAILED: "更新認證失敗",
  DELETE_FAILED: "刪除認證失敗",
  INVALID_PERMISSIONS: "沒有權限執行此操作",
  TIME_CONSTRAINT_NOT_MET: "不符合時間限制要求",
} as const;
```


**CERTIFICATION_SUCCESS_MESSAGES**

```typescript
export const CERTIFICATION_SUCCESS_MESSAGES = {
  LIST_RETRIEVED: "認證清單獲取成功",
  AVAILABILITY_CHECKED: "認證可用性檢查完成",
  CERTIFICATION_CREATED: "認證申請已建立",
  CERTIFICATION_UPDATED: "認證資料已更新",
  CERTIFICATION_DELETED: "認證申請已刪除",
  CERTIFICATION_SUBMITTED: "認證已提交審核",
} as const;
```


**TIME_CONSTRAINTS**

```typescript
export const TIME_CONSTRAINTS = {
  GREEN_FLAG_R1_WAIT: 2, // 綠旗通過滿兩年才能申請R1
  GREEN_FLAG_R2_WAIT: 2, // R1通過滿兩年才能申請R2
  GREEN_FLAG_R3_WAIT: 2, // R2通過滿兩年才能申請R3
} as const;
```


**getReviewStatusInfo**

```typescript
export const getReviewStatusInfo = (reviewStatus: number) => {
  return {
    label: REVIEW_STATUS_LABELS[reviewStatus] || "未知狀態",
    icon: REVIEW_STATUS_ICONS[reviewStatus] || "img/license-icon-no-status.svg",
    description: REVIEW_STATUS_DESCRIPTIONS[reviewStatus] || "狀態未知",
    color: REVIEW_STATUS_COLORS[reviewStatus] || "text-gray-700",
    bgColor: REVIEW_STATUS_BG_COLORS[reviewStatus] || "bg-gray-100",
  };
```


**mapCertificationType**

```typescript
export const mapCertificationType = (levelName: string, level: number) => {
  const levelMap: Record<number, { name: string;
```


**getYearsSinceApproval**

```typescript
export const getYearsSinceApproval = (approvedDate?: Date): number => {
  if (!approvedDate) return 0;
```


**isEditable**

```typescript
export const isEditable = (reviewStatus: number): boolean => {
  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED || reviewStatus === REVIEW_STATUS.NEEDS_SUPPLEMENT;
```


**isDeletable**

```typescript
export const isDeletable = (reviewStatus: number): boolean => {
  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED;
```


**canSubmit**

```typescript
export const canSubmit = (reviewStatus: number): boolean => {
  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED;
```


**getFrontendId**

```typescript
export const getFrontendId = (level: number): string => {
  return FRONTEND_ID_MAP[level] || "unknown";
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### dashboard

**文件**: `constants/dashboard.ts`

#### 常數定義


**DASHBOARD_REVIEW_STATUS**

```typescript
export const DASHBOARD_REVIEW_STATUS = {
  UNDER_REVIEW: 0,
  APPROVED: 1,
  REJECTED: 2,
  NEEDS_SUPPLEMENT: 3,
  NOT_SUBMITTED: 4,
} as const;
```


**DASHBOARD_STATUS**

```typescript
export const DASHBOARD_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;
```


**AUTHORIZED_ROLES**

```typescript
export const AUTHORIZED_ROLES = ["Government", "Tutor"] as const;
```


**DEFAULT_LIMITS**

```typescript
export const DEFAULT_LIMITS = {
  LATEST_CERTIFICATIONS: 6,
  SCHOOL_ARTICLES: 5,
  PASSED_CERTIFICATIONS: 10,
} as const;
```


**LOCALE_CODE**

```typescript
export const LOCALE_CODE = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;
```


**DASHBOARD_ERROR_MESSAGES**

```typescript
export const DASHBOARD_ERROR_MESSAGES = {
  INVALID_TOKEN: "無效的使用者令牌",
  INSUFFICIENT_PERMISSIONS: "權限不足",
  EPA_TUTOR_ONLY: "僅限縣市政府和輔導人員查看統計資料",
  SCHOOL_ONLY: "僅限學校身份查看此資料",
  CITY_NOT_FOUND: "找不到指定的縣市",
  USER_CITY_NOT_FOUND: "找不到用戶的縣市資料",
  SCHOOL_NOT_FOUND: "找不到學校資料或您沒有權限",
  USER_NOT_AUTHENTICATED: "使用者未認證",
  NO_CURRENT_CERTIFICATION: "目前沒有申請中的認證",
  NO_SCHOOL_ASSIGNED: "該帳號尚未分配學校",
  INTERNAL_ERROR: "內部系統錯誤",
} as const;
```


**DASHBOARD_SUCCESS_MESSAGES**

```typescript
export const DASHBOARD_SUCCESS_MESSAGES = {
  TEST_SUCCESS: "測試成功",
  CITY_STATISTICS_SUCCESS: "縣市統計資料獲取成功",
  LATEST_CERTIFICATIONS_SUCCESS: "最新認證資料獲取成功",
  SCHOOL_CERTIFICATION_SUCCESS: "學校申請中認證獲取成功",
  SCHOOL_ARTICLES_SUCCESS: "學校文章獲取成功",
  SCHOOL_PASSED_CERTIFICATIONS_SUCCESS: "學校已通過認證獲取成功",
} as const;
```


**getCertificationLevelName**

```typescript
export const getCertificationLevelName = (level: number): string => {
  return CERTIFICATION_LEVEL_NAMES[level] || "未知";
```


**isAuthorizedRole**

```typescript
export const isAuthorizedRole = (roleType: string): boolean => {
  return AUTHORIZED_ROLES.includes(roleType as (typeof AUTHORIZED_ROLES)[number]);
```


**isSchoolRole**

```typescript
export const isSchoolRole = (roleType: string): boolean => {
  return roleType === "School";
```


**formatDate**

```typescript
export const formatDate = (date: Date | string): string => {
  if (!date) return "";
```


**formatStatisticsResult**

```typescript
export const formatStatisticsResult = (
  cityInfo: { cityId: number;
```


**getTestData**

```typescript
export const getTestData = () => {
  return {
    cityId: 1,
    cityName: "基隆市",
    bronzeCount: 1,
    silverCount: 1,
    greenFlagCount: 0,
    totalSchools: 2,
  };
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### file

**文件**: `constants/file.ts`

#### 常數定義


**FILE_SIZE_LIMITS**

```typescript
export const FILE_SIZE_LIMITS = {
  GENERAL_FILE: 10 * 1024 * 1024, // 10MB
  SCHOOL_LOGO: 5 * 1024 * 1024, // 5MB
} as const;
```


**FILE_COUNT_LIMITS**

```typescript
export const FILE_COUNT_LIMITS = {
  MULTIPLE_UPLOAD: 5,
  SINGLE_UPLOAD: 1,
} as const;
```


**ALLOWED_MIME_TYPES**

```typescript
export const ALLOWED_MIME_TYPES = {
  IMAGES: ["image/jpeg", "image/jpg", "image/png", "image/gif"],
  DOCUMENTS: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ],
  VIDEOS: ["video/mp4", "video/avi", "video/mov"],
  SCHOOL_LOGO: ["image/jpeg", "image/jpg", "image/png"],
} as const;
```


**ALL_ALLOWED_TYPES**

```typescript
export const ALL_ALLOWED_TYPES = [...ALLOWED_MIME_TYPES.IMAGES, ...ALLOWED_MIME_TYPES.DOCUMENTS, ...ALLOWED_MIME_TYPES.VIDEOS] as const;
```


**SUPPORTED_EXTENSIONS**

```typescript
export const SUPPORTED_EXTENSIONS = {
  IMAGES: ["jpg", "jpeg", "png", "gif"],
  DOCUMENTS: ["pdf", "doc", "docx", "xls", "xlsx"],
  VIDEOS: ["mp4", "avi", "mov"],
} as const;
```


**FILE_TYPES**

```typescript
export const FILE_TYPES = {
  GENERAL: "general",
  SCHOOL_LOGO: "school-logo",
  CERTIFICATION: "certification",
  QUESTION: "question",
} as const;
```


**LOCALE_CODES**

```typescript
export const LOCALE_CODES = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;
```


**DATA_STATUS**

```typescript
export const DATA_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;
```


**FILE_ERROR_MESSAGES**

```typescript
export const FILE_ERROR_MESSAGES = {
  NO_FILE_UPLOADED: "沒有上傳檔案",
  UNSUPPORTED_FILE_TYPE: "不支援的檔案類型",
  FILE_TOO_LARGE: "檔案大小超過限制",
  TOO_MANY_FILES: "檔案數量超過限制",
  INVALID_FILENAME: "非法的檔案名稱",
  FILE_NOT_FOUND: "找不到檔案",
  SCHOOL_NOT_FOUND: "找不到學校資料",
  INVALID_USER_AUTH: "無效的用戶認證",
  FILE_RECORD_CREATE_FAILED: "檔案記錄建立失敗",
  UPLOAD_DIR_CREATE_FAILED: "無法創建上傳目錄",
  NO_WRITE_PERMISSION: "目錄沒有寫入權限",
  FILE_PROCESSING_FAILED: "檔案處理失敗",
  LOGO_FORMAT_ERROR: "校徽圖檔請上傳jpg、png格式",
  LOGO_SIZE_ERROR: "檔案大小不可超過 5MB",
  PLEASE_SELECT_LOGO: "請選擇校徽圖檔",
} as const;
```


**FILE_SUCCESS_MESSAGES**

```typescript
export const FILE_SUCCESS_MESSAGES = {
  FILE_UPLOADED: "檔案上傳成功",
  FILE_DELETED: "檔案刪除成功",
  FILE_INFO_RETRIEVED: "檔案資訊獲取成功",
  SCHOOL_LOGO_UPLOADED: "校徽上傳成功",
  SCHOOL_LOGO_RETRIEVED: "校徽獲取成功",
  UPLOAD_DIAGNOSTICS_COMPLETE: "上傳診斷完成",
} as const;
```


**MULTER_ERROR_CODES**

```typescript
export const MULTER_ERROR_CODES = {
  LIMIT_FILE_SIZE: "LIMIT_FILE_SIZE",
  LIMIT_FILE_COUNT: "LIMIT_FILE_COUNT",
  LIMIT_UNEXPECTED_FILE: "LIMIT_UNEXPECTED_FILE",
} as const;
```


**DEFAULT_DIRECTORIES**

```typescript
export const DEFAULT_DIRECTORIES = {
  UPLOADS: "uploads",
  SCHOOL_LOGOS: "school-logos",
} as const;
```


**isAllowedFileType**

```typescript
export const isAllowedFileType = (mimetype: string): boolean => {
  return ALL_ALLOWED_TYPES.includes(mimetype as any);
```


**isValidLogoFormat**

```typescript
export const isValidLogoFormat = (mimetype: string): boolean => {
  return ALLOWED_MIME_TYPES.SCHOOL_LOGO.includes(mimetype as any);
```


**isValidFileSize**

```typescript
export const isValidFileSize = (size: number, fileType: string = FILE_TYPES.GENERAL): boolean => {
  const limit = fileType === FILE_TYPES.SCHOOL_LOGO ? FILE_SIZE_LIMITS.SCHOOL_LOGO : FILE_SIZE_LIMITS.GENERAL_FILE;
```


**isValidFilename**

```typescript
export const isValidFilename = (filename: string): boolean => {
  return !filename.includes("..") && !filename.includes("/") && !filename.includes("\\");
```


**generateFilename**

```typescript
export const generateFilename = (originalName: string, accountId?: string | number): string => {
  const timestamp = Date.now();
```


**generateLogoFilename**

```typescript
export const generateLogoFilename = (originalName: string, accountId: string | number): string => {
  const timestamp = Date.now();
```


**parseOriginalName**

```typescript
export const parseOriginalName = (filename: string): string => {
  return filename.split("_").slice(1).join("_");
```


**getMulterErrorMessage**

```typescript
export const getMulterErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case MULTER_ERROR_CODES.LIMIT_FILE_SIZE:
      return `檔案大小超過限制 (最大 ${FILE_SIZE_LIMITS.GENERAL_FILE / (1024 * 1024)}MB)`;
```


**getSupportedFileTypes**

```typescript
export const getSupportedFileTypes = () => {
  return {
    images: SUPPORTED_EXTENSIONS.IMAGES,
    documents: SUPPORTED_EXTENSIONS.DOCUMENTS,
    videos: SUPPORTED_EXTENSIONS.VIDEOS,
  };
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### location

**文件**: `constants/location.ts`

#### 常數定義


**LOCALE_CODES**

```typescript
export const LOCALE_CODES = {
  ZH_TW: "zh-TW",
  EN_US: "en-US",
} as const;
```


**LOCATION_ERROR_MESSAGES**

```typescript
export const LOCATION_ERROR_MESSAGES = {
  INVALID_CITY_ID: "無效的縣市ID",
  GET_CITIES_FAILED: "獲取縣市列表失敗",
  GET_AREAS_FAILED: "獲取區域列表失敗",
  GET_HIERARCHY_FAILED: "獲取地區層級資料失敗",
  UNKNOWN_ERROR: "未知錯誤",
} as const;
```


**LOCATION_SUCCESS_MESSAGES**

```typescript
export const LOCATION_SUCCESS_MESSAGES = {
  CITIES_RETRIEVED: "縣市列表獲取成功",
  AREAS_RETRIEVED: "區域列表獲取成功",
  HIERARCHY_RETRIEVED: "地區層級資料獲取成功",
} as const;
```


**SQL_QUERIES**

```typescript
export const SQL_QUERIES = {
  GET_CITIES: `
    SELECT 
      c.CountyId,
      ct.Name
    FROM Counties c
    INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId
    WHERE ct.LocaleCode = @localeCode
    ORDER BY c.CountyId
  `,
  GET_AREAS_BY_CITY: `
    SELECT 
      d.DistrictId,
      dt.Name,
      d.CountyId
    FROM Districts d
    INNER JOIN DistrictTranslations dt ON d.DistrictId = dt.DistrictId
    WHERE d.CountyId = @cityId AND dt.LocaleCode = @localeCode
    ORDER BY d.DistrictId
  `,
  GET_HIERARCHY: `
    SELECT 
      c.CountyId,
      ct.Name as CountyName,
      d.DistrictId,
      dt.Name as DistrictName
    FROM Counties c
    INNER JOIN CountyTranslations ct ON c.CountyId = ct.CountyId
    LEFT JOIN Districts d ON c.CountyId = d.CountyId
    LEFT JOIN DistrictTranslations dt ON d.DistrictId = dt.DistrictId AND dt.LocaleCode = @localeCode
    WHERE ct.LocaleCode = @localeCode
    ORDER BY c.CountyId, d.DistrictId
  `,
} as const;
```


**isValidCityId**

```typescript
export const isValidCityId = (cityId: string): boolean => {
  const parsedId = parseInt(cityId);
```


**formatCityData**

```typescript
export const formatCityData = (cityRow: any): any => {
  return {
    id: cityRow.CountyId,
    name: cityRow.Name,
  };
```


**formatAreaData**

```typescript
export const formatAreaData = (areaRow: any): any => {
  return {
    id: areaRow.DistrictId,
    name: areaRow.Name,
    cityId: areaRow.CountyId,
  };
```


**organizeHierarchyData**

```typescript
export const organizeHierarchyData = (hierarchyRows: any[]): any[] => {
  const cities: Record<number, any> = {};
```


**generateHierarchyStats**

```typescript
export const generateHierarchyStats = (hierarchyData: any[]): { totalCities: number;
```


**generateSuccessMessage**

```typescript
export const generateSuccessMessage = (type: string, stats?: { totalCities?: number;
```


**parseCityId**

```typescript
export const parseCityId = (cityIdStr: string): number => {
  const cityId = parseInt(cityIdStr);
```


**DEFAULT_LOCALE**

```typescript
export const DEFAULT_LOCALE = LOCALE_CODES.ZH_TW;
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### profile

**文件**: `constants/profile.ts`

#### 常數定義


**MEMBER_ROLE_TYPE**

```typescript
export const MEMBER_ROLE_TYPE = {
  SCHOOL: "school",
  EPA: "epa",
  TUTOR: "tutor",
} as const;
```


**ROLE_FIELD_MAP**

```typescript
export const ROLE_FIELD_MAP = {
  IS_SCHOOL_PARTNER: 1,
  IS_EPA_USER: 1,
  IS_GUIDANCE_TEAM: 1,
} as const;
```


**LOCALE_CODE**

```typescript
export const LOCALE_CODE = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;
```


**DATA_STATUS**

```typescript
export const DATA_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;
```


**DEFAULT_VALUES**

```typescript
export const DEFAULT_VALUES = {
  MEMBER_ROLE: MEMBER_ROLE_TYPE.SCHOOL,
  LOCALE: LOCALE_CODE.ZH_TW,
  STATUS: DATA_STATUS.ACTIVE,
} as const;
```


**PROFILE_ERROR_MESSAGES**

```typescript
export const PROFILE_ERROR_MESSAGES = {
  USER_NOT_AUTHENTICATED: "使用者未認證",
  TOKEN_MISSING: "缺少 Token",
  MEMBER_NOT_FOUND: "找不到會員資料",
  SCHOOL_NOT_FOUND: "找不到學校資料",
  INVALID_DATA: "資料格式不正確",
  UPDATE_FAILED: "更新失敗",
  ACCESS_DENIED: "沒有權限存取此資料",
  INVALID_COUNTY: "無效的縣市",
  INVALID_DISTRICT: "無效的區域",
  CONTACT_LIMIT_EXCEEDED: "聯絡人數量超過限制",
  STATISTICS_INVALID: "統計資料格式不正確",
} as const;
```


**PROFILE_SUCCESS_MESSAGES**

```typescript
export const PROFILE_SUCCESS_MESSAGES = {
  PROFILE_RETRIEVED: "會員資料獲取成功",
  PROFILE_UPDATED: "會員資料更新成功",
  SCHOOL_BASIC_UPDATED: "學校基本資料更新成功",
  PRINCIPAL_UPDATED: "校長資料更新成功",
  CONTACTS_UPDATED: "聯絡人資料更新成功",
  STATISTICS_UPDATED: "學校統計資料更新成功",
} as const;
```


**FIELD_LIMITS**

```typescript
export const FIELD_LIMITS = {
  MEMBER_NAME: 50,
  JOB_TITLE: 100,
  EMAIL: 255,
  PHONE: 20,
  ADDRESS: 500,
  URL: 500,
  INTRODUCTION: 2000,
  SCHOOL_NAME: 200,
  PRINCIPAL_NAME: 50,
  CONTACT_NAME: 50,
} as const;
```


**CONTACT_LIMITS**

```typescript
export const CONTACT_LIMITS = {
  MAX_CONTACTS: 10,
  MIN_CONTACTS: 0,
} as const;
```


**STATISTICS_RANGES**

```typescript
export const STATISTICS_RANGES = {
  MIN_VALUE: 0,
  MAX_VALUE: 99999,
} as const;
```


**determineMemberRole**

```typescript
export const determineMemberRole = (isSchoolPartner: number, isEpaUser: number, isGuidanceTeam: number): string => {
  if (isEpaUser === ROLE_FIELD_MAP.IS_EPA_USER) {
    return MEMBER_ROLE_TYPE.EPA;
```


**isSchoolMember**

```typescript
export const isSchoolMember = (roleType: string): boolean => {
  return roleType === MEMBER_ROLE_TYPE.SCHOOL;
```


**isEpaMember**

```typescript
export const isEpaMember = (roleType: string): boolean => {
  return roleType === MEMBER_ROLE_TYPE.EPA;
```


**isTutorMember**

```typescript
export const isTutorMember = (roleType: string): boolean => {
  return roleType === MEMBER_ROLE_TYPE.TUTOR;
```


**validateStatisticsValue**

```typescript
export const validateStatisticsValue = (value: number): boolean => {
  return value >= STATISTICS_RANGES.MIN_VALUE && value <= STATISTICS_RANGES.MAX_VALUE;
```


**validateContactsCount**

```typescript
export const validateContactsCount = (count: number): boolean => {
  return count >= CONTACT_LIMITS.MIN_CONTACTS && count <= CONTACT_LIMITS.MAX_CONTACTS;
```


**validateEmail**

```typescript
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
```


**validatePhone**

```typescript
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\d\-\+\(\)\s]+$/;
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### question

**文件**: `constants/question.ts`

#### 常數定義


**MAX_CERTIFICATION_STEPS**

```typescript
export const MAX_CERTIFICATION_STEPS = 7;
```


**EXCLUDED_QUESTION_TEMPLATES**

```typescript
export const EXCLUDED_QUESTION_TEMPLATES = [12, 15];
```


**GREEN_FLAG_STEPS**

```typescript
export const GREEN_FLAG_STEPS = [8, 9];
```


**DEFAULT_CERTIFICATION_LEVEL**

```typescript
export const DEFAULT_CERTIFICATION_LEVEL = 1;
```


**QUESTION_STATUS**

```typescript
export const QUESTION_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
} as const;
```


**getStepTitle**

```typescript
export const getStepTitle = (stepNumber: number): string => {
  return STEP_TITLES[stepNumber] || `步驟 ${stepNumber}`;
```


**isGreenFlagStep**

```typescript
export const isGreenFlagStep = (stepNumber: number): boolean => {
  return GREEN_FLAG_STEPS.includes(stepNumber);
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### template-answer

**文件**: `constants/template-answer.ts`

#### 常數定義


**SUPPORTED_TEMPLATE_IDS**

```typescript
export const SUPPORTED_TEMPLATE_IDS = {
  YES_NO: 1, // 是非選擇題
  TEAM_MEMBER: 2, // 團隊成員表單
  MEETING_RECORD: 3, // 會議記錄
  SHARE_MEETING: 4, // 分享會議資訊
  RECRUIT_MEMBER: 5, // 招募新成員
  PHOTO_RECORD: 6, // 照片上傳
  ENVIRONMENT_PATH: 8, // 環境路徑選擇
  TEXTAREA: 16, // 文字區域
  SUMMARY: 19, // 總結申論
  RECERTIFICATION_MEETING: 21, // 會議記錄（再認證）
} as const;
```


**ANSWER_STATUS**

```typescript
export const ANSWER_STATUS = {
  DRAFT: "draft", // 草稿狀態
  SUBMITTED: "submitted", // 已提交
  VALIDATED: "validated", // 已驗證
  REJECTED: "rejected", // 已拒絕
} as const;
```


**YES_NO_VALUES**

```typescript
export const YES_NO_VALUES = ["0", "1", ""] as const;
```


**DATE_FORMAT_REGEX**

```typescript
export const DATE_FORMAT_REGEX = /^\d{4}-\d{2}-\d{2}$/;
```


**TEMPLATE_ANSWER_ERROR_MESSAGES**

```typescript
export const TEMPLATE_ANSWER_ERROR_MESSAGES = {
  MISSING_PARAMETERS: "缺少必要參數",
  VALIDATION_FAILED: "答案格式驗證失敗",
  SAVE_FAILED: "模板答案儲存失敗",
  VALIDATE_FAILED: "答案驗證失敗",
  UNKNOWN_ERROR: "未知錯誤",
  VALIDATION_ERROR: "驗證過程中發生錯誤",
  UNKNOWN_TEMPLATE: "未知的模板ID",

  // 欄位特定錯誤
  MISSING_YES_NO: "缺少 is_yes_no 欄位",
  INVALID_YES_NO: "is_yes_no 必須是 '0', '1' 或空字串",
  INVALID_ARRAY: "必須是陣列",
  INVALID_STRING: "必須是字串",
  MISSING_FIELD: "缺少必要欄位",
  INVALID_DATE_FORMAT: "日期格式錯誤，應為 YYYY-MM-DD",
  MISSING_DATE_OR_THEME: "缺少日期或主題",
  INVALID_MEMBER_FORMAT: "的欄位格式錯誤",
  MISSING_PATH_OR_NAME: "缺少路徑或名稱",
  MISSING_DATE_INFO: "缺少日期資訊",
} as const;
```


**TEMPLATE_ANSWER_SUCCESS_MESSAGES**

```typescript
export const TEMPLATE_ANSWER_SUCCESS_MESSAGES = {
  VALIDATION_SUCCESS: "答案格式驗證成功",
  SAVE_SUCCESS: "模板答案保存成功",
  FORMAT_VALID: "答案格式有效",
} as const;
```


**REQUIRED_PARAMETERS**

```typescript
export const REQUIRED_PARAMETERS = {
  SAVE: ["certification_sid", "question_sid", "template_id", "answer_data"],
  VALIDATE: ["template_id", "answer_data"],
} as const;
```


**TEMPLATE_VALIDATION_RULES**

```typescript
export const TEMPLATE_VALIDATION_RULES = {
  [SUPPORTED_TEMPLATE_IDS.YES_NO]: {
    required_fields: ["is_yes_no"],
    valid_values: {
      is_yes_no: YES_NO_VALUES,
    },
  },
  [SUPPORTED_TEMPLATE_IDS.TEAM_MEMBER]: {
    required_lists: ["student_list", "teacher_list", "community_member_list"],
    member_fields: ["input_1", "input_2", "input_3"],
  },
  [SUPPORTED_TEMPLATE_IDS.MEETING_RECORD]: {
    required_arrays: ["meeting_date_and_theme", "file"],
    meeting_fields: ["input_1", "input_2"],
    date_validation: true,
  },
  [SUPPORTED_TEMPLATE_IDS.SHARE_MEETING]: {
    required_fields: ["is_yes_no"],
    required_objects: ["share_people", "how_share_meeting"],
    checkbox_fields: ["share_people.checkbox", "how_share_meeting.checkbox"],
  },
  [SUPPORTED_TEMPLATE_IDS.RECRUIT_MEMBER]: {
    required_fields: ["is_yes_no", "textarea"],
    string_fields: ["textarea"],
  },
  [SUPPORTED_TEMPLATE_IDS.PHOTO_RECORD]: {
    required_arrays: ["photo_record"],
  },
  [SUPPORTED_TEMPLATE_IDS.ENVIRONMENT_PATH]: {
    required_arrays: ["improve_path_list"],
    path_fields: ["path", "cname"],
    date_fields: ["date.input_1", "date.input_2"],
  },
  [SUPPORTED_TEMPLATE_IDS.TEXTAREA]: {
    required_fields: ["textarea"],
    string_fields: ["textarea"],
  },
  [SUPPORTED_TEMPLATE_IDS.SUMMARY]: {
    required_textareas: 5,
  },
  [SUPPORTED_TEMPLATE_IDS.RECERTIFICATION_MEETING]: {
    required_textareas: 7,
  },
} as const;
```


**validateTemplateAnswer**

```typescript
export const validateTemplateAnswer = (templateId: number, answerData: Record<string, unknown>): { isValid: boolean;
```


**standardizeAnswerFormat**

```typescript
export const standardizeAnswerFormat = (templateId: number, answerData: Record<string, unknown>): Record<string, unknown> => {
  switch (templateId) {
    case SUPPORTED_TEMPLATE_IDS.YES_NO:
      return {
        is_yes_no: String(answerData.is_yes_no || ""),
      };
```


**checkRequiredParameters**

```typescript
export const checkRequiredParameters = (params: Record<string, unknown>, required: string[]): string[] => {
  const missing: string[] = [];
```


**generateQuestionTitle**

```typescript
export const generateQuestionTitle = (questionSid: string, customTitle?: string): string => {
  return customTitle || `問題${questionSid}`;
```


**formatSaveResult**

```typescript
export const formatSaveResult = (request: any, standardizedAnswer: Record<string, unknown>, validationResult: { isValid: boolean;
```


**isSupportedTemplate**

```typescript
export const isSupportedTemplate = (templateId: number): boolean => {
  return Object.values(SUPPORTED_TEMPLATE_IDS).includes(templateId as any);
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---
### user

**文件**: `constants/user.ts`

#### 常數定義


**ROLE_TYPES**

```typescript
export const ROLE_TYPES = {
  SCHOOL: "School",
  GOVERNMENT: "Government",
  TUTOR: "Tutor",
  ADMIN: "Admin",
} as const;
```


**USER_STATUS**

```typescript
export const USER_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  SUSPENDED: 2,
  DELETED: 3,
} as const;
```


**CERTIFICATION_STATUS**

```typescript
export const CERTIFICATION_STATUS = {
  DRAFT: "Draft",
  IN_REVIEW: "InReview",
  PASSED: "Passed",
  REJECTED: "Rejected",
  EXPIRED: "Expired",
} as const;
```


**CERTIFICATION_TYPES**

```typescript
export const CERTIFICATION_TYPES = {
  BRONZE: "Bronze",
  SILVER: "Silver",
  GREEN_FLAG: "GreenFlag",
} as const;
```


**CERTIFICATION_LEVELS**

```typescript
export const CERTIFICATION_LEVELS = {
  BRONZE: 2,
  SILVER: 3,
  GREEN_FLAG: 4,
  GREEN_FLAG_R1: 5,
  GREEN_FLAG_R2: 6,
  GREEN_FLAG_R3: 7,
} as const;
```


**REVIEW_STATUS**

```typescript
export const REVIEW_STATUS = {
  IN_REVIEW: 0,
  APPROVED: 1,
  REJECTED: 2,
  NEEDS_SUPPLEMENT: 3,
  NOT_SUBMITTED: 4,
} as const;
```


**DATA_STATUS**

```typescript
export const DATA_STATUS = {
  ACTIVE: 0,
  INACTIVE: 1,
  DELETED: 2,
} as const;
```


**PERMISSION_FLAGS**

```typescript
export const PERMISSION_FLAGS = {
  IS_USE: 1,
  NOT_USE: 0,
} as const;
```


**ALLOWED_ACCOUNT_FIELDS**

```typescript
export const ALLOWED_ACCOUNT_FIELDS = {
  [ROLE_TYPES.SCHOOL]: ["NickName", "Email", "Phone", "Avatar"],
  [ROLE_TYPES.GOVERNMENT]: ["NickName", "Email", "Phone", "Avatar"],
  [ROLE_TYPES.TUTOR]: ["NickName", "Email", "Phone", "Avatar"],
  [ROLE_TYPES.ADMIN]: ["NickName", "Email", "Phone", "Avatar"],
} as const;
```


**ALLOWED_SCHOOL_FIELDS**

```typescript
export const ALLOWED_SCHOOL_FIELDS = ["Name", "EnglishName", "Address", "Phone", "Email", "Website", "ContactPerson", "ContactPhone", "ContactEmail"] as const;
```


**USER_ERROR_MESSAGES**

```typescript
export const USER_ERROR_MESSAGES = {
  USER_NOT_AUTHENTICATED: "使用者未認證",
  USER_NOT_FOUND: "找不到使用者資料",
  NO_PERMISSION: "無權限訪問此資料",
  SCHOOL_ONLY: "只有學校身份可以訪問學校資料",
  SCHOOL_NOT_FOUND: "找不到對應的學校資料",
  INVALID_EMAIL: "電子郵件格式不正確",
  INVALID_PHONE: "電話號碼格式不正確",
  UPDATE_FAILED: "更新使用者資料失敗",
  NO_VALID_FIELDS: "沒有提供有效的更新欄位",
  TRANSACTION_FAILED: "資料庫事務處理失敗",
} as const;
```


**USER_SUCCESS_MESSAGES**

```typescript
export const USER_SUCCESS_MESSAGES = {
  PROFILE_RETRIEVED: "使用者資料獲取成功",
  PROFILE_UPDATED: "使用者資料更新成功",
  CERTIFICATIONS_RETRIEVED: "認證資料獲取成功",
  SCHOOL_INFO_RETRIEVED: "學校資料獲取成功",
  PERMISSIONS_RETRIEVED: "權限資料獲取成功",
} as const;
```


**DEFAULT_PERMISSIONS**

```typescript
export const DEFAULT_PERMISSIONS = {
  ADMIN: ["admin"],
  USER: ["user"],
  SCHOOL: ["school"],
  GOVERNMENT: ["government"],
  TUTOR: ["tutor"],
} as const;
```


**SQL_QUERIES**

```typescript
export const SQL_QUERIES = {
  GET_ACCOUNT: "SELECT * FROM Account WHERE Id = @userId AND IsActive = 1",
  GET_ACCOUNT_WITH_PERMISSIONS: `
    SELECT a.*, 
           STRING_AGG(DISTINCT p.ename, ',') as Permissions,
           STRING_AGG(DISTINCT pg.ename, ',') as PermissionGroups
    FROM Account a
    LEFT JOIN account_permission_group apg ON a.Id = apg.accountSid AND apg.dataStatus = 0
    LEFT JOIN permission_group pg ON apg.groupSid = pg.sid AND pg.isuse = 1
    LEFT JOIN permission_group_map pgm ON pg.sid = pgm.groupSid AND pgm.dataStatus = 0
    LEFT JOIN permission p ON pgm.permissionSid = p.sid AND p.isuse = 1
    WHERE a.Id = @userId AND a.IsActive = 1
    GROUP BY a.Id, a.Account, a.NickName, a.Email, a.Phone, a.Avatar, a.RoleType, a.IsActive, a.CreatedTime, a.UpdatedTime, a.Remark
  `,
  GET_SCHOOL_BY_ACCOUNT: `
    SELECT s.*
    FROM School s
    INNER JOIN Account a ON s.Id = a.CmsUserId
    WHERE a.Id = @accountId AND s.IsActive = 1
  `,
  GET_CERTIFICATIONS: `
    SELECT c.*
    FROM Certifications c
    WHERE c.SchoolId = @userId AND c.Status = 1 
    ORDER BY c.CreatedTime DESC
  `,
  GET_PERMISSIONS: `
    SELECT DISTINCT p.ename as Code, p.cname as Name, p.remark as Description
    FROM account_permission_group apg
    INNER JOIN permission_group pg ON apg.groupSid = pg.sid
    INNER JOIN permission_group_map pgm ON pg.sid = pgm.groupSid
    INNER JOIN permission p ON pgm.permissionSid = p.sid
    WHERE apg.accountSid = @accountId 
      AND apg.dataStatus = 0
      AND pg.isuse = 1
      AND pgm.dataStatus = 0
      AND p.isuse = 1
    ORDER BY p.cname
  `,
  GET_PERMISSION_GROUPS: `
    SELECT DISTINCT pg.ename as Code, pg.cname as Name, pg.remark as Description
    FROM account_permission_group apg
    INNER JOIN permission_group pg ON apg.groupSid = pg.sid
    WHERE apg.accountSid = @accountId 
      AND apg.dataStatus = 0
      AND pg.isuse = 1
    ORDER BY pg.cname
  `,
} as const;
```


**isValidEmail**

```typescript
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
```


**isValidPhone**

```typescript
export const isValidPhone = (phone: string): boolean => {
  // 允許多種電話號碼格式
  const phoneRegex = /^[\d\-+() ]{8,20}$/;
```


**hasPermission**

```typescript
export const hasPermission = (userRole: string, requiredRole: string): boolean => {
  if (userRole === ROLE_TYPES.ADMIN) return true;
```


**isSchoolRole**

```typescript
export const isSchoolRole = (roleType: string): boolean => {
  return roleType === ROLE_TYPES.SCHOOL;
```


**isAdminRole**

```typescript
export const isAdminRole = (roleType: string): boolean => {
  return roleType === ROLE_TYPES.ADMIN;
```


**getAllowedAccountFields**

```typescript
export const getAllowedAccountFields = (roleType: string): string[] => {
  return ALLOWED_ACCOUNT_FIELDS[roleType as keyof typeof ALLOWED_ACCOUNT_FIELDS] || [];
```


**getAllowedSchoolFields**

```typescript
export const getAllowedSchoolFields = (): string[] => {
  return [...ALLOWED_SCHOOL_FIELDS];
```


**mapCertificationLevelToType**

```typescript
export const mapCertificationLevelToType = (level: number): string => {
  switch (level) {
    case CERTIFICATION_LEVELS.BRONZE:
      return CERTIFICATION_TYPES.BRONZE;
```


**mapReviewStatusToCertificationStatus**

```typescript
export const mapReviewStatusToCertificationStatus = (reviewStatus: number): string => {
  switch (reviewStatus) {
    case REVIEW_STATUS.APPROVED:
      return CERTIFICATION_STATUS.PASSED;
```


**canAccessUserData**

```typescript
export const canAccessUserData = (currentUserId: string, targetUserId: string, permissions: string[]): boolean => {
  // 如果是同一個用戶，允許訪問
  if (currentUserId === targetUserId) return true;
```


**validateUpdateData**

```typescript
export const validateUpdateData = (data: any): { isValid: boolean;
```

#### 導出項目

- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
- `const`
---

## 📝 使用說明

### 路由使用

每個路由文件都包含相關的 API 端點定義。使用時請參考對應的路由文件。

### 模型使用

資料模型定義了 API 請求和回應的資料結構。在開發時請使用對應的 TypeScript 接口。

### 常數使用

常數定義了系統中使用的固定值和工具函數。請參考對應的常數文件。

---

*此文檔由 API 文檔生成器自動生成，請勿手動修改。*
