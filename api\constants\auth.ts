// ========== 認證相關常數 ==========

// 允許的角色類型
export const ALLOWED_ROLE_TYPES = ["School", "Government", "Tutor"] as const;

// 角色類型映射
export const ROLE_TYPE_MAP = {
  SCHOOL: "School",
  GOVERNMENT: "Government",
  TUTOR: "Tutor",
} as const;

// 前端角色映射
export const FRONTEND_ROLE_MAP: Record<string, string> = {
  School: "school",
  Government: "epa",
  Tutor: "tutor",
};

// 帳號狀態
export const ACCOUNT_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;

// Token 類型
export const TOKEN_TYPE = {
  LOGIN: "Login",
  SCHOOL_IDENTITY: "school_identity",
  EPA_IDENTITY: "epa_identity",
  TUTOR_IDENTITY: "tutor_identity",
} as const;

// Token 狀態
export const TOKEN_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
} as const;

// 密碼設定
export const PASSWORD_CONFIG = {
  MIN_LENGTH: 8,
  SALT_LENGTH: 16,
  HASH_ITERATIONS: 10000,
  HMAC_ALGORITHM: "sha256",
} as const;

// 預設 Token 有效期（天）
export const DEFAULT_TOKEN_VALIDITY_DAYS = {
  LOGIN: 30,
  IDENTITY: 3000,
  ADMIN: 1,
} as const;

// 錯誤訊息
export const AUTH_ERROR_MESSAGES = {
  TOKEN_REQUIRED: "Token is required",
  CREDENTIALS_REQUIRED: "Account and password are required",
  INVALID_TOKEN: "Invalid token",
  INVALID_CREDENTIALS: "Invalid account or password",
  ACCOUNT_DISABLED: "Access denied. Your account has been deactivated.",
  ROLE_NOT_ALLOWED: "Access denied. Your account role is not authorized to access this system.",
  PASSWORD_TOO_SHORT: `Password must be at least ${PASSWORD_CONFIG.MIN_LENGTH} characters long`,
  PASSWORD_REQUIREMENTS: "Password must contain at least one uppercase letter, one lowercase letter, and one number",
  OLD_PASSWORD_INCORRECT: "Old password is incorrect",
  ACCOUNT_NOT_FOUND: "Account not found",
  TOKEN_GENERATION_FAILED: "Failed to generate token",
  PASSWORD_UPDATE_FAILED: "Failed to update password",
  UNAUTHORIZED: "Unauthorized access",
  INTERNAL_ERROR: "Internal server error",
} as const;

// 成功訊息
export const AUTH_SUCCESS_MESSAGES = {
  TOKEN_LOGIN_SUCCESS: "Token login successful",
  PASSWORD_LOGIN_SUCCESS: "Password login successful",
  PASSWORD_CHANGED: "Password changed successfully",
  TOKEN_CREATED: "Token created successfully",
  LOGOUT_SUCCESS: "Logout successful",
  PASSWORDS_RESET: "Passwords reset successfully",
} as const;

// 角色判斷輔助函數
export const determineRoleTypeFromBooleanFields = (is_school_partner?: boolean, is_epa_user?: boolean, is_guidance_team?: boolean): string => {
  if (is_school_partner) return ROLE_TYPE_MAP.SCHOOL;
  if (is_epa_user) return ROLE_TYPE_MAP.GOVERNMENT;
  if (is_guidance_team) return ROLE_TYPE_MAP.TUTOR;
  return ROLE_TYPE_MAP.SCHOOL;
};

export const determineRoleTypeFromIntegerFields = (IsSchoolPartner?: number, IsEpaUser?: number, IsGuidanceTeam?: number): string => {
  if (IsSchoolPartner === 1) return ROLE_TYPE_MAP.SCHOOL;
  if (IsEpaUser === 1) return ROLE_TYPE_MAP.GOVERNMENT;
  if (IsGuidanceTeam === 1) return ROLE_TYPE_MAP.TUTOR;
  return ROLE_TYPE_MAP.SCHOOL;
};

export const mapRoleTypeToFrontendRole = (roleType: string): string => {
  return FRONTEND_ROLE_MAP[roleType] || "school";
};

// 密碼驗證函數
export const validatePasswordStrength = (password: string): { valid: boolean; message?: string } => {
  if (password.length < PASSWORD_CONFIG.MIN_LENGTH) {
    return { valid: false, message: AUTH_ERROR_MESSAGES.PASSWORD_TOO_SHORT };
  }

  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);

  if (!hasUppercase || !hasLowercase || !hasNumbers) {
    return { valid: false, message: AUTH_ERROR_MESSAGES.PASSWORD_REQUIREMENTS };
  }

  return { valid: true };
};

// 檢查是否為允許的角色
export const isAllowedRole = (roleType: string): boolean => {
  return ALLOWED_ROLE_TYPES.includes(roleType as (typeof ALLOWED_ROLE_TYPES)[number]);
};
