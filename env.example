# =====================================
# EcoCampus 前端環境配置範例
# =====================================
# 
# 多環境配置說明：
# 
# 📁 config/environments/env.development.example  - 本地開發環境（推薦新手使用）
# 📁 config/environments/env.testing.example      - 測試環境
# 📁 config/environments/env.production.example   - 正式環境
# 
# 使用方式：
# 1. 選擇適合的環境配置文件
# 2. 複製並重命名為 .env
# 3. 修改配置內容符合您的環境
# 
# 範例：
# cp config/environments/env.development.example .env   # 本地開發環境
# cp config/environments/env.testing.example .env       # 測試環境  
# cp config/environments/env.production.example .env    # 正式環境
# 
# =====================================

# =====================================
# 預設配置（本地開發環境）
# =====================================
# 注意：以下為本地開發環境的預設配置
# 如需其他環境，請使用對應的 env.{environment}.example 文件

# 環境設定
NODE_ENV=development
APP_ENV=development

# =====================================
# 前端服務器配置
# =====================================
# 前端應用連接埠
FRONTEND_PORT=8080
FRONTEND_URL=http://localhost:8080

# =====================================
# API 服務器配置（前端連接用）
# =====================================
# API 服務基礎 URL
API_BASE_URL=http://localhost:3001/api

# =====================================
# 外部連結配置（開發環境）
# =====================================

# 認證相關連結
VITE_CERTIFICATION_APPLICATION_GUIDE=https://ecocampus.sumire.com.tw/front/page_certifaction_intro
VITE_CERTIFICATION_STANDARDS_INFO=https://ecocampus.sumire.com.tw/front/page_certifaction_intro#standards
VITE_CERTIFICATION_PROCESS_FLOW=https://ecocampus.sumire.com.tw/front/page_certifaction_intro#process

# 輔導相關連結
VITE_GUIDANCE_APPLICATION_FORM=https://reurl.cc/ZGmkXA
VITE_GUIDANCE_CONTACT_INFO=https://ecocampus.sumire.com.tw/front/contact

# 官方網站連結
VITE_OFFICIAL_MAIN_WEBSITE=https://ecocampus.sumire.com.tw
VITE_OFFICIAL_NEWS_PAGE=https://ecocampus.sumire.com.tw/front/news
VITE_OFFICIAL_RESOURCES=https://ecocampus.sumire.com.tw/front/resources

# 社群媒體連結
VITE_SOCIAL_FACEBOOK=https://www.facebook.com/taiwanecoschools
VITE_SOCIAL_LINE=https://line.me/R/ti/p/@ecoschool

# 資源圖片連結（開發環境使用本地資源）
VITE_ASSET_MEDAL_BRONZE=http://localhost:8080/img/medal-bronze.png
VITE_ASSET_MEDAL_SILVER=http://localhost:8080/img/medal-silver.png
VITE_ASSET_MEDAL_GREEN=http://localhost:8080/img/medal-greenflag.png

# =====================================
# 前端開發工具配置
# =====================================
# 啟用詳細日誌
ENABLE_LOGGING=true

# 禁用快取（開發環境）
ENABLE_CACHING=false

# 啟用除錯模式
DEBUG_MODE=true

# =====================================
# 測試用 Token（開發環境）
# =====================================
# 注意：這些 Token 僅用於開發環境測試
DEV_SCHOOL_TOKEN=A0B0D18E-CFA0-4BEF-96EE-F157407E85A7
DEV_EPA_TOKEN=850D03CB-927A-45E6-AFDE-173C45F93E99
DEV_TUTOR_TOKEN=D1433FC9-85C6-4054-A97D-7D12B7AFD5BB 