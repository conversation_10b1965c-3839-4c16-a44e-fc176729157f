// ========== 認證相關常數 ==========

// 認證等級
export const CERTIFICATION_LEVEL = {
  BRONZE: 1,
  SILVER: 2,
  GREEN_FLAG: 3,
  G<PERSON>EN_FLAG_R1: 4,
  G<PERSON><PERSON>_FLAG_R2: 5,
  G<PERSON><PERSON>_FLAG_R3: 6,
} as const;

// 審核狀態
export const REVIEW_STATUS = {
  UNDER_REVIEW: 0,
  APPROVED: 1,
  REJECTED: 2,
  NEEDS_SUPPLEMENT: 3,
  NOT_SUBMITTED: 4,
} as const;

// 認證狀態
export const CERTIFICATION_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;

// 狀態文字映射
export const REVIEW_STATUS_LABELS: Record<number, string> = {
  [REVIEW_STATUS.APPROVED]: "審核通過",
  [REVIEW_STATUS.UNDER_REVIEW]: "審核中",
  [REVIEW_STATUS.REJECTED]: "已退件",
  [REVIEW_STATUS.NEEDS_SUPPLEMENT]: "待補件",
  [REVIEW_STATUS.NOT_SUBMITTED]: "尚未審核",
};

// 狀態圖示映射
export const REVIEW_STATUS_ICONS: Record<number, string> = {
  [REVIEW_STATUS.APPROVED]: "img/license-icon-accepted.svg",
  [REVIEW_STATUS.UNDER_REVIEW]: "img/license-icon-verifying.svg",
  [REVIEW_STATUS.REJECTED]: "img/license-icon-returned.svg",
  [REVIEW_STATUS.NEEDS_SUPPLEMENT]: "img/license-icon-modify.svg",
  [REVIEW_STATUS.NOT_SUBMITTED]: "img/license-icon-no-status.svg",
};

// 狀態描述映射
export const REVIEW_STATUS_DESCRIPTIONS: Record<number, string> = {
  [REVIEW_STATUS.APPROVED]: "認證已通過審核",
  [REVIEW_STATUS.UNDER_REVIEW]: "認證正在審核中",
  [REVIEW_STATUS.REJECTED]: "認證已被退回",
  [REVIEW_STATUS.NEEDS_SUPPLEMENT]: "需要補充資料",
  [REVIEW_STATUS.NOT_SUBMITTED]: "尚未提交審核",
};

// 狀態顏色映射
export const REVIEW_STATUS_COLORS: Record<number, string> = {
  [REVIEW_STATUS.APPROVED]: "text-green-700",
  [REVIEW_STATUS.UNDER_REVIEW]: "text-blue-700",
  [REVIEW_STATUS.REJECTED]: "text-red-700",
  [REVIEW_STATUS.NEEDS_SUPPLEMENT]: "text-orange-700",
  [REVIEW_STATUS.NOT_SUBMITTED]: "text-gray-700",
};

// 狀態背景顏色映射
export const REVIEW_STATUS_BG_COLORS: Record<number, string> = {
  [REVIEW_STATUS.APPROVED]: "bg-green-100",
  [REVIEW_STATUS.UNDER_REVIEW]: "bg-blue-100",
  [REVIEW_STATUS.REJECTED]: "bg-red-100",
  [REVIEW_STATUS.NEEDS_SUPPLEMENT]: "bg-orange-100",
  [REVIEW_STATUS.NOT_SUBMITTED]: "bg-gray-100",
};

// 認證類型定義
export const CERTIFICATION_TYPES = [
  { id: "Bronze", name: "銅牌", level: CERTIFICATION_LEVEL.BRONZE, requiresGreenFlag: false },
  { id: "Silver", name: "銀牌", level: CERTIFICATION_LEVEL.SILVER, requiresGreenFlag: false },
  { id: "GreenFlag", name: "綠旗", level: CERTIFICATION_LEVEL.GREEN_FLAG, requiresGreenFlag: false },
  { id: "GreenFlag", name: "綠旗R1", level: CERTIFICATION_LEVEL.GREEN_FLAG_R1, requiresGreenFlag: true },
  { id: "GreenFlag", name: "綠旗R2", level: CERTIFICATION_LEVEL.GREEN_FLAG_R2, requiresGreenFlag: true },
  { id: "GreenFlag", name: "綠旗R3", level: CERTIFICATION_LEVEL.GREEN_FLAG_R3, requiresGreenFlag: true },
] as const;

// 前端ID映射
export const FRONTEND_ID_MAP: Record<number, string> = {
  [CERTIFICATION_LEVEL.BRONZE]: "bronze",
  [CERTIFICATION_LEVEL.SILVER]: "silver",
  [CERTIFICATION_LEVEL.GREEN_FLAG]: "green-flag",
  [CERTIFICATION_LEVEL.GREEN_FLAG_R1]: "green-flag-r1",
  [CERTIFICATION_LEVEL.GREEN_FLAG_R2]: "green-flag-r2",
  [CERTIFICATION_LEVEL.GREEN_FLAG_R3]: "green-flag-r3",
};

// 錯誤訊息
export const CERTIFICATION_ERROR_MESSAGES = {
  USER_NOT_AUTHENTICATED: "使用者未認證",
  SCHOOL_NOT_ASSIGNED: "該帳號尚未分配學校",
  CERTIFICATION_NOT_FOUND: "找不到認證資料",
  DUPLICATE_CERTIFICATION: "已存在相同類型的認證申請",
  GREEN_FLAG_REQUIRED: "申請延續認證需要先通過綠旗認證",
  INVALID_CERTIFICATION_TYPE: "認證類型和等級為必填項目",
  CREATE_FAILED: "建立認證失敗",
  UPDATE_FAILED: "更新認證失敗",
  DELETE_FAILED: "刪除認證失敗",
  INVALID_PERMISSIONS: "沒有權限執行此操作",
  TIME_CONSTRAINT_NOT_MET: "不符合時間限制要求",
} as const;

// 成功訊息
export const CERTIFICATION_SUCCESS_MESSAGES = {
  LIST_RETRIEVED: "認證清單獲取成功",
  AVAILABILITY_CHECKED: "認證可用性檢查完成",
  CERTIFICATION_CREATED: "認證申請已建立",
  CERTIFICATION_UPDATED: "認證資料已更新",
  CERTIFICATION_DELETED: "認證申請已刪除",
  CERTIFICATION_SUBMITTED: "認證已提交審核",
} as const;

// 時間限制（年）
export const TIME_CONSTRAINTS = {
  GREEN_FLAG_R1_WAIT: 2, // 綠旗通過滿兩年才能申請R1
  GREEN_FLAG_R2_WAIT: 2, // R1通過滿兩年才能申請R2
  GREEN_FLAG_R3_WAIT: 2, // R2通過滿兩年才能申請R3
} as const;

// 工具函數：獲取審核狀態資訊
export const getReviewStatusInfo = (reviewStatus: number) => {
  return {
    label: REVIEW_STATUS_LABELS[reviewStatus] || "未知狀態",
    icon: REVIEW_STATUS_ICONS[reviewStatus] || "img/license-icon-no-status.svg",
    description: REVIEW_STATUS_DESCRIPTIONS[reviewStatus] || "狀態未知",
    color: REVIEW_STATUS_COLORS[reviewStatus] || "text-gray-700",
    bgColor: REVIEW_STATUS_BG_COLORS[reviewStatus] || "bg-gray-100",
  };
};

// 工具函數：獲取認證類型資訊
export const mapCertificationType = (levelName: string, level: number) => {
  const levelMap: Record<number, { name: string; fullName: string; isRenewal: boolean; icon: string }> = {
    [CERTIFICATION_LEVEL.BRONZE]: {
      name: "銅牌",
      fullName: "銅牌生態學校",
      isRenewal: false,
      icon: "img/medal-bronze.png",
    },
    [CERTIFICATION_LEVEL.SILVER]: {
      name: "銀牌",
      fullName: "銀牌生態學校",
      isRenewal: false,
      icon: "img/medal-silver.png",
    },
    [CERTIFICATION_LEVEL.GREEN_FLAG]: {
      name: "綠旗",
      fullName: "綠旗生態學校",
      isRenewal: false,
      icon: "img/medal-greenflag.png",
    },
    [CERTIFICATION_LEVEL.GREEN_FLAG_R1]: {
      name: "綠旗R1",
      fullName: "綠旗生態學校延續認證第1次",
      isRenewal: true,
      icon: "img/medal-greenflag.png",
    },
    [CERTIFICATION_LEVEL.GREEN_FLAG_R2]: {
      name: "綠旗R2",
      fullName: "綠旗生態學校延續認證第2次",
      isRenewal: true,
      icon: "img/medal-greenflag.png",
    },
    [CERTIFICATION_LEVEL.GREEN_FLAG_R3]: {
      name: "綠旗R3",
      fullName: "綠旗生態學校延續認證第3次",
      isRenewal: true,
      icon: "img/medal-greenflag.png",
    },
  };

  return (
    levelMap[level] || {
      name: "未知",
      fullName: "未知認證類型",
      isRenewal: false,
      icon: "img/medal-default.svg",
    }
  );
};

// 工具函數：計算年份差
export const getYearsSinceApproval = (approvedDate?: Date): number => {
  if (!approvedDate) return 0;
  const now = new Date();
  const diffInMs = now.getTime() - new Date(approvedDate).getTime();
  return diffInMs / (1000 * 60 * 60 * 24 * 365);
};

// 工具函數：檢查是否可編輯
export const isEditable = (reviewStatus: number): boolean => {
  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED || reviewStatus === REVIEW_STATUS.NEEDS_SUPPLEMENT;
};

// 工具函數：檢查是否可刪除
export const isDeletable = (reviewStatus: number): boolean => {
  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED;
};

// 工具函數：檢查是否可提交
export const canSubmit = (reviewStatus: number): boolean => {
  return reviewStatus === REVIEW_STATUS.NOT_SUBMITTED;
};

// 工具函數：獲取前端ID
export const getFrontendId = (level: number): string => {
  return FRONTEND_ID_MAP[level] || "unknown";
};
