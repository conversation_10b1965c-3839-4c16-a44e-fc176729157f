// ========== 憑證相關資料模型 ==========

export interface CertificateInfo {
  subject: string;
  issuer: string;
  serialNumber: string;
  validFrom: string;
  validTo: string;
  fingerprint: string;
}

export interface CertificateValidateRequest {
  certificateData: string;
}

export interface CertificateBindRequest {
  certificateInfo: CertificateInfo;
  accountId: string;
}

export interface CertificateLoginRequest {
  certificateData: string;
  password: string;
}

export interface CertificateValidationResult {
  isValid: boolean;
  certificateInfo: CertificateInfo;
}

export interface CertificateBindResult {
  certificateId: string;
  bindTime: string;
  status: string;
}

export interface CertificateBindingInfo {
  accountId: string;
  hasCertificate: boolean;
  certificateId: string | null;
  lastUpdateTime: Date;
}

export interface CertificateLoginResult {
  token: string;
  user: {
    id: string;
    username: string;
    displayName: string;
    role: string;
  };
  expiryDate: string;
}

export interface CertificateTestResult {
  userId: string;
  userAccount: string;
  userRole: string;
  timestamp: string;
}

// 資料庫查詢結果介面
export interface AccountQueryResult {
  citizen_digital_number: string;
}

export interface AccountFullQueryResult {
  sid: string;
  account: string;
  cname: string;
  isuse: number;
  member_role: string;
}

export interface BindingQueryResult {
  accountId: string;
  certificateId: string;
  lastUpdateTime: Date;
}

// API 回應介面
export interface CertificateValidateResponse {
  success: boolean;
  message: string;
  data?: CertificateValidationResult;
}

export interface CertificateBindResponse {
  success: boolean;
  message: string;
  data?: CertificateBindResult;
}

export interface CertificateUnbindResponse {
  success: boolean;
  message: string;
}

export interface CertificateBindingsResponse {
  success: boolean;
  message?: string;
  data?: CertificateBindingInfo;
}

export interface CertificateLoginResponse {
  success: boolean;
  message: string;
  data?: CertificateLoginResult;
}

export interface CertificateTestResponse {
  success: boolean;
  message: string;
  data?: CertificateTestResult;
}

// 查詢參數類型
export interface CertificateValidateParams {}

export interface CertificateBindParams {}

export interface CertificateUnbindParams {
  accountId: string;
}

export interface CertificateBindingsParams {}

export interface CertificateLoginParams {}

export interface CertificateTestParams {}

export interface CertificateValidateQueryParams {}

export interface CertificateBindQueryParams {}

export interface CertificateUnbindQueryParams {}

export interface CertificateBindingsQueryParams {}

export interface CertificateLoginQueryParams {}

export interface CertificateTestQueryParams {}
