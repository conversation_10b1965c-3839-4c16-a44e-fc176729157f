// ========== 憑證相關常數 ==========

// 憑證狀態
export const CERTIFICATE_STATUS = {
  VALID: "valid",
  INVALID: "invalid",
  EXPIRED: "expired",
} as const;

// Token 類型
export const TOKEN_TYPES = {
  CERTIFICATE_LOGIN: "CERTIFICATE_LOGIN",
  NORMAL_LOGIN: "NORMAL_LOGIN",
} as const;

// 帳號狀態
export const ACCOUNT_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  SUSPENDED: 2,
} as const;

// 憑證動作類型
export const CERTIFICATE_ACTIONS = {
  BIND: "CERTIFICATE_BIND",
  UNBIND: "CERTIFICATE_UNBIND",
  LOGIN: "CERTIFICATE_LOGIN",
  VALIDATE: "CERTIFICATE_VALIDATE",
} as const;

// 憑證驗證要求
export const CERTIFICATE_REQUIREMENTS = {
  MIN_DATA_LENGTH: 10,
  MIN_PASSWORD_LENGTH: 6,
  TOKEN_EXPIRY_DAYS: 30,
} as const;

// 錯誤訊息
export const CERTIFICATE_ERROR_MESSAGES = {
  MISSING_CERTIFICATE_DATA: "憑證資料不能為空",
  INVALID_CERTIFICATE: "憑證格式無效或已過期",
  MISSING_BIND_INFO: "憑證資訊和帳號ID不能為空",
  PERMISSION_DENIED_BIND: "只能綁定自己的帳號",
  PERMISSION_DENIED_UNBIND: "只能解除自己的憑證綁定",
  ACCOUNT_NOT_FOUND: "帳號不存在",
  NO_CERTIFICATE_BOUND: "此帳號未綁定任何憑證",
  UNBIND_FAILED: "解除憑證綁定失敗",
  MISSING_LOGIN_CREDENTIALS: "憑證資料和密碼不能為空",
  INVALID_PASSWORD: "憑證密碼錯誤",
  NO_BOUND_ACCOUNT: "未找到綁定此憑證的有效帳號",
  VALIDATION_ERROR: "憑證驗證過程發生錯誤",
  BIND_ERROR: "憑證綁定過程發生錯誤",
  UNBIND_ERROR: "解除憑證綁定過程發生錯誤",
  QUERY_ERROR: "查詢憑證綁定狀態發生錯誤",
  LOGIN_ERROR: "憑證登入過程發生錯誤",
  TEST_FAILED: "憑證測試失敗",
} as const;

// 成功訊息
export const CERTIFICATE_SUCCESS_MESSAGES = {
  VALIDATION_SUCCESS: "憑證驗證成功",
  BIND_SUCCESS: "憑證綁定成功",
  UNBIND_SUCCESS: "憑證綁定已成功解除",
  LOGIN_SUCCESS: "憑證登入成功",
  TEST_SUCCESS: "憑證API測試成功",
} as const;

// SQL 查詢模板
export const SQL_QUERIES = {
  CHECK_ACCOUNT_CERTIFICATE: "SELECT citizen_digital_number FROM Account WHERE sid = @accountId",
  UPDATE_UNBIND_CERTIFICATE: `
    UPDATE Account SET 
      citizen_digital_number = NULL,
      UpdateTime = GETDATE(),
      UpdateUser = @userId
    WHERE sid = @accountId
  `,
  GET_USER_BINDINGS: `
    SELECT 
      sid as accountId,
      citizen_digital_number as certificateId,
      UpdateTime as lastUpdateTime
    FROM Account 
    WHERE sid = @userId
  `,
  GET_ACCOUNT_BY_CERTIFICATE: `
    SELECT 
      a.sid as accountId,
      a.account as username,
      a.cname as displayName,
      a.isuse as status,
      mp.member_role as role
    FROM Account a
    LEFT JOIN MemberProfiles mp ON a.sid = mp.account_sid
    WHERE a.citizen_digital_number = @certificateId AND a.isuse = @activeStatus
  `,
  INSERT_TOKEN: `
    INSERT INTO UserToken (AccountSid, Token, TokenType, ExpiredTime, IsActive, CreatedTime)
    VALUES (@accountId, @token, @tokenType, @expiredTime, 1, GETDATE())
  `,
  INSERT_ACTION_LOG: `
    INSERT INTO sys_logs (account_sid, action, description, ip_address, user_agent, create_time)
    VALUES (@accountId, @action, @description, @ipAddress, @userAgent, GETDATE())
  `,
} as const;

// 憑證基本結構檢查
export const CERTIFICATE_PATTERNS = {
  BEGIN_CERTIFICATE: "BEGIN CERTIFICATE",
  COMMON_NAME: "CN=",
  MIN_CERT_LENGTH: 100,
} as const;

// 模擬憑證資訊
export const DEMO_CERTIFICATE = {
  SUBJECT: "CN=測試用戶,OU=臺灣自然人憑證,O=政府憑證管理中心,C=TW",
  ISSUER: "CN=政府憑證管理中心,O=行政院,C=TW",
  VALID_FROM: "2023-01-01",
  VALID_TO: "2026-12-31",
  SERIAL_NUMBER_LENGTH: 16,
} as const;

// 工具函數：驗證憑證資料格式
export const validateCertificateData = (certificateData: string): boolean => {
  try {
    if (!certificateData || certificateData.length < CERTIFICATE_REQUIREMENTS.MIN_DATA_LENGTH) {
      return false;
    }

    // 檢查是否包含憑證的基本結構
    const hasBasicStructure =
      certificateData.includes(CERTIFICATE_PATTERNS.BEGIN_CERTIFICATE) ||
      certificateData.includes(CERTIFICATE_PATTERNS.COMMON_NAME) ||
      certificateData.length > CERTIFICATE_PATTERNS.MIN_CERT_LENGTH;

    return hasBasicStructure;
  } catch (error) {
    console.error("憑證驗證錯誤:", error);
    return false;
  }
};

// 工具函數：解析憑證資訊
export const parseCertificateInfo = (certificateData: string): any => {
  const crypto = require("crypto");

  return {
    subject: DEMO_CERTIFICATE.SUBJECT,
    issuer: DEMO_CERTIFICATE.ISSUER,
    serialNumber: crypto.createHash("sha256").update(certificateData).digest("hex").substring(0, DEMO_CERTIFICATE.SERIAL_NUMBER_LENGTH),
    validFrom: DEMO_CERTIFICATE.VALID_FROM,
    validTo: DEMO_CERTIFICATE.VALID_TO,
    fingerprint: crypto.createHash("sha256").update(certificateData).digest("hex"),
  };
};

// 工具函數：驗證憑證密碼
export const validateCertificatePassword = (certificateData: string, password: string): boolean => {
  try {
    if (!password || password.length < CERTIFICATE_REQUIREMENTS.MIN_PASSWORD_LENGTH) {
      return false;
    }

    // 簡單的模擬驗證：密碼長度至少6位
    return password.length >= CERTIFICATE_REQUIREMENTS.MIN_PASSWORD_LENGTH;
  } catch (error) {
    console.error("憑證密碼驗證錯誤:", error);
    return false;
  }
};

// 工具函數：檢查用戶權限
export const hasPermissionForAccount = (userId: string, targetAccountId: string): boolean => {
  return userId === targetAccountId;
};

// 工具函數：生成Token過期時間
export const generateTokenExpiry = (): Date => {
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + CERTIFICATE_REQUIREMENTS.TOKEN_EXPIRY_DAYS);
  return expiryDate;
};

// 工具函數：格式化綁定資訊
export const formatBindingInfo = (binding: any): any => {
  const hasCertificate = !!binding.certificateId;

  return {
    accountId: binding.accountId,
    hasCertificate,
    certificateId: hasCertificate ? binding.certificateId : null,
    lastUpdateTime: binding.lastUpdateTime,
  };
};

// 工具函數：格式化登入結果
export const formatLoginResult = (account: any, token: string, expiryDate: Date): any => {
  return {
    token,
    user: {
      id: account.accountId,
      username: account.username,
      displayName: account.displayName,
      role: account.role || "user",
    },
    expiryDate: expiryDate.toISOString(),
  };
};

// 工具函數：生成日誌描述
export const generateLogDescription = (action: string): string => {
  switch (action) {
    case CERTIFICATE_ACTIONS.BIND:
      return "綁定自然人憑證";
    case CERTIFICATE_ACTIONS.UNBIND:
      return "解除自然人憑證綁定";
    case CERTIFICATE_ACTIONS.LOGIN:
      return "自然人憑證登入成功";
    case CERTIFICATE_ACTIONS.VALIDATE:
      return "驗證自然人憑證";
    default:
      return "憑證相關操作";
  }
};

// 工具函數：檢查帳號是否有效
export const isValidAccount = (account: any): boolean => {
  return account && account.status === ACCOUNT_STATUS.ACTIVE;
};
