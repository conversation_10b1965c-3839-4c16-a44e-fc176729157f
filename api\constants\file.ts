// ========== 檔案處理相關常數 ==========

// 檔案大小限制（位元組）
export const FILE_SIZE_LIMITS = {
  GENERAL_FILE: 10 * 1024 * 1024, // 10MB
  SCHOOL_LOGO: 5 * 1024 * 1024, // 5MB
} as const;

// 檔案數量限制
export const FILE_COUNT_LIMITS = {
  MULTIPLE_UPLOAD: 5,
  SINGLE_UPLOAD: 1,
} as const;

// 允許的檔案類型
export const ALLOWED_MIME_TYPES = {
  IMAGES: ["image/jpeg", "image/jpg", "image/png", "image/gif"],
  DOCUMENTS: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ],
  VIDEOS: ["video/mp4", "video/avi", "video/mov"],
  SCHOOL_LOGO: ["image/jpeg", "image/jpg", "image/png"],
} as const;

// 所有允許的檔案類型
export const ALL_ALLOWED_TYPES = [...ALLOWED_MIME_TYPES.IMAGES, ...ALLOWED_MIME_TYPES.DOCUMENTS, ...ALLOWED_MIME_TYPES.VIDEOS] as const;

// 支援的檔案副檔名
export const SUPPORTED_EXTENSIONS = {
  IMAGES: ["jpg", "jpeg", "png", "gif"],
  DOCUMENTS: ["pdf", "doc", "docx", "xls", "xlsx"],
  VIDEOS: ["mp4", "avi", "mov"],
} as const;

// 檔案類型
export const FILE_TYPES = {
  GENERAL: "general",
  SCHOOL_LOGO: "school-logo",
  CERTIFICATION: "certification",
  QUESTION: "question",
} as const;

// 語言代碼
export const LOCALE_CODES = {
  ZH_TW: "zh-TW",
  EN: "en",
} as const;

// 資料狀態
export const DATA_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  DELETED: 2,
} as const;

// 錯誤訊息
export const FILE_ERROR_MESSAGES = {
  NO_FILE_UPLOADED: "沒有上傳檔案",
  UNSUPPORTED_FILE_TYPE: "不支援的檔案類型",
  FILE_TOO_LARGE: "檔案大小超過限制",
  TOO_MANY_FILES: "檔案數量超過限制",
  INVALID_FILENAME: "非法的檔案名稱",
  FILE_NOT_FOUND: "找不到檔案",
  SCHOOL_NOT_FOUND: "找不到學校資料",
  INVALID_USER_AUTH: "無效的用戶認證",
  FILE_RECORD_CREATE_FAILED: "檔案記錄建立失敗",
  UPLOAD_DIR_CREATE_FAILED: "無法創建上傳目錄",
  NO_WRITE_PERMISSION: "目錄沒有寫入權限",
  FILE_PROCESSING_FAILED: "檔案處理失敗",
  LOGO_FORMAT_ERROR: "校徽圖檔請上傳jpg、png格式",
  LOGO_SIZE_ERROR: "檔案大小不可超過 5MB",
  PLEASE_SELECT_LOGO: "請選擇校徽圖檔",
} as const;

// 成功訊息
export const FILE_SUCCESS_MESSAGES = {
  FILE_UPLOADED: "檔案上傳成功",
  FILE_DELETED: "檔案刪除成功",
  FILE_INFO_RETRIEVED: "檔案資訊獲取成功",
  SCHOOL_LOGO_UPLOADED: "校徽上傳成功",
  SCHOOL_LOGO_RETRIEVED: "校徽獲取成功",
  UPLOAD_DIAGNOSTICS_COMPLETE: "上傳診斷完成",
} as const;

// Multer 錯誤代碼
export const MULTER_ERROR_CODES = {
  LIMIT_FILE_SIZE: "LIMIT_FILE_SIZE",
  LIMIT_FILE_COUNT: "LIMIT_FILE_COUNT",
  LIMIT_UNEXPECTED_FILE: "LIMIT_UNEXPECTED_FILE",
} as const;

// 預設目錄名稱
export const DEFAULT_DIRECTORIES = {
  UPLOADS: "uploads",
  SCHOOL_LOGOS: "school-logos",
} as const;

// 工具函數：檢查檔案類型是否允許
export const isAllowedFileType = (mimetype: string): boolean => {
  return ALL_ALLOWED_TYPES.includes(mimetype as any);
};

// 工具函數：檢查是否為校徽格式
export const isValidLogoFormat = (mimetype: string): boolean => {
  return ALLOWED_MIME_TYPES.SCHOOL_LOGO.includes(mimetype as any);
};

// 工具函數：檢查檔案大小
export const isValidFileSize = (size: number, fileType: string = FILE_TYPES.GENERAL): boolean => {
  const limit = fileType === FILE_TYPES.SCHOOL_LOGO ? FILE_SIZE_LIMITS.SCHOOL_LOGO : FILE_SIZE_LIMITS.GENERAL_FILE;
  return size <= limit;
};

// 工具函數：驗證檔名安全性
export const isValidFilename = (filename: string): boolean => {
  return !filename.includes("..") && !filename.includes("/") && !filename.includes("\\");
};

// 工具函數：生成檔名
export const generateFilename = (originalName: string, accountId?: string | number): string => {
  const timestamp = Date.now();
  const ext = originalName.split(".").pop() || "";
  const name = originalName.split(".").slice(0, -1).join(".");

  if (accountId) {
    return `${timestamp}_${accountId}_${name}.${ext}`;
  }
  return `${timestamp}_${name}.${ext}`;
};

// 工具函數：生成校徽檔名
export const generateLogoFilename = (originalName: string, accountId: string | number): string => {
  const timestamp = Date.now();
  const ext = originalName.split(".").pop() || "";
  return `school-logo-${accountId}-${timestamp}.${ext}`;
};

// 工具函數：解析檔名中的原始名稱
export const parseOriginalName = (filename: string): string => {
  return filename.split("_").slice(1).join("_");
};

// 工具函數：檢查 Multer 錯誤類型
export const getMulterErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case MULTER_ERROR_CODES.LIMIT_FILE_SIZE:
      return `檔案大小超過限制 (最大 ${FILE_SIZE_LIMITS.GENERAL_FILE / (1024 * 1024)}MB)`;
    case MULTER_ERROR_CODES.LIMIT_FILE_COUNT:
      return `檔案數量超過限制 (最多 ${FILE_COUNT_LIMITS.MULTIPLE_UPLOAD} 個檔案)`;
    default:
      return FILE_ERROR_MESSAGES.FILE_PROCESSING_FAILED;
  }
};

// 工具函數：取得支援的檔案類型
export const getSupportedFileTypes = () => {
  return {
    images: SUPPORTED_EXTENSIONS.IMAGES,
    documents: SUPPORTED_EXTENSIONS.DOCUMENTS,
    videos: SUPPORTED_EXTENSIONS.VIDEOS,
  };
};
