import { User } from "@/services/authService";

/**
 * 格式化使用者顯示名稱
 * 格式: [學校名稱(所屬單位)]-[使用者的中文姓名]
 */
export const formatUserDisplayName = (user: User): string => {
  // 提取基本資訊
  const userName = extractChineseName(user.name);
  const schoolName = user.organization || "未知機構";
  const department = user.position || "";

  // 構建顯示格式
  if (department && department.trim()) {
    return `${schoolName}(${department})-${userName}`;
  } else {
    return `${schoolName}-${userName}`;
  }
};

/**
 * 從複合名稱中提取中文姓名
 * 處理現有的格式: "職稱-中文姓名 (英文姓名)"
 */
const extractChineseName = (fullName: string): string => {
  if (!fullName) return "未知使用者";

  // 移除英文名稱部分 (括號內容)
  let name = fullName.replace(/\s*\([^)]*\)\s*$/, "");

  // 如果有職稱前綴，提取姓名部分
  if (name.includes("-")) {
    const parts = name.split("-");
    name = parts[parts.length - 1].trim();
  }

  return name || fullName;
};

/**
 * 根據角色調整顯示格式
 */
export const formatUserDisplayNameByRole = (user: User): string => {
  const userName = extractChineseName(user.name);

  // 根據角色使用不同的組織資訊處理邏輯
  switch (user.role) {
    case "school": {
      // 學校身份：優先使用API返回的學校名稱
      const schoolName = user.organization || "未知學校";
      const department = user.position || "";

      if (department && department.trim()) {
        return `${schoolName}(${department})-${userName}`;
      } else {
        return `${schoolName}-${userName}`;
      }
    }

    case "tutor": {
      // 輔導員：使用實際的輔導機構名稱
      const tutorOrg = user.organization || "輔導機構";
      const tutorDept = user.position || "";

      if (tutorDept && tutorDept.trim()) {
        return `${tutorOrg}(${tutorDept})-${userName}`;
      } else {
        return `${tutorOrg}-${userName}`;
      }
    }

    case "epa": {
      // 環保署：使用實際的環保署單位名稱
      const epaOrg = user.organization || "環保署";
      const epaDept = user.position || "";

      if (epaDept && epaDept.trim()) {
        return `${epaOrg}(${epaDept})-${userName}`;
      } else {
        return `${epaOrg}-${userName}`;
      }
    }

    default: {
      // 其他角色：使用標準格式
      const orgName = user.organization || "未知機構";
      const deptName = user.position || "";

      if (deptName && deptName.trim()) {
        return `${orgName}(${deptName})-${userName}`;
      } else {
        return `${orgName}-${userName}`;
      }
    }
  }
};

/**
 * 處理過長的顯示名稱
 */
export const truncateDisplayName = (
  name: string,
  maxLength: number = 30
): string => {
  if (name.length <= maxLength) return name;

  // 智能截斷：保留學校名稱和姓名
  const parts = name.split("-");
  if (parts.length === 2) {
    const [schoolPart, userName] = parts;
    const availableLength = maxLength - userName.length - 3; // 3 for "...-"

    if (availableLength > 10) {
      return `${schoolPart.substring(0, availableLength)}...-${userName}`;
    }
  }

  return `${name.substring(0, maxLength - 3)}...`;
};

/**
 * 從 simple-profile API 資料格式化顯示名稱
 */
export const formatDisplayNameFromProfile = (profileData: any): string => {
  if (!profileData) return "未知使用者";

  const chineseName = profileData.member_cname || "未知使用者";

  // 🔧 修復：根據角色正確獲取機構名稱
  let organizationName = "未知機構";
  let departmentName = "";

  // 根據member_role決定機構名稱的來源
  const memberRole = profileData.member_role;

  switch (memberRole) {
    case "school":
      // 學校身份：place_cname是學校名稱，job_cname可能是部門
      organizationName =
        profileData.place_cname || profileData.school_name || "未知學校";
      departmentName = profileData.job_cname || "";
      break;

    case "epa":
      // 環保署身份：place_cname是機構名稱
      organizationName = profileData.place_cname || "環保署";
      departmentName = profileData.job_cname || "";
      break;

    case "tutor":
      // 輔導員身份：place_cname是輔導機構名稱
      organizationName = profileData.place_cname || "輔導機構";
      departmentName = profileData.job_cname || "";
      break;

    default:
      // 預設邏輯
      organizationName =
        profileData.place_cname || profileData.school_name || "未知機構";
      departmentName = profileData.job_cname || "";
  }

  // 構建顯示名稱
  if (departmentName && departmentName.trim()) {
    return `${organizationName}(${departmentName})-${chineseName}`;
  } else {
    return `${organizationName}-${chineseName}`;
  }
};

/**
 * 安全的格式化函數，包含錯誤處理
 */
export const safeFormatUserDisplayName = (
  user: User | null | undefined
): string => {
  try {
    if (!user) {
      console.warn("⚠️ [UserDisplay] 使用者資料為空，使用預設值");
      return "未知使用者";
    }

    return formatUserDisplayNameByRole(user);
  } catch (error) {
    console.error("❌ [UserDisplay] 格式化使用者名稱失敗:", error);
    return user?.name || "未知使用者";
  }
};

/**
 * 驗證使用者資料完整性
 */
export const validateUserData = (
  user: User
): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} => {
  const missingFields: string[] = [];
  const warnings: string[] = [];

  if (!user.name) missingFields.push("name");
  if (!user.role) missingFields.push("role");

  if (!user.organization) warnings.push("缺少機構資訊");
  if (!user.position) warnings.push("缺少職位資訊");

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings,
  };
};
